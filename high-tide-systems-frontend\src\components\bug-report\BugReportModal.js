'use client';

import React, { useState } from 'react';
import { Bug, MapPin, FileText, AlertTriangle, Tag } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog.jsx';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Label from '@/components/ui/Label';
import CustomSelect from '@/components/ui/CustomSelect';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import api from '@/services/api';

const BugReportModal = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    location: '',
    description: '',
    priority: 'MEDIUM',
    category: 'GENERAL'
  });

  const priorityOptions = [
    { value: 'LOW', label: 'Baixa' },
    { value: 'MEDIUM', label: 'Média' },
    { value: 'HIGH', label: 'Alta' },
    { value: 'CRITICAL', label: 'Crítica' }
  ];

  const categoryOptions = [
    { value: 'GENERAL', label: 'Geral' },
    { value: 'UI_UX', label: 'Interface/Experiência' },
    { value: 'PERFORMANCE', label: 'Performance' },
    { value: 'FUNCTIONALITY', label: 'Funcionalidade' },
    { value: 'DATA', label: 'Dados' },
    { value: 'SECURITY', label: 'Segurança' },
    { value: 'INTEGRATION', label: 'Integração' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await api.post('/bug-reports', formData);

      if (response.data.success) {
        // Reset form and close modal
        resetForm();
        onClose();

        toast_success({
          title: 'Sucesso!',
          message: 'Bug reportado com sucesso! Nossa equipe foi notificada.'
        });
      }
    } catch (error) {
      console.error('Erro ao reportar bug:', error);

      const errorMessage = error.response?.data?.message || 'Erro ao reportar bug. Tente novamente.';

      toast_error({
        title: 'Erro',
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      location: '',
      description: '',
      priority: 'MEDIUM',
      category: 'GENERAL'
    });
  };

  const handleClose = () => {
    if (!isSubmitting) {
      resetForm();
      onClose();
    }
  };

  const isFormValid = formData.title.trim() && formData.description.trim();

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[85vh] overflow-hidden border-2 border-amber-400 dark:border-amber-500">
        <DialogHeader className="pb-4 border-b-2 border-amber-400 dark:border-amber-500 flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg text-white">
              <Bug className="h-5 w-5" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold text-amber-800 dark:text-white border-l-4 border-amber-400 dark:border-amber-500 pl-3">
                Reportar Bug
              </DialogTitle>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                Relate um problema encontrado no sistema para nossa equipe
              </p>
            </div>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(85vh-100px)]">
          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Dica de como reportar */}
            <div className="mb-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-md">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-xs font-medium text-amber-800 dark:text-amber-200 mb-1">
                    Como reportar um bug efetivamente
                  </h4>
                  <p className="text-xs text-amber-700 dark:text-amber-300 leading-tight">
                    Seja específico sobre onde o problema ocorreu e descreva os passos para reproduzi-lo.
                    Isso nos ajudará a resolver o problema mais rapidamente.
                  </p>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Categoria */}
                <div className="space-y-2">
                  <Label htmlFor="category" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    <Tag className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    Categoria *
                  </Label>
                  <CustomSelect
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    options={categoryOptions}
                    required
                    className="border-2 border-amber-300 dark:border-amber-600 focus:border-amber-500 dark:focus:border-amber-400"
                  />
                </div>

                {/* Prioridade */}
                <div className="space-y-2">
                  <Label htmlFor="priority" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    Prioridade Sugerida *
                  </Label>
                  <CustomSelect
                    id="priority"
                    name="priority"
                    value={formData.priority}
                    onChange={handleInputChange}
                    options={priorityOptions}
                    required
                    className="border-2 border-amber-300 dark:border-amber-600 focus:border-amber-500 dark:focus:border-amber-400"
                  />
                </div>
              </div>

              {/* Título */}
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <FileText className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  Título do Bug *
                </Label>
                <Input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Ex: Erro ao salvar dados do paciente"
                  required
                  maxLength={100}
                  className="border-2 border-amber-300 dark:border-amber-600 focus:border-amber-500 dark:focus:border-amber-400"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Descreva brevemente o problema encontrado
                </p>
              </div>

              {/* Local */}
              <div className="space-y-2">
                <Label htmlFor="location" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  Local onde ocorreu
                </Label>
                <Input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="Ex: Módulo Pessoas > Cadastro de Pacientes"
                  maxLength={150}
                  className="border-2 border-amber-300 dark:border-amber-600 focus:border-amber-500 dark:focus:border-amber-400"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Informe a página, módulo ou seção onde o bug aconteceu
                </p>
              </div>

              {/* Descrição */}
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <FileText className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  Descrição detalhada *
                </Label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Descreva o que aconteceu, o que você esperava que acontecesse e os passos para reproduzir o problema..."
                  rows={4}
                  required
                  maxLength={1000}
                  className="w-full px-3 py-2 border-2 border-amber-300 dark:border-amber-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 dark:focus:border-amber-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
                />
                <div className="flex justify-between text-xs">
                  <p className="text-gray-500 dark:text-gray-400">
                    Descreva o problema em detalhes, incluindo os passos para reproduzi-lo
                  </p>
                  <span className="text-amber-600 dark:text-amber-400 font-medium">
                    {formData.description.length}/1000
                  </span>
                </div>
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-amber-400 dark:border-amber-500 pt-3 flex-shrink-0 px-4 pb-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="border-2 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={!isFormValid || isSubmitting}
              className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white border-2 border-amber-500 hover:border-amber-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Enviando...
                </>
              ) : (
                'Reportar Bug'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BugReportModal;
