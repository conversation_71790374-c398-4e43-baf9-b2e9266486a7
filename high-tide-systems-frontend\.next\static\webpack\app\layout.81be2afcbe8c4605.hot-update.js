"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/ChatContext.js":
/*!*************************************!*\
  !*** ./src/contexts/ChatContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n// Cache para evitar requisições duplicadas\nconst requestCache = {\n    conversations: {\n        data: null,\n        timestamp: 0\n    },\n    unreadCount: {\n        data: null,\n        timestamp: 0\n    },\n    messages: {},\n    tokenCheck: {\n        timestamp: 0,\n        valid: false\n    } // Cache para verificação de token\n};\n// Tempo de expiração do cache em milissegundos\nconst CACHE_EXPIRATION = 30000; // 30 segundos para dados normais\nconst TOKEN_CHECK_EXPIRATION = 60000; // 1 minuto para verificação de token\n// Função de debounce para limitar a frequência de chamadas\nconst debounce = (func, wait)=>{\n    let timeout;\n    return function executedFunction() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n};\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const { user, logout } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversation, setActiveConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPanelOpen, setIsPanelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Obter o token atual do localStorage - memoizado para evitar chamadas desnecessárias\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"ChatProvider.useCallback[getCurrentToken]\"], []);\n    // Função para lidar com erros de autenticação\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[handleAuthError]\": ()=>{\n            console.error('Erro de autenticação detectado. Redirecionando para login...');\n            // Limpar dados locais\n            setConversations([]);\n            setMessages({});\n            setActiveConversation(null);\n            setUnreadCount(0);\n            setIsPanelOpen(false);\n            setIsModalOpen(false);\n            // Desconectar socket se existir\n            if (socket) {\n                try {\n                    socket.disconnect();\n                } catch (error) {\n                    console.error('Erro ao desconectar socket:', error);\n                }\n                setIsConnected(false);\n            }\n            // Limpar cache\n            requestCache.conversations.data = null;\n            requestCache.unreadCount.data = null;\n            requestCache.messages = {}; // Limpar cache de mensagens\n            // Redirecionar para login\n            if (logout) {\n                logout();\n            }\n        }\n    }[\"ChatProvider.useCallback[handleAuthError]\"], [\n        socket,\n        logout\n    ]);\n    // Usar referências para controlar estados que não devem causar re-renderizações\n    const socketInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isLoadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const lastInitAttemptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // Redefinir a referência quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário for null (logout), desconectar o socket\n            if (!user && socket) {\n                try {\n                    socket.disconnect();\n                    setIsConnected(false);\n                    setSocket(null);\n                } catch (error) {\n                    console.error('Erro ao desconectar socket após logout:', error);\n                }\n            }\n            // Resetar estado quando o usuário muda\n            socketInitializedRef.current = false;\n            lastInitAttemptRef.current = 0;\n            reconnectAttemptsRef.current = 0;\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        socket\n    ]);\n    // Limpar estado do chat quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário mudou (novo login), limpar todo o estado do chat\n            if (user) {\n                console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);\n                // Limpar conversas e mensagens para o novo usuário\n                setConversations([]);\n                setMessages({});\n                setActiveConversation(null);\n                setUnreadCount(0);\n                setIsPanelOpen(false);\n                setIsModalOpen(false);\n                // Limpar cache\n                requestCache.conversations.data = null;\n                requestCache.unreadCount.data = null;\n                requestCache.messages = {};\n                requestCache.tokenCheck.timestamp = 0;\n                requestCache.tokenCheck.valid = false;\n                console.log('Cache limpo para novo usuário');\n                // Limpar dados residuais do localStorage relacionados ao chat\n                if (true) {\n                    // Limpar qualquer cache específico do chat que possa estar no localStorage\n                    const keysToRemove = [];\n                    for(let i = 0; i < localStorage.length; i++){\n                        const key = localStorage.key(i);\n                        if (key && (key.startsWith('chat_') || key.startsWith('conversation_') || key.startsWith('message_'))) {\n                            keysToRemove.push(key);\n                        }\n                    }\n                    keysToRemove.forEach({\n                        \"ChatProvider.useEffect\": (key)=>localStorage.removeItem(key)\n                    }[\"ChatProvider.useEffect\"]);\n                    console.log('Dados residuais do chat removidos do localStorage');\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o ID do usuário mudar\n    // Inicializar conexão WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se não há usuário, não inicializar\n            if (!user) {\n                console.log('Usuário não logado, não inicializando WebSocket');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Inicializando chat para usuário:', user.role || 'USER');\n            // Se já existe um socket conectado, não fazer nada\n            if (socket && isConnected) {\n                console.log('WebSocket já está conectado, não é necessário reinicializar');\n                return;\n            }\n            // Se já está inicializado e a última tentativa foi recente, não tentar novamente\n            if (socketInitializedRef.current && Date.now() - lastInitAttemptRef.current < 60000) {\n                console.log('Socket já inicializado recentemente, aguardando...');\n                return;\n            }\n            // Marcar como inicializado imediatamente para evitar múltiplas tentativas\n            socketInitializedRef.current = true;\n            lastInitAttemptRef.current = Date.now();\n            // Função assíncrona para inicializar o WebSocket\n            const initializeWebSocket = {\n                \"ChatProvider.useEffect.initializeWebSocket\": async ()=>{\n                    // Verificar se o usuário está logado antes de inicializar o WebSocket\n                    if (!user) return;\n                    // Clientes agora podem usar o chat\n                    console.log('Inicializando WebSocket para:', user.role || 'USER');\n                    const currentToken = getCurrentToken();\n                    if (!currentToken) return;\n                    // Evitar reconexões desnecessárias - verificação adicional\n                    if (socket) {\n                        if (isConnected) {\n                            console.log('WebSocket já conectado, ignorando inicialização');\n                            return socket; // Retornar o socket existente\n                        } else {\n                            // Se o socket existe mas não está conectado, tentar reconectar em vez de criar um novo\n                            console.log('Socket existe mas não está conectado, tentando reconectar');\n                            try {\n                                // Tentar reconectar o socket existente em vez de criar um novo\n                                if (!socket.connected && socket.connect) {\n                                    socket.connect();\n                                    console.log('Tentativa de reconexão iniciada');\n                                    return socket;\n                                }\n                            } catch (error) {\n                                console.error('Erro ao reconectar socket existente:', error);\n                                // Só desconectar se a reconexão falhar\n                                try {\n                                    socket.disconnect();\n                                } catch (disconnectError) {\n                                    console.error('Erro ao desconectar socket após falha na reconexão:', disconnectError);\n                                }\n                            }\n                        }\n                    }\n                    // Marcar como inicializado para evitar múltiplas inicializações\n                    socketInitializedRef.current = true;\n                    // Limitar a frequência de reconexões\n                    const maxReconnectAttempts = 5;\n                    const reconnectDelay = 3000; // 3 segundos\n                    console.log('Inicializando WebSocket...');\n                    try {\n                        // Verificar se o token é válido antes de inicializar o WebSocket\n                        // Usar uma verificação mais simples para evitar múltiplas requisições\n                        // Assumir que o token é válido se o usuário está logado\n                        console.log('Token válido, inicializando WebSocket para o usuário:', user.fullName);\n                        console.log('Inicializando WebSocket com autenticação');\n                        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(API_URL, {\n                            path: '/socket.io',\n                            auth: {\n                                token: currentToken\n                            },\n                            transports: [\n                                'websocket',\n                                'polling'\n                            ],\n                            reconnectionAttempts: maxReconnectAttempts,\n                            reconnectionDelay: reconnectDelay,\n                            timeout: 10000,\n                            autoConnect: true,\n                            reconnection: true\n                        });\n                        socketInstance.on('connect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket connected');\n                                setIsConnected(true);\n                                reconnectAttemptsRef.current = 0;\n                                // Disparar evento personalizado para notificar componentes sobre a conexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'connected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('disconnect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket disconnected');\n                                setIsConnected(false);\n                                // Disparar evento personalizado para notificar componentes sobre a desconexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'disconnected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('connect_error', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (error)=>{\n                                console.error('WebSocket connection error:', error);\n                                reconnectAttemptsRef.current++;\n                                // Se o erro for de autenticação, não tentar reconectar\n                                if (error.message && error.message.includes('Authentication error')) {\n                                    console.error('Erro de autenticação no WebSocket, não tentando reconectar');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                    return;\n                                }\n                                if (reconnectAttemptsRef.current >= maxReconnectAttempts) {\n                                    console.error('Máximo de tentativas de reconexão atingido');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para receber novas mensagens\n                        socketInstance.on('message:new', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (message)=>{\n                                if (!message || !message.conversationId) {\n                                    console.error('Mensagem inválida recebida:', message);\n                                    return;\n                                }\n                                // Usar uma função anônima para evitar dependências circulares\n                                const userId = user === null || user === void 0 ? void 0 : user.id;\n                                // Verificar se a mensagem tem um tempId associado (para substituir mensagem temporária)\n                                // O backend pode não retornar o tempId, então precisamos verificar se é uma mensagem do usuário atual\n                                const tempId = message.tempId;\n                                const isCurrentUserMessage = message.senderId === userId;\n                                // Atualizar mensagens da conversa\n                                setMessages({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        const conversationMessages = prev[message.conversationId] || [];\n                                        // Verificar se a mensagem já existe para evitar duplicação\n                                        if (conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === message.id\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return prev;\n                                        }\n                                        // Se tiver um tempId, substituir a mensagem temporária pela mensagem real\n                                        if (tempId && conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return {\n                                                ...prev,\n                                                [message.conversationId]: conversationMessages.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId ? message : m\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                            };\n                                        }\n                                        // Se for uma mensagem do usuário atual, verificar se há mensagens temporárias recentes\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId) {\n                                            // Procurar por mensagens temporárias recentes (nos últimos 10 segundos)\n                                            const now = new Date();\n                                            const tempMessages = conversationMessages.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket.tempMessages\": (m)=>{\n                                                    if (!m.isTemp) return false;\n                                                    if (!(m.senderId === userId || m.senderClientId === userId)) return false;\n                                                    if (now - new Date(m.createdAt) >= 10000) return false; // 10 segundos\n                                                    // Para mensagens com anexos, verificar se ambas são ATTACHMENT\n                                                    if (message.contentType === 'ATTACHMENT' && m.contentType === 'ATTACHMENT') {\n                                                        return true;\n                                                    }\n                                                    // Para mensagens de texto, verificar se o conteúdo é igual\n                                                    if (message.contentType === 'TEXT' && m.contentType === 'TEXT' && m.content === message.content) {\n                                                        return true;\n                                                    }\n                                                    return false;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket.tempMessages\"]);\n                                            if (tempMessages.length > 0) {\n                                                // Substituir a primeira mensagem temporária encontrada\n                                                const tempMessage = tempMessages[0];\n                                                return {\n                                                    ...prev,\n                                                    [message.conversationId]: conversationMessages.map({\n                                                        \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempMessage.id ? message : m\n                                                    }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                };\n                                            }\n                                        }\n                                        // Caso contrário, adicionar a nova mensagem\n                                        // Manter a ordem cronológica (mais antigas primeiro)\n                                        const updatedMessages = [\n                                            ...conversationMessages,\n                                            message\n                                        ].sort({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedMessages\"]);\n                                        return {\n                                            ...prev,\n                                            [message.conversationId]: updatedMessages\n                                        };\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Atualizar lista de conversas (mover para o topo)\n                                setConversations({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        // Verificar se a conversa existe\n                                        const conversation = prev.find({\n                                            \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                        if (!conversation) return prev;\n                                        // Se a conversa já tiver uma mensagem temporária com o mesmo tempId, não mover para o topo\n                                        if (tempId && conversation.lastMessage && conversation.lastMessage.id === tempId) {\n                                            // Apenas atualizar a última mensagem sem reordenar\n                                            return prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                    if (c.id === message.conversationId) {\n                                                        return {\n                                                            ...c,\n                                                            lastMessage: message\n                                                        };\n                                                    }\n                                                    return c;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        }\n                                        // Se for uma mensagem do usuário atual e não tiver tempId, verificar se há uma mensagem temporária recente\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId && conversation.lastMessage && conversation.lastMessage.isTemp) {\n                                            // Verificar se a última mensagem é temporária e tem o mesmo conteúdo\n                                            if (conversation.lastMessage.content === message.content && (conversation.lastMessage.senderId === userId || conversation.lastMessage.senderClientId === userId)) {\n                                                // Apenas atualizar a última mensagem sem reordenar\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                        if (c.id === message.conversationId) {\n                                                            return {\n                                                                ...c,\n                                                                lastMessage: message\n                                                            };\n                                                        }\n                                                        return c;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }\n                                        // Caso contrário, mover para o topo\n                                        const updatedConversations = prev.filter({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedConversations\": (c)=>c.id !== message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedConversations\"]);\n                                        return [\n                                            {\n                                                ...conversation,\n                                                lastMessage: message\n                                            },\n                                            ...updatedConversations\n                                        ];\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Marcar conversa como tendo mensagens não lidas se a mensagem não for do usuário atual\n                                const isFromCurrentUser = message.senderId === userId || message.senderClientId === userId;\n                                if (!isFromCurrentUser) {\n                                    console.log('[WebSocket] Nova mensagem de outro usuário, marcando conversa como não lida');\n                                    // Verificar se a conversa já estava marcada como não lida\n                                    const conversation = conversations.find({\n                                        \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                    }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                    const wasAlreadyUnread = (conversation === null || conversation === void 0 ? void 0 : conversation.hasUnread) || false;\n                                    // Marcar conversa como tendo mensagens não lidas\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                    if (conv.id === message.conversationId) {\n                                                        console.log(\"[WebSocket] Conversa \".concat(conv.id, \" marcada como n\\xe3o lida\"));\n                                                        return {\n                                                            ...conv,\n                                                            hasUnread: true\n                                                        };\n                                                    }\n                                                    return conv;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Incrementar contador total apenas se a conversa não estava marcada como não lida\n                                    if (!wasAlreadyUnread) {\n                                        setUnreadCount({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const newCount = prev + 1;\n                                                console.log('[WebSocket] Contador de conversas não lidas atualizado de', prev, 'para', newCount);\n                                                return newCount;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                } else {\n                                    console.log('[WebSocket] Mensagem do próprio usuário, não alterando status de não lida');\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a nova mensagem\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'message',\n                                        conversationId: message.conversationId,\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de contagem de mensagens não lidas\n                        socketInstance.on('unread:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('[WebSocket] Atualização de mensagens não lidas recebida:', data);\n                                if (data && typeof data.totalUnread === 'number') {\n                                    console.log(\"[WebSocket] Atualizando contador total para: \".concat(data.totalUnread));\n                                    setUnreadCount(data.totalUnread);\n                                    // Atualizar as conversas com as contagens de não lidas\n                                    if (data.conversations && Array.isArray(data.conversations)) {\n                                        console.log(\"[WebSocket] Atualizando \".concat(data.conversations.length, \" conversas com contadores\"));\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        // Procurar esta conversa nos dados de não lidas\n                                                        const unreadInfo = data.conversations.find({\n                                                            \"ChatProvider.useEffect.initializeWebSocket.unreadInfo\": (item)=>item.conversationId === conv.id\n                                                        }[\"ChatProvider.useEffect.initializeWebSocket.unreadInfo\"]);\n                                                        if (unreadInfo) {\n                                                            console.log(\"[WebSocket] Conversa \".concat(conv.id, \" agora tem \").concat(unreadInfo.unreadCount, \" mensagens n\\xe3o lidas\"));\n                                                            return {\n                                                                ...conv,\n                                                                unreadCount: unreadInfo.unreadCount\n                                                            };\n                                                        }\n                                                        // Resetar contador se não estiver na lista de não lidas\n                                                        return {\n                                                            ...conv,\n                                                            unreadCount: 0\n                                                        };\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a atualização de não lidas\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'unread',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de lista de conversas\n                        socketInstance.on('conversations:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Atualização de lista de conversas recebida via WebSocket:', data);\n                                // Verificar se os dados estão no formato esperado\n                                if (data) {\n                                    // Pode vir como array direto ou como objeto com propriedade conversations\n                                    const conversationsArray = Array.isArray(data) ? data : data.conversations || [];\n                                    if (Array.isArray(conversationsArray) && conversationsArray.length > 0) {\n                                        console.log(\"Atualizando \".concat(conversationsArray.length, \" conversas via WebSocket\"));\n                                        setConversations(conversationsArray);\n                                        // Atualizar a flag para indicar que os dados foram carregados\n                                        initialDataLoadedRef.current = true;\n                                        // Atualizar o cache\n                                        requestCache.conversations.data = conversationsArray;\n                                        requestCache.conversations.timestamp = Date.now();\n                                        // Disparar evento personalizado para notificar componentes sobre a atualização\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    } else {\n                                        console.error('Formato inválido ou array vazio de conversas recebido via WebSocket:', data);\n                                        // Se recebemos um array vazio, forçar carregamento das conversas\n                                        if (Array.isArray(conversationsArray) && conversationsArray.length === 0) {\n                                            console.log('Array vazio recebido, forçando carregamento de conversas...');\n                                            loadConversations(true);\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para notificar que uma nova conversa foi criada\n                        socketInstance.on('conversation:created', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Nova conversa criada recebida via WebSocket:', data);\n                                if (data && data.id) {\n                                    // Adicionar a nova conversa ao início da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            // Verificar se a conversa já existe\n                                            if (prev.some({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id === data.id\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                                return prev;\n                                            }\n                                            return [\n                                                data,\n                                                ...prev\n                                            ];\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando um participante é removido\n                        socketInstance.on('participant:removed', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Participante removido recebido via WebSocket:', data);\n                                if (data && data.conversationId && data.participantId) {\n                                    // Se o usuário atual foi removido, remover a conversa da lista\n                                    if (data.participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        // Se era a conversa ativa, limpar\n                                        if (activeConversation === data.conversationId) {\n                                            setActiveConversation(null);\n                                        }\n                                        // Limpar mensagens da conversa\n                                        setMessages({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const updated = {\n                                                    ...prev\n                                                };\n                                                delete updated[data.conversationId];\n                                                return updated;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    } else {\n                                        // Atualizar a lista de participantes da conversa\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        if (conv.id === data.conversationId) {\n                                                            return {\n                                                                ...conv,\n                                                                participants: conv.participants.filter({\n                                                                    \"ChatProvider.useEffect.initializeWebSocket\": (p)=>p.userId !== data.participantId && p.clientId !== data.participantId\n                                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                            };\n                                                        }\n                                                        return conv;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando o usuário sai de uma conversa\n                        socketInstance.on('conversation:left', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Usuário saiu da conversa via WebSocket:', data);\n                                if (data && data.conversationId && data.userId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                    // Remover a conversa da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Se era a conversa ativa, limpar\n                                    if (activeConversation === data.conversationId) {\n                                        setActiveConversation(null);\n                                    }\n                                    // Limpar mensagens da conversa\n                                    setMessages({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            const updated = {\n                                                ...prev\n                                            };\n                                            delete updated[data.conversationId];\n                                            return updated;\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Disparar evento para atualizar a interface\n                                    window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                        detail: {\n                                            type: 'conversations',\n                                            action: 'left',\n                                            conversationId: data.conversationId,\n                                            timestamp: Date.now()\n                                        }\n                                    }));\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        setSocket(socketInstance);\n                        return socketInstance;\n                    } catch (error) {\n                        console.error('Erro ao inicializar WebSocket:', error);\n                        setIsConnected(false);\n                        socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                        return null;\n                    }\n                }\n            }[\"ChatProvider.useEffect.initializeWebSocket\"];\n            // Usar uma variável para controlar se já estamos tentando inicializar\n            let initializationInProgress = false;\n            // Função para inicializar com segurança\n            const safeInitialize = {\n                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                    if (initializationInProgress) {\n                        console.log('Já existe uma inicialização em andamento, ignorando');\n                        return;\n                    }\n                    initializationInProgress = true;\n                    // Usar um timeout para garantir que não tentamos inicializar muito frequentemente\n                    setTimeout({\n                        \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                            initializeWebSocket().then({\n                                \"ChatProvider.useEffect.safeInitialize\": (result)=>{\n                                    console.log('Inicialização do WebSocket concluída:', result ? 'sucesso' : 'falha');\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).catch({\n                                \"ChatProvider.useEffect.safeInitialize\": (error)=>{\n                                    console.error('Erro ao inicializar WebSocket:', error);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).finally({\n                                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                                    initializationInProgress = false;\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]);\n                        }\n                    }[\"ChatProvider.useEffect.safeInitialize\"], 2000); // Esperar 2 segundos antes de inicializar\n                }\n            }[\"ChatProvider.useEffect.safeInitialize\"];\n            // Chamar a função de inicialização\n            safeInitialize();\n            // Cleanup function - só desconectar quando o componente for desmontado completamente\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                    // Verificar se estamos realmente desmontando o componente (usuário fez logout)\n                    if (!user) {\n                        console.log('Usuário fez logout, desconectando socket');\n                        if (socket) {\n                            try {\n                                socket.disconnect();\n                                socketInitializedRef.current = false; // Permitir nova tentativa após cleanup\n                            } catch (error) {\n                                console.error('Erro ao desconectar socket:', error);\n                            }\n                        }\n                    } else {\n                        console.log('Componente sendo remontado, mantendo socket conectado');\n                    }\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo socket e isConnected das dependências para evitar loops\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Carregar conversas do usuário com cache\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n            console.log('loadConversations chamado com forceRefresh =', forceRefresh);\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar conversas');\n                    return [];\n                }\n                // Clientes agora podem carregar conversas\n                console.log('Carregando conversas para:', user.role || 'USER');\n                // Verificar se já está carregando\n                if (isLoading || isLoadingRef.current) {\n                    console.log('Já está carregando conversas, retornando estado atual');\n                    return conversations;\n                }\n                // Verificar se já temos conversas carregadas e não é uma atualização forçada\n                if (!forceRefresh && conversations.length > 0) {\n                    console.log('Já temos conversas carregadas e não é uma atualização forçada, retornando estado atual');\n                    return conversations;\n                }\n                // Marcar como carregando para evitar chamadas simultâneas\n                isLoadingRef.current = true;\n                // Verificar cache apenas se não for refresh forçado\n                const now = Date.now();\n                if (!forceRefresh && requestCache.conversations.data && requestCache.conversations.data.length > 0 && now - requestCache.conversations.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para conversas');\n                    return requestCache.conversations.data;\n                }\n                console.log('Buscando conversas atualizadas da API');\n                const currentToken = getCurrentToken();\n                console.log('Token ao carregar conversas:', currentToken ? 'Disponível' : 'Não disponível');\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar conversas');\n                    return [];\n                }\n                setIsLoading(true);\n                try {\n                    console.log('Buscando conversas da API...');\n                    console.log('Fazendo requisição para:', \"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"));\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    console.log('Status da resposta:', response.status, response.statusText);\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar conversas');\n                            handleAuthError();\n                            return [];\n                        }\n                        throw new Error(\"Erro ao carregar conversas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de conversas:', data);\n                    if (data.success) {\n                        var _data_data;\n                        // Verificar se há dados válidos\n                        // A API retorna { success: true, data: { conversations: [...], total, limit, offset } }\n                        console.log('Estrutura completa da resposta da API:', JSON.stringify(data));\n                        const conversationsArray = ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.conversations) || [];\n                        console.log('Array de conversas extraído:', conversationsArray);\n                        if (!Array.isArray(conversationsArray)) {\n                            console.error('Resposta da API não contém um array de conversas:', data);\n                            return [];\n                        }\n                        console.log(\"Recebidas \".concat(conversationsArray.length, \" conversas da API\"));\n                        // Processar as últimas mensagens das conversas\n                        console.log('Processando últimas mensagens das conversas...');\n                        const processedConversations = conversationsArray.map({\n                            \"ChatProvider.useCallback[loadConversations].processedConversations\": (conversation)=>{\n                                // Verificar se a conversa tem mensagens\n                                if (conversation.messages && conversation.messages.length > 0) {\n                                    console.log(\"Conversa \".concat(conversation.id, \" tem \").concat(conversation.messages.length, \" mensagens\"));\n                                    // Extrair a última mensagem\n                                    const lastMessage = conversation.messages[0]; // A primeira mensagem é a mais recente (ordenada por createdId desc)\n                                    console.log('Ultima mensagem:', lastMessage);\n                                    // IMPORTANTE: Também salvar todas as mensagens desta conversa no estado\n                                    const sortedMessages = [\n                                        ...conversation.messages\n                                    ].sort({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\"]);\n                                    console.log(\"DEBUG: Salvando \".concat(sortedMessages.length, \" mensagens da conversa \").concat(conversation.id), sortedMessages);\n                                    // Atualizar estado das mensagens\n                                    setMessages({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations\": (prev)=>({\n                                                ...prev,\n                                                [conversation.id]: sortedMessages\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                                    // Remover o array de mensagens para evitar duplicação\n                                    const { messages, ...conversationWithoutMessages } = conversation;\n                                    // Adicionar a última mensagem como propriedade lastMessage\n                                    return {\n                                        ...conversationWithoutMessages,\n                                        lastMessage\n                                    };\n                                } else {\n                                    console.log(\"Conversa \".concat(conversation.id, \" n\\xe3o tem mensagens\"));\n                                }\n                                return conversation;\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                        // Log para debug dos dados das conversas\n                        console.log('Conversas processadas:', processedConversations.map({\n                            \"ChatProvider.useCallback[loadConversations]\": (c)=>{\n                                var _c_participants;\n                                return {\n                                    id: c.id,\n                                    participants: (_c_participants = c.participants) === null || _c_participants === void 0 ? void 0 : _c_participants.map({\n                                        \"ChatProvider.useCallback[loadConversations]\": (p)=>({\n                                                userId: p.userId,\n                                                clientId: p.clientId,\n                                                user: p.user,\n                                                client: p.client\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations]\"]));\n                        // Atualizar cache com as conversas processadas\n                        const now = Date.now();\n                        requestCache.conversations.data = processedConversations;\n                        requestCache.conversations.timestamp = now;\n                        // Atualizar estado - garantir que estamos atualizando o estado mesmo se o array estiver vazio\n                        setConversations(processedConversations);\n                        // Disparar evento para notificar componentes sobre a atualização\n                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                            detail: {\n                                type: 'conversations',\n                                timestamp: Date.now()\n                            }\n                        }));\n                        return conversationsArray;\n                    } else {\n                        console.error('Resposta da API não foi bem-sucedida:', data);\n                        return [];\n                    }\n                } catch (error) {\n                    console.error('Error loading conversations:', error);\n                    return [];\n                } finally{\n                    setIsLoading(false);\n                    isLoadingRef.current = false;\n                }\n            } catch (error) {\n                console.error('Error in loadConversations:', error);\n                return [];\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadConversations]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError,\n        isLoading\n    ]);\n    // Carregar mensagens de uma conversa com cache aprimorado\n    const loadMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadMessages]\": async (conversationId)=>{\n            try {\n                var _requestCache_messages_conversationId;\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar mensagens');\n                    return;\n                }\n                // Verificar cache de mensagens\n                const now = Date.now();\n                if (requestCache.messages[conversationId] && now - requestCache.messages[conversationId].timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para mensagens da conversa:', conversationId);\n                    return requestCache.messages[conversationId].data;\n                }\n                // Verificar se já tem mensagens completas carregadas no cache\n                // Não usar o estado messages aqui pois pode conter apenas a última mensagem do loadConversations\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar mensagens');\n                    return;\n                }\n                // Evitar múltiplas requisições simultâneas para a mesma conversa\n                if ((_requestCache_messages_conversationId = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId === void 0 ? void 0 : _requestCache_messages_conversationId.loading) {\n                    console.log('Já existe uma requisição em andamento para esta conversa');\n                    return;\n                }\n                // Marcar como carregando\n                requestCache.messages[conversationId] = {\n                    loading: true\n                };\n                try {\n                    console.log('Buscando mensagens da API para conversa:', conversationId);\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/messages\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar mensagens');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar mensagens: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    if (data.success) {\n                        // Debug para verificar mensagens carregadas\n                        console.log('DEBUG: Mensagens carregadas para conversa', conversationId, data.data);\n                        // Atualizar cache\n                        requestCache.messages[conversationId] = {\n                            data: data.data,\n                            timestamp: now,\n                            loading: false\n                        };\n                        // Atualizar estado das mensagens\n                        // Garantir que as mensagens estejam na ordem correta (mais antigas primeiro)\n                        // para que a ordenação no componente funcione corretamente\n                        const sortedMessages = [\n                            ...data.data\n                        ].sort({\n                            \"ChatProvider.useCallback[loadMessages].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                        }[\"ChatProvider.useCallback[loadMessages].sortedMessages\"]);\n                        console.log('DEBUG: Mensagens ordenadas', sortedMessages);\n                        setMessages({\n                            \"ChatProvider.useCallback[loadMessages]\": (prev)=>({\n                                    ...prev,\n                                    [conversationId]: sortedMessages\n                                })\n                        }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        // Atualizar a última mensagem da conversa\n                        if (data.data && data.data.length > 0) {\n                            const lastMessage = data.data[data.data.length - 1];\n                            setConversations({\n                                \"ChatProvider.useCallback[loadMessages]\": (prev)=>{\n                                    return prev.map({\n                                        \"ChatProvider.useCallback[loadMessages]\": (conv)=>{\n                                            if (conv.id === conversationId && (!conv.lastMessage || new Date(lastMessage.createdAt) > new Date(conv.lastMessage.createdAt))) {\n                                                return {\n                                                    ...conv,\n                                                    lastMessage\n                                                };\n                                            }\n                                            return conv;\n                                        }\n                                    }[\"ChatProvider.useCallback[loadMessages]\"]);\n                                }\n                            }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        }\n                        return data.data;\n                    }\n                } catch (error) {\n                    console.error('Error loading messages:', error);\n                } finally{\n                    var _requestCache_messages_conversationId1;\n                    // Remover flag de carregamento em caso de erro\n                    if ((_requestCache_messages_conversationId1 = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId1 === void 0 ? void 0 : _requestCache_messages_conversationId1.loading) {\n                        requestCache.messages[conversationId].loading = false;\n                    }\n                }\n            } catch (error) {\n                console.error('Error in loadMessages:', error);\n            }\n        // Removendo messages das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadMessages]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Enviar mensagem\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[sendMessage]\": function(conversationId, content) {\n            let contentType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'TEXT', metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            if (!socket || !isConnected) return;\n            // Gerar um ID temporário para a mensagem\n            const tempId = \"temp-\".concat(Date.now());\n            // Criar uma mensagem temporária para exibir imediatamente\n            const tempMessage = {\n                id: tempId,\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                senderId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : user === null || user === void 0 ? void 0 : user.id,\n                senderClientId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? user === null || user === void 0 ? void 0 : user.id : null,\n                createdAt: new Date().toISOString(),\n                sender: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: user === null || user === void 0 ? void 0 : user.fullName\n                },\n                senderClient: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.login),\n                    login: user === null || user === void 0 ? void 0 : user.login\n                } : null,\n                // Marcar como temporária para evitar duplicação\n                isTemp: true\n            };\n            // Atualizar mensagens localmente antes de enviar\n            setMessages({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>({\n                        ...prev,\n                        [conversationId]: [\n                            ...prev[conversationId] || [],\n                            tempMessage\n                        ]\n                    })\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Atualizar a conversa com a última mensagem\n            setConversations({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                    return prev.map({\n                        \"ChatProvider.useCallback[sendMessage]\": (conv)=>{\n                            if (conv.id === conversationId) {\n                                return {\n                                    ...conv,\n                                    lastMessage: tempMessage\n                                };\n                            }\n                            return conv;\n                        }\n                    }[\"ChatProvider.useCallback[sendMessage]\"]);\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Enviar a mensagem via WebSocket\n            const messageData = {\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                tempId\n            };\n            socket.emit('message:send', messageData, {\n                \"ChatProvider.useCallback[sendMessage]\": (response)=>{\n                    if (response.success) {\n                    // A mensagem real será adicionada pelo evento message:new do WebSocket\n                    // Não precisamos fazer nada aqui, pois o WebSocket vai atualizar a mensagem\n                    } else {\n                        console.error('Error sending message:', response.error);\n                        // Em caso de erro, podemos marcar a mensagem como falha\n                        setMessages({\n                            \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                                const conversationMessages = prev[conversationId] || [];\n                                return {\n                                    ...prev,\n                                    [conversationId]: conversationMessages.map({\n                                        \"ChatProvider.useCallback[sendMessage]\": (msg)=>msg.id === tempId ? {\n                                                ...msg,\n                                                failed: true\n                                            } : msg\n                                    }[\"ChatProvider.useCallback[sendMessage]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[sendMessage]\"]);\n                    }\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n        }\n    }[\"ChatProvider.useCallback[sendMessage]\"], [\n        socket,\n        isConnected,\n        user\n    ]);\n    // Criar nova conversa\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createConversation]\": async function(participantIds) {\n            let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n            try {\n                console.log('Criando conversa com participantes:', participantIds);\n                console.log('Tipo de conversa:', participantIds.length > 1 ? 'GRUPO' : 'INDIVIDUAL');\n                console.log('Título da conversa:', title);\n                // ✅ VALIDAÇÃO: System admin não pode criar grupos com usuários de empresas diferentes\n                if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && participantIds.length > 1) {\n                    console.log('Validando empresas para system admin...');\n                    // Buscar informações dos participantes para validar empresas\n                    const currentToken = getCurrentToken();\n                    if (currentToken) {\n                        try {\n                            const participantPromises = participantIds.map({\n                                \"ChatProvider.useCallback[createConversation].participantPromises\": async (id)=>{\n                                    const response = await fetch(\"\".concat(API_URL, \"/users/\").concat(id), {\n                                        headers: {\n                                            Authorization: \"Bearer \".concat(currentToken)\n                                        }\n                                    });\n                                    if (response.ok) {\n                                        const userData = await response.json();\n                                        return userData.success ? userData.data : userData;\n                                    }\n                                    return null;\n                                }\n                            }[\"ChatProvider.useCallback[createConversation].participantPromises\"]);\n                            const participants = await Promise.all(participantPromises);\n                            console.log('Participantes carregados:', participants);\n                            const validParticipants = participants.filter({\n                                \"ChatProvider.useCallback[createConversation].validParticipants\": (p)=>p && p.companyId\n                            }[\"ChatProvider.useCallback[createConversation].validParticipants\"]);\n                            console.log('Participantes válidos:', validParticipants);\n                            if (validParticipants.length > 1) {\n                                const companies = [\n                                    ...new Set(validParticipants.map({\n                                        \"ChatProvider.useCallback[createConversation]\": (p)=>p.companyId\n                                    }[\"ChatProvider.useCallback[createConversation]\"]))\n                                ];\n                                console.log('Empresas encontradas:', companies);\n                                if (companies.length > 1) {\n                                    alert('Não é possível criar grupos com usuários de empresas diferentes.');\n                                    return null;\n                                }\n                            }\n                        } catch (validationError) {\n                            console.error('Erro na validação de empresas:', validationError);\n                            alert('Erro na validação: ' + validationError.message);\n                            return null;\n                        }\n                    }\n                }\n                // Verificar se algum participante é cliente\n                const hasClientParticipants = participantIds.some({\n                    \"ChatProvider.useCallback[createConversation].hasClientParticipants\": (id)=>{\n                        // Verificar se o ID corresponde a um cliente (assumindo que clientes têm isClient: true)\n                        return typeof id === 'object' && id.isClient;\n                    }\n                }[\"ChatProvider.useCallback[createConversation].hasClientParticipants\"]);\n                console.log('Tem participantes clientes:', hasClientParticipants);\n                // Obter o token mais recente do localStorage\n                const currentToken = getCurrentToken();\n                console.log('Token disponível:', currentToken ? 'Sim' : 'Não');\n                if (!currentToken) {\n                    console.error('Token não disponível. Usuário precisa fazer login novamente.');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        type: participantIds.length > 1 ? 'GROUP' : 'INDIVIDUAL',\n                        title,\n                        participantIds: participantIds.map({\n                            \"ChatProvider.useCallback[createConversation]\": (id)=>typeof id === 'object' ? id.id : id\n                        }[\"ChatProvider.useCallback[createConversation]\"]),\n                        includeClients: true // Permitir incluir clientes nas conversas\n                    })\n                });\n                console.log('Resposta da API:', response.status, response.statusText);\n                const data = await response.json();\n                console.log('Dados da resposta:', data);\n                if (data.success) {\n                    // Extrair os dados da conversa criada\n                    const conversationData = data.data;\n                    console.log('Conversa criada com sucesso:', conversationData);\n                    // Adicionar a nova conversa à lista\n                    setConversations({\n                        \"ChatProvider.useCallback[createConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createConversation]\": (c)=>c.id === conversationData.id\n                            }[\"ChatProvider.useCallback[createConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                conversationData,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"]);\n                    // Disparar evento para notificar componentes sobre a nova conversa\n                    setTimeout({\n                        \"ChatProvider.useCallback[createConversation]\": ()=>{\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: conversationData.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"], 300);\n                    return conversationData;\n                } else {\n                    console.error('Erro ao criar conversa:', data.error || 'Erro desconhecido');\n                    // Se o erro for de autenticação, redirecionar para login\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido. Redirecionando para login...');\n                        handleAuthError();\n                    }\n                }\n                return null;\n            } catch (error) {\n                console.error('Error creating conversation:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[createConversation]\"], [\n        handleAuthError\n    ]);\n    // Criar ou obter conversa com um usuário específico\n    const createOrGetConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createOrGetConversation]\": async (otherUser)=>{\n            try {\n                console.log('createOrGetConversation chamado com usuário:', otherUser);\n                if (!otherUser || !otherUser.id) {\n                    console.error('Usuário inválido:', otherUser);\n                    return null;\n                }\n                // Primeiro, verificar se já existe uma conversa individual com este usuário\n                const existingConversation = conversations.find({\n                    \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (conv)=>{\n                        var _conv_participants;\n                        if (conv.type !== 'INDIVIDUAL') return false;\n                        return (_conv_participants = conv.participants) === null || _conv_participants === void 0 ? void 0 : _conv_participants.some({\n                            \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (p)=>p.userId === otherUser.id\n                        }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                    }\n                }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                if (existingConversation) {\n                    console.log('Conversa existente encontrada:', existingConversation);\n                    setActiveConversation(existingConversation.id);\n                    return existingConversation;\n                }\n                console.log('Criando nova conversa com usuário:', otherUser.fullName);\n                // Se não existir, criar uma nova conversa\n                const newConversation = await createConversation([\n                    otherUser.id\n                ]);\n                if (newConversation) {\n                    console.log('Nova conversa criada com sucesso:', newConversation);\n                    // Garantir que a conversa seja adicionada à lista antes de definir como ativa\n                    setConversations({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createOrGetConversation]\": (c)=>c.id === newConversation.id\n                            }[\"ChatProvider.useCallback[createOrGetConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                newConversation,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"]);\n                    // Definir a conversa como ativa após um pequeno delay para garantir que a lista foi atualizada\n                    setTimeout({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": ()=>{\n                            setActiveConversation(newConversation.id);\n                            // Disparar evento para notificar componentes sobre a nova conversa\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: newConversation.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"], 300);\n                } else {\n                    console.error('Falha ao criar nova conversa, retorno nulo ou indefinido');\n                }\n                return newConversation;\n            } catch (error) {\n                console.error('Error creating or getting conversation:', error);\n                return null;\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[createOrGetConversation]\"], [\n        createConversation,\n        setActiveConversation,\n        handleAuthError\n    ]);\n    // Carregar contagem de mensagens não lidas com cache\n    const loadUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadUnreadCount]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                // Verificar cache\n                const now = Date.now();\n                if (!forceRefresh && requestCache.unreadCount.data !== null && now - requestCache.unreadCount.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para contagem de mensagens não lidas');\n                    return requestCache.unreadCount.data;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                try {\n                    console.log('Buscando contagem de mensagens não lidas da API...');\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/messages/unread\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar contagem de mensagens não lidas');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar contagem de mensagens n\\xe3o lidas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de mensagens não lidas:', data);\n                    if (data.success) {\n                        console.log('Dados de mensagens não lidas recebidos:', data.data);\n                        // Atualizar cache com o totalUnread\n                        requestCache.unreadCount.data = data.data.totalUnread;\n                        requestCache.unreadCount.timestamp = now;\n                        // Atualizar estado\n                        setUnreadCount(data.data.totalUnread);\n                        // Atualizar as conversas com as contagens de não lidas\n                        if (data.data.conversations && Array.isArray(data.data.conversations)) {\n                            // Primeiro, verificar se já temos as conversas carregadas\n                            const conversationIds = data.data.conversations.map({\n                                \"ChatProvider.useCallback[loadUnreadCount].conversationIds\": (c)=>c.conversationId\n                            }[\"ChatProvider.useCallback[loadUnreadCount].conversationIds\"]);\n                            // Se não temos conversas carregadas ou se temos menos conversas do que as não lidas,\n                            // forçar uma atualização das conversas\n                            if (conversations.length === 0 || !conversationIds.every({\n                                \"ChatProvider.useCallback[loadUnreadCount]\": (id)=>conversations.some({\n                                        \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>c.id === id\n                                    }[\"ChatProvider.useCallback[loadUnreadCount]\"])\n                            }[\"ChatProvider.useCallback[loadUnreadCount]\"])) {\n                                console.log('Forçando atualização das conversas porque há mensagens não lidas em conversas não carregadas');\n                                await loadConversations(true);\n                            } else {\n                                // Atualizar as conversas existentes com as contagens de não lidas\n                                setConversations({\n                                    \"ChatProvider.useCallback[loadUnreadCount]\": (prev)=>{\n                                        return prev.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount]\": (conv)=>{\n                                                // Procurar esta conversa nos dados de não lidas\n                                                const unreadInfo = data.data.conversations.find({\n                                                    \"ChatProvider.useCallback[loadUnreadCount].unreadInfo\": (item)=>item.conversationId === conv.id\n                                                }[\"ChatProvider.useCallback[loadUnreadCount].unreadInfo\"]);\n                                                if (unreadInfo) {\n                                                    return {\n                                                        ...conv,\n                                                        unreadCount: unreadInfo.unreadCount\n                                                    };\n                                                }\n                                                return conv;\n                                            }\n                                        }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                                    }\n                                }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                            }\n                        }\n                        return data.data.totalUnread;\n                    }\n                } catch (error) {\n                    console.error('Error loading unread count:', error);\n                }\n            } catch (error) {\n                console.error('Error in loadUnreadCount:', error);\n            }\n        // Removendo conversations e loadConversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadUnreadCount]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Referência para controlar se os dados iniciais já foram carregados\n    const initialDataLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Resetar a flag quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user) {\n                console.log('Usuário alterado, resetando flag de dados carregados');\n                initialDataLoadedRef.current = false;\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Dependendo apenas do ID do usuário para evitar re-renders desnecessários\n    // Efeito para carregar dados iniciais e configurar atualização periódica\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de carregar dados\n            if (!user) {\n                console.log('Usuário não logado, não carregando dados iniciais');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Carregando dados iniciais para:', user.role || 'USER');\n            // Verificar token\n            const currentToken = getCurrentToken();\n            if (!currentToken) {\n                console.log('Token não disponível, não carregando dados iniciais');\n                return;\n            }\n            // Verificar se já está carregando\n            if (isLoading || isLoadingRef.current) {\n                console.log('Já está carregando dados, aguardando...');\n                return;\n            }\n            // Verificar se os dados já foram carregados\n            if (initialDataLoadedRef.current) {\n                console.log('Dados já foram carregados anteriormente, ignorando');\n                return;\n            }\n            // Função para carregar dados iniciais\n            const loadInitialData = {\n                \"ChatProvider.useEffect.loadInitialData\": async ()=>{\n                    if (isLoading || isLoadingRef.current) {\n                        console.log('Já está carregando dados, cancelando carregamento inicial');\n                        return;\n                    }\n                    // Marcar como carregando\n                    isLoadingRef.current = true;\n                    console.log('Iniciando carregamento de dados do chat...');\n                    try {\n                        // Primeiro carregar as conversas\n                        console.log('Carregando conversas...');\n                        const conversationsData = await loadConversations(true);\n                        console.log('Conversas carregadas:', (conversationsData === null || conversationsData === void 0 ? void 0 : conversationsData.length) || 0, 'conversas');\n                        // Carregar contagem de mensagens não lidas\n                        console.log('Carregando contagem de mensagens não lidas...');\n                        await loadUnreadCount(true);\n                        console.log('Contagem de não lidas carregada');\n                        // Marcar como carregado\n                        initialDataLoadedRef.current = true;\n                        console.log('Carregamento de dados concluído com sucesso');\n                    } catch (error) {\n                        console.error('Erro ao carregar dados:', error);\n                    } finally{\n                        isLoadingRef.current = false;\n                    }\n                }\n            }[\"ChatProvider.useEffect.loadInitialData\"];\n            // Carregar dados iniciais com debounce para evitar múltiplas chamadas\n            console.log('Agendando carregamento de dados iniciais...');\n            const debouncedLoadData = debounce({\n                \"ChatProvider.useEffect.debouncedLoadData\": ()=>{\n                    console.log('Carregando dados iniciais (debounced)...');\n                    loadInitialData();\n                }\n            }[\"ChatProvider.useEffect.debouncedLoadData\"], 1000); // Esperar 1 segundo antes de carregar\n            // Chamar a função com debounce\n            debouncedLoadData();\n            // Removemos a atualização periódica via HTTP para evitar flood no backend\n            // Agora dependemos apenas do WebSocket para atualizações em tempo real\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                // Cleanup function\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo loadUnreadCount e isPanelOpen, isModalOpen das dependências para evitar chamadas desnecessárias\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        loadConversations,\n        isLoading\n    ]);\n    // Efeito para verificar se activeConversation é válido\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (activeConversation && conversations.length > 0) {\n                const conversationExists = conversations.some({\n                    \"ChatProvider.useEffect.conversationExists\": (c)=>c.id === activeConversation\n                }[\"ChatProvider.useEffect.conversationExists\"]);\n                if (!conversationExists) {\n                    console.warn('Conversa ativa não encontrada na lista de conversas, resetando...', activeConversation);\n                    setActiveConversation(null);\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        activeConversation,\n        conversations\n    ]);\n    // Efeito para monitorar mudanças no token e reconectar quando necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de monitorar o token\n            if (!user) return;\n            // Verificar o token a cada 2 minutos (aumentado para reduzir a frequência)\n            const tokenCheckInterval = setInterval({\n                \"ChatProvider.useEffect.tokenCheckInterval\": ()=>{\n                    // Evitar verificações desnecessárias se estiver carregando\n                    if (isLoading || isLoadingRef.current) return;\n                    const currentToken = getCurrentToken();\n                    // Se não há token mas há conexão, desconectar\n                    if (!currentToken && isConnected && socket) {\n                        console.log('Token não encontrado, desconectando WebSocket...');\n                        try {\n                            socket.disconnect();\n                        } catch (error) {\n                            console.error('Erro ao desconectar socket:', error);\n                        }\n                        setIsConnected(false);\n                    }\n                    // Se há token mas não há conexão, tentar reconectar (com verificação de tempo)\n                    if (currentToken && !isConnected && !socket) {\n                        const now = Date.now();\n                        // Limitar tentativas de reconexão (no máximo uma a cada 2 minutos)\n                        if (now - lastInitAttemptRef.current > 120000) {\n                            console.log('Token encontrado, tentando reconectar WebSocket...');\n                            // Permitir nova tentativa de inicialização do WebSocket\n                            socketInitializedRef.current = false;\n                            lastInitAttemptRef.current = now;\n                        }\n                    }\n                }\n            }[\"ChatProvider.useEffect.tokenCheckInterval\"], 120000); // Verificar a cada 2 minutos\n            return ({\n                \"ChatProvider.useEffect\": ()=>clearInterval(tokenCheckInterval)\n            })[\"ChatProvider.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Abrir/fechar painel de chat\n    const toggleChatPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatPanel]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsPanelOpen({\n                \"ChatProvider.useCallback[toggleChatPanel]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o painel, fechar o modal\n                    if (newState) {\n                        setIsModalOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatPanel]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatPanel]\"], [\n        user\n    ]);\n    // Abrir/fechar modal de chat\n    const toggleChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatModal]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsModalOpen({\n                \"ChatProvider.useCallback[toggleChatModal]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o modal, fechar o painel\n                    if (newState) {\n                        setIsPanelOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatModal]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatModal]\"], [\n        user\n    ]);\n    // Verificar se createConversation é uma função válida\n    console.log('ChatContext: createConversation é uma função?', typeof createConversation === 'function');\n    // Adicionar participante a um grupo\n    const addParticipantToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addParticipantToGroup]\": async (conversationId, participantId)=>{\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao adicionar participante');\n                    return null;\n                }\n                console.log(\"Adicionando participante \".concat(participantId, \" \\xe0 conversa \").concat(conversationId));\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        participantId\n                    })\n                });\n                console.log(\"Resposta da API: \".concat(response.status, \" \").concat(response.statusText));\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao adicionar participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    // Log detalhado do erro\n                    const errorText = await response.text();\n                    console.error(\"Erro \".concat(response.status, \" ao adicionar participante:\"), errorText);\n                    throw new Error(\"Erro ao adicionar participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Atualizar a conversa com o novo participante\n                    setConversations({\n                        \"ChatProvider.useCallback[addParticipantToGroup]\": (prev)=>{\n                            return prev.map({\n                                \"ChatProvider.useCallback[addParticipantToGroup]\": (conv)=>{\n                                    if (conv.id === conversationId) {\n                                        // Verificar se o participante já existe\n                                        const participantExists = conv.participants.some({\n                                            \"ChatProvider.useCallback[addParticipantToGroup].participantExists\": (p)=>p.userId === participantId\n                                        }[\"ChatProvider.useCallback[addParticipantToGroup].participantExists\"]);\n                                        if (participantExists) {\n                                            return conv;\n                                        }\n                                        // Adicionar o novo participante\n                                        return {\n                                            ...conv,\n                                            participants: [\n                                                ...conv.participants,\n                                                data.data\n                                            ]\n                                        };\n                                    }\n                                    return conv;\n                                }\n                            }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                        }\n                    }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                    // Criar uma mensagem do sistema para notificar que um participante foi adicionado\n                    if (socket && isConnected) {\n                        const systemMessage = {\n                            conversationId,\n                            content: \"\".concat(data.data.user.fullName, \" foi adicionado ao grupo\"),\n                            contentType: 'SYSTEM'\n                        };\n                        socket.emit('message:send', systemMessage);\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao adicionar participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[addParticipantToGroup]\"], [\n        socket,\n        isConnected,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Adicionar múltiplos participantes a um grupo\n    const addMultipleParticipantsToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addMultipleParticipantsToGroup]\": async (conversationId, participants)=>{\n            try {\n                if (!conversationId || !participants || !participants.length) {\n                    console.error('ID da conversa e lista de participantes são obrigatórios');\n                    return null;\n                }\n                console.log(\"Adicionando \".concat(participants.length, \" participantes ao grupo \").concat(conversationId));\n                // Array para armazenar os resultados\n                const results = [];\n                const errors = [];\n                // Adicionar participantes um por um\n                for (const participant of participants){\n                    try {\n                        const result = await addParticipantToGroup(conversationId, participant.id);\n                        if (result) {\n                            results.push(result);\n                        } else {\n                            errors.push({\n                                user: participant,\n                                error: 'Falha ao adicionar participante'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao adicionar participante \".concat(participant.id, \":\"), error);\n                        errors.push({\n                            user: participant,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    added: results,\n                    errors: errors,\n                    total: participants.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao adicionar múltiplos participantes:', error);\n                return {\n                    success: false,\n                    added: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: participants.length,\n                    successCount: 0,\n                    errorCount: participants.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[addMultipleParticipantsToGroup]\"], [\n        addParticipantToGroup\n    ]);\n    // Remover participante de um grupo (ou sair do grupo)\n    const removeParticipantFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[removeParticipantFromGroup]\": async (conversationId, participantId)=>{\n            console.log('removeParticipantFromGroup chamado:', {\n                conversationId,\n                participantId,\n                userId: user === null || user === void 0 ? void 0 : user.id\n            });\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao remover participante');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants/\").concat(participantId), {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao remover participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    throw new Error(\"Erro ao remover participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Sempre remover da lista local quando o usuário sai\n                    if (participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                        console.log(\"Removendo conversa \".concat(conversationId, \" da lista local\"));\n                        // Remover da lista local\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>prev.filter({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>conv.id !== conversationId\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar mensagens\n                        setMessages({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                const updated = {\n                                    ...prev\n                                };\n                                delete updated[conversationId];\n                                return updated;\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar cache\n                        requestCache.conversations.data = null;\n                        requestCache.conversations.timestamp = 0;\n                        if (requestCache.messages[conversationId]) {\n                            delete requestCache.messages[conversationId];\n                        }\n                        // Se era a conversa ativa, limpar\n                        if (activeConversation === conversationId) {\n                            setActiveConversation(null);\n                        }\n                        // Forçar recarregamento das conversas após sair\n                        setTimeout({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": ()=>{\n                                loadConversations(true);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], 500);\n                        console.log(\"Conversa \".concat(conversationId, \" removida da lista local\"));\n                    } else {\n                        var _data_data_user, _data_data;\n                        // Atualizar a conversa removendo o participante\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                return prev.map({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>{\n                                        if (conv.id === conversationId) {\n                                            return {\n                                                ...conv,\n                                                participants: conv.participants.filter({\n                                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (p)=>p.userId !== participantId\n                                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                                            };\n                                        }\n                                        return conv;\n                                    }\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Criar uma mensagem do sistema para notificar que um participante saiu\n                        if (socket && isConnected && ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : (_data_data_user = _data_data.user) === null || _data_data_user === void 0 ? void 0 : _data_data_user.fullName)) {\n                            const systemMessage = {\n                                conversationId,\n                                content: \"\".concat(data.data.user.fullName, \" saiu do grupo\"),\n                                contentType: 'SYSTEM'\n                            };\n                            socket.emit('message:send', systemMessage);\n                        }\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao remover participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], [\n        socket,\n        isConnected,\n        user,\n        activeConversation,\n        getCurrentToken,\n        handleAuthError,\n        loadUnreadCount\n    ]);\n    // Marcar mensagens como lidas\n    const markMessagesAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markMessagesAsRead]\": async (conversationId, messageId)=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/mark-read\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                if (response.ok) {\n                    // Atualizar contador local imediatamente\n                    setConversations({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[markMessagesAsRead]\": (conv)=>conv.id === conversationId ? {\n                                        ...conv,\n                                        unreadCount: 0\n                                    } : conv\n                            }[\"ChatProvider.useCallback[markMessagesAsRead]\"])\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                    // Recalcular total de não lidas\n                    setUnreadCount({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>{\n                            const conv = conversations.find({\n                                \"ChatProvider.useCallback[markMessagesAsRead].conv\": (c)=>c.id === conversationId\n                            }[\"ChatProvider.useCallback[markMessagesAsRead].conv\"]);\n                            const currentUnread = (conv === null || conv === void 0 ? void 0 : conv.unreadCount) || 0;\n                            return Math.max(0, prev - currentUnread);\n                        }\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                    await loadUnreadCount(true);\n                }\n            } catch (error) {\n                console.error('Error marking messages as read:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[markMessagesAsRead]\"], [\n        user,\n        getCurrentToken,\n        loadUnreadCount,\n        conversations\n    ]);\n    // Resetar contador de mensagens não lidas\n    const resetUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[resetUnreadCount]\": async ()=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/reset-unread\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (response.ok) {\n                    setUnreadCount(0);\n                    setConversations({\n                        \"ChatProvider.useCallback[resetUnreadCount]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[resetUnreadCount]\": (conv)=>({\n                                        ...conv,\n                                        unreadCount: 0\n                                    })\n                            }[\"ChatProvider.useCallback[resetUnreadCount]\"])\n                    }[\"ChatProvider.useCallback[resetUnreadCount]\"]);\n                }\n            } catch (error) {\n                console.error('Error resetting unread count:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[resetUnreadCount]\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Apagar mensagens\n    const deleteMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[deleteMessages]\": async (messageIds, conversationId)=>{\n            try {\n                if (!messageIds || messageIds.length === 0) {\n                    console.error('IDs das mensagens são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao apagar mensagens');\n                    return null;\n                }\n                const results = [];\n                const errors = [];\n                // Apagar mensagens uma por uma\n                for (const messageId of messageIds){\n                    try {\n                        const response = await fetch(\"\".concat(API_URL, \"/chat/messages/\").concat(messageId), {\n                            method: 'DELETE',\n                            headers: {\n                                Authorization: \"Bearer \".concat(currentToken)\n                            }\n                        });\n                        if (!response.ok) {\n                            if (response.status === 401) {\n                                console.error('Token expirado ou inválido ao apagar mensagem');\n                                handleAuthError();\n                                return null;\n                            }\n                            throw new Error(\"Erro ao apagar mensagem: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        if (data.success) {\n                            results.push(data.data);\n                            // Atualizar o estado das mensagens\n                            setMessages({\n                                \"ChatProvider.useCallback[deleteMessages]\": (prev)=>{\n                                    const updatedMessages = {\n                                        ...prev\n                                    };\n                                    // Percorrer todas as conversas\n                                    Object.keys(updatedMessages).forEach({\n                                        \"ChatProvider.useCallback[deleteMessages]\": (convId)=>{\n                                            // Atualizar a mensagem na conversa correspondente\n                                            updatedMessages[convId] = updatedMessages[convId].map({\n                                                \"ChatProvider.useCallback[deleteMessages]\": (msg)=>{\n                                                    if (msg.id === messageId) {\n                                                        return {\n                                                            ...msg,\n                                                            ...data.data,\n                                                            isDeleted: true\n                                                        };\n                                                    }\n                                                    return msg;\n                                                }\n                                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                        }\n                                    }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                    return updatedMessages;\n                                }\n                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                        } else {\n                            errors.push({\n                                messageId,\n                                error: 'Falha ao apagar mensagem'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao apagar mensagem \".concat(messageId, \":\"), error);\n                        errors.push({\n                            messageId,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                // Limpar o cache de mensagens para forçar uma nova busca\n                if (conversationId && results.length > 0) {\n                    console.log('Limpando cache de mensagens para a conversa:', conversationId);\n                    if (requestCache.messages[conversationId]) {\n                        delete requestCache.messages[conversationId];\n                    }\n                    // Recarregar as mensagens da conversa para garantir que estamos sincronizados com o backend\n                    try {\n                        console.log('Recarregando mensagens após exclusão');\n                        await loadMessages(conversationId);\n                        // Também recarregar a lista de conversas para garantir que tudo está atualizado\n                        await loadConversations(true);\n                    } catch (reloadError) {\n                        console.error('Erro ao recarregar mensagens após exclusão:', reloadError);\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    deleted: results,\n                    errors: errors,\n                    total: messageIds.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao apagar mensagens:', error);\n                return {\n                    success: false,\n                    deleted: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: messageIds.length,\n                    successCount: 0,\n                    errorCount: messageIds.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[deleteMessages]\"], [\n        getCurrentToken,\n        handleAuthError,\n        loadMessages,\n        loadConversations\n    ]);\n    // Memoizar o valor do contexto para evitar re-renderizações desnecessárias\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatProvider.useMemo[contextValue]\": ()=>({\n                conversations,\n                messages,\n                unreadCount,\n                activeConversation,\n                isPanelOpen,\n                isModalOpen,\n                isConnected,\n                isLoading,\n                setActiveConversation,\n                loadMessages,\n                sendMessage,\n                createConversation,\n                createOrGetConversation,\n                toggleChatPanel,\n                toggleChatModal,\n                loadConversations,\n                loadUnreadCount,\n                markMessagesAsRead,\n                resetUnreadCount,\n                addParticipantToGroup,\n                addMultipleParticipantsToGroup,\n                removeParticipantFromGroup,\n                deleteMessages\n            })\n    }[\"ChatProvider.useMemo[contextValue]\"], [\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation,\n        isPanelOpen,\n        isModalOpen,\n        isConnected,\n        isLoading,\n        setActiveConversation,\n        loadMessages,\n        sendMessage,\n        createConversation,\n        createOrGetConversation,\n        toggleChatPanel,\n        toggleChatModal,\n        loadConversations,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        deleteMessages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ChatContext.js\",\n        lineNumber: 1927,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatProvider, \"knvLij1shdeGVeBVK+PUiQX2mtg=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ChatProvider;\nconst useChat = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChat deve ser usado dentro de um ChatProvider');\n    }\n    return context;\n};\n_s1(useChat, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.js\n"));

/***/ })

});