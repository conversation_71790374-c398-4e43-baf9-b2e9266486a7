{"timestamp":"2025-08-07 14:18:51","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 14:18:51 GMT+0000 (Coordinated Universal Time)","process":{"pid":1719,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":194940928,"heapTotal":109195264,"heapUsed":42672776,"external":3382518,"arrayBuffers":110453}},"os":{"loadavg":[1.24,1.57,1.7],"uptime":1269913.19},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:27:18","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 14:27:18 GMT+0000 (Coordinated Universal Time)","process":{"pid":1795,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195833856,"heapTotal":110243840,"heapUsed":43655856,"external":3391680,"arrayBuffers":119615}},"os":{"loadavg":[0.7,1.41,1.63],"uptime":1270420.25},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:30:50","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 14:30:50 GMT+0000 (Coordinated Universal Time)","process":{"pid":1869,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":194953216,"heapTotal":109195264,"heapUsed":42856464,"external":3382506,"arrayBuffers":110441}},"os":{"loadavg":[2.26,2.15,1.9],"uptime":1270632.24},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:31:20","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 14:31:20 GMT+0000 (Coordinated Universal Time)","process":{"pid":1943,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195809280,"heapTotal":110505984,"heapUsed":43514880,"external":3391668,"arrayBuffers":119603}},"os":{"loadavg":[1.37,1.95,1.84],"uptime":1270662.26},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:33:56","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 14:33:56 GMT+0000 (Coordinated Universal Time)","process":{"pid":146,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195547136,"heapTotal":110505984,"heapUsed":45619728,"external":3386876,"arrayBuffers":112253}},"os":{"loadavg":[1.12,1.75,1.8],"uptime":1270818.23},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:40:09","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 14:40:09 GMT+0000 (Coordinated Universal Time)","process":{"pid":533,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196423680,"heapTotal":109719552,"heapUsed":43422904,"external":3383506,"arrayBuffers":111441}},"os":{"loadavg":[2.23,2.14,1.94],"uptime":1271191.3},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:08:49","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 15:08:49 GMT+0000 (Coordinated Universal Time)","process":{"pid":804,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196087808,"heapTotal":110505984,"heapUsed":46128920,"external":3387864,"arrayBuffers":113241}},"os":{"loadavg":[2.08,1.97,1.91],"uptime":1272911.34},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:32:44","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 15:32:44 GMT+0000 (Coordinated Universal Time)","process":{"pid":882,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196669440,"heapTotal":109981696,"heapUsed":43989576,"external":3384506,"arrayBuffers":112441}},"os":{"loadavg":[1.41,1.74,1.81],"uptime":1274346.39},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:33:25","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 15:33:25 GMT+0000 (Coordinated Universal Time)","process":{"pid":956,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":194936832,"heapTotal":110243840,"heapUsed":43428416,"external":3383506,"arrayBuffers":111441}},"os":{"loadavg":[0.72,1.52,1.73],"uptime":1274387.39},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:34:40","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 15:34:40 GMT+0000 (Coordinated Universal Time)","process":{"pid":1104,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195571712,"heapTotal":110518272,"heapUsed":43921776,"external":3384506,"arrayBuffers":112441}},"os":{"loadavg":[2.56,1.82,1.81],"uptime":1274462.39},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:34:50","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Thu Aug 07 2025 15:34:50 GMT+0000 (Coordinated Universal Time)","process":{"pid":1178,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195813376,"heapTotal":109207552,"heapUsed":43900336,"external":3384506,"arrayBuffers":112441}},"os":{"loadavg":[2.24,1.77,1.8],"uptime":1274472.39},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:35:35","level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Object]\nError: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","error":{},"stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","exception":true,"date":"Thu Aug 07 2025 15:35:35 GMT+0000 (Coordinated Universal Time)","process":{"pid":1252,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":159473664,"heapTotal":110100480,"heapUsed":42436736,"external":3225852,"arrayBuffers":98464}},"os":{"loadavg":[1.39,1.63,1.75],"uptime":1274516.98},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"/usr/src/app/src/routes/bugReportRoutes.js","function":null,"line":140,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":25,"file":"/usr/src/app/src/server.js","function":null,"line":62,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:35:45","level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Object]\nError: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","error":{},"stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","exception":true,"date":"Thu Aug 07 2025 15:35:45 GMT+0000 (Coordinated Universal Time)","process":{"pid":1323,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":161755136,"heapTotal":104976384,"heapUsed":79922960,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[1.57,1.66,1.76],"uptime":1274527.19},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"/usr/src/app/src/routes/bugReportRoutes.js","function":null,"line":140,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":25,"file":"/usr/src/app/src/server.js","function":null,"line":62,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:45:26","level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Object]\nError: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","error":{},"stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","exception":true,"date":"Thu Aug 07 2025 15:45:26 GMT+0000 (Coordinated Universal Time)","process":{"pid":146,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":159608832,"heapTotal":110362624,"heapUsed":42320648,"external":3225332,"arrayBuffers":98464}},"os":{"loadavg":[2.24,1.92,1.85],"uptime":1275108.58},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"/usr/src/app/src/routes/bugReportRoutes.js","function":null,"line":140,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":25,"file":"/usr/src/app/src/server.js","function":null,"line":62,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:52:08","level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Undefined]\nError: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","error":{},"stack":"Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/bugReportRoutes.js:140:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:62:25)","exception":true,"date":"Thu Aug 07 2025 15:52:08 GMT+0000 (Coordinated Universal Time)","process":{"pid":365,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":159514624,"heapTotal":109576192,"heapUsed":42523408,"external":3225852,"arrayBuffers":98464}},"os":{"loadavg":[1.3,1.78,1.83],"uptime":1275510.35},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"/usr/src/app/src/routes/bugReportRoutes.js","function":null,"line":140,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":25,"file":"/usr/src/app/src/server.js","function":null,"line":62,"method":null,"native":false}]}
