{"permissions": {"allow": ["Bash(npx:*)", "<PERSON><PERSON>(docker exec:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(npm install:*)", "Bash(npm search:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(where @modelcontextprotocol/server-filesystem)", "Bash(where claude)", "Bash(claude --version)", "Bash(echo $env:APPDATA)", "Bash(dir \"%APPDATA%\\claude*\" /s)", "Bash(ls:*)", "Bash(find:*)", "Bash(mcp-server-postgres:*)", "mcp__ide__getDiagnostics", "mcp__ide__executeCode", "Bash(psql:*)", "Bash(pg_dump:*)", "Bash(node:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(npm run lint)", "Bash(winpty docker exec -it high-tide-systems-api npx prisma migrate status)", "Bash(winpty docker exec -it high-tide-systems-api npx prisma db pull)", "<PERSON><PERSON>(postgres-mcp-server:*)", "Bash(where:*)", "Bash(mcp-server-filesystem:*)", "Bash(docker logs:*)", "Bash(set DATABASE_URL=postgresql://admin:q04XVHr+sKwW1C+mL1u0ZB6yLlcsHvy93aw4drO7cSo=@localhost:5432/hightidesystems)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(winpty docker exec:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(netsh:*)", "<PERSON><PERSON>(dir:*)", "Bash(echo $USERPROFILE)", "<PERSON><PERSON>(claude config --help)", "<PERSON><PERSON>(claude config list)", "<PERSON><PERSON>(claude config)", "Bash(copy:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker cp:*)"], "deny": []}}