"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/hooks/useUnreadMessages.js":
/*!****************************************!*\
  !*** ./src/hooks/useUnreadMessages.js ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnreadMessages: () => (/* binding */ useUnreadMessages)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\nvar _s = $RefreshSig$();\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\nconst useUnreadMessages = ()=>{\n    _s();\n    const [unreadConversationsCount, setUnreadConversationsCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [conversationsWithUnread, setConversationsWithUnread] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"useUnreadMessages.useCallback[getCurrentToken]\"], []);\n    const fetchUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[fetchUnreadCount]\": async ()=>{\n            if (!user) return;\n            setIsLoading(true);\n            try {\n                const token = getCurrentToken();\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/unread\"), {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token)\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success) {\n                        // Agora contamos conversas não lidas, não mensagens\n                        setUnreadConversationsCount(data.data.totalUnreadConversations || data.data.totalUnread);\n                        // Criar Set de conversas com mensagens não lidas\n                        const conversationsSet = new Set();\n                        data.data.conversations.forEach({\n                            \"useUnreadMessages.useCallback[fetchUnreadCount]\": (conv)=>{\n                                if (conv.hasUnread || conv.unreadCount > 0) {\n                                    conversationsSet.add(conv.conversationId);\n                                }\n                            }\n                        }[\"useUnreadMessages.useCallback[fetchUnreadCount]\"]);\n                        setConversationsWithUnread(conversationsSet);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching unread count:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useUnreadMessages.useCallback[fetchUnreadCount]\"], [\n        user,\n        getCurrentToken\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[markAsRead]\": async (conversationId, messageId)=>{\n            if (!user) return;\n            try {\n                const token = getCurrentToken();\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/mark-read\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                if (response.ok) {\n                    // Remover conversa da lista de não lidas\n                    setConversationsWithUnread({\n                        \"useUnreadMessages.useCallback[markAsRead]\": (prev)=>{\n                            const newSet = new Set(prev);\n                            newSet.delete(conversationId);\n                            return newSet;\n                        }\n                    }[\"useUnreadMessages.useCallback[markAsRead]\"]);\n                    // Decrementar contador de conversas não lidas\n                    setUnreadConversationsCount({\n                        \"useUnreadMessages.useCallback[markAsRead]\": (prev)=>Math.max(0, prev - 1)\n                    }[\"useUnreadMessages.useCallback[markAsRead]\"]);\n                }\n            } catch (error) {\n                console.error('Error marking messages as read:', error);\n            }\n        }\n    }[\"useUnreadMessages.useCallback[markAsRead]\"], [\n        user,\n        getCurrentToken\n    ]); // ✅ CORRIGIDO: Removida dependência que causava stale closure\n    const addUnreadConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[addUnreadConversation]\": (conversationId)=>{\n            setConversationsWithUnread({\n                \"useUnreadMessages.useCallback[addUnreadConversation]\": (prev)=>{\n                    if (!prev.has(conversationId)) {\n                        const newSet = new Set(prev);\n                        newSet.add(conversationId);\n                        setUnreadConversationsCount({\n                            \"useUnreadMessages.useCallback[addUnreadConversation]\": (prevCount)=>prevCount + 1\n                        }[\"useUnreadMessages.useCallback[addUnreadConversation]\"]);\n                        return newSet;\n                    }\n                    return prev;\n                }\n            }[\"useUnreadMessages.useCallback[addUnreadConversation]\"]);\n        }\n    }[\"useUnreadMessages.useCallback[addUnreadConversation]\"], []);\n    const removeUnreadConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[removeUnreadConversation]\": (conversationId)=>{\n            setConversationsWithUnread({\n                \"useUnreadMessages.useCallback[removeUnreadConversation]\": (prev)=>{\n                    if (prev.has(conversationId)) {\n                        const newSet = new Set(prev);\n                        newSet.delete(conversationId);\n                        setUnreadConversationsCount({\n                            \"useUnreadMessages.useCallback[removeUnreadConversation]\": (prevCount)=>Math.max(0, prevCount - 1)\n                        }[\"useUnreadMessages.useCallback[removeUnreadConversation]\"]);\n                        return newSet;\n                    }\n                    return prev;\n                }\n            }[\"useUnreadMessages.useCallback[removeUnreadConversation]\"]);\n        }\n    }[\"useUnreadMessages.useCallback[removeUnreadConversation]\"], []);\n    // Carregar contagem inicial\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUnreadMessages.useEffect\": ()=>{\n            if (user) {\n                fetchUnreadCount();\n            } else {\n                setUnreadCount(0);\n                setConversationUnreadCounts({});\n            }\n        }\n    }[\"useUnreadMessages.useEffect\"], [\n        user,\n        fetchUnreadCount\n    ]);\n    return {\n        unreadCount,\n        conversationUnreadCounts,\n        isLoading,\n        fetchUnreadCount,\n        markAsRead,\n        incrementUnreadCount,\n        decrementUnreadCount\n    };\n};\n_s(useUnreadMessages, \"CDJvHL2sutYfVktLLOvAVk2U8OA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useUnreadMessages.js\n"));

/***/ })

});