"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ec4bad2cd1e8\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFByb2dyYW1hY2FvXFxISUdIIFRJREUgU1lTVEVNU1xcaGlnaC10aWRlLXN5c3RlbXMtZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVjNGJhZDJjZDFlOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chat/ChatConversation.js":
/*!*************************************************!*\
  !*** ./src/components/chat/ChatConversation.js ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatInput */ \"(app-pages-browser)/./src/components/chat/ChatInput.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MultiUserSearch */ \"(app-pages-browser)/./src/components/chat/MultiUserSearch.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SharedItemMessage */ \"(app-pages-browser)/./src/components/chat/SharedItemMessage.js\");\n/* harmony import */ var _AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AttachmentViewer */ \"(app-pages-browser)/./src/components/chat/AttachmentViewer.js\");\n/* harmony import */ var _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useSharedItemNavigation */ \"(app-pages-browser)/./src/hooks/useSharedItemNavigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ChatConversation = (param)=>{\n    let { conversationId, onBack, compact = false } = param;\n    var _conversation_participants;\n    _s();\n    const { messages, loadMessages, conversations, setConversations, activeConversation, setActiveConversation, removeParticipantFromGroup, addParticipantToGroup, addMultipleParticipantsToGroup, markMessagesAsRead } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { handleSharedItemClick } = (0,_hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation)();\n    // Wrapper para logar o clique no item compartilhado\n    const handleSharedItemClickWithLog = (contentType, itemData)=>{\n        console.log('[CHAT] Clique em item compartilhado:', {\n            contentType,\n            itemData\n        });\n        handleSharedItemClick(contentType, itemData);\n    };\n    // Estados para gerenciar o menu de opções e adição de participantes\n    const [showOptionsMenu, setShowOptionsMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddParticipant, setShowAddParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticipantsList, setShowParticipantsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingParticipants, setAddingParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para diálogos de confirmação\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmAction, setConfirmAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        title: '',\n        message: '',\n        onConfirm: {\n            \"ChatConversation.useState\": ()=>{}\n        }[\"ChatConversation.useState\"]\n    });\n    // Memoizar a conversa atual para evitar recalculos\n    const conversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversation]\": ()=>{\n            return conversations.find({\n                \"ChatConversation.useMemo[conversation]\": (c)=>c.id === conversationId\n            }[\"ChatConversation.useMemo[conversation]\"]);\n        }\n    }[\"ChatConversation.useMemo[conversation]\"], [\n        conversations,\n        conversationId\n    ]);\n    // Memoizar as mensagens da conversa para evitar recalculos\n    const conversationMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversationMessages]\": ()=>{\n            return messages[conversationId] || [];\n        }\n    }[\"ChatConversation.useMemo[conversationMessages]\"], [\n        messages,\n        conversationId\n    ]);\n    // Referência para controlar se as mensagens já foram carregadas\n    const messagesLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Carregar mensagens ao montar o componente - apenas uma vez\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            if (conversationId && !messagesLoadedRef.current) {\n                // Sempre carregar mensagens completas quando abrir uma conversa\n                // O loadConversations só traz a última mensagem, precisamos de todas\n                console.log('[ChatConversation] Carregando mensagens completas para conversa:', conversationId);\n                loadMessages(conversationId);\n                messagesLoadedRef.current = true;\n            }\n            // Limpar a referência quando o componente for desmontado\n            return ({\n                \"ChatConversation.useEffect\": ()=>{\n                    messagesLoadedRef.current = false;\n                }\n            })[\"ChatConversation.useEffect\"];\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationId,\n        loadMessages\n    ]);\n    // Referência para controlar se já marcou como lida\n    const markedAsReadRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Marcar mensagens como lidas quando a conversa for visualizada - apenas uma vez\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            if (conversationId && conversationMessages.length > 0 && !markedAsReadRef.current) {\n                const lastMessage = conversationMessages[conversationMessages.length - 1];\n                if (lastMessage) {\n                    markedAsReadRef.current = true; // Marcar como já processado\n                    // Delay para garantir que a conversa foi totalmente carregada\n                    const timer = setTimeout({\n                        \"ChatConversation.useEffect.timer\": ()=>{\n                            markMessagesAsRead(conversationId, lastMessage.id);\n                        }\n                    }[\"ChatConversation.useEffect.timer\"], 1000);\n                    return ({\n                        \"ChatConversation.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatConversation.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationId,\n        conversationMessages.length\n    ]); // Removido markMessagesAsRead para evitar loop\n    // Reset da referência quando muda de conversa\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            markedAsReadRef.current = false;\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationId\n    ]);\n    // Rolar para a última mensagem\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationMessages\n    ]);\n    // Verificar se o usuário atual é administrador do grupo\n    const isGroupAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupAdmin]\": ()=>{\n            var _conversation_participants;\n            if (!conversation || !user) return false;\n            const userParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useMemo[isGroupAdmin]\": (p)=>p.userId === user.id\n            }[\"ChatConversation.useMemo[isGroupAdmin]\"]);\n            return (userParticipant === null || userParticipant === void 0 ? void 0 : userParticipant.isAdmin) || false;\n        }\n    }[\"ChatConversation.useMemo[isGroupAdmin]\"], [\n        conversation,\n        user\n    ]);\n    // Verificar se é uma conversa de grupo\n    const isGroupChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupChat]\": ()=>{\n            return (conversation === null || conversation === void 0 ? void 0 : conversation.type) === 'GROUP';\n        }\n    }[\"ChatConversation.useMemo[isGroupChat]\"], [\n        conversation\n    ]);\n    // Função para confirmar saída do grupo\n    const handleLeaveGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'leave-group',\n                title: 'Sair do grupo',\n                message: 'Tem certeza que deseja sair do grupo \"'.concat(conversation.title || 'Grupo', '\"?'),\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                console.log('Saiu do grupo com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após sair do grupo');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleLeaveGroup]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]).catch({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": (error)=>{\n                                console.error('Erro ao sair do grupo:', error);\n                                alert('Não foi possível sair do grupo. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleLeaveGroup]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleLeaveGroup]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Função para adicionar múltiplos participantes\n    const handleAddParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleAddParticipants]\": (selectedUsers)=>{\n            if (!conversation || !selectedUsers || selectedUsers.length === 0) return;\n            setAddingParticipants(true);\n            addMultipleParticipantsToGroup(conversation.id, selectedUsers).then({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (result)=>{\n                    console.log('Resultado da adição de participantes:', result);\n                    if (result.success) {\n                        console.log(\"\".concat(result.successCount, \" participantes adicionados com sucesso\"));\n                        if (result.errorCount > 0) {\n                            console.warn(\"\".concat(result.errorCount, \" participantes n\\xe3o puderam ser adicionados\"));\n                        // Poderia mostrar um toast com essa informação\n                        }\n                    } else {\n                        console.error('Falha ao adicionar participantes');\n                        alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                    }\n                    setShowAddParticipant(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).catch({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (error)=>{\n                    console.error('Erro ao adicionar participantes:', error);\n                    alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).finally({\n                \"ChatConversation.useCallback[handleAddParticipants]\": ()=>{\n                    setAddingParticipants(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]);\n        }\n    }[\"ChatConversation.useCallback[handleAddParticipants]\"], [\n        conversation,\n        addMultipleParticipantsToGroup\n    ]);\n    // Função para apagar conversa (sair da conversa)\n    const handleDeleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'delete-conversation',\n                title: 'Apagar conversa',\n                message: 'Tem certeza que deseja apagar esta conversa? Esta ação não pode ser desfeita.',\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                        // Para apagar uma conversa, usamos a mesma função de sair do grupo\n                        // No backend, isso marca o participante como tendo saído da conversa\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                console.log('Conversa apagada com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após apagar conversa');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleDeleteConversation]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]).catch({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": (error)=>{\n                                console.error('Erro ao apagar conversa:', error);\n                                alert('Não foi possível apagar a conversa. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleDeleteConversation]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleDeleteConversation]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Obter o nome da conversa - memoizado para evitar recalculos\n    const getConversationName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationName]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return 'Conversa';\n            if (conversation.type === 'GROUP') {\n                return conversation.title || 'Grupo';\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationName]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationName]\"]);\n            // Priorizar fullName para usuários e clientes\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                return otherParticipant.user.fullName;\n            }\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                // Usar o nome do paciente titular se disponível\n                const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                if (clientPersonName && clientPersonName.trim() !== '') {\n                    return clientPersonName;\n                }\n                // Fallback para fullName do cliente ou login\n                return otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n            }\n            return 'Usuário';\n        }\n    }[\"ChatConversation.useCallback[getConversationName]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter a imagem da conversa - memoizado para evitar recalculos\n    const getConversationImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationImage]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return null;\n            if (conversation.type === 'GROUP') {\n                return null;\n            }\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationImage]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationImage]\"]);\n            return otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.profileImageUrl;\n        }\n    }[\"ChatConversation.useCallback[getConversationImage]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter iniciais para avatar - memoizado para evitar recalculos\n    const getInitials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getInitials]\": (name)=>{\n            if (!name) return 'U';\n            try {\n                const names = name.split(' ');\n                if (names.length === 1) return names[0].charAt(0);\n                return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n            } catch (error) {\n                console.error('Erro ao obter iniciais:', error);\n                return 'U';\n            }\n        }\n    }[\"ChatConversation.useCallback[getInitials]\"], []);\n    // Formatar data da mensagem - memoizado para evitar recalculos\n    const formatMessageDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[formatMessageDate]\": (timestamp)=>{\n            if (!timestamp) return '';\n            try {\n                return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(new Date(timestamp), 'HH:mm', {\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.ptBR\n                });\n            } catch (error) {\n                console.error('Erro ao formatar data:', error);\n                return '';\n            }\n        }\n    }[\"ChatConversation.useCallback[formatMessageDate]\"], []);\n    // Agrupar mensagens por data - memoizado para evitar recalculos\n    const groupMessagesByDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[groupMessagesByDate]\": (messages)=>{\n            if (!messages || messages.length === 0) {\n                console.log(\"Nenhuma mensagem para agrupar\");\n                return [];\n            }\n            console.log(\"Agrupando \".concat(messages.length, \" mensagens\"));\n            try {\n                const groups = {};\n                // Ordenar mensagens por data (mais antigas primeiro)\n                const sortedMessages = [\n                    ...messages\n                ].sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                }[\"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\"]);\n                sortedMessages.forEach({\n                    \"ChatConversation.useCallback[groupMessagesByDate]\": (message)=>{\n                        if (!message || !message.createdAt) {\n                            console.warn(\"Mensagem inválida encontrada:\", message);\n                            return;\n                        }\n                        try {\n                            const messageDate = new Date(message.createdAt);\n                            const date = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(messageDate, 'dd/MM/yyyy');\n                            if (!groups[date]) {\n                                groups[date] = [];\n                            }\n                            groups[date].push(message);\n                        } catch (err) {\n                            console.error(\"Erro ao processar mensagem:\", err, message);\n                        }\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate]\"]);\n                // Verificar se temos grupos\n                if (Object.keys(groups).length === 0) {\n                    console.warn(\"Nenhum grupo criado após processamento\");\n                    return [];\n                }\n                // Ordenar os grupos por data (mais antigos primeiro)\n                const result = Object.entries(groups).sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param, param1)=>{\n                        let [dateA] = param, [dateB] = param1;\n                        // Converter strings de data para objetos Date para comparação\n                        const [dayA, monthA, yearA] = dateA.split('/').map(Number);\n                        const [dayB, monthB, yearB] = dateB.split('/').map(Number);\n                        return new Date(yearA, monthA - 1, dayA) - new Date(yearB, monthB - 1, dayB);\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]).map({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param)=>{\n                        let [date, messages] = param;\n                        return {\n                            date,\n                            messages\n                        };\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]);\n                console.log(\"Criados \".concat(result.length, \" grupos de mensagens\"));\n                return result;\n            } catch (error) {\n                console.error('Erro ao agrupar mensagens:', error);\n                return [];\n            }\n        }\n    }[\"ChatConversation.useCallback[groupMessagesByDate]\"], []);\n    // Memoizar os grupos de mensagens para evitar recalculos\n    const messageGroups = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[messageGroups]\": ()=>{\n            return groupMessagesByDate(conversationMessages);\n        }\n    }[\"ChatConversation.useMemo[messageGroups]\"], [\n        groupMessagesByDate,\n        conversationMessages\n    ]);\n    // Se a conversa não for encontrada, mostrar mensagem de erro\n    if (!conversation) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 text-white rounded-t-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                            \"aria-label\": \"Voltar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: compact ? 16 : 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-white text-lg\",\n                            children: \"Conversa n\\xe3o encontrada\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4 text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"A conversa n\\xe3o foi encontrada ou foi removida.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n            lineNumber: 390,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                        \"aria-label\": \"Voltar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: compact ? 16 : 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-1\",\n                        children: [\n                            getConversationImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: getConversationImage(),\n                                alt: getConversationName(),\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full object-cover\"),\n                                onError: (e)=>{\n                                    e.target.onerror = null;\n                                    e.target.style.display = 'none';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full bg-white/20 flex items-center justify-center text-white font-medium shadow-sm\"),\n                                children: getInitials(getConversationName())\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white \".concat(compact ? 'text-sm' : 'text-base'),\n                                        children: getConversationName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.isOnline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-white/80\",\n                                        children: \"Online\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowOptionsMenu(!showOptionsMenu),\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Mais op\\xe7\\xf5es\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: compact ? 16 : 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, undefined),\n                            showOptionsMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 w-48 z-10\",\n                                children: isGroupChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowParticipantsList(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ver participantes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 463,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowAddParticipant(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Adicionar participante\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 475,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLeaveGroup,\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Sair do grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 487,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setConfirmAction({\n                                                    type: 'delete-group',\n                                                    title: 'Deletar grupo',\n                                                    message: 'Tem certeza que deseja deletar o grupo \"'.concat(conversation.title || 'Grupo', '\"? Todos os participantes ser\\xe3o removidos e esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.'),\n                                                    onConfirm: async ()=>{\n                                                        console.log('Deletando grupo:', conversation.id);\n                                                        try {\n                                                            const currentToken = localStorage.getItem('token');\n                                                            const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n                                                            console.log('Fazendo requisição DELETE para:', \"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id));\n                                                            // Remover todos os participantes para deletar o grupo\n                                                            const participants = conversation.participants || [];\n                                                            for (const participant of participants){\n                                                                const participantId = participant.userId || participant.clientId;\n                                                                if (participantId) {\n                                                                    await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id, \"/participants/\").concat(participantId), {\n                                                                        method: 'DELETE',\n                                                                        headers: {\n                                                                            'Authorization': \"Bearer \".concat(currentToken)\n                                                                        }\n                                                                    });\n                                                                }\n                                                            }\n                                                            const response = {\n                                                                ok: true\n                                                            }; // Simular sucesso\n                                                            console.log('Resposta:', response.status, response.statusText);\n                                                            if (response.ok) {\n                                                                console.log('Grupo deletado com sucesso');\n                                                                // Usar removeParticipantFromGroup para remover o usuário atual\n                                                                await removeParticipantFromGroup(conversation.id, user.id);\n                                                                onBack();\n                                                            } else {\n                                                                const errorText = await response.text();\n                                                                console.error('Erro na resposta:', errorText);\n                                                                alert('Erro ao deletar grupo: ' + response.status);\n                                                            }\n                                                        } catch (error) {\n                                                            console.error('Erro ao deletar grupo:', error);\n                                                            alert('Erro ao deletar grupo: ' + error.message);\n                                                        }\n                                                    }\n                                                });\n                                                setConfirmDialogOpen(true);\n                                                setShowOptionsMenu(false);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-t border-gray-200 dark:border-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Deletar grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 496,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : // Opções para conversas individuais\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDeleteConversation,\n                                    className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 557,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Apagar conversa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 558,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 553,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, undefined),\n            showAddParticipant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col h-full overflow-hidden\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddUsers: handleAddParticipants,\n                        onClose: ()=>setShowAddParticipant(false),\n                        title: \"Adicionar participantes ao grupo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 569,\n                        columnNumber: 11\n                    }, undefined),\n                    addingParticipants && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50 flex items-center justify-center z-30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-10 w-10 border-b-2 border-cyan-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 578,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 dark:text-gray-300\",\n                                    children: \"Adicionando participantes...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 579,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 577,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 576,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, undefined),\n            showParticipantsList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowParticipantsList(false),\n                                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                        \"aria-label\": \"Voltar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white text-base\",\n                                        children: \"Participantes do grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, undefined),\n                            isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setShowParticipantsList(false);\n                                    setShowAddParticipant(true);\n                                },\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Adicionar participante\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 610,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200 dark:divide-gray-700\",\n                            children: conversation === null || conversation === void 0 ? void 0 : (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.map((participant)=>{\n                                var _participant_user;\n                                // Determinar se é usuário ou cliente e obter nome correto\n                                const getParticipantName = ()=>{\n                                    var _participant_user;\n                                    if (participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        return 'Você';\n                                    }\n                                    // Se é usuário\n                                    if ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.fullName) {\n                                        return participant.user.fullName;\n                                    }\n                                    // Se é cliente\n                                    if (participant.client) {\n                                        var _participant_client_clientPersons__person, _participant_client_clientPersons_, _participant_client_clientPersons;\n                                        const clientPersonName = (_participant_client_clientPersons = participant.client.clientPersons) === null || _participant_client_clientPersons === void 0 ? void 0 : (_participant_client_clientPersons_ = _participant_client_clientPersons[0]) === null || _participant_client_clientPersons_ === void 0 ? void 0 : (_participant_client_clientPersons__person = _participant_client_clientPersons_.person) === null || _participant_client_clientPersons__person === void 0 ? void 0 : _participant_client_clientPersons__person.fullName;\n                                        if (clientPersonName && clientPersonName.trim() !== '') {\n                                            return clientPersonName;\n                                        }\n                                        return participant.client.fullName && participant.client.fullName.trim() !== '' ? participant.client.fullName : participant.client.login;\n                                    }\n                                    return 'Usuário';\n                                };\n                                const participantName = getParticipantName();\n                                const isCurrentUser = participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id);\n                                const participantId = participant.userId || participant.clientId;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex-shrink-0\",\n                                            children: ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.profileImageUrl) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: participant.user.profileImageUrl,\n                                                alt: participantName,\n                                                className: \"h-10 w-10 rounded-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                    e.target.parentNode.innerHTML = '<div class=\"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\">'.concat(getInitials(participantName), \"</div>\");\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 652,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\",\n                                                children: getInitials(participantName)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 663,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 650,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                    children: participantName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        participant.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-cyan-600 dark:text-cyan-400 bg-cyan-50 dark:bg-cyan-900/30 px-2 py-0.5 rounded-full\",\n                                                            children: \"Administrador\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        participant.client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"Cliente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 670,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        isGroupAdmin && !isCurrentUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                if (window.confirm(\"Tem certeza que deseja remover \".concat(participantName, \" do grupo?\"))) {\n                                                    removeParticipantFromGroup(conversation.id, participantId).catch((error)=>{\n                                                        console.error('Erro ao remover participante:', error);\n                                                        alert('Não foi possível remover o participante. Tente novamente mais tarde.');\n                                                    });\n                                                }\n                                            },\n                                            className: \"p-1.5 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n                                            \"aria-label\": \"Remover participante\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 703,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 690,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, participant.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 648,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 615,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 588,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-5 space-y-5\",\n                children: [\n                    messageGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-1 bg-cyan-100 dark:bg-cyan-900/30 rounded-full text-xs text-cyan-700 dark:text-cyan-300 shadow-sm\",\n                                        children: group.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 719,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, undefined),\n                                group.messages.map((message)=>{\n                                    var _message_metadata;\n                                    const isOwnMessage = message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id);\n                                    const isGroupChat = conversation.type === 'GROUP';\n                                    // Encontrar o nome do remetente para mensagens de grupo\n                                    const getSenderName = ()=>{\n                                        var _message_sender;\n                                        if (message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                            return 'Você';\n                                        }\n                                        // Se a mensagem tem sender (usuário)\n                                        if ((_message_sender = message.sender) === null || _message_sender === void 0 ? void 0 : _message_sender.fullName) {\n                                            return message.sender.fullName;\n                                        }\n                                        // Se a mensagem tem senderClient (cliente)\n                                        if (message.senderClient) {\n                                            var _message_senderClient_clientPersons__person, _message_senderClient_clientPersons_, _message_senderClient_clientPersons;\n                                            // Usar o nome do paciente titular se disponível\n                                            const clientPersonName = (_message_senderClient_clientPersons = message.senderClient.clientPersons) === null || _message_senderClient_clientPersons === void 0 ? void 0 : (_message_senderClient_clientPersons_ = _message_senderClient_clientPersons[0]) === null || _message_senderClient_clientPersons_ === void 0 ? void 0 : (_message_senderClient_clientPersons__person = _message_senderClient_clientPersons_.person) === null || _message_senderClient_clientPersons__person === void 0 ? void 0 : _message_senderClient_clientPersons__person.fullName;\n                                            if (clientPersonName && clientPersonName.trim() !== '') {\n                                                return clientPersonName;\n                                            }\n                                            // Fallback para fullName do cliente ou login\n                                            return message.senderClient.fullName && message.senderClient.fullName.trim() !== '' ? message.senderClient.fullName : message.senderClient.login;\n                                        }\n                                        return 'Usuário';\n                                    };\n                                    // Obter as iniciais do remetente para mensagens de grupo\n                                    const getSenderInitials = ()=>{\n                                        if (isOwnMessage) return 'Você';\n                                        const senderName = getSenderName();\n                                        return getInitials(senderName);\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(isOwnMessage ? 'justify-end' : 'justify-start'),\n                                        children: [\n                                            isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-2 flex flex-col items-center justify-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-xs font-medium\",\n                                                    children: getSenderInitials()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 770,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[75%] \".concat(compact ? 'max-w-[85%]' : ''),\n                                                children: [\n                                                    isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 ml-1\",\n                                                        children: getSenderName()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-3 rounded-lg shadow-sm \".concat(isOwnMessage ? 'bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white rounded-br-none' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none border border-cyan-100 dark:border-cyan-800'),\n                                                        children: message.contentType && [\n                                                            'SHARED_APPOINTMENT',\n                                                            'SHARED_PERSON',\n                                                            'SHARED_CLIENT',\n                                                            'SHARED_USER',\n                                                            'SHARED_SERVICE_TYPE',\n                                                            'SHARED_LOCATION',\n                                                            'SHARED_WORKING_HOURS',\n                                                            'SHARED_INSURANCE',\n                                                            'SHARED_INSURANCE_LIMIT'\n                                                        ].includes(message.contentType) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            message: message,\n                                                            onItemClick: handleSharedItemClickWithLog\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.contentType === 'ATTACHMENT' && ((_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.attachments) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                message.metadata.attachments.map((attachment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        attachment: attachment,\n                                                                        compact: true\n                                                                    }, attachment.id || index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 29\n                                                                    }, undefined)),\n                                                                message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1.5 \".concat(isOwnMessage ? 'text-right' : '', \" text-gray-500 dark:text-gray-400\"),\n                                                        children: formatMessageDate(message.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 777,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 764,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            ]\n                        }, group.date, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 717,\n                            columnNumber: 11\n                        }, undefined)),\n                    conversationMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm\",\n                            children: \"Nenhuma mensagem ainda. Comece a conversar!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 827,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 833,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 715,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                conversationId: conversationId,\n                compact: compact\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 837,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ConfirmationDialog, {\n                isOpen: confirmDialogOpen,\n                onClose: ()=>setConfirmDialogOpen(false),\n                onConfirm: confirmAction.onConfirm,\n                title: confirmAction.title,\n                message: confirmAction.message,\n                confirmText: \"Confirmar\",\n                cancelText: \"Cancelar\",\n                variant: \"warning\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 840,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n        lineNumber: 411,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatConversation, \"4TyLDf2YsDkUA7okKe4kf9x8fsc=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation\n    ];\n});\n_c = ChatConversation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatConversation);\nvar _c;\n$RefreshReg$(_c, \"ChatConversation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatConversation.js\n"));

/***/ })

});