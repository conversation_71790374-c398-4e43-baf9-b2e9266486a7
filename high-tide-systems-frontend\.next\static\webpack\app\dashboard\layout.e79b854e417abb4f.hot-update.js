"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/chat/ChatList.js":
/*!*****************************************!*\
  !*** ./src/components/chat/ChatList.js ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock,MessageCircle,MoreVertical,Plus,Search,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock,MessageCircle,MoreVertical,Plus,Search,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatList = (param)=>{\n    let { searchQuery = '' } = param;\n    _s();\n    const { conversations, setActiveConversation, messages, loadConversations, isLoading } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Estado para forçar re-renderização\n    const [updateTrigger, setUpdateTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Carregar conversas quando o componente é montado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatList.useEffect\": ()=>{\n            // Carregar conversas inicialmente\n            console.log('ChatList montado, verificando se é necessário carregar conversas...');\n            if (user && user.id) {\n                console.log('Usuário logado:', user.id);\n                // Carregar conversas inicialmente\n                console.log('Carregando conversas iniciais');\n                loadConversations(false); // Usar cache se disponível na primeira carga\n            } else {\n                console.log('Usuário não logado, não carregando conversas');\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatList.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o usuário mudar\n    // Adicionar listener para eventos de atualização do WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatList.useEffect\": ()=>{\n            // Função para lidar com eventos de atualização\n            const handleWebSocketUpdate = {\n                \"ChatList.useEffect.handleWebSocketUpdate\": (event)=>{\n                    const { type, action, conversationId } = event.detail;\n                    console.log(\"ChatList recebeu evento de atualiza\\xe7\\xe3o: \".concat(type), event.detail);\n                    // Forçar re-renderização para qualquer tipo de evento\n                    setUpdateTrigger({\n                        \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                    }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                    // Se for uma atualização de conversas, verificar se precisamos recarregar\n                    if (type === 'conversations') {\n                        // Se for uma ação de exclusão, não precisamos recarregar as conversas\n                        // pois o estado já foi atualizado no ChatContext\n                        if (action === 'delete') {\n                            console.log(\"ChatList: Conversa \".concat(conversationId, \" foi apagada, atualizando interface\"));\n                            // Apenas forçar re-renderização\n                            setUpdateTrigger({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        } else if (conversations.length === 0) {\n                            // Só recarregar se não tivermos conversas\n                            console.log('ChatList: Não há conversas carregadas, recarregando após evento de atualização');\n                            loadConversations(false).then({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": ()=>{\n                                    console.log('ChatList: Conversas carregadas com sucesso');\n                                    // Forçar outra re-renderização após o carregamento\n                                    setUpdateTrigger({\n                                        \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                                    }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                                }\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        } else {\n                            console.log('ChatList: Já existem conversas carregadas, apenas atualizando interface');\n                            // Apenas forçar re-renderização\n                            setUpdateTrigger({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        }\n                    }\n                }\n            }[\"ChatList.useEffect.handleWebSocketUpdate\"];\n            // Adicionar listener\n            window.addEventListener('chat:websocket:update', handleWebSocketUpdate);\n            // Remover listener quando o componente for desmontado\n            return ({\n                \"ChatList.useEffect\": ()=>{\n                    window.removeEventListener('chat:websocket:update', handleWebSocketUpdate);\n                }\n            })[\"ChatList.useEffect\"];\n        }\n    }[\"ChatList.useEffect\"], [\n        loadConversations\n    ]); // Dependência: loadConversations\n    // Filtrar conversas com base na pesquisa\n    const filteredConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatList.useMemo[filteredConversations]\": ()=>{\n            // Log para debug\n            console.log(\"Recalculando filteredConversations. Trigger: \".concat(updateTrigger, \", Conversas: \").concat(conversations.length));\n            if (!searchQuery) return conversations;\n            return conversations.filter({\n                \"ChatList.useMemo[filteredConversations]\": (conversation)=>{\n                    var _conversation_title;\n                    // Para conversas individuais, pesquisar pelo nome do outro participante\n                    if (conversation.type === 'INDIVIDUAL') {\n                        var _conversation_participants, _otherParticipant_user;\n                        const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                            \"ChatList.useMemo[filteredConversations]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n                        }[\"ChatList.useMemo[filteredConversations]\"]);\n                        // Buscar pelo fullName do usuário ou cliente\n                        if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                            return otherParticipant.user.fullName.toLowerCase().includes(searchQuery.toLowerCase());\n                        }\n                        if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                            var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                            const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                            let clientName;\n                            if (clientPersonName && clientPersonName.trim() !== '') {\n                                clientName = clientPersonName;\n                            } else {\n                                clientName = otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n                            }\n                            return clientName.toLowerCase().includes(searchQuery.toLowerCase());\n                        }\n                        return false;\n                    }\n                    // Para grupos, pesquisar pelo título\n                    return (_conversation_title = conversation.title) === null || _conversation_title === void 0 ? void 0 : _conversation_title.toLowerCase().includes(searchQuery.toLowerCase());\n                }\n            }[\"ChatList.useMemo[filteredConversations]\"]);\n        }\n    }[\"ChatList.useMemo[filteredConversations]\"], [\n        conversations,\n        searchQuery,\n        user === null || user === void 0 ? void 0 : user.id,\n        updateTrigger\n    ]); // Adicionado updateTrigger como dependência\n    // Obter o nome da conversa - memoizado para evitar recalculos\n    const getConversationName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getConversationName]\": (conversation)=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return 'Conversa';\n            if (conversation.type === 'GROUP') {\n                return conversation.title || 'Grupo';\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatList.useCallback[getConversationName]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatList.useCallback[getConversationName]\"]);\n            // Retornar nome do usuário ou cliente\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                return otherParticipant.user.fullName;\n            }\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                if (clientPersonName && clientPersonName.trim() !== '') {\n                    return clientPersonName;\n                }\n                return otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n            }\n            return 'Usuário';\n        }\n    }[\"ChatList.useCallback[getConversationName]\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter a imagem da conversa - memoizado para evitar recalculos\n    const getConversationImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getConversationImage]\": (conversation)=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return null;\n            if (conversation.type === 'GROUP') {\n                return null; // Usar um ícone de grupo\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatList.useCallback[getConversationImage]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatList.useCallback[getConversationImage]\"]);\n            return otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.profileImageUrl;\n        }\n    }[\"ChatList.useCallback[getConversationImage]\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Formatar a data da última mensagem - memoizado para evitar recalculos\n    const formatLastMessageTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[formatLastMessageTime]\": (timestamp)=>{\n            if (!timestamp) return '';\n            try {\n                return (0,_barrel_optimize_names_format_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(new Date(timestamp), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_5__.ptBR\n                });\n            } catch (error) {\n                console.error('Erro ao formatar data:', error);\n                return '';\n            }\n        }\n    }[\"ChatList.useCallback[formatLastMessageTime]\"], []);\n    // Obter iniciais para avatar - memoizado para evitar recalculos\n    const getInitials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getInitials]\": (name)=>{\n            if (!name) return 'U';\n            try {\n                const names = name.split(' ');\n                if (names.length === 1) return names[0].charAt(0);\n                return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n            } catch (error) {\n                console.error('Erro ao obter iniciais:', error);\n                return 'U';\n            }\n        }\n    }[\"ChatList.useCallback[getInitials]\"], []);\n    // Log para depuração - quando o componente é renderizado\n    console.log(\"ChatList renderizando. Trigger: \".concat(updateTrigger, \", Conversas: \").concat(conversations.length, \", Filtradas: \").concat(filteredConversations.length));\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.3,\n                ease: \"easeOut\"\n            }\n        },\n        exit: {\n            opacity: 0,\n            y: -20,\n            transition: {\n                duration: 0.2\n            }\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    if (filteredConversations.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full p-6 text-gray-500 dark:text-gray-400 relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-4 right-4 flex flex-col items-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 232,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 font-medium\",\n                                    children: \"Carregando...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 231,\n                            columnNumber: 23\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        scale: 0.8,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 48,\n                            className: \"mx-auto mb-3 text-blue-500 dark:text-blue-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2\",\n                            children: searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa iniciada'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: searchQuery ? 'Tente ajustar sua pesquisa' : 'Inicie uma conversa para começar a trocar mensagens'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        className: \"relative\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 flex flex-col items-end z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-blue-600 dark:text-blue-400 font-medium\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                        lineNumber: 271,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1 p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: filteredConversations.map((conversation, index)=>{\n                        const name = getConversationName(conversation);\n                        const image = getConversationImage(conversation);\n                        // Verificar se há mensagens carregadas para esta conversa\n                        const conversationMessages = messages[conversation.id] || [];\n                        // Usar a última mensagem da conversa ou a última mensagem carregada\n                        const lastMessage = conversation.lastMessage || (conversationMessages.length > 0 ? conversationMessages[conversationMessages.length - 1] : null);\n                        // Verificar se há mensagens não lidas (usar hasUnread do backend ou fallback para unreadCount)\n                        const hasUnread = conversation.hasUnread || conversation.unreadCount > 0;\n                        console.log(hasUnread, \"aaaaaaaaaaaa\", conversation);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                            variants: itemVariants,\n                            initial: \"hidden\",\n                            animate: \"visible\",\n                            exit: \"exit\",\n                            onClick: ()=>setActiveConversation(conversation.id),\n                            className: \"w-full flex items-center gap-4 p-4 transition-all duration-300 text-left rounded-2xl group relative overflow-hidden \".concat(hasUnread ? 'bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/30 dark:to-blue-900/30 hover:from-cyan-100 hover:to-blue-100 dark:hover:from-cyan-800/40 dark:hover:to-blue-800/40 border-l-4 border-cyan-500 dark:border-cyan-400 shadow-md' : 'hover:bg-gradient-to-r hover:from-cyan-50 hover:to-cyan-100 dark:hover:from-cyan-900/20 dark:hover:to-cyan-800/20'),\n                            whileHover: {\n                                scale: 1.02,\n                                transition: {\n                                    duration: 0.2\n                                }\n                            },\n                            whileTap: {\n                                scale: 0.98,\n                                transition: {\n                                    duration: 0.1\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 transition-opacity duration-300 \".concat(hasUnread ? 'bg-gradient-to-r from-cyan-500/10 via-blue-500/5 to-cyan-700/10 opacity-50 group-hover:opacity-70' : 'bg-gradient-to-r from-cyan-500/5 via-transparent to-cyan-700/5 opacity-0 group-hover:opacity-100')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-shrink-0 z-10\",\n                                    children: image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image,\n                                                alt: name,\n                                                className: \"h-12 w-12 rounded-2xl object-cover transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200 shadow-lg' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 338,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 21\n                                    }, undefined) : conversation.type === 'GROUP' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white shadow-lg transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white font-semibold shadow-lg transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                children: getInitials(name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 364,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 355,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0 z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"truncate transition-colors duration-300 \".concat(hasUnread ? 'font-bold text-gray-900 dark:text-white group-hover:text-cyan-700 dark:group-hover:text-cyan-300' : 'font-semibold text-gray-900 dark:text-gray-100 group-hover:text-cyan-600 dark:group-hover:text-cyan-400'),\n                                                    children: name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-2\",\n                                                    children: formatLastMessageTime(lastMessage.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        lastMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm truncate transition-colors duration-300 \".concat(hasUnread ? 'font-medium text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'),\n                                            children: lastMessage.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 388,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 italic\",\n                                            children: \"Nenhuma mensagem\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 371,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    children: hasUnread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        exit: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 500,\n                                            damping: 30\n                                        },\n                                        className: \"flex-shrink-0 z-10 flex flex-col items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 w-4 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 shadow-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-white animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 413,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-cyan-600 dark:text-cyan-400 uppercase tracking-wide\",\n                                                children: \"Nova\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 417,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 405,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, conversation.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 292,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatList, \"XFZkOmi5v6Y4DQyDJMfXLBkr4gg=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ChatList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatList);\nvar _c;\n$RefreshReg$(_c, \"ChatList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatList.js\n"));

/***/ })

});