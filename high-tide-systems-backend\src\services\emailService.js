const nodemailer = require("nodemailer");
const prisma = require("../utils/prisma");

class EmailService {
  constructor() {
    // Initialize with null transporter - will be configured in initialize()
    this.systemTransporter = null;
  }

  /**
   * Initialize the email service with configuration from environment variables
   * @returns {Promise} - Result of initialization
   */
  async initialize() {
    try {
      this.systemTransporter = nodemailer.createTransporter({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: process.env.EMAIL_SECURE === "true",
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD,
        },
                tls: {
          // Para desenvolvimento, aceitar certificados auto-assinados
          rejectUnauthorized: process.env.NODE_ENV === 'production',
          // Forçar TLS 1.2 ou superior
          minVersion: 'TLSv1.2',
          // Para Gmail especificamente
          servername: process.env.EMAIL_HOST,
        },
        // Configurações adicionais para debugging
        debug: process.env.NODE_ENV === 'development',
        logger: process.env.NODE_ENV === 'development',

      });

      // Test connection
      await this.systemTransporter.verify();
      console.log("Serviço de email do sistema inicializado");
      return { success: true };
    } catch (error) {
      console.error("Error initializing system email service:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Busca a configuração de email ativa de uma empresa
   * @param {string} companyId - ID da empresa
   * @returns {Promise} - Configuração de email ou null
   */
  async getCompanyEmailConfig(companyId) {
    try {
      const config = await prisma.emailConfig.findFirst({
        where: {
          companyId,
          active: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      return config;
    } catch (error) {
      console.error(`Erro ao buscar configuração de email da empresa ${companyId}:`, error);
      return null;
    }
  }

  /**
   * Cria um transporter com base na configuração fornecida
   * @param {object} config - Configuração SMTP
   * @returns {object} - Transporter do nodemailer
   */
  createTransporter(config) {
    return nodemailer.createTransporter({
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure,
      auth: {
        user: config.smtpUser,
        pass: config.smtpPassword,
      },
      tls: {
        rejectUnauthorized: process.env.NODE_ENV === 'production',
        minVersion: 'TLSv1.2',
        servername: config.smtpHost,
      },
      debug: process.env.NODE_ENV === 'development',
      logger: process.env.NODE_ENV === 'development',
    });
  }

  /**
   * Envia um email usando SMTP do sistema (para admins) ou da empresa
   * @param {string} to - Email do destinatário
   * @param {string} subject - Assunto do email
   * @param {string} html - Conteúdo HTML do email
   * @param {object} options - Opções adicionais
   * @param {string} options.companyId - ID da empresa (opcional)
   * @param {boolean} options.useSystemSMTP - Forçar uso do SMTP do sistema
   * @param {object} options.user - Dados do usuário que está enviando (opcional)
   * @returns {Promise} - Resultado do envio
   */
  async sendEmail(to, subject, html, options = {}) {
    try {
      const { companyId, useSystemSMTP = false, user = null } = options;
      let transporter;
      let fromConfig = {};

      // Verificar se deve usar SMTP do sistema
      const shouldUseSystemSMTP = useSystemSMTP || 
                                 !companyId || 
                                 (user && user.role === 'SYSTEM_ADMIN');

      if (shouldUseSystemSMTP) {
        // Usar SMTP do sistema
        if (!this.systemTransporter) {
          await this.initialize();
        }
        transporter = this.systemTransporter;
        fromConfig = {
          name: process.env.EMAIL_NAME,
          email: process.env.EMAIL_USER,
        };
      } else {
        // Tentar usar SMTP da empresa
        const companyConfig = await this.getCompanyEmailConfig(companyId);
        
        if (companyConfig) {
          transporter = this.createTransporter(companyConfig);
          fromConfig = {
            name: companyConfig.emailFromName,
            email: companyConfig.emailFromAddress,
          };
        } else {
          // Fallback para SMTP do sistema
          console.log(`Empresa ${companyId} não possui configuração de email ativa. Usando SMTP do sistema.`);
          if (!this.systemTransporter) {
            await this.initialize();
          }
          transporter = this.systemTransporter;
          fromConfig = {
            name: process.env.EMAIL_NAME,
            email: process.env.EMAIL_USER,
          };
        }
      }

      const result = await transporter.sendMail({
        from: `"${fromConfig.name}" <${fromConfig.email}>`,
        to,
        subject,
        html,
      });

      console.log(`Email enviado para ${to}, ID: ${result.messageId}`);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error("Erro ao enviar email:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Envia email de confirmação de novo agendamento
   * @param {object} scheduling - Dados do agendamento
   * @param {object} client - Dados do cliente
   * @param {object} provider - Dados do profissional
   * @param {object} serviceType - Tipo de serviço
   * @param {object} location - Local do agendamento
   * @param {object} branch - Unidade onde será realizado o agendamento (opcional)
   * @param {string} companyId - ID da empresa (opcional)
   */
  async sendNewAppointmentEmail(
    scheduling,
    client,
    provider,
    serviceType,
    location,
    branch = null,
    companyId = null
  ) {
    // Verificar se o cliente é válido
    if (!client || !client.email) {
      console.error("Cliente inválido ou sem email", client);
      return { success: false, error: "Cliente inválido ou sem email" };
    }

    const formattedDate = scheduling.startDate.toLocaleDateString("pt-BR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const formattedStartTime = scheduling.startDate.toLocaleTimeString(
      "pt-BR",
      {
        hour: "2-digit",
        minute: "2-digit",
      }
    );

    const formattedEndTime = scheduling.endDate.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    const subject = `Confirmação de Agendamento - ${scheduling.title}`;

    // Add branch information if available
    const branchInfo = branch
      ? `<p><strong>Unidade:</strong> ${branch.name}${branch.code ? ` (${branch.code})` : ""
      }</p>
       <p><strong>Endereço da Unidade:</strong> ${branch.address}</p>`
      : "";

    const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4682B4;">Confirmação de Agendamento</h2>
      <p>Olá, <strong>${client.fullName || client.login || "Cliente"}</strong>!</p>
      <p>Seu agendamento foi confirmado com sucesso.</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4682B4;">${scheduling.title}</h3>
        <p><strong>Data:</strong> ${formattedDate}</p>
        <p><strong>Horário:</strong> ${formattedStartTime} às ${formattedEndTime}</p>
        <p><strong>Profissional:</strong> ${provider.fullName}</p>
        <p><strong>Serviço:</strong> ${serviceType.name}</p>
        ${branchInfo}
        <p><strong>Local:</strong> ${location.name} - ${location.address}</p>
        ${scheduling.description
        ? `<p><strong>Observações:</strong> ${scheduling.description}</p>`
        : ""
      }
      </div>
      
      <p>Para cancelar ou reagendar, entre em contato conosco.</p>
      <p>Agradecemos pela preferência!</p>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777;">
        <p>Este é um email automático, por favor não responda.</p>
      </div>
    </div>
  `;

    return this.sendEmail(client.email, subject, html, { companyId });
  }

  /**
   * Envia email de lembrete 1 dia antes do agendamento
   * @param {object} scheduling - Dados do agendamento
   * @param {object} client - Dados do cliente
   * @param {object} provider - Dados do profissional
   * @param {object} serviceType - Tipo de serviço
   * @param {object} location - Local do agendamento
   * @param {object} branch - Unidade onde será realizado o agendamento (opcional)
   * @param {string} companyId - ID da empresa (opcional)
   */
  async sendReminderEmail(
    scheduling,
    client,
    provider,
    serviceType,
    location,
    branch = null,
    companyId = null
  ) {
    // Verificar se o cliente é válido
    if (!client || !client.email) {
      console.error("Cliente inválido ou sem email", client);
      return { success: false, error: "Cliente inválido ou sem email" };
    }

    const formattedDate = scheduling.startDate.toLocaleDateString("pt-BR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const formattedStartTime = scheduling.startDate.toLocaleTimeString(
      "pt-BR",
      {
        hour: "2-digit",
        minute: "2-digit",
      }
    );

    const formattedEndTime = scheduling.endDate.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    // Tokens para confirmar ou cancelar (em uma implementação real, você usaria JWT para maior segurança)
    const confirmToken = Buffer.from(
      `confirm-${scheduling.id}-${Date.now()}`
    ).toString("base64");
    const cancelToken = Buffer.from(
      `cancel-${scheduling.id}-${Date.now()}`
    ).toString("base64");

    const subject = `Lembrete: Seu agendamento amanhã - ${scheduling.title}`;

    // Add branch information if available
    const branchInfo = branch
      ? `<p><strong>Unidade:</strong> ${branch.name}${branch.code ? ` (${branch.code})` : ""
      }</p>
       <p><strong>Endereço da Unidade:</strong> ${branch.address}</p>`
      : "";

    const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4682B4;">Lembrete de Agendamento</h2>
      <p>Olá, <strong>${client.fullName || client.login || "Cliente"}</strong>!</p>
      <p>Este é um lembrete do seu agendamento <strong>amanhã</strong>.</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4682B4;">${scheduling.title}</h3>
        <p><strong>Data:</strong> ${formattedDate}</p>
        <p><strong>Horário:</strong> ${formattedStartTime} às ${formattedEndTime}</p>
        <p><strong>Profissional:</strong> ${provider.fullName}</p>
        <p><strong>Serviço:</strong> ${serviceType.name}</p>
        ${branchInfo}
        <p><strong>Local:</strong> ${location.name} - ${location.address}</p>
        ${scheduling.description
        ? `<p><strong>Observações:</strong> ${scheduling.description}</p>`
        : ""
      }
      </div>
      
      <div style="margin: 30px 0; text-align: center;">
        <p>Por favor, confirme ou cancele seu agendamento:</p>
        
        <a href="${process.env.FRONTEND_URL
      }/appointments/confirm/${confirmToken}" 
           style="display: inline-block; background-color: #4CAF50; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin-right: 10px;">
           Confirmar
        </a>
        
        <a href="${process.env.FRONTEND_URL
      }/appointments/cancel/${cancelToken}" 
           style="display: inline-block; background-color: #f44336; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px;">
           Cancelar
        </a>
      </div>
      
      <p>Se tiver dúvidas, entre em contato conosco.</p>
      <p>Agradecemos pela preferência!</p>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777;">
        <p>Este é um email automático, por favor não responda.</p>
      </div>
    </div>
  `;

    return this.sendEmail(client.email, subject, html, { companyId });
  }

  /**
 * Envia email de confirmação de conta com template bonito
 * @param {string} email - Email do destinatário
 * @param {string} code - Código de 6 dígitos
 * @param {string} userName - Nome do usuário (opcional)
 * @returns {Promise} - Resultado do envio
 */
  async sendEmailConfirmation(email, code, userName = null) {
    try {
      const subject = "Confirme seu email - High Tide Systems";

      const html = `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Confirmação de Email</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc;">
      <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f8fafc; padding: 40px 0;">
        <tr>
          <td align="center">
            <!-- Container principal -->
            <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden; max-width: 100%;">
              
              <!-- Header com gradiente -->
              <tr>
                <td style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 30px; text-align: center;">
                  <h1 style="color: #ffffff; font-size: 28px; font-weight: 600; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    🌊 High Tide Systems
                  </h1>
                  <p style="color: #e2e8f0; font-size: 16px; margin: 10px 0 0 0; opacity: 0.9;">
                    Confirme seu email para continuar
                  </p>
                </td>
              </tr>
              
              <!-- Conteúdo principal -->
              <tr>
                <td style="padding: 40px 30px;">
                  ${userName ? `<h2 style="color: #2d3748; font-size: 22px; font-weight: 600; margin: 0 0 20px 0;">Olá, ${userName}! 👋</h2>` : '<h2 style="color: #2d3748; font-size: 22px; font-weight: 600; margin: 0 0 20px 0;">Quase lá! 🚀</h2>'}
                  
                  <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                    Para completar seu cadastro e começar a usar nossa plataforma, confirme seu email com o código abaixo:
                  </p>
                  
                  <!-- Código em destaque -->
                  <table cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
                    <tr>
                      <td align="center">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; padding: 25px; display: inline-block; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);">
                          <p style="color: #ffffff; font-size: 14px; font-weight: 500; margin: 0 0 8px 0; text-transform: uppercase; letter-spacing: 1px; opacity: 0.9;">
                            Seu código de verificação
                          </p>
                          <div style="background-color: rgba(255, 255, 255, 0.15); border-radius: 8px; padding: 15px; backdrop-filter: blur(10px);">
                            <span style="color: #ffffff; font-size: 32px; font-weight: 700; letter-spacing: 8px; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                              ${code}
                            </span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </table>
                  
                  <!-- Instruções -->
                  <div style="background-color: #f7fafc; border-left: 4px solid #667eea; padding: 20px; border-radius: 0 8px 8px 0; margin: 25px 0;">
                    <h3 style="color: #2d3748; font-size: 16px; font-weight: 600; margin: 0 0 10px 0;">
                      📋 Como usar:
                    </h3>
                    <ul style="color: #4a5568; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 18px;">
                      <li>Digite este código na tela de confirmação</li>
                      <li>O código expira em <strong>15 minutos</strong></li>
                      <li>Se não foi você, ignore este email</li>
                    </ul>
                  </div>
                  
                  <p style="color: #718096; font-size: 14px; line-height: 1.6; margin: 25px 0 0 0;">
                    Se você não solicitou este código, pode ignorar este email com segurança. 
                    Nenhuma alteração será feita em sua conta.
                  </p>
                </td>
              </tr>
              
              <!-- Rodapé -->
              <tr>
                <td style="background-color: #f7fafc; padding: 30px; text-align: center; border-top: 1px solid #e2e8f0;">
                  <p style="color: #a0aec0; font-size: 14px; margin: 0 0 10px 0;">
                    Este email foi enviado automaticamente. Por favor, não responda.
                  </p>
                  <p style="color: #718096; font-size: 13px; margin: 0;">
                    © 2024 High Tide Systems. Todos os direitos reservados.
                  </p>
                  
                  <!-- Links úteis -->
                  <div style="margin-top: 20px;">
                    <a href="#" style="color: #667eea; text-decoration: none; font-size: 12px; margin: 0 15px;">
                      Central de Ajuda
                    </a>
                    <a href="#" style="color: #667eea; text-decoration: none; font-size: 12px; margin: 0 15px;">
                      Política de Privacidade
                    </a>
                    <a href="#" style="color: #667eea; text-decoration: none; font-size: 12px; margin: 0 15px;">
                      Contato
                    </a>
                  </div>
                </td>
              </tr>
            </table>
            
            <!-- Texto alternativo para clientes de email antigos -->
            <div style="display: none; max-height: 0; overflow: hidden;">
              Confirme seu email com o código ${code}. Este código expira em 15 minutos. High Tide Systems.
            </div>
          </td>
        </tr>
      </table>
    </body>
    </html>
    `;

      return this.sendEmail(email, subject, html, { useSystemSMTP: true });
    } catch (error) {
      console.error("Erro ao enviar email de confirmação:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Envia email de notificação de bug reportado para admins
   * @param {object} bugReport - Dados do bug report
   * @returns {Promise} - Resultado do envio
   */
  async sendBugReportNotification(bugReport) {
    try {
      // Email de destino para notificações de bugs
      const adminEmail = process.env.BUG_REPORT_EMAIL || process.env.EMAIL_USER;

      if (!adminEmail) {
        console.error('Email de destino para bugs não configurado');
        return { success: false, error: 'Email de destino não configurado' };
      }

      const priorityLabels = {
        LOW: 'Baixa',
        MEDIUM: 'Média',
        HIGH: 'Alta',
        CRITICAL: 'Crítica'
      };

      const categoryLabels = {
        GENERAL: 'Geral',
        UI_UX: 'Interface/Experiência',
        PERFORMANCE: 'Performance',
        FUNCTIONALITY: 'Funcionalidade',
        DATA: 'Dados',
        SECURITY: 'Segurança',
        INTEGRATION: 'Integração'
      };

      const priorityColor = {
        LOW: '#10B981',
        MEDIUM: '#F59E0B',
        HIGH: '#EF4444',
        CRITICAL: '#DC2626'
      };

      const subject = `🐛 Novo Bug Reportado - ${bugReport.title}`;

      const html = `
      <!DOCTYPE html>
      <html lang="pt-BR">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bug Report</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8fafc;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f8fafc; padding: 40px 0;">
          <tr>
            <td align="center">
              <!-- Container principal -->
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color: #ffffff; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden; max-width: 100%;">

                <!-- Header -->
                <tr>
                  <td style="background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%); padding: 30px; text-align: center;">
                    <h1 style="color: #ffffff; font-size: 24px; font-weight: 600; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                      🐛 Novo Bug Reportado
                    </h1>
                    <p style="color: #fecaca; font-size: 14px; margin: 8px 0 0 0; opacity: 0.9;">
                      High Tide Systems - Sistema de Relatórios
                    </p>
                  </td>
                </tr>

                <!-- Conteúdo principal -->
                <tr>
                  <td style="padding: 30px;">
                    <!-- Informações do bug -->
                    <div style="background-color: #f8fafc; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
                      <h2 style="color: #1f2937; font-size: 18px; font-weight: 600; margin: 0 0 15px 0;">
                        ${bugReport.title}
                      </h2>

                      <!-- Tags de prioridade e categoria -->
                      <div style="margin-bottom: 15px;">
                        <span style="display: inline-block; background-color: ${priorityColor[bugReport.priority]}; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; margin-right: 8px;">
                          ${priorityLabels[bugReport.priority]}
                        </span>
                        <span style="display: inline-block; background-color: #6b7280; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                          ${categoryLabels[bugReport.category]}
                        </span>
                      </div>

                      <p style="color: #4b5563; font-size: 14px; line-height: 1.6; margin: 0;">
                        ${bugReport.description}
                      </p>

                      ${bugReport.location ? `
                      <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; font-size: 13px; margin: 0;">
                          <strong>📍 Local:</strong> ${bugReport.location}
                        </p>
                      </div>
                      ` : ''}
                    </div>

                    <!-- Informações do usuário -->
                    <div style="background-color: #f0f9ff; border-left: 4px solid #3b82f6; padding: 20px; border-radius: 0 8px 8px 0; margin-bottom: 25px;">
                      <h3 style="color: #1e40af; font-size: 16px; font-weight: 600; margin: 0 0 10px 0;">
                        👤 Reportado por:
                      </h3>
                      <p style="color: #1f2937; font-size: 14px; margin: 5px 0;">
                        <strong>Nome:</strong> ${bugReport.reportedBy.fullName}
                      </p>
                      <p style="color: #1f2937; font-size: 14px; margin: 5px 0;">
                        <strong>Email:</strong> ${bugReport.reportedBy.email}
                      </p>
                      <p style="color: #1f2937; font-size: 14px; margin: 5px 0;">
                        <strong>Empresa:</strong> ${bugReport.company.name}
                      </p>
                      <p style="color: #6b7280; font-size: 12px; margin: 10px 0 0 0;">
                        <strong>Data:</strong> ${new Date(bugReport.createdAt).toLocaleString('pt-BR')}
                      </p>
                    </div>

                    <!-- Call to action -->
                    <div style="text-align: center; margin: 25px 0;">
                      <a href="${process.env.FRONTEND_URL}/dashboard/admin/bug-reports"
                         style="display: inline-block; background-color: #3b82f6; color: white; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; font-size: 14px;">
                         Ver no Sistema
                      </a>
                    </div>

                    <p style="color: #6b7280; font-size: 13px; line-height: 1.6; margin: 20px 0 0 0; text-align: center;">
                      Este bug foi reportado através do sistema High Tide Systems.<br>
                      Acesse o painel administrativo para gerenciar este relatório.
                    </p>
                  </td>
                </tr>

                <!-- Rodapé -->
                <tr>
                  <td style="background-color: #f8fafc; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb;">
                    <p style="color: #9ca3af; font-size: 12px; margin: 0;">
                      © 2024 High Tide Systems. Este email foi enviado automaticamente.
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
      `;

      return this.sendEmail(adminEmail, subject, html, { useSystemSMTP: true });
    } catch (error) {
      console.error('Erro ao enviar email de notificação de bug:', error);
      return { success: false, error: error.message };
    }
  }
}



module.exports = new EmailService();