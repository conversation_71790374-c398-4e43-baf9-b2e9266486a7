// NOVA ARQUITETURA - MessageService Simplificado

/**
 * 📱 LÓGICA ESTILO WHATSAPP
 * 
 * 1. SENT (✓): Mensagem foi enviada para o servidor
 * 2. DELIVERED (✓✓): Usuário está online/app aberto
 * 3. READ (✓✓ azul): Usuário VIU a mensagem na tela
 */

class MessageService {
  
  /**
   * ✉️ Criar nova mensagem
   */
  async createMessage(data) {
    const message = await prisma.message.create({
      data: {
        conversationId: data.conversationId,
        senderId: data.senderId,
        content: data.content,
        contentType: data.contentType || 'TEXT'
      }
    });

    // Criar status SENT para todos os participantes (exceto remetente)
    await this.createMessageStatuses(message.id, data.conversationId, data.senderId);
    
    // Marcar como DELIVERED para usuários online
    await this.markAsDeliveredForOnlineUsers(message.id, data.conversationId, data.senderId);
    
    return message;
  }

  /**
   * 📊 Status SENT para todos os participantes
   */
  async createMessageStatuses(messageId, conversationId, senderId) {
    const participants = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        leftAt: null,
        // Excluir o remetente
        NOT: {
          OR: [
            { userId: senderId },
            { clientId: senderId }
          ]
        }
      }
    });

    const statusPromises = participants.map(participant =>
      prisma.messageStatus.create({
        data: {
          messageId,
          participantId: participant.id,
          status: 'SENT'
        }
      })
    );

    await Promise.all(statusPromises);
  }

  /**
   * 🟢 Marcar como DELIVERED para usuários online
   */
  async markAsDeliveredForOnlineUsers(messageId, conversationId, excludeUserId) {
    const socketService = require('../socket/socketService');
    const io = socketService.getIO();
    
    if (!io) return;

    // Buscar participantes da conversa
    const participants = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        leftAt: null,
        NOT: {
          OR: [
            { userId: excludeUserId },
            { clientId: excludeUserId }
          ]
        }
      }
    });

    for (const participant of participants) {
      const userId = participant.userId || participant.clientId;
      
      // Verificar se o usuário está online
      const isOnline = this.isUserOnline(io, userId);
      
      if (isOnline) {
        await prisma.messageStatus.updateMany({
          where: {
            messageId,
            participantId: participant.id,
            status: 'SENT'
          },
          data: {
            status: 'DELIVERED',
            timestamp: new Date()
          }
        });
      }
    }
  }

  /**
   * 👀 Marcar mensagem como LIDA (apenas quando usuário VÊ)
   * - Só chama quando a mensagem aparecer na viewport do usuário
   */
  async markMessageAsRead(userId, conversationId, messageId) {
    // Verificar se usuário é participante
    const participation = await this.getUserParticipation(userId, conversationId);
    if (!participation) {
      throw new Error('Usuário não é participante da conversa');
    }

    // Atualizar status para READ
    const updated = await prisma.messageStatus.updateMany({
      where: {
        messageId,
        participantId: participation.id,
        status: { in: ['SENT', 'DELIVERED'] }
      },
      data: {
        status: 'READ',
        timestamp: new Date()
      }
    });

    if (updated.count > 0) {
      // Emitir evento Socket.IO para o remetente
      await this.notifyMessageRead(conversationId, messageId, userId);
    }

    return { success: true, updated: updated.count > 0 };
  }

  /**
   * 📈 Contar mensagens não lidas (SIMPLIFICADO)
   */
  async getUnreadCount(userId) {
    const isClient = await prisma.client.findUnique({ where: { id: userId } });
    
    const participations = await prisma.conversationParticipant.findMany({
      where: {
        ...(isClient ? { clientId: userId } : { userId }),
        leftAt: null
      }
    });

    const unreadCounts = [];

    for (const participation of participations) {
      // MÉTODO ÚNICO: Apenas contar por status das mensagens
      const unreadCount = await prisma.messageStatus.count({
        where: {
          participantId: participation.id,
          status: { in: ['SENT', 'DELIVERED'] }
        }
      });

      if (unreadCount > 0) {
        unreadCounts.push({
          conversationId: participation.conversationId,
          unreadCount
        });
      }
    }

    return {
      conversations: unreadCounts,
      totalUnread: unreadCounts.reduce((total, item) => total + item.unreadCount, 0)
    };
  }

  /**
   * 🔍 Verificar se usuário está online
   */
  isUserOnline(io, userId) {
    const userSockets = Array.from(io.sockets.sockets.values())
      .filter(socket => socket.user && socket.user.id === userId);
    return userSockets.length > 0;
  }

  /**
   * 📤 Notificar remetente que mensagem foi lida
   */
  async notifyMessageRead(conversationId, messageId, readBy) {
    const socketService = require('../socket/socketService');
    const io = socketService.getIO();
    
    if (io) {
      io.to(`conversation:${conversationId}`).emit('message:read', {
        conversationId,
        messageId,
        readBy,
        timestamp: new Date()
      });
    }
  }

  // ... outros métodos auxiliares
}

module.exports = new MessageService();