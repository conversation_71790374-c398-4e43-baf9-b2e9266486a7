"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/chat/ChatList.js":
/*!*****************************************!*\
  !*** ./src/components/chat/ChatList.js ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock,MessageCircle,MoreVertical,Plus,Search,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock,MessageCircle,MoreVertical,Plus,Search,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatList = (param)=>{\n    let { searchQuery = '' } = param;\n    _s();\n    const { conversations, setActiveConversation, messages, loadConversations, isLoading } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Estado para forçar re-renderização\n    const [updateTrigger, setUpdateTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Carregar conversas quando o componente é montado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatList.useEffect\": ()=>{\n            // Carregar conversas inicialmente\n            console.log('ChatList montado, verificando se é necessário carregar conversas...');\n            if (user && user.id) {\n                console.log('Usuário logado:', user.id);\n                // Carregar conversas inicialmente\n                console.log('Carregando conversas iniciais');\n                loadConversations(false); // Usar cache se disponível na primeira carga\n            } else {\n                console.log('Usuário não logado, não carregando conversas');\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatList.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o usuário mudar\n    // Adicionar listener para eventos de atualização do WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatList.useEffect\": ()=>{\n            // Função para lidar com eventos de atualização\n            const handleWebSocketUpdate = {\n                \"ChatList.useEffect.handleWebSocketUpdate\": (event)=>{\n                    const { type, action, conversationId } = event.detail;\n                    console.log(\"ChatList recebeu evento de atualiza\\xe7\\xe3o: \".concat(type), event.detail);\n                    // Forçar re-renderização para qualquer tipo de evento\n                    setUpdateTrigger({\n                        \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                    }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                    // Se for uma atualização de conversas, verificar se precisamos recarregar\n                    if (type === 'conversations') {\n                        // Se for uma ação de exclusão, não precisamos recarregar as conversas\n                        // pois o estado já foi atualizado no ChatContext\n                        if (action === 'delete') {\n                            console.log(\"ChatList: Conversa \".concat(conversationId, \" foi apagada, atualizando interface\"));\n                            // Apenas forçar re-renderização\n                            setUpdateTrigger({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        } else if (conversations.length === 0) {\n                            // Só recarregar se não tivermos conversas\n                            console.log('ChatList: Não há conversas carregadas, recarregando após evento de atualização');\n                            loadConversations(false).then({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": ()=>{\n                                    console.log('ChatList: Conversas carregadas com sucesso');\n                                    // Forçar outra re-renderização após o carregamento\n                                    setUpdateTrigger({\n                                        \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                                    }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                                }\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        } else {\n                            console.log('ChatList: Já existem conversas carregadas, apenas atualizando interface');\n                            // Apenas forçar re-renderização\n                            setUpdateTrigger({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        }\n                    }\n                }\n            }[\"ChatList.useEffect.handleWebSocketUpdate\"];\n            // Adicionar listener\n            window.addEventListener('chat:websocket:update', handleWebSocketUpdate);\n            // Remover listener quando o componente for desmontado\n            return ({\n                \"ChatList.useEffect\": ()=>{\n                    window.removeEventListener('chat:websocket:update', handleWebSocketUpdate);\n                }\n            })[\"ChatList.useEffect\"];\n        }\n    }[\"ChatList.useEffect\"], [\n        loadConversations\n    ]); // Dependência: loadConversations\n    // Filtrar conversas com base na pesquisa\n    const filteredConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatList.useMemo[filteredConversations]\": ()=>{\n            // Log para debug\n            console.log(\"Recalculando filteredConversations. Trigger: \".concat(updateTrigger, \", Conversas: \").concat(conversations.length));\n            if (!searchQuery) return conversations;\n            return conversations.filter({\n                \"ChatList.useMemo[filteredConversations]\": (conversation)=>{\n                    var _conversation_title;\n                    // Para conversas individuais, pesquisar pelo nome do outro participante\n                    if (conversation.type === 'INDIVIDUAL') {\n                        var _conversation_participants, _otherParticipant_user;\n                        const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                            \"ChatList.useMemo[filteredConversations]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n                        }[\"ChatList.useMemo[filteredConversations]\"]);\n                        // Buscar pelo fullName do usuário ou cliente\n                        if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                            return otherParticipant.user.fullName.toLowerCase().includes(searchQuery.toLowerCase());\n                        }\n                        if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                            var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                            const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                            let clientName;\n                            if (clientPersonName && clientPersonName.trim() !== '') {\n                                clientName = clientPersonName;\n                            } else {\n                                clientName = otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n                            }\n                            return clientName.toLowerCase().includes(searchQuery.toLowerCase());\n                        }\n                        return false;\n                    }\n                    // Para grupos, pesquisar pelo título\n                    return (_conversation_title = conversation.title) === null || _conversation_title === void 0 ? void 0 : _conversation_title.toLowerCase().includes(searchQuery.toLowerCase());\n                }\n            }[\"ChatList.useMemo[filteredConversations]\"]);\n        }\n    }[\"ChatList.useMemo[filteredConversations]\"], [\n        conversations,\n        searchQuery,\n        user === null || user === void 0 ? void 0 : user.id,\n        updateTrigger\n    ]); // Adicionado updateTrigger como dependência\n    // Obter o nome da conversa - memoizado para evitar recalculos\n    const getConversationName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getConversationName]\": (conversation)=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return 'Conversa';\n            if (conversation.type === 'GROUP') {\n                return conversation.title || 'Grupo';\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatList.useCallback[getConversationName]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatList.useCallback[getConversationName]\"]);\n            // Retornar nome do usuário ou cliente\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                return otherParticipant.user.fullName;\n            }\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                if (clientPersonName && clientPersonName.trim() !== '') {\n                    return clientPersonName;\n                }\n                return otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n            }\n            return 'Usuário';\n        }\n    }[\"ChatList.useCallback[getConversationName]\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter a imagem da conversa - memoizado para evitar recalculos\n    const getConversationImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getConversationImage]\": (conversation)=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return null;\n            if (conversation.type === 'GROUP') {\n                return null; // Usar um ícone de grupo\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatList.useCallback[getConversationImage]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatList.useCallback[getConversationImage]\"]);\n            return otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.profileImageUrl;\n        }\n    }[\"ChatList.useCallback[getConversationImage]\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Formatar a data da última mensagem - memoizado para evitar recalculos\n    const formatLastMessageTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[formatLastMessageTime]\": (timestamp)=>{\n            if (!timestamp) return '';\n            try {\n                return (0,_barrel_optimize_names_format_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(new Date(timestamp), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_5__.ptBR\n                });\n            } catch (error) {\n                console.error('Erro ao formatar data:', error);\n                return '';\n            }\n        }\n    }[\"ChatList.useCallback[formatLastMessageTime]\"], []);\n    // Obter iniciais para avatar - memoizado para evitar recalculos\n    const getInitials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getInitials]\": (name)=>{\n            if (!name) return 'U';\n            try {\n                const names = name.split(' ');\n                if (names.length === 1) return names[0].charAt(0);\n                return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n            } catch (error) {\n                console.error('Erro ao obter iniciais:', error);\n                return 'U';\n            }\n        }\n    }[\"ChatList.useCallback[getInitials]\"], []);\n    // Log para depuração - quando o componente é renderizado\n    console.log(\"ChatList renderizando. Trigger: \".concat(updateTrigger, \", Conversas: \").concat(conversations.length, \", Filtradas: \").concat(filteredConversations.length));\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.3,\n                ease: \"easeOut\"\n            }\n        },\n        exit: {\n            opacity: 0,\n            y: -20,\n            transition: {\n                duration: 0.2\n            }\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    if (filteredConversations.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full p-6 text-gray-500 dark:text-gray-400 relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-4 right-4 flex flex-col items-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 232,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 font-medium\",\n                                    children: \"Carregando...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 231,\n                            columnNumber: 23\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        scale: 0.8,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 48,\n                            className: \"mx-auto mb-3 text-blue-500 dark:text-blue-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2\",\n                            children: searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa iniciada'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: searchQuery ? 'Tente ajustar sua pesquisa' : 'Inicie uma conversa para começar a trocar mensagens'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        className: \"relative\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 flex flex-col items-end z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-blue-600 dark:text-blue-400 font-medium\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                        lineNumber: 271,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1 p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: filteredConversations.map((conversation, index)=>{\n                        const name = getConversationName(conversation);\n                        const image = getConversationImage(conversation);\n                        // Verificar se há mensagens carregadas para esta conversa\n                        const conversationMessages = messages[conversation.id] || [];\n                        // Usar a última mensagem da conversa ou a última mensagem carregada\n                        const lastMessage = conversation.lastMessage || (conversationMessages.length > 0 ? conversationMessages[conversationMessages.length - 1] : null);\n                        // Verificar se há mensagens não lidas (usar hasUnread do backend ou fallback para unreadCount)\n                        const hasUnread = conversation.hasUnread || conversation.unreadCount > 0;\n                        console.log(\"[ChatList] Conversa \".concat(conversation.id, \": hasUnread=\").concat(hasUnread, \", conversation.hasUnread=\").concat(conversation.hasUnread, \", conversation.unreadCount=\").concat(conversation.unreadCount));\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                            variants: itemVariants,\n                            initial: \"hidden\",\n                            animate: \"visible\",\n                            exit: \"exit\",\n                            onClick: ()=>setActiveConversation(conversation.id),\n                            className: \"w-full flex items-center gap-4 p-4 transition-all duration-300 text-left rounded-2xl group relative overflow-hidden \".concat(hasUnread ? 'bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/30 dark:to-blue-900/30 hover:from-cyan-100 hover:to-blue-100 dark:hover:from-cyan-800/40 dark:hover:to-blue-800/40 border-l-4 border-cyan-500 dark:border-cyan-400 shadow-md' : 'hover:bg-gradient-to-r hover:from-cyan-50 hover:to-cyan-100 dark:hover:from-cyan-900/20 dark:hover:to-cyan-800/20'),\n                            whileHover: {\n                                scale: 1.02,\n                                transition: {\n                                    duration: 0.2\n                                }\n                            },\n                            whileTap: {\n                                scale: 0.98,\n                                transition: {\n                                    duration: 0.1\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 transition-opacity duration-300 \".concat(hasUnread ? 'bg-gradient-to-r from-cyan-500/10 via-blue-500/5 to-cyan-700/10 opacity-50 group-hover:opacity-70' : 'bg-gradient-to-r from-cyan-500/5 via-transparent to-cyan-700/5 opacity-0 group-hover:opacity-100')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-shrink-0 z-10\",\n                                    children: image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image,\n                                                alt: name,\n                                                className: \"h-12 w-12 rounded-2xl object-cover transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200 shadow-lg' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 338,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 21\n                                    }, undefined) : conversation.type === 'GROUP' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white shadow-lg transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white font-semibold shadow-lg transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                children: getInitials(name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 364,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 355,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0 z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"truncate transition-colors duration-300 \".concat(hasUnread ? 'font-bold text-gray-900 dark:text-white group-hover:text-cyan-700 dark:group-hover:text-cyan-300' : 'font-semibold text-gray-900 dark:text-gray-100 group-hover:text-cyan-600 dark:group-hover:text-cyan-400'),\n                                                    children: name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-2\",\n                                                    children: formatLastMessageTime(lastMessage.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        lastMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm truncate transition-colors duration-300 \".concat(hasUnread ? 'font-medium text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'),\n                                            children: lastMessage.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 388,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 italic\",\n                                            children: \"Nenhuma mensagem\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 371,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    children: hasUnread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        exit: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 500,\n                                            damping: 30\n                                        },\n                                        className: \"flex-shrink-0 z-10 flex flex-col items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 w-4 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 shadow-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full bg-white animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 413,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-cyan-600 dark:text-cyan-400 uppercase tracking-wide\",\n                                                children: \"Nova\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 417,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 405,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, conversation.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 292,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatList, \"XFZkOmi5v6Y4DQyDJMfXLBkr4gg=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ChatList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatList);\nvar _c;\n$RefreshReg$(_c, \"ChatList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatList.js\n"));

/***/ })

});