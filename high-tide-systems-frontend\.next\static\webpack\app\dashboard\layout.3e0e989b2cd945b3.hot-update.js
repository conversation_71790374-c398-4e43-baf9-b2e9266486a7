"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/hooks/useUnreadMessages.js":
/*!****************************************!*\
  !*** ./src/hooks/useUnreadMessages.js ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnreadMessages: () => (/* binding */ useUnreadMessages)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\nvar _s = $RefreshSig$();\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\nconst useUnreadMessages = ()=>{\n    _s();\n    const [unreadConversationsCount, setUnreadConversationsCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [conversationsWithUnread, setConversationsWithUnread] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"useUnreadMessages.useCallback[getCurrentToken]\"], []);\n    const fetchUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[fetchUnreadCount]\": async ()=>{\n            if (!user) return;\n            setIsLoading(true);\n            try {\n                const token = getCurrentToken();\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/unread\"), {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token)\n                    }\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success) {\n                        // Agora contamos conversas não lidas, não mensagens\n                        setUnreadConversationsCount(data.data.totalUnreadConversations || data.data.totalUnread);\n                        // Criar Set de conversas com mensagens não lidas\n                        const conversationsSet = new Set();\n                        data.data.conversations.forEach({\n                            \"useUnreadMessages.useCallback[fetchUnreadCount]\": (conv)=>{\n                                if (conv.hasUnread || conv.unreadCount > 0) {\n                                    conversationsSet.add(conv.conversationId);\n                                }\n                            }\n                        }[\"useUnreadMessages.useCallback[fetchUnreadCount]\"]);\n                        setConversationsWithUnread(conversationsSet);\n                    }\n                }\n            } catch (error) {\n                console.error('Error fetching unread count:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useUnreadMessages.useCallback[fetchUnreadCount]\"], [\n        user,\n        getCurrentToken\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[markAsRead]\": async (conversationId, messageId)=>{\n            if (!user) return;\n            try {\n                const token = getCurrentToken();\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/mark-read\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                if (response.ok) {\n                    // Atualizar contagem local\n                    setConversationUnreadCounts({\n                        \"useUnreadMessages.useCallback[markAsRead]\": (prev)=>({\n                                ...prev,\n                                [conversationId]: 0\n                            })\n                    }[\"useUnreadMessages.useCallback[markAsRead]\"]);\n                    // Recalcular total\n                    setUnreadCount({\n                        \"useUnreadMessages.useCallback[markAsRead]\": (prev)=>{\n                            const currentConvCount = conversationUnreadCounts[conversationId] || 0;\n                            return Math.max(0, prev - currentConvCount);\n                        }\n                    }[\"useUnreadMessages.useCallback[markAsRead]\"]);\n                }\n            } catch (error) {\n                console.error('Error marking messages as read:', error);\n            }\n        }\n    }[\"useUnreadMessages.useCallback[markAsRead]\"], [\n        user,\n        getCurrentToken,\n        conversationUnreadCounts\n    ]);\n    const incrementUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[incrementUnreadCount]\": function(conversationId) {\n            let increment = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            setConversationUnreadCounts({\n                \"useUnreadMessages.useCallback[incrementUnreadCount]\": (prev)=>({\n                        ...prev,\n                        [conversationId]: (prev[conversationId] || 0) + increment\n                    })\n            }[\"useUnreadMessages.useCallback[incrementUnreadCount]\"]);\n            setUnreadCount({\n                \"useUnreadMessages.useCallback[incrementUnreadCount]\": (prev)=>prev + increment\n            }[\"useUnreadMessages.useCallback[incrementUnreadCount]\"]);\n        }\n    }[\"useUnreadMessages.useCallback[incrementUnreadCount]\"], []);\n    const decrementUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUnreadMessages.useCallback[decrementUnreadCount]\": function(conversationId) {\n            let decrement = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            setConversationUnreadCounts({\n                \"useUnreadMessages.useCallback[decrementUnreadCount]\": (prev)=>({\n                        ...prev,\n                        [conversationId]: Math.max(0, (prev[conversationId] || 0) - decrement)\n                    })\n            }[\"useUnreadMessages.useCallback[decrementUnreadCount]\"]);\n            setUnreadCount({\n                \"useUnreadMessages.useCallback[decrementUnreadCount]\": (prev)=>Math.max(0, prev - decrement)\n            }[\"useUnreadMessages.useCallback[decrementUnreadCount]\"]);\n        }\n    }[\"useUnreadMessages.useCallback[decrementUnreadCount]\"], []);\n    // Carregar contagem inicial\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUnreadMessages.useEffect\": ()=>{\n            if (user) {\n                fetchUnreadCount();\n            } else {\n                setUnreadCount(0);\n                setConversationUnreadCounts({});\n            }\n        }\n    }[\"useUnreadMessages.useEffect\"], [\n        user,\n        fetchUnreadCount\n    ]);\n    return {\n        unreadCount,\n        conversationUnreadCounts,\n        isLoading,\n        fetchUnreadCount,\n        markAsRead,\n        incrementUnreadCount,\n        decrementUnreadCount\n    };\n};\n_s(useUnreadMessages, \"7/dOBc0GcuG8b6ZfPPGTf4XElG4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useUnreadMessages.js\n"));

/***/ })

});