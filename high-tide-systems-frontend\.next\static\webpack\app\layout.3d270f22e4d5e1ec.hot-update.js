"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/ClientLayout.js":
/*!*********************************!*\
  !*** ./src/app/ClientLayout.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(app-pages-browser)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_PermissionContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/PermissionContext */ \"(app-pages-browser)/./src/contexts/PermissionContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.js\");\n/* harmony import */ var _components_ui_ToastContainer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ToastContainer */ \"(app-pages-browser)/./src/components/ui/ToastContainer.js\");\n/* harmony import */ var _contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/TutorialContext */ \"(app-pages-browser)/./src/contexts/TutorialContext.js\");\n/* harmony import */ var _contexts_ConstructionContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/ConstructionContext */ \"(app-pages-browser)/./src/contexts/ConstructionContext.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _contexts_UpdateNotesContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/UpdateNotesContext */ \"(app-pages-browser)/./src/contexts/UpdateNotesContext.js\");\n/* harmony import */ var _contexts_CompanySelectionContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/CompanySelectionContext */ \"(app-pages-browser)/./src/contexts/CompanySelectionContext.js\");\n/* harmony import */ var _components_construction_ConstructionMessage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/construction/ConstructionMessage */ \"(app-pages-browser)/./src/components/construction/ConstructionMessage.js\");\n/* harmony import */ var _components_ui_ModuleScrollbar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/ModuleScrollbar */ \"(app-pages-browser)/./src/components/ui/ModuleScrollbar.js\");\n/* harmony import */ var _styles_scrollbar_inline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/styles/scrollbar-inline */ \"(app-pages-browser)/./src/styles/scrollbar-inline.js\");\n/* harmony import */ var _components_navigation_QuickNav__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/navigation/QuickNav */ \"(app-pages-browser)/./src/components/navigation/QuickNav.js\");\n/* harmony import */ var _components_chat__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/chat */ \"(app-pages-browser)/./src/components/chat/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RootLayout(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"bg-background text-foreground transition-colors custom-scrollbar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_PermissionContext__WEBPACK_IMPORTED_MODULE_3__.PermissionProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_7__.TutorialProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ConstructionContext__WEBPACK_IMPORTED_MODULE_8__.ConstructionProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CompanySelectionContext__WEBPACK_IMPORTED_MODULE_12__.CompanySelectionProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_9__.QuickNavProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_10__.ChatProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_UpdateNotesContext__WEBPACK_IMPORTED_MODULE_11__.UpdateNotesProvider, {\n                                                    children: [\n                                                        children,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ToastContainer__WEBPACK_IMPORTED_MODULE_6__.ToastContainer, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                            lineNumber: 36,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction_ConstructionMessage__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                            lineNumber: 37,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleScrollbar__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                            lineNumber: 38,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styles_scrollbar_inline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                            lineNumber: 39,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_QuickNav__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                            lineNumber: 40,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat__WEBPACK_IMPORTED_MODULE_17__.ChatPanel, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                            lineNumber: 41,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat__WEBPACK_IMPORTED_MODULE_17__.ChatModal, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                            lineNumber: 42,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                                lineNumber: 33,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                            lineNumber: 32,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                        lineNumber: 31,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                    lineNumber: 30,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                                lineNumber: 29,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                        lineNumber: 27,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\app\\\\ClientLayout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_c = RootLayout;\nvar _c;\n$RefreshReg$(_c, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ClientLayout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8286f73dae95\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFByb2dyYW1hY2FvXFxISUdIIFRJREUgU1lTVEVNU1xcaGlnaC10aWRlLXN5c3RlbXMtZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjgyODZmNzNkYWU5NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chat/ChatConversation.js":
/*!*************************************************!*\
  !*** ./src/components/chat/ChatConversation.js ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatInput */ \"(app-pages-browser)/./src/components/chat/ChatInput.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MultiUserSearch */ \"(app-pages-browser)/./src/components/chat/MultiUserSearch.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SharedItemMessage */ \"(app-pages-browser)/./src/components/chat/SharedItemMessage.js\");\n/* harmony import */ var _AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AttachmentViewer */ \"(app-pages-browser)/./src/components/chat/AttachmentViewer.js\");\n/* harmony import */ var _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useSharedItemNavigation */ \"(app-pages-browser)/./src/hooks/useSharedItemNavigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ChatConversation = (param)=>{\n    let { conversationId, onBack, compact = false } = param;\n    var _conversation_participants;\n    _s();\n    const { messages, loadMessages, conversations, setConversations, activeConversation, setActiveConversation, removeParticipantFromGroup, addParticipantToGroup, addMultipleParticipantsToGroup } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { handleSharedItemClick } = (0,_hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation)();\n    // Wrapper para logar o clique no item compartilhado\n    const handleSharedItemClickWithLog = (contentType, itemData)=>{\n        console.log('[CHAT] Clique em item compartilhado:', {\n            contentType,\n            itemData\n        });\n        handleSharedItemClick(contentType, itemData);\n    };\n    // Estados para gerenciar o menu de opções e adição de participantes\n    const [showOptionsMenu, setShowOptionsMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddParticipant, setShowAddParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticipantsList, setShowParticipantsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingParticipants, setAddingParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para diálogos de confirmação\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmAction, setConfirmAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        title: '',\n        message: '',\n        onConfirm: {\n            \"ChatConversation.useState\": ()=>{}\n        }[\"ChatConversation.useState\"]\n    });\n    // Memoizar a conversa atual para evitar recalculos\n    const conversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversation]\": ()=>{\n            return conversations.find({\n                \"ChatConversation.useMemo[conversation]\": (c)=>c.id === conversationId\n            }[\"ChatConversation.useMemo[conversation]\"]);\n        }\n    }[\"ChatConversation.useMemo[conversation]\"], [\n        conversations,\n        conversationId\n    ]);\n    // Memoizar as mensagens da conversa para evitar recalculos\n    const conversationMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversationMessages]\": ()=>{\n            return messages[conversationId] || [];\n        }\n    }[\"ChatConversation.useMemo[conversationMessages]\"], [\n        messages,\n        conversationId\n    ]);\n    // Referência para controlar se as mensagens já foram carregadas\n    const messagesLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Carregar mensagens ao montar o componente - apenas uma vez\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            if (conversationId && !messagesLoadedRef.current) {\n                // Sempre carregar mensagens completas quando abrir uma conversa\n                // O loadConversations só traz a última mensagem, precisamos de todas\n                console.log('Carregando mensagens completas para conversa:', conversationId);\n                loadMessages(conversationId);\n                messagesLoadedRef.current = true;\n            }\n            // Limpar a referência quando o componente for desmontado\n            return ({\n                \"ChatConversation.useEffect\": ()=>{\n                    messagesLoadedRef.current = false;\n                }\n            })[\"ChatConversation.useEffect\"];\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationId,\n        loadMessages\n    ]);\n    // Rolar para a última mensagem\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationMessages\n    ]);\n    // Verificar se o usuário atual é administrador do grupo\n    const isGroupAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupAdmin]\": ()=>{\n            var _conversation_participants;\n            if (!conversation || !user) return false;\n            const userParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useMemo[isGroupAdmin]\": (p)=>p.userId === user.id\n            }[\"ChatConversation.useMemo[isGroupAdmin]\"]);\n            return (userParticipant === null || userParticipant === void 0 ? void 0 : userParticipant.isAdmin) || false;\n        }\n    }[\"ChatConversation.useMemo[isGroupAdmin]\"], [\n        conversation,\n        user\n    ]);\n    // Verificar se é uma conversa de grupo\n    const isGroupChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupChat]\": ()=>{\n            return (conversation === null || conversation === void 0 ? void 0 : conversation.type) === 'GROUP';\n        }\n    }[\"ChatConversation.useMemo[isGroupChat]\"], [\n        conversation\n    ]);\n    // Função para confirmar saída do grupo\n    const handleLeaveGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'leave-group',\n                title: 'Sair do grupo',\n                message: 'Tem certeza que deseja sair do grupo \"'.concat(conversation.title || 'Grupo', '\"?'),\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                console.log('Saiu do grupo com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após sair do grupo');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleLeaveGroup]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]).catch({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": (error)=>{\n                                console.error('Erro ao sair do grupo:', error);\n                                alert('Não foi possível sair do grupo. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleLeaveGroup]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleLeaveGroup]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Função para adicionar múltiplos participantes\n    const handleAddParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleAddParticipants]\": (selectedUsers)=>{\n            if (!conversation || !selectedUsers || selectedUsers.length === 0) return;\n            setAddingParticipants(true);\n            addMultipleParticipantsToGroup(conversation.id, selectedUsers).then({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (result)=>{\n                    console.log('Resultado da adição de participantes:', result);\n                    if (result.success) {\n                        console.log(\"\".concat(result.successCount, \" participantes adicionados com sucesso\"));\n                        if (result.errorCount > 0) {\n                            console.warn(\"\".concat(result.errorCount, \" participantes n\\xe3o puderam ser adicionados\"));\n                        // Poderia mostrar um toast com essa informação\n                        }\n                    } else {\n                        console.error('Falha ao adicionar participantes');\n                        alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                    }\n                    setShowAddParticipant(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).catch({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (error)=>{\n                    console.error('Erro ao adicionar participantes:', error);\n                    alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).finally({\n                \"ChatConversation.useCallback[handleAddParticipants]\": ()=>{\n                    setAddingParticipants(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]);\n        }\n    }[\"ChatConversation.useCallback[handleAddParticipants]\"], [\n        conversation,\n        addMultipleParticipantsToGroup\n    ]);\n    // Função para apagar conversa (sair da conversa)\n    const handleDeleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'delete-conversation',\n                title: 'Apagar conversa',\n                message: 'Tem certeza que deseja apagar esta conversa? Esta ação não pode ser desfeita.',\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                        // Para apagar uma conversa, usamos a mesma função de sair do grupo\n                        // No backend, isso marca o participante como tendo saído da conversa\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                console.log('Conversa apagada com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após apagar conversa');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleDeleteConversation]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]).catch({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": (error)=>{\n                                console.error('Erro ao apagar conversa:', error);\n                                alert('Não foi possível apagar a conversa. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleDeleteConversation]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleDeleteConversation]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Obter o nome da conversa - memoizado para evitar recalculos\n    const getConversationName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationName]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return 'Conversa';\n            if (conversation.type === 'GROUP') {\n                return conversation.title || 'Grupo';\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationName]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationName]\"]);\n            // Priorizar fullName para usuários e clientes\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                return otherParticipant.user.fullName;\n            }\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                // Usar o nome do paciente titular se disponível\n                const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                if (clientPersonName && clientPersonName.trim() !== '') {\n                    return clientPersonName;\n                }\n                // Fallback para fullName do cliente ou login\n                return otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n            }\n            return 'Usuário';\n        }\n    }[\"ChatConversation.useCallback[getConversationName]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter a imagem da conversa - memoizado para evitar recalculos\n    const getConversationImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationImage]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return null;\n            if (conversation.type === 'GROUP') {\n                return null;\n            }\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationImage]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationImage]\"]);\n            return otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.profileImageUrl;\n        }\n    }[\"ChatConversation.useCallback[getConversationImage]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter iniciais para avatar - memoizado para evitar recalculos\n    const getInitials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getInitials]\": (name)=>{\n            if (!name) return 'U';\n            try {\n                const names = name.split(' ');\n                if (names.length === 1) return names[0].charAt(0);\n                return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n            } catch (error) {\n                console.error('Erro ao obter iniciais:', error);\n                return 'U';\n            }\n        }\n    }[\"ChatConversation.useCallback[getInitials]\"], []);\n    // Formatar data da mensagem - memoizado para evitar recalculos\n    const formatMessageDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[formatMessageDate]\": (timestamp)=>{\n            if (!timestamp) return '';\n            try {\n                return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(new Date(timestamp), 'HH:mm', {\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.ptBR\n                });\n            } catch (error) {\n                console.error('Erro ao formatar data:', error);\n                return '';\n            }\n        }\n    }[\"ChatConversation.useCallback[formatMessageDate]\"], []);\n    // Agrupar mensagens por data - memoizado para evitar recalculos\n    const groupMessagesByDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[groupMessagesByDate]\": (messages)=>{\n            if (!messages || messages.length === 0) {\n                console.log(\"Nenhuma mensagem para agrupar\");\n                return [];\n            }\n            console.log(\"Agrupando \".concat(messages.length, \" mensagens\"));\n            try {\n                const groups = {};\n                // Ordenar mensagens por data (mais antigas primeiro)\n                const sortedMessages = [\n                    ...messages\n                ].sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                }[\"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\"]);\n                sortedMessages.forEach({\n                    \"ChatConversation.useCallback[groupMessagesByDate]\": (message)=>{\n                        if (!message || !message.createdAt) {\n                            console.warn(\"Mensagem inválida encontrada:\", message);\n                            return;\n                        }\n                        try {\n                            const messageDate = new Date(message.createdAt);\n                            const date = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(messageDate, 'dd/MM/yyyy');\n                            if (!groups[date]) {\n                                groups[date] = [];\n                            }\n                            groups[date].push(message);\n                        } catch (err) {\n                            console.error(\"Erro ao processar mensagem:\", err, message);\n                        }\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate]\"]);\n                // Verificar se temos grupos\n                if (Object.keys(groups).length === 0) {\n                    console.warn(\"Nenhum grupo criado após processamento\");\n                    return [];\n                }\n                // Ordenar os grupos por data (mais antigos primeiro)\n                const result = Object.entries(groups).sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param, param1)=>{\n                        let [dateA] = param, [dateB] = param1;\n                        // Converter strings de data para objetos Date para comparação\n                        const [dayA, monthA, yearA] = dateA.split('/').map(Number);\n                        const [dayB, monthB, yearB] = dateB.split('/').map(Number);\n                        return new Date(yearA, monthA - 1, dayA) - new Date(yearB, monthB - 1, dayB);\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]).map({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param)=>{\n                        let [date, messages] = param;\n                        return {\n                            date,\n                            messages\n                        };\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]);\n                console.log(\"Criados \".concat(result.length, \" grupos de mensagens\"));\n                return result;\n            } catch (error) {\n                console.error('Erro ao agrupar mensagens:', error);\n                return [];\n            }\n        }\n    }[\"ChatConversation.useCallback[groupMessagesByDate]\"], []);\n    // Memoizar os grupos de mensagens para evitar recalculos\n    const messageGroups = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[messageGroups]\": ()=>{\n            return groupMessagesByDate(conversationMessages);\n        }\n    }[\"ChatConversation.useMemo[messageGroups]\"], [\n        groupMessagesByDate,\n        conversationMessages\n    ]);\n    // Se a conversa não for encontrada, mostrar mensagem de erro\n    if (!conversation) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 text-white rounded-t-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                            \"aria-label\": \"Voltar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: compact ? 16 : 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-white text-lg\",\n                            children: \"Conversa n\\xe3o encontrada\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4 text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"A conversa n\\xe3o foi encontrada ou foi removida.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n            lineNumber: 363,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                        \"aria-label\": \"Voltar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: compact ? 16 : 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-1\",\n                        children: [\n                            getConversationImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: getConversationImage(),\n                                alt: getConversationName(),\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full object-cover\"),\n                                onError: (e)=>{\n                                    e.target.onerror = null;\n                                    e.target.style.display = 'none';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full bg-white/20 flex items-center justify-center text-white font-medium shadow-sm\"),\n                                children: getInitials(getConversationName())\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white \".concat(compact ? 'text-sm' : 'text-base'),\n                                        children: getConversationName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.isOnline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-white/80\",\n                                        children: \"Online\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowOptionsMenu(!showOptionsMenu),\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Mais op\\xe7\\xf5es\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: compact ? 16 : 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined),\n                            showOptionsMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 w-48 z-10\",\n                                children: isGroupChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowParticipantsList(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ver participantes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 436,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowAddParticipant(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Adicionar participante\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 448,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLeaveGroup,\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Sair do grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setConfirmAction({\n                                                    type: 'delete-group',\n                                                    title: 'Deletar grupo',\n                                                    message: 'Tem certeza que deseja deletar o grupo \"'.concat(conversation.title || 'Grupo', '\"? Todos os participantes ser\\xe3o removidos e esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.'),\n                                                    onConfirm: async ()=>{\n                                                        console.log('Deletando grupo:', conversation.id);\n                                                        try {\n                                                            const currentToken = localStorage.getItem('token');\n                                                            const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n                                                            console.log('Fazendo requisição DELETE para:', \"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id));\n                                                            // Remover todos os participantes para deletar o grupo\n                                                            const participants = conversation.participants || [];\n                                                            for (const participant of participants){\n                                                                const participantId = participant.userId || participant.clientId;\n                                                                if (participantId) {\n                                                                    await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id, \"/participants/\").concat(participantId), {\n                                                                        method: 'DELETE',\n                                                                        headers: {\n                                                                            'Authorization': \"Bearer \".concat(currentToken)\n                                                                        }\n                                                                    });\n                                                                }\n                                                            }\n                                                            const response = {\n                                                                ok: true\n                                                            }; // Simular sucesso\n                                                            console.log('Resposta:', response.status, response.statusText);\n                                                            if (response.ok) {\n                                                                console.log('Grupo deletado com sucesso');\n                                                                // Usar removeParticipantFromGroup para remover o usuário atual\n                                                                await removeParticipantFromGroup(conversation.id, user.id);\n                                                                onBack();\n                                                            } else {\n                                                                const errorText = await response.text();\n                                                                console.error('Erro na resposta:', errorText);\n                                                                alert('Erro ao deletar grupo: ' + response.status);\n                                                            }\n                                                        } catch (error) {\n                                                            console.error('Erro ao deletar grupo:', error);\n                                                            alert('Erro ao deletar grupo: ' + error.message);\n                                                        }\n                                                    }\n                                                });\n                                                setConfirmDialogOpen(true);\n                                                setShowOptionsMenu(false);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-t border-gray-200 dark:border-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Deletar grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 469,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : // Opções para conversas individuais\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDeleteConversation,\n                                    className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 530,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Apagar conversa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 526,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, undefined),\n            showAddParticipant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col h-full overflow-hidden\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddUsers: handleAddParticipants,\n                        onClose: ()=>setShowAddParticipant(false),\n                        title: \"Adicionar participantes ao grupo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 542,\n                        columnNumber: 11\n                    }, undefined),\n                    addingParticipants && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50 flex items-center justify-center z-30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-10 w-10 border-b-2 border-cyan-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 551,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 dark:text-gray-300\",\n                                    children: \"Adicionando participantes...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 552,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 550,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 549,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 541,\n                columnNumber: 9\n            }, undefined),\n            showParticipantsList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowParticipantsList(false),\n                                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                        \"aria-label\": \"Voltar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white text-base\",\n                                        children: \"Participantes do grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, undefined),\n                            isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setShowParticipantsList(false);\n                                    setShowAddParticipant(true);\n                                },\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Adicionar participante\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 583,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 575,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 562,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200 dark:divide-gray-700\",\n                            children: conversation === null || conversation === void 0 ? void 0 : (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.map((participant)=>{\n                                var _participant_user;\n                                // Determinar se é usuário ou cliente e obter nome correto\n                                const getParticipantName = ()=>{\n                                    var _participant_user;\n                                    if (participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        return 'Você';\n                                    }\n                                    // Se é usuário\n                                    if ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.fullName) {\n                                        return participant.user.fullName;\n                                    }\n                                    // Se é cliente\n                                    if (participant.client) {\n                                        var _participant_client_clientPersons__person, _participant_client_clientPersons_, _participant_client_clientPersons;\n                                        const clientPersonName = (_participant_client_clientPersons = participant.client.clientPersons) === null || _participant_client_clientPersons === void 0 ? void 0 : (_participant_client_clientPersons_ = _participant_client_clientPersons[0]) === null || _participant_client_clientPersons_ === void 0 ? void 0 : (_participant_client_clientPersons__person = _participant_client_clientPersons_.person) === null || _participant_client_clientPersons__person === void 0 ? void 0 : _participant_client_clientPersons__person.fullName;\n                                        if (clientPersonName && clientPersonName.trim() !== '') {\n                                            return clientPersonName;\n                                        }\n                                        return participant.client.fullName && participant.client.fullName.trim() !== '' ? participant.client.fullName : participant.client.login;\n                                    }\n                                    return 'Usuário';\n                                };\n                                const participantName = getParticipantName();\n                                const isCurrentUser = participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id);\n                                const participantId = participant.userId || participant.clientId;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex-shrink-0\",\n                                            children: ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.profileImageUrl) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: participant.user.profileImageUrl,\n                                                alt: participantName,\n                                                className: \"h-10 w-10 rounded-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                    e.target.parentNode.innerHTML = '<div class=\"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\">'.concat(getInitials(participantName), \"</div>\");\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 625,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\",\n                                                children: getInitials(participantName)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 636,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 623,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                    children: participantName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        participant.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-cyan-600 dark:text-cyan-400 bg-cyan-50 dark:bg-cyan-900/30 px-2 py-0.5 rounded-full\",\n                                                            children: \"Administrador\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        participant.client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"Cliente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 643,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        isGroupAdmin && !isCurrentUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                if (window.confirm(\"Tem certeza que deseja remover \".concat(participantName, \" do grupo?\"))) {\n                                                    removeParticipantFromGroup(conversation.id, participantId).catch((error)=>{\n                                                        console.error('Erro ao remover participante:', error);\n                                                        alert('Não foi possível remover o participante. Tente novamente mais tarde.');\n                                                    });\n                                                }\n                                            },\n                                            className: \"p-1.5 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n                                            \"aria-label\": \"Remover participante\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 676,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 663,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, participant.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 621,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 588,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 561,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-5 space-y-5\",\n                children: [\n                    messageGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-1 bg-cyan-100 dark:bg-cyan-900/30 rounded-full text-xs text-cyan-700 dark:text-cyan-300 shadow-sm\",\n                                        children: group.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, undefined),\n                                group.messages.map((message)=>{\n                                    var _message_metadata;\n                                    const isOwnMessage = message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id);\n                                    const isGroupChat = conversation.type === 'GROUP';\n                                    // Encontrar o nome do remetente para mensagens de grupo\n                                    const getSenderName = ()=>{\n                                        var _message_sender;\n                                        if (message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                            return 'Você';\n                                        }\n                                        // Se a mensagem tem sender (usuário)\n                                        if ((_message_sender = message.sender) === null || _message_sender === void 0 ? void 0 : _message_sender.fullName) {\n                                            return message.sender.fullName;\n                                        }\n                                        // Se a mensagem tem senderClient (cliente)\n                                        if (message.senderClient) {\n                                            var _message_senderClient_clientPersons__person, _message_senderClient_clientPersons_, _message_senderClient_clientPersons;\n                                            // Usar o nome do paciente titular se disponível\n                                            const clientPersonName = (_message_senderClient_clientPersons = message.senderClient.clientPersons) === null || _message_senderClient_clientPersons === void 0 ? void 0 : (_message_senderClient_clientPersons_ = _message_senderClient_clientPersons[0]) === null || _message_senderClient_clientPersons_ === void 0 ? void 0 : (_message_senderClient_clientPersons__person = _message_senderClient_clientPersons_.person) === null || _message_senderClient_clientPersons__person === void 0 ? void 0 : _message_senderClient_clientPersons__person.fullName;\n                                            if (clientPersonName && clientPersonName.trim() !== '') {\n                                                return clientPersonName;\n                                            }\n                                            // Fallback para fullName do cliente ou login\n                                            return message.senderClient.fullName && message.senderClient.fullName.trim() !== '' ? message.senderClient.fullName : message.senderClient.login;\n                                        }\n                                        return 'Usuário';\n                                    };\n                                    // Obter as iniciais do remetente para mensagens de grupo\n                                    const getSenderInitials = ()=>{\n                                        if (isOwnMessage) return 'Você';\n                                        const senderName = getSenderName();\n                                        return getInitials(senderName);\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(isOwnMessage ? 'justify-end' : 'justify-start'),\n                                        children: [\n                                            isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-2 flex flex-col items-center justify-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-xs font-medium\",\n                                                    children: getSenderInitials()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 743,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[75%] \".concat(compact ? 'max-w-[85%]' : ''),\n                                                children: [\n                                                    isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 ml-1\",\n                                                        children: getSenderName()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-3 rounded-lg shadow-sm \".concat(isOwnMessage ? 'bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white rounded-br-none' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none border border-cyan-100 dark:border-cyan-800'),\n                                                        children: message.contentType && [\n                                                            'SHARED_APPOINTMENT',\n                                                            'SHARED_PERSON',\n                                                            'SHARED_CLIENT',\n                                                            'SHARED_USER',\n                                                            'SHARED_SERVICE_TYPE',\n                                                            'SHARED_LOCATION',\n                                                            'SHARED_WORKING_HOURS',\n                                                            'SHARED_INSURANCE',\n                                                            'SHARED_INSURANCE_LIMIT'\n                                                        ].includes(message.contentType) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            message: message,\n                                                            onItemClick: handleSharedItemClickWithLog\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.contentType === 'ATTACHMENT' && ((_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.attachments) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                message.metadata.attachments.map((attachment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        attachment: attachment,\n                                                                        compact: true\n                                                                    }, attachment.id || index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                        lineNumber: 771,\n                                                                        columnNumber: 29\n                                                                    }, undefined)),\n                                                                message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1.5 \".concat(isOwnMessage ? 'text-right' : '', \" text-gray-500 dark:text-gray-400\"),\n                                                        children: formatMessageDate(message.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 750,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 737,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            ]\n                        }, group.date, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 690,\n                            columnNumber: 11\n                        }, undefined)),\n                    conversationMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm\",\n                            children: \"Nenhuma mensagem ainda. Comece a conversar!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 800,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 799,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 806,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 688,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                conversationId: conversationId,\n                compact: compact\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 810,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ConfirmationDialog, {\n                isOpen: confirmDialogOpen,\n                onClose: ()=>setConfirmDialogOpen(false),\n                onConfirm: confirmAction.onConfirm,\n                title: confirmAction.title,\n                message: confirmAction.message,\n                confirmText: \"Confirmar\",\n                cancelText: \"Cancelar\",\n                variant: \"warning\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 813,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n        lineNumber: 384,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatConversation, \"1Tt6iBJ/v/FsNTddsy62GFixbsk=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation\n    ];\n});\n_c = ChatConversation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatConversation);\nvar _c;\n$RefreshReg$(_c, \"ChatConversation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatConversation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ChatContext.js":
/*!*************************************!*\
  !*** ./src/contexts/ChatContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n// Cache para evitar requisições duplicadas\nconst requestCache = {\n    conversations: {\n        data: null,\n        timestamp: 0\n    },\n    unreadCount: {\n        data: null,\n        timestamp: 0\n    },\n    messages: {},\n    tokenCheck: {\n        timestamp: 0,\n        valid: false\n    } // Cache para verificação de token\n};\n// Tempo de expiração do cache em milissegundos\nconst CACHE_EXPIRATION = 30000; // 30 segundos para dados normais\nconst TOKEN_CHECK_EXPIRATION = 60000; // 1 minuto para verificação de token\n// Função de debounce para limitar a frequência de chamadas\nconst debounce = (func, wait)=>{\n    let timeout;\n    return function executedFunction() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n};\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const { user, logout } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversation, setActiveConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPanelOpen, setIsPanelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Obter o token atual do localStorage - memoizado para evitar chamadas desnecessárias\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"ChatProvider.useCallback[getCurrentToken]\"], []);\n    // Função para lidar com erros de autenticação\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[handleAuthError]\": ()=>{\n            console.error('Erro de autenticação detectado. Redirecionando para login...');\n            // Limpar dados locais\n            setConversations([]);\n            setMessages({});\n            setActiveConversation(null);\n            setUnreadCount(0);\n            setIsPanelOpen(false);\n            setIsModalOpen(false);\n            // Desconectar socket se existir\n            if (socket) {\n                try {\n                    socket.disconnect();\n                } catch (error) {\n                    console.error('Erro ao desconectar socket:', error);\n                }\n                setIsConnected(false);\n            }\n            // Limpar cache\n            requestCache.conversations.data = null;\n            requestCache.unreadCount.data = null;\n            requestCache.messages = {}; // Limpar cache de mensagens\n            // Redirecionar para login\n            if (logout) {\n                logout();\n            }\n        }\n    }[\"ChatProvider.useCallback[handleAuthError]\"], [\n        socket,\n        logout\n    ]);\n    // Usar referências para controlar estados que não devem causar re-renderizações\n    const socketInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isLoadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const lastInitAttemptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // Redefinir a referência quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário for null (logout), desconectar o socket\n            if (!user && socket) {\n                try {\n                    socket.disconnect();\n                    setIsConnected(false);\n                    setSocket(null);\n                } catch (error) {\n                    console.error('Erro ao desconectar socket após logout:', error);\n                }\n            }\n            // Resetar estado quando o usuário muda\n            socketInitializedRef.current = false;\n            lastInitAttemptRef.current = 0;\n            reconnectAttemptsRef.current = 0;\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        socket\n    ]);\n    // Limpar estado do chat quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário mudou (novo login), limpar todo o estado do chat\n            if (user) {\n                console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);\n                // Limpar conversas e mensagens para o novo usuário\n                setConversations([]);\n                setMessages({});\n                setActiveConversation(null);\n                setUnreadCount(0);\n                setIsPanelOpen(false);\n                setIsModalOpen(false);\n                // Limpar cache\n                requestCache.conversations.data = null;\n                requestCache.unreadCount.data = null;\n                requestCache.messages = {};\n                requestCache.tokenCheck.timestamp = 0;\n                requestCache.tokenCheck.valid = false;\n                console.log('Cache limpo para novo usuário');\n                // Limpar dados residuais do localStorage relacionados ao chat\n                if (true) {\n                    // Limpar qualquer cache específico do chat que possa estar no localStorage\n                    const keysToRemove = [];\n                    for(let i = 0; i < localStorage.length; i++){\n                        const key = localStorage.key(i);\n                        if (key && (key.startsWith('chat_') || key.startsWith('conversation_') || key.startsWith('message_'))) {\n                            keysToRemove.push(key);\n                        }\n                    }\n                    keysToRemove.forEach({\n                        \"ChatProvider.useEffect\": (key)=>localStorage.removeItem(key)\n                    }[\"ChatProvider.useEffect\"]);\n                    console.log('Dados residuais do chat removidos do localStorage');\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o ID do usuário mudar\n    // Inicializar conexão WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se não há usuário, não inicializar\n            if (!user) {\n                console.log('Usuário não logado, não inicializando WebSocket');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Inicializando chat para usuário:', user.role || 'USER');\n            // Se já existe um socket conectado, não fazer nada\n            if (socket && isConnected) {\n                console.log('WebSocket já está conectado, não é necessário reinicializar');\n                return;\n            }\n            // Se já está inicializado e a última tentativa foi recente, não tentar novamente\n            if (socketInitializedRef.current && Date.now() - lastInitAttemptRef.current < 60000) {\n                console.log('Socket já inicializado recentemente, aguardando...');\n                return;\n            }\n            // Marcar como inicializado imediatamente para evitar múltiplas tentativas\n            socketInitializedRef.current = true;\n            lastInitAttemptRef.current = Date.now();\n            // Função assíncrona para inicializar o WebSocket\n            const initializeWebSocket = {\n                \"ChatProvider.useEffect.initializeWebSocket\": async ()=>{\n                    // Verificar se o usuário está logado antes de inicializar o WebSocket\n                    if (!user) return;\n                    // Clientes agora podem usar o chat\n                    console.log('Inicializando WebSocket para:', user.role || 'USER');\n                    const currentToken = getCurrentToken();\n                    if (!currentToken) return;\n                    // Evitar reconexões desnecessárias - verificação adicional\n                    if (socket) {\n                        if (isConnected) {\n                            console.log('WebSocket já conectado, ignorando inicialização');\n                            return socket; // Retornar o socket existente\n                        } else {\n                            // Se o socket existe mas não está conectado, tentar reconectar em vez de criar um novo\n                            console.log('Socket existe mas não está conectado, tentando reconectar');\n                            try {\n                                // Tentar reconectar o socket existente em vez de criar um novo\n                                if (!socket.connected && socket.connect) {\n                                    socket.connect();\n                                    console.log('Tentativa de reconexão iniciada');\n                                    return socket;\n                                }\n                            } catch (error) {\n                                console.error('Erro ao reconectar socket existente:', error);\n                                // Só desconectar se a reconexão falhar\n                                try {\n                                    socket.disconnect();\n                                } catch (disconnectError) {\n                                    console.error('Erro ao desconectar socket após falha na reconexão:', disconnectError);\n                                }\n                            }\n                        }\n                    }\n                    // Marcar como inicializado para evitar múltiplas inicializações\n                    socketInitializedRef.current = true;\n                    // Limitar a frequência de reconexões\n                    const maxReconnectAttempts = 5;\n                    const reconnectDelay = 3000; // 3 segundos\n                    console.log('Inicializando WebSocket...');\n                    try {\n                        // Verificar se o token é válido antes de inicializar o WebSocket\n                        // Usar uma verificação mais simples para evitar múltiplas requisições\n                        // Assumir que o token é válido se o usuário está logado\n                        console.log('Token válido, inicializando WebSocket para o usuário:', user.fullName);\n                        console.log('Inicializando WebSocket com autenticação');\n                        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(API_URL, {\n                            path: '/socket.io',\n                            auth: {\n                                token: currentToken\n                            },\n                            transports: [\n                                'websocket',\n                                'polling'\n                            ],\n                            reconnectionAttempts: maxReconnectAttempts,\n                            reconnectionDelay: reconnectDelay,\n                            timeout: 10000,\n                            autoConnect: true,\n                            reconnection: true\n                        });\n                        socketInstance.on('connect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket connected');\n                                setIsConnected(true);\n                                reconnectAttemptsRef.current = 0;\n                                // Disparar evento personalizado para notificar componentes sobre a conexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'connected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('disconnect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket disconnected');\n                                setIsConnected(false);\n                                // Disparar evento personalizado para notificar componentes sobre a desconexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'disconnected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('connect_error', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (error)=>{\n                                console.error('WebSocket connection error:', error);\n                                reconnectAttemptsRef.current++;\n                                // Se o erro for de autenticação, não tentar reconectar\n                                if (error.message && error.message.includes('Authentication error')) {\n                                    console.error('Erro de autenticação no WebSocket, não tentando reconectar');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                    return;\n                                }\n                                if (reconnectAttemptsRef.current >= maxReconnectAttempts) {\n                                    console.error('Máximo de tentativas de reconexão atingido');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para receber novas mensagens\n                        socketInstance.on('message:new', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (message)=>{\n                                if (!message || !message.conversationId) {\n                                    console.error('Mensagem inválida recebida:', message);\n                                    return;\n                                }\n                                // Usar uma função anônima para evitar dependências circulares\n                                const userId = user === null || user === void 0 ? void 0 : user.id;\n                                // Verificar se a mensagem tem um tempId associado (para substituir mensagem temporária)\n                                // O backend pode não retornar o tempId, então precisamos verificar se é uma mensagem do usuário atual\n                                const tempId = message.tempId;\n                                const isCurrentUserMessage = message.senderId === userId;\n                                // Atualizar mensagens da conversa\n                                setMessages({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        const conversationMessages = prev[message.conversationId] || [];\n                                        // Verificar se a mensagem já existe para evitar duplicação\n                                        if (conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === message.id\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return prev;\n                                        }\n                                        // Se tiver um tempId, substituir a mensagem temporária pela mensagem real\n                                        if (tempId && conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return {\n                                                ...prev,\n                                                [message.conversationId]: conversationMessages.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId ? message : m\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                            };\n                                        }\n                                        // Se for uma mensagem do usuário atual, verificar se há mensagens temporárias recentes\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId) {\n                                            // Procurar por mensagens temporárias recentes (nos últimos 10 segundos)\n                                            const now = new Date();\n                                            const tempMessages = conversationMessages.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket.tempMessages\": (m)=>{\n                                                    if (!m.isTemp) return false;\n                                                    if (!(m.senderId === userId || m.senderClientId === userId)) return false;\n                                                    if (now - new Date(m.createdAt) >= 10000) return false; // 10 segundos\n                                                    // Para mensagens com anexos, verificar se ambas são ATTACHMENT\n                                                    if (message.contentType === 'ATTACHMENT' && m.contentType === 'ATTACHMENT') {\n                                                        return true;\n                                                    }\n                                                    // Para mensagens de texto, verificar se o conteúdo é igual\n                                                    if (message.contentType === 'TEXT' && m.contentType === 'TEXT' && m.content === message.content) {\n                                                        return true;\n                                                    }\n                                                    return false;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket.tempMessages\"]);\n                                            if (tempMessages.length > 0) {\n                                                // Substituir a primeira mensagem temporária encontrada\n                                                const tempMessage = tempMessages[0];\n                                                return {\n                                                    ...prev,\n                                                    [message.conversationId]: conversationMessages.map({\n                                                        \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempMessage.id ? message : m\n                                                    }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                };\n                                            }\n                                        }\n                                        // Caso contrário, adicionar a nova mensagem\n                                        // Manter a ordem cronológica (mais antigas primeiro)\n                                        const updatedMessages = [\n                                            ...conversationMessages,\n                                            message\n                                        ].sort({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedMessages\"]);\n                                        return {\n                                            ...prev,\n                                            [message.conversationId]: updatedMessages\n                                        };\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Atualizar lista de conversas (mover para o topo)\n                                setConversations({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        // Verificar se a conversa existe\n                                        const conversation = prev.find({\n                                            \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                        if (!conversation) return prev;\n                                        // Se a conversa já tiver uma mensagem temporária com o mesmo tempId, não mover para o topo\n                                        if (tempId && conversation.lastMessage && conversation.lastMessage.id === tempId) {\n                                            // Apenas atualizar a última mensagem sem reordenar\n                                            return prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                    if (c.id === message.conversationId) {\n                                                        return {\n                                                            ...c,\n                                                            lastMessage: message\n                                                        };\n                                                    }\n                                                    return c;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        }\n                                        // Se for uma mensagem do usuário atual e não tiver tempId, verificar se há uma mensagem temporária recente\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId && conversation.lastMessage && conversation.lastMessage.isTemp) {\n                                            // Verificar se a última mensagem é temporária e tem o mesmo conteúdo\n                                            if (conversation.lastMessage.content === message.content && (conversation.lastMessage.senderId === userId || conversation.lastMessage.senderClientId === userId)) {\n                                                // Apenas atualizar a última mensagem sem reordenar\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                        if (c.id === message.conversationId) {\n                                                            return {\n                                                                ...c,\n                                                                lastMessage: message\n                                                            };\n                                                        }\n                                                        return c;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }\n                                        // Caso contrário, mover para o topo\n                                        const updatedConversations = prev.filter({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedConversations\": (c)=>c.id !== message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedConversations\"]);\n                                        return [\n                                            {\n                                                ...conversation,\n                                                lastMessage: message\n                                            },\n                                            ...updatedConversations\n                                        ];\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Incrementar contador de não lidas se a mensagem não for do usuário atual\n                                const isFromCurrentUser = message.senderId === userId || message.senderClientId === userId;\n                                if (!isFromCurrentUser) {\n                                    console.log('[WebSocket] Nova mensagem de outro usuário, incrementando contador');\n                                    setUnreadCount({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            const newCount = prev + 1;\n                                            console.log('[WebSocket] Contador atualizado de', prev, 'para', newCount);\n                                            return newCount;\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Atualizar contador da conversa específica\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                    if (conv.id === message.conversationId) {\n                                                        const newUnreadCount = (conv.unreadCount || 0) + 1;\n                                                        console.log(\"[WebSocket] Conversa \".concat(conv.id, \" contador: \").concat(conv.unreadCount || 0, \" -> \").concat(newUnreadCount));\n                                                        return {\n                                                            ...conv,\n                                                            unreadCount: newUnreadCount\n                                                        };\n                                                    }\n                                                    return conv;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                } else {\n                                    console.log('[WebSocket] Mensagem do próprio usuário, não incrementando contador');\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a nova mensagem\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'message',\n                                        conversationId: message.conversationId,\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de contagem de mensagens não lidas\n                        socketInstance.on('unread:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('[WebSocket] Atualização de mensagens não lidas recebida:', data);\n                                if (data && typeof data.totalUnread === 'number') {\n                                    console.log(\"[WebSocket] Atualizando contador total para: \".concat(data.totalUnread));\n                                    setUnreadCount(data.totalUnread);\n                                    // Atualizar as conversas com as contagens de não lidas\n                                    if (data.conversations && Array.isArray(data.conversations)) {\n                                        console.log(\"[WebSocket] Atualizando \".concat(data.conversations.length, \" conversas com contadores\"));\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        // Procurar esta conversa nos dados de não lidas\n                                                        const unreadInfo = data.conversations.find({\n                                                            \"ChatProvider.useEffect.initializeWebSocket.unreadInfo\": (item)=>item.conversationId === conv.id\n                                                        }[\"ChatProvider.useEffect.initializeWebSocket.unreadInfo\"]);\n                                                        if (unreadInfo) {\n                                                            console.log(\"[WebSocket] Conversa \".concat(conv.id, \" agora tem \").concat(unreadInfo.unreadCount, \" mensagens n\\xe3o lidas\"));\n                                                            return {\n                                                                ...conv,\n                                                                unreadCount: unreadInfo.unreadCount\n                                                            };\n                                                        }\n                                                        // Resetar contador se não estiver na lista de não lidas\n                                                        return {\n                                                            ...conv,\n                                                            unreadCount: 0\n                                                        };\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a atualização de não lidas\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'unread',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de lista de conversas\n                        socketInstance.on('conversations:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Atualização de lista de conversas recebida via WebSocket:', data);\n                                // Verificar se os dados estão no formato esperado\n                                if (data) {\n                                    // Pode vir como array direto ou como objeto com propriedade conversations\n                                    const conversationsArray = Array.isArray(data) ? data : data.conversations || [];\n                                    if (Array.isArray(conversationsArray) && conversationsArray.length > 0) {\n                                        console.log(\"Atualizando \".concat(conversationsArray.length, \" conversas via WebSocket\"));\n                                        setConversations(conversationsArray);\n                                        // Atualizar a flag para indicar que os dados foram carregados\n                                        initialDataLoadedRef.current = true;\n                                        // Atualizar o cache\n                                        requestCache.conversations.data = conversationsArray;\n                                        requestCache.conversations.timestamp = Date.now();\n                                        // Disparar evento personalizado para notificar componentes sobre a atualização\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    } else {\n                                        console.error('Formato inválido ou array vazio de conversas recebido via WebSocket:', data);\n                                        // Se recebemos um array vazio, forçar carregamento das conversas\n                                        if (Array.isArray(conversationsArray) && conversationsArray.length === 0) {\n                                            console.log('Array vazio recebido, forçando carregamento de conversas...');\n                                            loadConversations(true);\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para notificar que uma nova conversa foi criada\n                        socketInstance.on('conversation:created', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Nova conversa criada recebida via WebSocket:', data);\n                                if (data && data.id) {\n                                    // Adicionar a nova conversa ao início da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            // Verificar se a conversa já existe\n                                            if (prev.some({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id === data.id\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                                return prev;\n                                            }\n                                            return [\n                                                data,\n                                                ...prev\n                                            ];\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando um participante é removido\n                        socketInstance.on('participant:removed', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Participante removido recebido via WebSocket:', data);\n                                if (data && data.conversationId && data.participantId) {\n                                    // Se o usuário atual foi removido, remover a conversa da lista\n                                    if (data.participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        // Se era a conversa ativa, limpar\n                                        if (activeConversation === data.conversationId) {\n                                            setActiveConversation(null);\n                                        }\n                                        // Limpar mensagens da conversa\n                                        setMessages({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const updated = {\n                                                    ...prev\n                                                };\n                                                delete updated[data.conversationId];\n                                                return updated;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    } else {\n                                        // Atualizar a lista de participantes da conversa\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        if (conv.id === data.conversationId) {\n                                                            return {\n                                                                ...conv,\n                                                                participants: conv.participants.filter({\n                                                                    \"ChatProvider.useEffect.initializeWebSocket\": (p)=>p.userId !== data.participantId && p.clientId !== data.participantId\n                                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                            };\n                                                        }\n                                                        return conv;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando o usuário sai de uma conversa\n                        socketInstance.on('conversation:left', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Usuário saiu da conversa via WebSocket:', data);\n                                if (data && data.conversationId && data.userId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                    // Remover a conversa da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Se era a conversa ativa, limpar\n                                    if (activeConversation === data.conversationId) {\n                                        setActiveConversation(null);\n                                    }\n                                    // Limpar mensagens da conversa\n                                    setMessages({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            const updated = {\n                                                ...prev\n                                            };\n                                            delete updated[data.conversationId];\n                                            return updated;\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Disparar evento para atualizar a interface\n                                    window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                        detail: {\n                                            type: 'conversations',\n                                            action: 'left',\n                                            conversationId: data.conversationId,\n                                            timestamp: Date.now()\n                                        }\n                                    }));\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        setSocket(socketInstance);\n                        return socketInstance;\n                    } catch (error) {\n                        console.error('Erro ao inicializar WebSocket:', error);\n                        setIsConnected(false);\n                        socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                        return null;\n                    }\n                }\n            }[\"ChatProvider.useEffect.initializeWebSocket\"];\n            // Usar uma variável para controlar se já estamos tentando inicializar\n            let initializationInProgress = false;\n            // Função para inicializar com segurança\n            const safeInitialize = {\n                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                    if (initializationInProgress) {\n                        console.log('Já existe uma inicialização em andamento, ignorando');\n                        return;\n                    }\n                    initializationInProgress = true;\n                    // Usar um timeout para garantir que não tentamos inicializar muito frequentemente\n                    setTimeout({\n                        \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                            initializeWebSocket().then({\n                                \"ChatProvider.useEffect.safeInitialize\": (result)=>{\n                                    console.log('Inicialização do WebSocket concluída:', result ? 'sucesso' : 'falha');\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).catch({\n                                \"ChatProvider.useEffect.safeInitialize\": (error)=>{\n                                    console.error('Erro ao inicializar WebSocket:', error);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).finally({\n                                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                                    initializationInProgress = false;\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]);\n                        }\n                    }[\"ChatProvider.useEffect.safeInitialize\"], 2000); // Esperar 2 segundos antes de inicializar\n                }\n            }[\"ChatProvider.useEffect.safeInitialize\"];\n            // Chamar a função de inicialização\n            safeInitialize();\n            // Cleanup function - só desconectar quando o componente for desmontado completamente\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                    // Verificar se estamos realmente desmontando o componente (usuário fez logout)\n                    if (!user) {\n                        console.log('Usuário fez logout, desconectando socket');\n                        if (socket) {\n                            try {\n                                socket.disconnect();\n                                socketInitializedRef.current = false; // Permitir nova tentativa após cleanup\n                            } catch (error) {\n                                console.error('Erro ao desconectar socket:', error);\n                            }\n                        }\n                    } else {\n                        console.log('Componente sendo remontado, mantendo socket conectado');\n                    }\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo socket e isConnected das dependências para evitar loops\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Carregar conversas do usuário com cache\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n            console.log('loadConversations chamado com forceRefresh =', forceRefresh);\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar conversas');\n                    return [];\n                }\n                // Clientes agora podem carregar conversas\n                console.log('Carregando conversas para:', user.role || 'USER');\n                // Verificar se já está carregando\n                if (isLoading || isLoadingRef.current) {\n                    console.log('Já está carregando conversas, retornando estado atual');\n                    return conversations;\n                }\n                // Verificar se já temos conversas carregadas e não é uma atualização forçada\n                if (!forceRefresh && conversations.length > 0) {\n                    console.log('Já temos conversas carregadas e não é uma atualização forçada, retornando estado atual');\n                    return conversations;\n                }\n                // Marcar como carregando para evitar chamadas simultâneas\n                isLoadingRef.current = true;\n                // Verificar cache apenas se não for refresh forçado\n                const now = Date.now();\n                if (!forceRefresh && requestCache.conversations.data && requestCache.conversations.data.length > 0 && now - requestCache.conversations.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para conversas');\n                    return requestCache.conversations.data;\n                }\n                console.log('Buscando conversas atualizadas da API');\n                const currentToken = getCurrentToken();\n                console.log('Token ao carregar conversas:', currentToken ? 'Disponível' : 'Não disponível');\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar conversas');\n                    return [];\n                }\n                setIsLoading(true);\n                try {\n                    console.log('Buscando conversas da API...');\n                    console.log('Fazendo requisição para:', \"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"));\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    console.log('Status da resposta:', response.status, response.statusText);\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar conversas');\n                            handleAuthError();\n                            return [];\n                        }\n                        throw new Error(\"Erro ao carregar conversas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de conversas:', data);\n                    if (data.success) {\n                        var _data_data;\n                        // Verificar se há dados válidos\n                        // A API retorna { success: true, data: { conversations: [...], total, limit, offset } }\n                        console.log('Estrutura completa da resposta da API:', JSON.stringify(data));\n                        const conversationsArray = ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.conversations) || [];\n                        console.log('Array de conversas extraído:', conversationsArray);\n                        if (!Array.isArray(conversationsArray)) {\n                            console.error('Resposta da API não contém um array de conversas:', data);\n                            return [];\n                        }\n                        console.log(\"Recebidas \".concat(conversationsArray.length, \" conversas da API\"));\n                        // Processar as últimas mensagens das conversas\n                        console.log('Processando últimas mensagens das conversas...');\n                        const processedConversations = conversationsArray.map({\n                            \"ChatProvider.useCallback[loadConversations].processedConversations\": (conversation)=>{\n                                // Verificar se a conversa tem mensagens\n                                if (conversation.messages && conversation.messages.length > 0) {\n                                    console.log(\"Conversa \".concat(conversation.id, \" tem \").concat(conversation.messages.length, \" mensagens\"));\n                                    // Extrair a última mensagem\n                                    const lastMessage = conversation.messages[0]; // A primeira mensagem é a mais recente (ordenada por createdId desc)\n                                    console.log('Ultima mensagem:', lastMessage);\n                                    // IMPORTANTE: Também salvar todas as mensagens desta conversa no estado\n                                    const sortedMessages = [\n                                        ...conversation.messages\n                                    ].sort({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\"]);\n                                    console.log(\"DEBUG: Salvando \".concat(sortedMessages.length, \" mensagens da conversa \").concat(conversation.id), sortedMessages);\n                                    // Atualizar estado das mensagens\n                                    setMessages({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations\": (prev)=>({\n                                                ...prev,\n                                                [conversation.id]: sortedMessages\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                                    // Remover o array de mensagens para evitar duplicação\n                                    const { messages, ...conversationWithoutMessages } = conversation;\n                                    // Adicionar a última mensagem como propriedade lastMessage\n                                    return {\n                                        ...conversationWithoutMessages,\n                                        lastMessage\n                                    };\n                                } else {\n                                    console.log(\"Conversa \".concat(conversation.id, \" n\\xe3o tem mensagens\"));\n                                }\n                                return conversation;\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                        // Log para debug dos dados das conversas\n                        console.log('Conversas processadas:', processedConversations.map({\n                            \"ChatProvider.useCallback[loadConversations]\": (c)=>{\n                                var _c_participants;\n                                return {\n                                    id: c.id,\n                                    participants: (_c_participants = c.participants) === null || _c_participants === void 0 ? void 0 : _c_participants.map({\n                                        \"ChatProvider.useCallback[loadConversations]\": (p)=>({\n                                                userId: p.userId,\n                                                clientId: p.clientId,\n                                                user: p.user,\n                                                client: p.client\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations]\"]));\n                        // Atualizar cache com as conversas processadas\n                        const now = Date.now();\n                        requestCache.conversations.data = processedConversations;\n                        requestCache.conversations.timestamp = now;\n                        // Atualizar estado - garantir que estamos atualizando o estado mesmo se o array estiver vazio\n                        setConversations(processedConversations);\n                        // Disparar evento para notificar componentes sobre a atualização\n                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                            detail: {\n                                type: 'conversations',\n                                timestamp: Date.now()\n                            }\n                        }));\n                        return conversationsArray;\n                    } else {\n                        console.error('Resposta da API não foi bem-sucedida:', data);\n                        return [];\n                    }\n                } catch (error) {\n                    console.error('Error loading conversations:', error);\n                    return [];\n                } finally{\n                    setIsLoading(false);\n                    isLoadingRef.current = false;\n                }\n            } catch (error) {\n                console.error('Error in loadConversations:', error);\n                return [];\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadConversations]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError,\n        isLoading\n    ]);\n    // Carregar mensagens de uma conversa com cache aprimorado\n    const loadMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadMessages]\": async (conversationId)=>{\n            try {\n                var _requestCache_messages_conversationId;\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar mensagens');\n                    return;\n                }\n                // Verificar cache de mensagens\n                const now = Date.now();\n                if (requestCache.messages[conversationId] && now - requestCache.messages[conversationId].timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para mensagens da conversa:', conversationId);\n                    return requestCache.messages[conversationId].data;\n                }\n                // Verificar se já tem mensagens completas carregadas no cache\n                // Não usar o estado messages aqui pois pode conter apenas a última mensagem do loadConversations\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar mensagens');\n                    return;\n                }\n                // Evitar múltiplas requisições simultâneas para a mesma conversa\n                if ((_requestCache_messages_conversationId = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId === void 0 ? void 0 : _requestCache_messages_conversationId.loading) {\n                    console.log('Já existe uma requisição em andamento para esta conversa');\n                    return;\n                }\n                // Marcar como carregando\n                requestCache.messages[conversationId] = {\n                    loading: true\n                };\n                try {\n                    console.log('Buscando mensagens da API para conversa:', conversationId);\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/messages\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar mensagens');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar mensagens: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    if (data.success) {\n                        // Debug para verificar mensagens carregadas\n                        console.log('DEBUG: Mensagens carregadas para conversa', conversationId, data.data);\n                        // Atualizar cache\n                        requestCache.messages[conversationId] = {\n                            data: data.data,\n                            timestamp: now,\n                            loading: false\n                        };\n                        // Atualizar estado das mensagens\n                        // Garantir que as mensagens estejam na ordem correta (mais antigas primeiro)\n                        // para que a ordenação no componente funcione corretamente\n                        const sortedMessages = [\n                            ...data.data\n                        ].sort({\n                            \"ChatProvider.useCallback[loadMessages].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                        }[\"ChatProvider.useCallback[loadMessages].sortedMessages\"]);\n                        console.log('DEBUG: Mensagens ordenadas', sortedMessages);\n                        setMessages({\n                            \"ChatProvider.useCallback[loadMessages]\": (prev)=>({\n                                    ...prev,\n                                    [conversationId]: sortedMessages\n                                })\n                        }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        // Atualizar a última mensagem da conversa\n                        if (data.data && data.data.length > 0) {\n                            const lastMessage = data.data[data.data.length - 1];\n                            setConversations({\n                                \"ChatProvider.useCallback[loadMessages]\": (prev)=>{\n                                    return prev.map({\n                                        \"ChatProvider.useCallback[loadMessages]\": (conv)=>{\n                                            if (conv.id === conversationId && (!conv.lastMessage || new Date(lastMessage.createdAt) > new Date(conv.lastMessage.createdAt))) {\n                                                return {\n                                                    ...conv,\n                                                    lastMessage\n                                                };\n                                            }\n                                            return conv;\n                                        }\n                                    }[\"ChatProvider.useCallback[loadMessages]\"]);\n                                }\n                            }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        }\n                        return data.data;\n                    }\n                } catch (error) {\n                    console.error('Error loading messages:', error);\n                } finally{\n                    var _requestCache_messages_conversationId1;\n                    // Remover flag de carregamento em caso de erro\n                    if ((_requestCache_messages_conversationId1 = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId1 === void 0 ? void 0 : _requestCache_messages_conversationId1.loading) {\n                        requestCache.messages[conversationId].loading = false;\n                    }\n                }\n            } catch (error) {\n                console.error('Error in loadMessages:', error);\n            }\n        // Removendo messages das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadMessages]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Enviar mensagem\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[sendMessage]\": function(conversationId, content) {\n            let contentType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'TEXT', metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            if (!socket || !isConnected) return;\n            // Gerar um ID temporário para a mensagem\n            const tempId = \"temp-\".concat(Date.now());\n            // Criar uma mensagem temporária para exibir imediatamente\n            const tempMessage = {\n                id: tempId,\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                senderId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : user === null || user === void 0 ? void 0 : user.id,\n                senderClientId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? user === null || user === void 0 ? void 0 : user.id : null,\n                createdAt: new Date().toISOString(),\n                sender: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: user === null || user === void 0 ? void 0 : user.fullName\n                },\n                senderClient: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.login),\n                    login: user === null || user === void 0 ? void 0 : user.login\n                } : null,\n                // Marcar como temporária para evitar duplicação\n                isTemp: true\n            };\n            // Atualizar mensagens localmente antes de enviar\n            setMessages({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>({\n                        ...prev,\n                        [conversationId]: [\n                            ...prev[conversationId] || [],\n                            tempMessage\n                        ]\n                    })\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Atualizar a conversa com a última mensagem\n            setConversations({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                    return prev.map({\n                        \"ChatProvider.useCallback[sendMessage]\": (conv)=>{\n                            if (conv.id === conversationId) {\n                                return {\n                                    ...conv,\n                                    lastMessage: tempMessage\n                                };\n                            }\n                            return conv;\n                        }\n                    }[\"ChatProvider.useCallback[sendMessage]\"]);\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Enviar a mensagem via WebSocket\n            const messageData = {\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                tempId\n            };\n            socket.emit('message:send', messageData, {\n                \"ChatProvider.useCallback[sendMessage]\": (response)=>{\n                    if (response.success) {\n                    // A mensagem real será adicionada pelo evento message:new do WebSocket\n                    // Não precisamos fazer nada aqui, pois o WebSocket vai atualizar a mensagem\n                    } else {\n                        console.error('Error sending message:', response.error);\n                        // Em caso de erro, podemos marcar a mensagem como falha\n                        setMessages({\n                            \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                                const conversationMessages = prev[conversationId] || [];\n                                return {\n                                    ...prev,\n                                    [conversationId]: conversationMessages.map({\n                                        \"ChatProvider.useCallback[sendMessage]\": (msg)=>msg.id === tempId ? {\n                                                ...msg,\n                                                failed: true\n                                            } : msg\n                                    }[\"ChatProvider.useCallback[sendMessage]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[sendMessage]\"]);\n                    }\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n        }\n    }[\"ChatProvider.useCallback[sendMessage]\"], [\n        socket,\n        isConnected,\n        user\n    ]);\n    // Criar nova conversa\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createConversation]\": async function(participantIds) {\n            let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n            try {\n                console.log('Criando conversa com participantes:', participantIds);\n                console.log('Tipo de conversa:', participantIds.length > 1 ? 'GRUPO' : 'INDIVIDUAL');\n                console.log('Título da conversa:', title);\n                // ✅ VALIDAÇÃO: System admin não pode criar grupos com usuários de empresas diferentes\n                if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && participantIds.length > 1) {\n                    console.log('Validando empresas para system admin...');\n                    // Buscar informações dos participantes para validar empresas\n                    const currentToken = getCurrentToken();\n                    if (currentToken) {\n                        try {\n                            const participantPromises = participantIds.map({\n                                \"ChatProvider.useCallback[createConversation].participantPromises\": async (id)=>{\n                                    const response = await fetch(\"\".concat(API_URL, \"/users/\").concat(id), {\n                                        headers: {\n                                            Authorization: \"Bearer \".concat(currentToken)\n                                        }\n                                    });\n                                    if (response.ok) {\n                                        const userData = await response.json();\n                                        return userData.success ? userData.data : userData;\n                                    }\n                                    return null;\n                                }\n                            }[\"ChatProvider.useCallback[createConversation].participantPromises\"]);\n                            const participants = await Promise.all(participantPromises);\n                            console.log('Participantes carregados:', participants);\n                            const validParticipants = participants.filter({\n                                \"ChatProvider.useCallback[createConversation].validParticipants\": (p)=>p && p.companyId\n                            }[\"ChatProvider.useCallback[createConversation].validParticipants\"]);\n                            console.log('Participantes válidos:', validParticipants);\n                            if (validParticipants.length > 1) {\n                                const companies = [\n                                    ...new Set(validParticipants.map({\n                                        \"ChatProvider.useCallback[createConversation]\": (p)=>p.companyId\n                                    }[\"ChatProvider.useCallback[createConversation]\"]))\n                                ];\n                                console.log('Empresas encontradas:', companies);\n                                if (companies.length > 1) {\n                                    alert('Não é possível criar grupos com usuários de empresas diferentes.');\n                                    return null;\n                                }\n                            }\n                        } catch (validationError) {\n                            console.error('Erro na validação de empresas:', validationError);\n                            alert('Erro na validação: ' + validationError.message);\n                            return null;\n                        }\n                    }\n                }\n                // Verificar se algum participante é cliente\n                const hasClientParticipants = participantIds.some({\n                    \"ChatProvider.useCallback[createConversation].hasClientParticipants\": (id)=>{\n                        // Verificar se o ID corresponde a um cliente (assumindo que clientes têm isClient: true)\n                        return typeof id === 'object' && id.isClient;\n                    }\n                }[\"ChatProvider.useCallback[createConversation].hasClientParticipants\"]);\n                console.log('Tem participantes clientes:', hasClientParticipants);\n                // Obter o token mais recente do localStorage\n                const currentToken = getCurrentToken();\n                console.log('Token disponível:', currentToken ? 'Sim' : 'Não');\n                if (!currentToken) {\n                    console.error('Token não disponível. Usuário precisa fazer login novamente.');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        type: participantIds.length > 1 ? 'GROUP' : 'INDIVIDUAL',\n                        title,\n                        participantIds: participantIds.map({\n                            \"ChatProvider.useCallback[createConversation]\": (id)=>typeof id === 'object' ? id.id : id\n                        }[\"ChatProvider.useCallback[createConversation]\"]),\n                        includeClients: true // Permitir incluir clientes nas conversas\n                    })\n                });\n                console.log('Resposta da API:', response.status, response.statusText);\n                const data = await response.json();\n                console.log('Dados da resposta:', data);\n                if (data.success) {\n                    // Extrair os dados da conversa criada\n                    const conversationData = data.data;\n                    console.log('Conversa criada com sucesso:', conversationData);\n                    // Adicionar a nova conversa à lista\n                    setConversations({\n                        \"ChatProvider.useCallback[createConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createConversation]\": (c)=>c.id === conversationData.id\n                            }[\"ChatProvider.useCallback[createConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                conversationData,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"]);\n                    // Disparar evento para notificar componentes sobre a nova conversa\n                    setTimeout({\n                        \"ChatProvider.useCallback[createConversation]\": ()=>{\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: conversationData.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"], 300);\n                    return conversationData;\n                } else {\n                    console.error('Erro ao criar conversa:', data.error || 'Erro desconhecido');\n                    // Se o erro for de autenticação, redirecionar para login\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido. Redirecionando para login...');\n                        handleAuthError();\n                    }\n                }\n                return null;\n            } catch (error) {\n                console.error('Error creating conversation:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[createConversation]\"], [\n        handleAuthError\n    ]);\n    // Criar ou obter conversa com um usuário específico\n    const createOrGetConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createOrGetConversation]\": async (otherUser)=>{\n            try {\n                console.log('createOrGetConversation chamado com usuário:', otherUser);\n                if (!otherUser || !otherUser.id) {\n                    console.error('Usuário inválido:', otherUser);\n                    return null;\n                }\n                // Primeiro, verificar se já existe uma conversa individual com este usuário\n                const existingConversation = conversations.find({\n                    \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (conv)=>{\n                        var _conv_participants;\n                        if (conv.type !== 'INDIVIDUAL') return false;\n                        return (_conv_participants = conv.participants) === null || _conv_participants === void 0 ? void 0 : _conv_participants.some({\n                            \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (p)=>p.userId === otherUser.id\n                        }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                    }\n                }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                if (existingConversation) {\n                    console.log('Conversa existente encontrada:', existingConversation);\n                    setActiveConversation(existingConversation.id);\n                    return existingConversation;\n                }\n                console.log('Criando nova conversa com usuário:', otherUser.fullName);\n                // Se não existir, criar uma nova conversa\n                const newConversation = await createConversation([\n                    otherUser.id\n                ]);\n                if (newConversation) {\n                    console.log('Nova conversa criada com sucesso:', newConversation);\n                    // Garantir que a conversa seja adicionada à lista antes de definir como ativa\n                    setConversations({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createOrGetConversation]\": (c)=>c.id === newConversation.id\n                            }[\"ChatProvider.useCallback[createOrGetConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                newConversation,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"]);\n                    // Definir a conversa como ativa após um pequeno delay para garantir que a lista foi atualizada\n                    setTimeout({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": ()=>{\n                            setActiveConversation(newConversation.id);\n                            // Disparar evento para notificar componentes sobre a nova conversa\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: newConversation.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"], 300);\n                } else {\n                    console.error('Falha ao criar nova conversa, retorno nulo ou indefinido');\n                }\n                return newConversation;\n            } catch (error) {\n                console.error('Error creating or getting conversation:', error);\n                return null;\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[createOrGetConversation]\"], [\n        createConversation,\n        setActiveConversation,\n        handleAuthError\n    ]);\n    // Carregar contagem de mensagens não lidas com cache\n    const loadUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadUnreadCount]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                // Verificar cache\n                const now = Date.now();\n                if (!forceRefresh && requestCache.unreadCount.data !== null && now - requestCache.unreadCount.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para contagem de mensagens não lidas');\n                    return requestCache.unreadCount.data;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                try {\n                    console.log('Buscando contagem de mensagens não lidas da API...');\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/messages/unread\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar contagem de mensagens não lidas');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar contagem de mensagens n\\xe3o lidas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de mensagens não lidas:', data);\n                    if (data.success) {\n                        console.log('Dados de mensagens não lidas recebidos:', data.data);\n                        // Atualizar cache com o totalUnread\n                        requestCache.unreadCount.data = data.data.totalUnread;\n                        requestCache.unreadCount.timestamp = now;\n                        // Atualizar estado\n                        setUnreadCount(data.data.totalUnread);\n                        // Atualizar as conversas com as contagens de não lidas\n                        if (data.data.conversations && Array.isArray(data.data.conversations)) {\n                            // Primeiro, verificar se já temos as conversas carregadas\n                            const conversationIds = data.data.conversations.map({\n                                \"ChatProvider.useCallback[loadUnreadCount].conversationIds\": (c)=>c.conversationId\n                            }[\"ChatProvider.useCallback[loadUnreadCount].conversationIds\"]);\n                            // Se não temos conversas carregadas ou se temos menos conversas do que as não lidas,\n                            // forçar uma atualização das conversas\n                            if (conversations.length === 0 || !conversationIds.every({\n                                \"ChatProvider.useCallback[loadUnreadCount]\": (id)=>conversations.some({\n                                        \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>c.id === id\n                                    }[\"ChatProvider.useCallback[loadUnreadCount]\"])\n                            }[\"ChatProvider.useCallback[loadUnreadCount]\"])) {\n                                console.log('Forçando atualização das conversas porque há mensagens não lidas em conversas não carregadas');\n                                await loadConversations(true);\n                            } else {\n                                // Atualizar as conversas existentes com as contagens de não lidas\n                                setConversations({\n                                    \"ChatProvider.useCallback[loadUnreadCount]\": (prev)=>{\n                                        return prev.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount]\": (conv)=>{\n                                                // Procurar esta conversa nos dados de não lidas\n                                                const unreadInfo = data.data.conversations.find({\n                                                    \"ChatProvider.useCallback[loadUnreadCount].unreadInfo\": (item)=>item.conversationId === conv.id\n                                                }[\"ChatProvider.useCallback[loadUnreadCount].unreadInfo\"]);\n                                                if (unreadInfo) {\n                                                    return {\n                                                        ...conv,\n                                                        unreadCount: unreadInfo.unreadCount\n                                                    };\n                                                }\n                                                return conv;\n                                            }\n                                        }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                                    }\n                                }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                            }\n                        }\n                        return data.data.totalUnread;\n                    }\n                } catch (error) {\n                    console.error('Error loading unread count:', error);\n                }\n            } catch (error) {\n                console.error('Error in loadUnreadCount:', error);\n            }\n        // Removendo conversations e loadConversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadUnreadCount]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Referência para controlar se os dados iniciais já foram carregados\n    const initialDataLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Resetar a flag quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user) {\n                console.log('Usuário alterado, resetando flag de dados carregados');\n                initialDataLoadedRef.current = false;\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Dependendo apenas do ID do usuário para evitar re-renders desnecessários\n    // Efeito para carregar dados iniciais e configurar atualização periódica\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de carregar dados\n            if (!user) {\n                console.log('Usuário não logado, não carregando dados iniciais');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Carregando dados iniciais para:', user.role || 'USER');\n            // Verificar token\n            const currentToken = getCurrentToken();\n            if (!currentToken) {\n                console.log('Token não disponível, não carregando dados iniciais');\n                return;\n            }\n            // Verificar se já está carregando\n            if (isLoading || isLoadingRef.current) {\n                console.log('Já está carregando dados, aguardando...');\n                return;\n            }\n            // Verificar se os dados já foram carregados\n            if (initialDataLoadedRef.current) {\n                console.log('Dados já foram carregados anteriormente, ignorando');\n                return;\n            }\n            // Função para carregar dados iniciais\n            const loadInitialData = {\n                \"ChatProvider.useEffect.loadInitialData\": async ()=>{\n                    if (isLoading || isLoadingRef.current) {\n                        console.log('Já está carregando dados, cancelando carregamento inicial');\n                        return;\n                    }\n                    // Marcar como carregando\n                    isLoadingRef.current = true;\n                    console.log('Iniciando carregamento de dados do chat...');\n                    try {\n                        // Primeiro carregar as conversas\n                        console.log('Carregando conversas...');\n                        const conversationsData = await loadConversations(true);\n                        console.log('Conversas carregadas:', (conversationsData === null || conversationsData === void 0 ? void 0 : conversationsData.length) || 0, 'conversas');\n                        // Carregar contagem de mensagens não lidas\n                        console.log('Carregando contagem de mensagens não lidas...');\n                        await loadUnreadCount(true);\n                        console.log('Contagem de não lidas carregada');\n                        // Marcar como carregado\n                        initialDataLoadedRef.current = true;\n                        console.log('Carregamento de dados concluído com sucesso');\n                    } catch (error) {\n                        console.error('Erro ao carregar dados:', error);\n                    } finally{\n                        isLoadingRef.current = false;\n                    }\n                }\n            }[\"ChatProvider.useEffect.loadInitialData\"];\n            // Carregar dados iniciais com debounce para evitar múltiplas chamadas\n            console.log('Agendando carregamento de dados iniciais...');\n            const debouncedLoadData = debounce({\n                \"ChatProvider.useEffect.debouncedLoadData\": ()=>{\n                    console.log('Carregando dados iniciais (debounced)...');\n                    loadInitialData();\n                }\n            }[\"ChatProvider.useEffect.debouncedLoadData\"], 1000); // Esperar 1 segundo antes de carregar\n            // Chamar a função com debounce\n            debouncedLoadData();\n            // Removemos a atualização periódica via HTTP para evitar flood no backend\n            // Agora dependemos apenas do WebSocket para atualizações em tempo real\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                // Cleanup function\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo loadUnreadCount e isPanelOpen, isModalOpen das dependências para evitar chamadas desnecessárias\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        loadConversations,\n        isLoading\n    ]);\n    // Efeito para verificar se activeConversation é válido\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (activeConversation && conversations.length > 0) {\n                const conversationExists = conversations.some({\n                    \"ChatProvider.useEffect.conversationExists\": (c)=>c.id === activeConversation\n                }[\"ChatProvider.useEffect.conversationExists\"]);\n                if (!conversationExists) {\n                    console.warn('Conversa ativa não encontrada na lista de conversas, resetando...', activeConversation);\n                    setActiveConversation(null);\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        activeConversation,\n        conversations\n    ]);\n    // Efeito para monitorar mudanças no token e reconectar quando necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de monitorar o token\n            if (!user) return;\n            // Verificar o token a cada 2 minutos (aumentado para reduzir a frequência)\n            const tokenCheckInterval = setInterval({\n                \"ChatProvider.useEffect.tokenCheckInterval\": ()=>{\n                    // Evitar verificações desnecessárias se estiver carregando\n                    if (isLoading || isLoadingRef.current) return;\n                    const currentToken = getCurrentToken();\n                    // Se não há token mas há conexão, desconectar\n                    if (!currentToken && isConnected && socket) {\n                        console.log('Token não encontrado, desconectando WebSocket...');\n                        try {\n                            socket.disconnect();\n                        } catch (error) {\n                            console.error('Erro ao desconectar socket:', error);\n                        }\n                        setIsConnected(false);\n                    }\n                    // Se há token mas não há conexão, tentar reconectar (com verificação de tempo)\n                    if (currentToken && !isConnected && !socket) {\n                        const now = Date.now();\n                        // Limitar tentativas de reconexão (no máximo uma a cada 2 minutos)\n                        if (now - lastInitAttemptRef.current > 120000) {\n                            console.log('Token encontrado, tentando reconectar WebSocket...');\n                            // Permitir nova tentativa de inicialização do WebSocket\n                            socketInitializedRef.current = false;\n                            lastInitAttemptRef.current = now;\n                        }\n                    }\n                }\n            }[\"ChatProvider.useEffect.tokenCheckInterval\"], 120000); // Verificar a cada 2 minutos\n            return ({\n                \"ChatProvider.useEffect\": ()=>clearInterval(tokenCheckInterval)\n            })[\"ChatProvider.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Abrir/fechar painel de chat\n    const toggleChatPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatPanel]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsPanelOpen({\n                \"ChatProvider.useCallback[toggleChatPanel]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o painel, fechar o modal\n                    if (newState) {\n                        setIsModalOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatPanel]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatPanel]\"], [\n        user\n    ]);\n    // Abrir/fechar modal de chat\n    const toggleChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatModal]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsModalOpen({\n                \"ChatProvider.useCallback[toggleChatModal]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o modal, fechar o painel\n                    if (newState) {\n                        setIsPanelOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatModal]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatModal]\"], [\n        user\n    ]);\n    // Verificar se createConversation é uma função válida\n    console.log('ChatContext: createConversation é uma função?', typeof createConversation === 'function');\n    // Adicionar participante a um grupo\n    const addParticipantToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addParticipantToGroup]\": async (conversationId, participantId)=>{\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao adicionar participante');\n                    return null;\n                }\n                console.log(\"Adicionando participante \".concat(participantId, \" \\xe0 conversa \").concat(conversationId));\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        participantId\n                    })\n                });\n                console.log(\"Resposta da API: \".concat(response.status, \" \").concat(response.statusText));\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao adicionar participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    // Log detalhado do erro\n                    const errorText = await response.text();\n                    console.error(\"Erro \".concat(response.status, \" ao adicionar participante:\"), errorText);\n                    throw new Error(\"Erro ao adicionar participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Atualizar a conversa com o novo participante\n                    setConversations({\n                        \"ChatProvider.useCallback[addParticipantToGroup]\": (prev)=>{\n                            return prev.map({\n                                \"ChatProvider.useCallback[addParticipantToGroup]\": (conv)=>{\n                                    if (conv.id === conversationId) {\n                                        // Verificar se o participante já existe\n                                        const participantExists = conv.participants.some({\n                                            \"ChatProvider.useCallback[addParticipantToGroup].participantExists\": (p)=>p.userId === participantId\n                                        }[\"ChatProvider.useCallback[addParticipantToGroup].participantExists\"]);\n                                        if (participantExists) {\n                                            return conv;\n                                        }\n                                        // Adicionar o novo participante\n                                        return {\n                                            ...conv,\n                                            participants: [\n                                                ...conv.participants,\n                                                data.data\n                                            ]\n                                        };\n                                    }\n                                    return conv;\n                                }\n                            }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                        }\n                    }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                    // Criar uma mensagem do sistema para notificar que um participante foi adicionado\n                    if (socket && isConnected) {\n                        const systemMessage = {\n                            conversationId,\n                            content: \"\".concat(data.data.user.fullName, \" foi adicionado ao grupo\"),\n                            contentType: 'SYSTEM'\n                        };\n                        socket.emit('message:send', systemMessage);\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao adicionar participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[addParticipantToGroup]\"], [\n        socket,\n        isConnected,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Adicionar múltiplos participantes a um grupo\n    const addMultipleParticipantsToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addMultipleParticipantsToGroup]\": async (conversationId, participants)=>{\n            try {\n                if (!conversationId || !participants || !participants.length) {\n                    console.error('ID da conversa e lista de participantes são obrigatórios');\n                    return null;\n                }\n                console.log(\"Adicionando \".concat(participants.length, \" participantes ao grupo \").concat(conversationId));\n                // Array para armazenar os resultados\n                const results = [];\n                const errors = [];\n                // Adicionar participantes um por um\n                for (const participant of participants){\n                    try {\n                        const result = await addParticipantToGroup(conversationId, participant.id);\n                        if (result) {\n                            results.push(result);\n                        } else {\n                            errors.push({\n                                user: participant,\n                                error: 'Falha ao adicionar participante'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao adicionar participante \".concat(participant.id, \":\"), error);\n                        errors.push({\n                            user: participant,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    added: results,\n                    errors: errors,\n                    total: participants.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao adicionar múltiplos participantes:', error);\n                return {\n                    success: false,\n                    added: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: participants.length,\n                    successCount: 0,\n                    errorCount: participants.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[addMultipleParticipantsToGroup]\"], [\n        addParticipantToGroup\n    ]);\n    // Remover participante de um grupo (ou sair do grupo)\n    const removeParticipantFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[removeParticipantFromGroup]\": async (conversationId, participantId)=>{\n            console.log('removeParticipantFromGroup chamado:', {\n                conversationId,\n                participantId,\n                userId: user === null || user === void 0 ? void 0 : user.id\n            });\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao remover participante');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants/\").concat(participantId), {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao remover participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    throw new Error(\"Erro ao remover participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Sempre remover da lista local quando o usuário sai\n                    if (participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                        console.log(\"Removendo conversa \".concat(conversationId, \" da lista local\"));\n                        // Remover da lista local\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>prev.filter({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>conv.id !== conversationId\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar mensagens\n                        setMessages({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                const updated = {\n                                    ...prev\n                                };\n                                delete updated[conversationId];\n                                return updated;\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar cache\n                        requestCache.conversations.data = null;\n                        requestCache.conversations.timestamp = 0;\n                        if (requestCache.messages[conversationId]) {\n                            delete requestCache.messages[conversationId];\n                        }\n                        // Se era a conversa ativa, limpar\n                        if (activeConversation === conversationId) {\n                            setActiveConversation(null);\n                        }\n                        // Forçar recarregamento das conversas após sair\n                        setTimeout({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": ()=>{\n                                loadConversations(true);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], 500);\n                        console.log(\"Conversa \".concat(conversationId, \" removida da lista local\"));\n                    } else {\n                        var _data_data_user, _data_data;\n                        // Atualizar a conversa removendo o participante\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                return prev.map({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>{\n                                        if (conv.id === conversationId) {\n                                            return {\n                                                ...conv,\n                                                participants: conv.participants.filter({\n                                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (p)=>p.userId !== participantId\n                                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                                            };\n                                        }\n                                        return conv;\n                                    }\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Criar uma mensagem do sistema para notificar que um participante saiu\n                        if (socket && isConnected && ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : (_data_data_user = _data_data.user) === null || _data_data_user === void 0 ? void 0 : _data_data_user.fullName)) {\n                            const systemMessage = {\n                                conversationId,\n                                content: \"\".concat(data.data.user.fullName, \" saiu do grupo\"),\n                                contentType: 'SYSTEM'\n                            };\n                            socket.emit('message:send', systemMessage);\n                        }\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao remover participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], [\n        socket,\n        isConnected,\n        user,\n        activeConversation,\n        getCurrentToken,\n        handleAuthError,\n        loadUnreadCount\n    ]);\n    // Marcar mensagens como lidas\n    const markMessagesAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markMessagesAsRead]\": async (conversationId, messageId)=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/mark-read\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                if (response.ok) {\n                    // Atualizar contador local imediatamente\n                    setConversations({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[markMessagesAsRead]\": (conv)=>conv.id === conversationId ? {\n                                        ...conv,\n                                        unreadCount: 0\n                                    } : conv\n                            }[\"ChatProvider.useCallback[markMessagesAsRead]\"])\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                    // Recalcular total de não lidas\n                    setUnreadCount({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>{\n                            const conv = conversations.find({\n                                \"ChatProvider.useCallback[markMessagesAsRead].conv\": (c)=>c.id === conversationId\n                            }[\"ChatProvider.useCallback[markMessagesAsRead].conv\"]);\n                            const currentUnread = (conv === null || conv === void 0 ? void 0 : conv.unreadCount) || 0;\n                            return Math.max(0, prev - currentUnread);\n                        }\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                    await loadUnreadCount(true);\n                }\n            } catch (error) {\n                console.error('Error marking messages as read:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[markMessagesAsRead]\"], [\n        user,\n        getCurrentToken,\n        loadUnreadCount,\n        conversations\n    ]);\n    // Resetar contador de mensagens não lidas\n    const resetUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[resetUnreadCount]\": async ()=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/reset-unread\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (response.ok) {\n                    setUnreadCount(0);\n                    setConversations({\n                        \"ChatProvider.useCallback[resetUnreadCount]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[resetUnreadCount]\": (conv)=>({\n                                        ...conv,\n                                        unreadCount: 0\n                                    })\n                            }[\"ChatProvider.useCallback[resetUnreadCount]\"])\n                    }[\"ChatProvider.useCallback[resetUnreadCount]\"]);\n                }\n            } catch (error) {\n                console.error('Error resetting unread count:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[resetUnreadCount]\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Apagar mensagens\n    const deleteMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[deleteMessages]\": async (messageIds, conversationId)=>{\n            try {\n                if (!messageIds || messageIds.length === 0) {\n                    console.error('IDs das mensagens são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao apagar mensagens');\n                    return null;\n                }\n                const results = [];\n                const errors = [];\n                // Apagar mensagens uma por uma\n                for (const messageId of messageIds){\n                    try {\n                        const response = await fetch(\"\".concat(API_URL, \"/chat/messages/\").concat(messageId), {\n                            method: 'DELETE',\n                            headers: {\n                                Authorization: \"Bearer \".concat(currentToken)\n                            }\n                        });\n                        if (!response.ok) {\n                            if (response.status === 401) {\n                                console.error('Token expirado ou inválido ao apagar mensagem');\n                                handleAuthError();\n                                return null;\n                            }\n                            throw new Error(\"Erro ao apagar mensagem: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        if (data.success) {\n                            results.push(data.data);\n                            // Atualizar o estado das mensagens\n                            setMessages({\n                                \"ChatProvider.useCallback[deleteMessages]\": (prev)=>{\n                                    const updatedMessages = {\n                                        ...prev\n                                    };\n                                    // Percorrer todas as conversas\n                                    Object.keys(updatedMessages).forEach({\n                                        \"ChatProvider.useCallback[deleteMessages]\": (convId)=>{\n                                            // Atualizar a mensagem na conversa correspondente\n                                            updatedMessages[convId] = updatedMessages[convId].map({\n                                                \"ChatProvider.useCallback[deleteMessages]\": (msg)=>{\n                                                    if (msg.id === messageId) {\n                                                        return {\n                                                            ...msg,\n                                                            ...data.data,\n                                                            isDeleted: true\n                                                        };\n                                                    }\n                                                    return msg;\n                                                }\n                                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                        }\n                                    }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                    return updatedMessages;\n                                }\n                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                        } else {\n                            errors.push({\n                                messageId,\n                                error: 'Falha ao apagar mensagem'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao apagar mensagem \".concat(messageId, \":\"), error);\n                        errors.push({\n                            messageId,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                // Limpar o cache de mensagens para forçar uma nova busca\n                if (conversationId && results.length > 0) {\n                    console.log('Limpando cache de mensagens para a conversa:', conversationId);\n                    if (requestCache.messages[conversationId]) {\n                        delete requestCache.messages[conversationId];\n                    }\n                    // Recarregar as mensagens da conversa para garantir que estamos sincronizados com o backend\n                    try {\n                        console.log('Recarregando mensagens após exclusão');\n                        await loadMessages(conversationId);\n                        // Também recarregar a lista de conversas para garantir que tudo está atualizado\n                        await loadConversations(true);\n                    } catch (reloadError) {\n                        console.error('Erro ao recarregar mensagens após exclusão:', reloadError);\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    deleted: results,\n                    errors: errors,\n                    total: messageIds.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao apagar mensagens:', error);\n                return {\n                    success: false,\n                    deleted: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: messageIds.length,\n                    successCount: 0,\n                    errorCount: messageIds.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[deleteMessages]\"], [\n        getCurrentToken,\n        handleAuthError,\n        loadMessages,\n        loadConversations\n    ]);\n    // Memoizar o valor do contexto para evitar re-renderizações desnecessárias\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatProvider.useMemo[contextValue]\": ()=>({\n                conversations,\n                messages,\n                unreadCount,\n                activeConversation,\n                isPanelOpen,\n                isModalOpen,\n                isConnected,\n                isLoading,\n                setActiveConversation,\n                loadMessages,\n                sendMessage,\n                createConversation,\n                createOrGetConversation,\n                toggleChatPanel,\n                toggleChatModal,\n                loadConversations,\n                loadUnreadCount,\n                markMessagesAsRead,\n                resetUnreadCount,\n                addParticipantToGroup,\n                addMultipleParticipantsToGroup,\n                removeParticipantFromGroup,\n                deleteMessages\n            })\n    }[\"ChatProvider.useMemo[contextValue]\"], [\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation,\n        isPanelOpen,\n        isModalOpen,\n        isConnected,\n        isLoading,\n        setActiveConversation,\n        loadMessages,\n        sendMessage,\n        createConversation,\n        createOrGetConversation,\n        toggleChatPanel,\n        toggleChatModal,\n        loadConversations,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        deleteMessages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ChatContext.js\",\n        lineNumber: 1920,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatProvider, \"knvLij1shdeGVeBVK+PUiQX2mtg=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ChatProvider;\nconst useChat = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChat deve ser usado dentro de um ChatProvider');\n    }\n    return context;\n};\n_s1(useChat, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9DaGF0Q29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTRHO0FBQ3BFO0FBQ047QUFFbEMsTUFBTVUsVUFBVUMsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDQyxtQkFBbUIsSUFBSTtBQUVuRCwyQ0FBMkM7QUFDM0MsTUFBTUMsZUFBZTtJQUNuQkMsZUFBZTtRQUFFQyxNQUFNO1FBQU1DLFdBQVc7SUFBRTtJQUMxQ0MsYUFBYTtRQUFFRixNQUFNO1FBQU1DLFdBQVc7SUFBRTtJQUN4Q0UsVUFBVSxDQUFDO0lBQ1hDLFlBQVk7UUFBRUgsV0FBVztRQUFHSSxPQUFPO0lBQU0sRUFBRSxrQ0FBa0M7QUFDL0U7QUFFQSwrQ0FBK0M7QUFDL0MsTUFBTUMsbUJBQW1CLE9BQU8saUNBQWlDO0FBQ2pFLE1BQU1DLHlCQUF5QixPQUFPLHFDQUFxQztBQUUzRSwyREFBMkQ7QUFDM0QsTUFBTUMsV0FBVyxDQUFDQyxNQUFNQztJQUN0QixJQUFJQztJQUNKLE9BQU8sU0FBU0M7UUFBaUI7WUFBR0MsS0FBSCx1QkFBTzs7UUFDdEMsTUFBTUMsUUFBUTtZQUNaQyxhQUFhSjtZQUNiRixRQUFRSTtRQUNWO1FBQ0FFLGFBQWFKO1FBQ2JBLFVBQVVLLFdBQVdGLE9BQU9KO0lBQzlCO0FBQ0Y7QUFFQSxNQUFNTyw0QkFBY2hDLG9EQUFhQSxDQUFDO0FBRTNCLE1BQU1pQyxlQUFlO1FBQUMsRUFBRUMsUUFBUSxFQUFFOztJQUN2QyxNQUFNLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFLEdBQUc3QixxREFBT0E7SUFDaEMsTUFBTSxDQUFDOEIsUUFBUUMsVUFBVSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDcUMsYUFBYUMsZUFBZSxHQUFHdEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDWSxlQUFlMkIsaUJBQWlCLEdBQUd2QywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3JELE1BQU0sQ0FBQ3dDLG9CQUFvQkMsc0JBQXNCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUNnQixVQUFVMEIsWUFBWSxHQUFHMUMsK0NBQVFBLENBQUMsQ0FBQztJQUMxQyxNQUFNLENBQUNlLGFBQWE0QixlQUFlLEdBQUczQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM0QyxhQUFhQyxlQUFlLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM4QyxhQUFhQyxlQUFlLEdBQUcvQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNnRCxXQUFXQyxhQUFhLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUUzQyxzRkFBc0Y7SUFDdEYsTUFBTWtELGtCQUFrQmhELGtEQUFXQTtxREFBQztZQUNsQyxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLE9BQU9pRCxhQUFhQyxPQUFPLENBQUM7WUFDOUI7WUFDQSxPQUFPO1FBQ1Q7b0RBQUcsRUFBRTtJQUVMLDhDQUE4QztJQUM5QyxNQUFNQyxrQkFBa0JuRCxrREFBV0E7cURBQUM7WUFDbENvRCxRQUFRQyxLQUFLLENBQUM7WUFDZCxzQkFBc0I7WUFDdEJoQixpQkFBaUIsRUFBRTtZQUNuQkcsWUFBWSxDQUFDO1lBQ2JELHNCQUFzQjtZQUN0QkUsZUFBZTtZQUNmRSxlQUFlO1lBQ2ZFLGVBQWU7WUFFZixnQ0FBZ0M7WUFDaEMsSUFBSVosUUFBUTtnQkFDVixJQUFJO29CQUNGQSxPQUFPcUIsVUFBVTtnQkFDbkIsRUFBRSxPQUFPRCxPQUFPO29CQUNkRCxRQUFRQyxLQUFLLENBQUMsK0JBQStCQTtnQkFDL0M7Z0JBQ0FqQixlQUFlO1lBQ2pCO1lBRUEsZUFBZTtZQUNmM0IsYUFBYUMsYUFBYSxDQUFDQyxJQUFJLEdBQUc7WUFDbENGLGFBQWFJLFdBQVcsQ0FBQ0YsSUFBSSxHQUFHO1lBQ2hDRixhQUFhSyxRQUFRLEdBQUcsQ0FBQyxHQUFHLDRCQUE0QjtZQUV4RCwwQkFBMEI7WUFDMUIsSUFBSWtCLFFBQVE7Z0JBQ1ZBO1lBQ0Y7UUFDRjtvREFBRztRQUFDQztRQUFRRDtLQUFPO0lBRW5CLGdGQUFnRjtJQUNoRixNQUFNdUIsdUJBQXVCckQsNkNBQU1BLENBQUM7SUFDcEMsTUFBTXNELGVBQWV0RCw2Q0FBTUEsQ0FBQztJQUM1QixNQUFNdUQscUJBQXFCdkQsNkNBQU1BLENBQUM7SUFDbEMsTUFBTXdELHVCQUF1QnhELDZDQUFNQSxDQUFDO0lBRXBDLGdEQUFnRDtJQUNoREgsZ0RBQVNBO2tDQUFDO1lBQ1IsdURBQXVEO1lBQ3ZELElBQUksQ0FBQ2dDLFFBQVFFLFFBQVE7Z0JBQ25CLElBQUk7b0JBQ0ZBLE9BQU9xQixVQUFVO29CQUNqQmxCLGVBQWU7b0JBQ2ZGLFVBQVU7Z0JBQ1osRUFBRSxPQUFPbUIsT0FBTztvQkFDZEQsUUFBUUMsS0FBSyxDQUFDLDJDQUEyQ0E7Z0JBQzNEO1lBQ0Y7WUFFQSx1Q0FBdUM7WUFDdkNFLHFCQUFxQkksT0FBTyxHQUFHO1lBQy9CRixtQkFBbUJFLE9BQU8sR0FBRztZQUM3QkQscUJBQXFCQyxPQUFPLEdBQUc7UUFDakM7aUNBQUc7UUFBQzVCO1FBQU1FO0tBQU87SUFFakIsOENBQThDO0lBQzlDbEMsZ0RBQVNBO2tDQUFDO1lBQ1IsZ0VBQWdFO1lBQ2hFLElBQUlnQyxNQUFNO2dCQUNScUIsUUFBUVEsR0FBRyxDQUFDLDhEQUE4RDdCLEtBQUs4QixRQUFRO2dCQUN2RixtREFBbUQ7Z0JBQ25EeEIsaUJBQWlCLEVBQUU7Z0JBQ25CRyxZQUFZLENBQUM7Z0JBQ2JELHNCQUFzQjtnQkFDdEJFLGVBQWU7Z0JBQ2ZFLGVBQWU7Z0JBQ2ZFLGVBQWU7Z0JBRWYsZUFBZTtnQkFDZnBDLGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSSxHQUFHO2dCQUNsQ0YsYUFBYUksV0FBVyxDQUFDRixJQUFJLEdBQUc7Z0JBQ2hDRixhQUFhSyxRQUFRLEdBQUcsQ0FBQztnQkFDekJMLGFBQWFNLFVBQVUsQ0FBQ0gsU0FBUyxHQUFHO2dCQUNwQ0gsYUFBYU0sVUFBVSxDQUFDQyxLQUFLLEdBQUc7Z0JBRWhDb0MsUUFBUVEsR0FBRyxDQUFDO2dCQUVaLDhEQUE4RDtnQkFDOUQsSUFBSSxJQUE2QixFQUFFO29CQUNqQywyRUFBMkU7b0JBQzNFLE1BQU1FLGVBQWUsRUFBRTtvQkFDdkIsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlkLGFBQWFlLE1BQU0sRUFBRUQsSUFBSzt3QkFDNUMsTUFBTUUsTUFBTWhCLGFBQWFnQixHQUFHLENBQUNGO3dCQUM3QixJQUFJRSxPQUFRQSxDQUFBQSxJQUFJQyxVQUFVLENBQUMsWUFBWUQsSUFBSUMsVUFBVSxDQUFDLG9CQUFvQkQsSUFBSUMsVUFBVSxDQUFDLFdBQVUsR0FBSTs0QkFDckdKLGFBQWFLLElBQUksQ0FBQ0Y7d0JBQ3BCO29CQUNGO29CQUNBSCxhQUFhTSxPQUFPO2tEQUFDSCxDQUFBQSxNQUFPaEIsYUFBYW9CLFVBQVUsQ0FBQ0o7O29CQUNwRGIsUUFBUVEsR0FBRyxDQUFDO2dCQUNkO1lBQ0Y7UUFDRjtpQ0FBRztRQUFDN0IsaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRTtLQUFDLEdBQUcsd0NBQXdDO0lBRXhELGdDQUFnQztJQUNoQ3ZFLGdEQUFTQTtrQ0FBQztZQUNSLHFDQUFxQztZQUNyQyxJQUFJLENBQUNnQyxNQUFNO2dCQUNUcUIsUUFBUVEsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxtQ0FBbUM7WUFDbkNSLFFBQVFRLEdBQUcsQ0FBQyxvQ0FBb0M3QixLQUFLd0MsSUFBSSxJQUFJO1lBRTdELG1EQUFtRDtZQUNuRCxJQUFJdEMsVUFBVUUsYUFBYTtnQkFDekJpQixRQUFRUSxHQUFHLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLGlGQUFpRjtZQUNqRixJQUFJTCxxQkFBcUJJLE9BQU8sSUFBSWEsS0FBS0MsR0FBRyxLQUFLaEIsbUJBQW1CRSxPQUFPLEdBQUcsT0FBTztnQkFDbkZQLFFBQVFRLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsMEVBQTBFO1lBQzFFTCxxQkFBcUJJLE9BQU8sR0FBRztZQUMvQkYsbUJBQW1CRSxPQUFPLEdBQUdhLEtBQUtDLEdBQUc7WUFFckMsaURBQWlEO1lBQ2pELE1BQU1DOzhEQUFzQjtvQkFDMUIsc0VBQXNFO29CQUN0RSxJQUFJLENBQUMzQyxNQUFNO29CQUVYLG1DQUFtQztvQkFDbkNxQixRQUFRUSxHQUFHLENBQUMsaUNBQWlDN0IsS0FBS3dDLElBQUksSUFBSTtvQkFFMUQsTUFBTUksZUFBZTNCO29CQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUVuQiwyREFBMkQ7b0JBQzNELElBQUkxQyxRQUFRO3dCQUNWLElBQUlFLGFBQWE7NEJBQ2ZpQixRQUFRUSxHQUFHLENBQUM7NEJBQ1osT0FBTzNCLFFBQVEsOEJBQThCO3dCQUMvQyxPQUFPOzRCQUNMLHVGQUF1Rjs0QkFDdkZtQixRQUFRUSxHQUFHLENBQUM7NEJBQ1osSUFBSTtnQ0FDRiwrREFBK0Q7Z0NBQy9ELElBQUksQ0FBQzNCLE9BQU8yQyxTQUFTLElBQUkzQyxPQUFPNEMsT0FBTyxFQUFFO29DQUN2QzVDLE9BQU80QyxPQUFPO29DQUNkekIsUUFBUVEsR0FBRyxDQUFDO29DQUNaLE9BQU8zQjtnQ0FDVDs0QkFDRixFQUFFLE9BQU9vQixPQUFPO2dDQUNkRCxRQUFRQyxLQUFLLENBQUMsd0NBQXdDQTtnQ0FDdEQsdUNBQXVDO2dDQUN2QyxJQUFJO29DQUNGcEIsT0FBT3FCLFVBQVU7Z0NBQ25CLEVBQUUsT0FBT3dCLGlCQUFpQjtvQ0FDeEIxQixRQUFRQyxLQUFLLENBQUMsdURBQXVEeUI7Z0NBQ3ZFOzRCQUNGO3dCQUNGO29CQUNGO29CQUVBLGdFQUFnRTtvQkFDaEV2QixxQkFBcUJJLE9BQU8sR0FBRztvQkFFL0IscUNBQXFDO29CQUNyQyxNQUFNb0IsdUJBQXVCO29CQUM3QixNQUFNQyxpQkFBaUIsTUFBTSxhQUFhO29CQUUxQzVCLFFBQVFRLEdBQUcsQ0FBQztvQkFFWixJQUFJO3dCQUNGLGlFQUFpRTt3QkFDakUsc0VBQXNFO3dCQUN0RSx3REFBd0Q7d0JBQ3hEUixRQUFRUSxHQUFHLENBQUMseURBQXlEN0IsS0FBSzhCLFFBQVE7d0JBRWxGVCxRQUFRUSxHQUFHLENBQUM7d0JBRVosTUFBTXFCLGlCQUFpQjdFLDREQUFFQSxDQUFDQyxTQUFTOzRCQUNqQzZFLE1BQU07NEJBQ05DLE1BQU07Z0NBQUVDLE9BQU9UOzRCQUFhOzRCQUM1QlUsWUFBWTtnQ0FBQztnQ0FBYTs2QkFBVTs0QkFDcENDLHNCQUFzQlA7NEJBQ3RCUSxtQkFBbUJQOzRCQUNuQjFELFNBQVM7NEJBQ1RrRSxhQUFhOzRCQUNiQyxjQUFjO3dCQUNoQjt3QkFFQVIsZUFBZVMsRUFBRSxDQUFDOzBFQUFXO2dDQUMzQnRDLFFBQVFRLEdBQUcsQ0FBQztnQ0FDWnhCLGVBQWU7Z0NBQ2ZzQixxQkFBcUJDLE9BQU8sR0FBRztnQ0FFL0IsMkVBQTJFO2dDQUMzRWdDLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHlCQUF5QjtvQ0FDNURDLFFBQVE7d0NBQUVDLE1BQU07d0NBQWNDLFFBQVE7d0NBQWFwRixXQUFXNEQsS0FBS0MsR0FBRztvQ0FBRztnQ0FDM0U7NEJBQ0Y7O3dCQUVBUSxlQUFlUyxFQUFFLENBQUM7MEVBQWM7Z0NBQzlCdEMsUUFBUVEsR0FBRyxDQUFDO2dDQUNaeEIsZUFBZTtnQ0FFZiw4RUFBOEU7Z0NBQzlFdUQsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVkseUJBQXlCO29DQUM1REMsUUFBUTt3Q0FBRUMsTUFBTTt3Q0FBY0MsUUFBUTt3Q0FBZ0JwRixXQUFXNEQsS0FBS0MsR0FBRztvQ0FBRztnQ0FDOUU7NEJBQ0Y7O3dCQUVBUSxlQUFlUyxFQUFFLENBQUM7MEVBQWlCLENBQUNyQztnQ0FDbENELFFBQVFDLEtBQUssQ0FBQywrQkFBK0JBO2dDQUM3Q0sscUJBQXFCQyxPQUFPO2dDQUU1Qix1REFBdUQ7Z0NBQ3ZELElBQUlOLE1BQU00QyxPQUFPLElBQUk1QyxNQUFNNEMsT0FBTyxDQUFDQyxRQUFRLENBQUMseUJBQXlCO29DQUNuRTlDLFFBQVFDLEtBQUssQ0FBQztvQ0FDZDRCLGVBQWUzQixVQUFVO29DQUN6QmxCLGVBQWU7b0NBQ2ZtQixxQkFBcUJJLE9BQU8sR0FBRyxPQUFPLG9DQUFvQztvQ0FDMUU7Z0NBQ0Y7Z0NBRUEsSUFBSUQscUJBQXFCQyxPQUFPLElBQUlvQixzQkFBc0I7b0NBQ3hEM0IsUUFBUUMsS0FBSyxDQUFDO29DQUNkNEIsZUFBZTNCLFVBQVU7b0NBQ3pCbEIsZUFBZTtvQ0FDZm1CLHFCQUFxQkksT0FBTyxHQUFHLE9BQU8sb0NBQW9DO2dDQUM1RTs0QkFDRjs7d0JBRUEsc0NBQXNDO3dCQUN0Q3NCLGVBQWVTLEVBQUUsQ0FBQzswRUFBZSxDQUFDTztnQ0FDaEMsSUFBSSxDQUFDQSxXQUFXLENBQUNBLFFBQVFFLGNBQWMsRUFBRTtvQ0FDdkMvQyxRQUFRQyxLQUFLLENBQUMsK0JBQStCNEM7b0NBQzdDO2dDQUNGO2dDQUVBLDhEQUE4RDtnQ0FDOUQsTUFBTUcsU0FBU3JFLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUU7Z0NBRXZCLHdGQUF3RjtnQ0FDeEYsc0dBQXNHO2dDQUN0RyxNQUFNK0IsU0FBU0osUUFBUUksTUFBTTtnQ0FDN0IsTUFBTUMsdUJBQXVCTCxRQUFRTSxRQUFRLEtBQUtIO2dDQUdsRCxrQ0FBa0M7Z0NBQ2xDNUQ7a0ZBQVlnRSxDQUFBQTt3Q0FDVixNQUFNQyx1QkFBdUJELElBQUksQ0FBQ1AsUUFBUUUsY0FBYyxDQUFDLElBQUksRUFBRTt3Q0FFL0QsMkRBQTJEO3dDQUMzRCxJQUFJTSxxQkFBcUJDLElBQUk7MEZBQUNDLENBQUFBLElBQUtBLEVBQUVyQyxFQUFFLEtBQUsyQixRQUFRM0IsRUFBRTswRkFBRzs0Q0FDdkQsT0FBT2tDO3dDQUNUO3dDQUVBLDBFQUEwRTt3Q0FDMUUsSUFBSUgsVUFBVUkscUJBQXFCQyxJQUFJOzBGQUFDQyxDQUFBQSxJQUFLQSxFQUFFckMsRUFBRSxLQUFLK0I7MEZBQVM7NENBQzdELE9BQU87Z0RBQ0wsR0FBR0csSUFBSTtnREFDUCxDQUFDUCxRQUFRRSxjQUFjLENBQUMsRUFBRU0scUJBQXFCRyxHQUFHO2tHQUFDRCxDQUFBQSxJQUNqREEsRUFBRXJDLEVBQUUsS0FBSytCLFNBQVNKLFVBQVVVOzs0Q0FFaEM7d0NBQ0Y7d0NBRUEsdUZBQXVGO3dDQUN2RixNQUFNTCx1QkFBdUJMLFFBQVFNLFFBQVEsS0FBS0gsVUFBVUgsUUFBUVksY0FBYyxLQUFLVDt3Q0FFdkYsSUFBSUUsd0JBQXdCLENBQUNELFFBQVE7NENBQ25DLHdFQUF3RTs0Q0FDeEUsTUFBTTVCLE1BQU0sSUFBSUQ7NENBQ2hCLE1BQU1zQyxlQUFlTCxxQkFBcUJNLE1BQU07MkdBQUNKLENBQUFBO29EQUMvQyxJQUFJLENBQUNBLEVBQUVLLE1BQU0sRUFBRSxPQUFPO29EQUN0QixJQUFJLENBQUVMLENBQUFBLEVBQUVKLFFBQVEsS0FBS0gsVUFBVU8sRUFBRUUsY0FBYyxLQUFLVCxNQUFLLEdBQUksT0FBTztvREFDcEUsSUFBSSxNQUFPLElBQUk1QixLQUFLbUMsRUFBRU0sU0FBUyxLQUFNLE9BQU8sT0FBTyxPQUFPLGNBQWM7b0RBRXhFLCtEQUErRDtvREFDL0QsSUFBSWhCLFFBQVFpQixXQUFXLEtBQUssZ0JBQWdCUCxFQUFFTyxXQUFXLEtBQUssY0FBYzt3REFDMUUsT0FBTztvREFDVDtvREFFQSwyREFBMkQ7b0RBQzNELElBQUlqQixRQUFRaUIsV0FBVyxLQUFLLFVBQVVQLEVBQUVPLFdBQVcsS0FBSyxVQUFVUCxFQUFFUSxPQUFPLEtBQUtsQixRQUFRa0IsT0FBTyxFQUFFO3dEQUMvRixPQUFPO29EQUNUO29EQUVBLE9BQU87Z0RBQ1Q7OzRDQUVBLElBQUlMLGFBQWE5QyxNQUFNLEdBQUcsR0FBRztnREFDM0IsdURBQXVEO2dEQUN2RCxNQUFNb0QsY0FBY04sWUFBWSxDQUFDLEVBQUU7Z0RBRW5DLE9BQU87b0RBQ0wsR0FBR04sSUFBSTtvREFDUCxDQUFDUCxRQUFRRSxjQUFjLENBQUMsRUFBRU0scUJBQXFCRyxHQUFHO3NHQUFDRCxDQUFBQSxJQUNqREEsRUFBRXJDLEVBQUUsS0FBSzhDLFlBQVk5QyxFQUFFLEdBQUcyQixVQUFVVTs7Z0RBRXhDOzRDQUNGO3dDQUNGO3dDQUVBLDRDQUE0Qzt3Q0FDNUMscURBQXFEO3dDQUNyRCxNQUFNVSxrQkFBa0I7K0NBQUlaOzRDQUFzQlI7eUNBQVEsQ0FBQ3FCLElBQUk7MEdBQUMsQ0FBQ0MsR0FBR0MsSUFDbEUsSUFBSWhELEtBQUsrQyxFQUFFTixTQUFTLElBQUksSUFBSXpDLEtBQUtnRCxFQUFFUCxTQUFTOzt3Q0FHOUMsT0FBTzs0Q0FDTCxHQUFHVCxJQUFJOzRDQUNQLENBQUNQLFFBQVFFLGNBQWMsQ0FBQyxFQUFFa0I7d0NBQzVCO29DQUNGOztnQ0FFQSxtREFBbUQ7Z0NBQ25EaEY7a0ZBQWlCbUUsQ0FBQUE7d0NBQ2YsaUNBQWlDO3dDQUNqQyxNQUFNaUIsZUFBZWpCLEtBQUtrQixJQUFJO3VHQUFDQyxDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYzs7d0NBQ25FLElBQUksQ0FBQ3NCLGNBQWMsT0FBT2pCO3dDQUUxQiwyRkFBMkY7d0NBQzNGLElBQUlILFVBQVVvQixhQUFhRyxXQUFXLElBQUlILGFBQWFHLFdBQVcsQ0FBQ3RELEVBQUUsS0FBSytCLFFBQVE7NENBQ2hGLG1EQUFtRDs0Q0FDbkQsT0FBT0csS0FBS0ksR0FBRzs4RkFBQ2UsQ0FBQUE7b0RBQ2QsSUFBSUEsRUFBRXJELEVBQUUsS0FBSzJCLFFBQVFFLGNBQWMsRUFBRTt3REFDbkMsT0FBTzs0REFBRSxHQUFHd0IsQ0FBQzs0REFBRUMsYUFBYTNCO3dEQUFRO29EQUN0QztvREFDQSxPQUFPMEI7Z0RBQ1Q7O3dDQUNGO3dDQUVBLDJHQUEyRzt3Q0FDM0csTUFBTXJCLHVCQUF1QkwsUUFBUU0sUUFBUSxLQUFLSCxVQUFVSCxRQUFRWSxjQUFjLEtBQUtUO3dDQUN2RixJQUFJRSx3QkFBd0IsQ0FBQ0QsVUFBVW9CLGFBQWFHLFdBQVcsSUFBSUgsYUFBYUcsV0FBVyxDQUFDWixNQUFNLEVBQUU7NENBQ2xHLHFFQUFxRTs0Q0FDckUsSUFBSVMsYUFBYUcsV0FBVyxDQUFDVCxPQUFPLEtBQUtsQixRQUFRa0IsT0FBTyxJQUNuRE0sQ0FBQUEsYUFBYUcsV0FBVyxDQUFDckIsUUFBUSxLQUFLSCxVQUFVcUIsYUFBYUcsV0FBVyxDQUFDZixjQUFjLEtBQUtULE1BQUssR0FBSTtnREFDeEcsbURBQW1EO2dEQUNuRCxPQUFPSSxLQUFLSSxHQUFHO2tHQUFDZSxDQUFBQTt3REFDZCxJQUFJQSxFQUFFckQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYyxFQUFFOzREQUNuQyxPQUFPO2dFQUFFLEdBQUd3QixDQUFDO2dFQUFFQyxhQUFhM0I7NERBQVE7d0RBQ3RDO3dEQUNBLE9BQU8wQjtvREFDVDs7NENBQ0Y7d0NBQ0Y7d0NBRUEsb0NBQW9DO3dDQUNwQyxNQUFNRSx1QkFBdUJyQixLQUFLTyxNQUFNOytHQUFDWSxDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYzs7d0NBQzdFLE9BQU87NENBQ0w7Z0RBQUUsR0FBR3NCLFlBQVk7Z0RBQUVHLGFBQWEzQjs0Q0FBUTsrQ0FDckM0Qjt5Q0FDSjtvQ0FDSDs7Z0NBRUEsMkVBQTJFO2dDQUMzRSxNQUFNQyxvQkFBb0I3QixRQUFRTSxRQUFRLEtBQUtILFVBQVVILFFBQVFZLGNBQWMsS0FBS1Q7Z0NBQ3BGLElBQUksQ0FBQzBCLG1CQUFtQjtvQ0FDdEIxRSxRQUFRUSxHQUFHLENBQUM7b0NBQ1puQjtzRkFBZStELENBQUFBOzRDQUNiLE1BQU11QixXQUFXdkIsT0FBTzs0Q0FDeEJwRCxRQUFRUSxHQUFHLENBQUMsc0NBQXNDNEMsTUFBTSxRQUFRdUI7NENBQ2hFLE9BQU9BO3dDQUNUOztvQ0FFQSw0Q0FBNEM7b0NBQzVDMUY7c0ZBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS0ksR0FBRzs4RkFBQ29CLENBQUFBO29EQUNoQyxJQUFJQSxLQUFLMUQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYyxFQUFFO3dEQUN0QyxNQUFNOEIsaUJBQWlCLENBQUNELEtBQUtuSCxXQUFXLElBQUksS0FBSzt3REFDakR1QyxRQUFRUSxHQUFHLENBQUMsd0JBQTZDb0UsT0FBckJBLEtBQUsxRCxFQUFFLEVBQUMsZUFBeUMyRCxPQUE1QkQsS0FBS25ILFdBQVcsSUFBSSxHQUFFLFFBQXFCLE9BQWZvSDt3REFDckYsT0FBTzs0REFBRSxHQUFHRCxJQUFJOzREQUFFbkgsYUFBYW9IO3dEQUFlO29EQUNoRDtvREFDQSxPQUFPRDtnREFDVDs7O2dDQUNGLE9BQU87b0NBQ0w1RSxRQUFRUSxHQUFHLENBQUM7Z0NBQ2Q7Z0NBRUEsaUZBQWlGO2dDQUNqRitCLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHlCQUF5QjtvQ0FDNURDLFFBQVE7d0NBQUVDLE1BQU07d0NBQVdJLGdCQUFnQkYsUUFBUUUsY0FBYzt3Q0FBRXZGLFdBQVc0RCxLQUFLQyxHQUFHO29DQUFHO2dDQUMzRjs0QkFDRjs7d0JBRUEsNkRBQTZEO3dCQUM3RFEsZUFBZVMsRUFBRSxDQUFDOzBFQUFpQixDQUFDL0U7Z0NBQ2xDeUMsUUFBUVEsR0FBRyxDQUFDLDREQUE0RGpEO2dDQUN4RSxJQUFJQSxRQUFRLE9BQU9BLEtBQUt1SCxXQUFXLEtBQUssVUFBVTtvQ0FDaEQ5RSxRQUFRUSxHQUFHLENBQUMsZ0RBQWlFLE9BQWpCakQsS0FBS3VILFdBQVc7b0NBQzVFekYsZUFBZTlCLEtBQUt1SCxXQUFXO29DQUUvQix1REFBdUQ7b0NBQ3ZELElBQUl2SCxLQUFLRCxhQUFhLElBQUl5SCxNQUFNQyxPQUFPLENBQUN6SCxLQUFLRCxhQUFhLEdBQUc7d0NBQzNEMEMsUUFBUVEsR0FBRyxDQUFDLDJCQUFxRCxPQUExQmpELEtBQUtELGFBQWEsQ0FBQ3NELE1BQU0sRUFBQzt3Q0FDakUzQjswRkFBaUJtRSxDQUFBQTtnREFDZixPQUFPQSxLQUFLSSxHQUFHO2tHQUFDb0IsQ0FBQUE7d0RBQ2QsZ0RBQWdEO3dEQUNoRCxNQUFNSyxhQUFhMUgsS0FBS0QsYUFBYSxDQUFDZ0gsSUFBSTtxSEFDeENZLENBQUFBLE9BQVFBLEtBQUtuQyxjQUFjLEtBQUs2QixLQUFLMUQsRUFBRTs7d0RBR3pDLElBQUkrRCxZQUFZOzREQUNkakYsUUFBUVEsR0FBRyxDQUFDLHdCQUE2Q3lFLE9BQXJCTCxLQUFLMUQsRUFBRSxFQUFDLGVBQW9DLE9BQXZCK0QsV0FBV3hILFdBQVcsRUFBQzs0REFDaEYsT0FBTztnRUFBRSxHQUFHbUgsSUFBSTtnRUFBRW5ILGFBQWF3SCxXQUFXeEgsV0FBVzs0REFBQzt3REFDeEQ7d0RBQ0Esd0RBQXdEO3dEQUN4RCxPQUFPOzREQUFFLEdBQUdtSCxJQUFJOzREQUFFbkgsYUFBYTt3REFBRTtvREFDbkM7OzRDQUNGOztvQ0FDRjtnQ0FDRjtnQ0FFQSw0RkFBNEY7Z0NBQzVGOEUsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVkseUJBQXlCO29DQUM1REMsUUFBUTt3Q0FBRUMsTUFBTTt3Q0FBVW5GLFdBQVc0RCxLQUFLQyxHQUFHO29DQUFHO2dDQUNsRDs0QkFDRjs7d0JBRUEsZ0RBQWdEO3dCQUNoRFEsZUFBZVMsRUFBRSxDQUFDOzBFQUF3QixDQUFDL0U7Z0NBQ3pDeUMsUUFBUVEsR0FBRyxDQUFDLDZEQUE2RGpEO2dDQUN6RSxrREFBa0Q7Z0NBQ2xELElBQUlBLE1BQU07b0NBQ1IsMEVBQTBFO29DQUMxRSxNQUFNNEgscUJBQXFCSixNQUFNQyxPQUFPLENBQUN6SCxRQUFRQSxPQUFPQSxLQUFLRCxhQUFhLElBQUksRUFBRTtvQ0FFaEYsSUFBSXlILE1BQU1DLE9BQU8sQ0FBQ0csdUJBQXVCQSxtQkFBbUJ2RSxNQUFNLEdBQUcsR0FBRzt3Q0FDdEVaLFFBQVFRLEdBQUcsQ0FBQyxlQUF5QyxPQUExQjJFLG1CQUFtQnZFLE1BQU0sRUFBQzt3Q0FDckQzQixpQkFBaUJrRzt3Q0FFakIsOERBQThEO3dDQUM5REMscUJBQXFCN0UsT0FBTyxHQUFHO3dDQUUvQixvQkFBb0I7d0NBQ3BCbEQsYUFBYUMsYUFBYSxDQUFDQyxJQUFJLEdBQUc0SDt3Q0FDbEM5SCxhQUFhQyxhQUFhLENBQUNFLFNBQVMsR0FBRzRELEtBQUtDLEdBQUc7d0NBRS9DLCtFQUErRTt3Q0FDL0VrQixPQUFPQyxhQUFhLENBQUMsSUFBSUMsWUFBWSx5QkFBeUI7NENBQzVEQyxRQUFRO2dEQUFFQyxNQUFNO2dEQUFpQm5GLFdBQVc0RCxLQUFLQyxHQUFHOzRDQUFHO3dDQUN6RDtvQ0FDRixPQUFPO3dDQUNMckIsUUFBUUMsS0FBSyxDQUFDLHdFQUF3RTFDO3dDQUV0RixpRUFBaUU7d0NBQ2pFLElBQUl3SCxNQUFNQyxPQUFPLENBQUNHLHVCQUF1QkEsbUJBQW1CdkUsTUFBTSxLQUFLLEdBQUc7NENBQ3hFWixRQUFRUSxHQUFHLENBQUM7NENBQ1o2RSxrQkFBa0I7d0NBQ3BCO29DQUNGO2dDQUNGOzRCQUNGOzt3QkFFQSx5REFBeUQ7d0JBQ3pEeEQsZUFBZVMsRUFBRSxDQUFDOzBFQUF3QixDQUFDL0U7Z0NBQ3pDeUMsUUFBUVEsR0FBRyxDQUFDLGdEQUFnRGpEO2dDQUM1RCxJQUFJQSxRQUFRQSxLQUFLMkQsRUFBRSxFQUFFO29DQUNuQiwrQ0FBK0M7b0NBQy9DakM7c0ZBQWlCbUUsQ0FBQUE7NENBQ2Ysb0NBQW9DOzRDQUNwQyxJQUFJQSxLQUFLRSxJQUFJOzhGQUFDaUIsQ0FBQUEsSUFBS0EsRUFBRXJELEVBQUUsS0FBSzNELEtBQUsyRCxFQUFFOzhGQUFHO2dEQUNwQyxPQUFPa0M7NENBQ1Q7NENBQ0EsT0FBTztnREFBQzdGO21EQUFTNkY7NkNBQUs7d0NBQ3hCOztnQ0FDRjs0QkFDRjs7d0JBRUEsZ0RBQWdEO3dCQUNoRHZCLGVBQWVTLEVBQUUsQ0FBQzswRUFBdUIsQ0FBQy9FO2dDQUN4Q3lDLFFBQVFRLEdBQUcsQ0FBQyxpREFBaURqRDtnQ0FDN0QsSUFBSUEsUUFBUUEsS0FBS3dGLGNBQWMsSUFBSXhGLEtBQUsrSCxhQUFhLEVBQUU7b0NBQ3JELCtEQUErRDtvQ0FDL0QsSUFBSS9ILEtBQUsrSCxhQUFhLE1BQUszRyxpQkFBQUEsMkJBQUFBLEtBQU11QyxFQUFFLEdBQUU7d0NBQ25DakM7MEZBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTTtrR0FBQ1ksQ0FBQUEsSUFBS0EsRUFBRXJELEVBQUUsS0FBSzNELEtBQUt3RixjQUFjOzs7d0NBRXRFLGtDQUFrQzt3Q0FDbEMsSUFBSTdELHVCQUF1QjNCLEtBQUt3RixjQUFjLEVBQUU7NENBQzlDNUQsc0JBQXNCO3dDQUN4Qjt3Q0FFQSwrQkFBK0I7d0NBQy9CQzswRkFBWWdFLENBQUFBO2dEQUNWLE1BQU1tQyxVQUFVO29EQUFFLEdBQUduQyxJQUFJO2dEQUFDO2dEQUMxQixPQUFPbUMsT0FBTyxDQUFDaEksS0FBS3dGLGNBQWMsQ0FBQztnREFDbkMsT0FBT3dDOzRDQUNUOztvQ0FDRixPQUFPO3dDQUNMLGlEQUFpRDt3Q0FDakR0RzswRkFBaUJtRSxDQUFBQSxPQUFRQSxLQUFLSSxHQUFHO2tHQUFDb0IsQ0FBQUE7d0RBQ2hDLElBQUlBLEtBQUsxRCxFQUFFLEtBQUszRCxLQUFLd0YsY0FBYyxFQUFFOzREQUNuQyxPQUFPO2dFQUNMLEdBQUc2QixJQUFJO2dFQUNQWSxjQUFjWixLQUFLWSxZQUFZLENBQUM3QixNQUFNO2tIQUFDOEIsQ0FBQUEsSUFDcENBLEVBQUV6QyxNQUFNLEtBQUt6RixLQUFLK0gsYUFBYSxJQUFJRyxFQUFFQyxRQUFRLEtBQUtuSSxLQUFLK0gsYUFBYTs7NERBRXpFO3dEQUNGO3dEQUNBLE9BQU9WO29EQUNUOzs7b0NBQ0Y7Z0NBQ0Y7NEJBQ0Y7O3dCQUVBLG1EQUFtRDt3QkFDbkQvQyxlQUFlUyxFQUFFLENBQUM7MEVBQXFCLENBQUMvRTtnQ0FDdEN5QyxRQUFRUSxHQUFHLENBQUMsMkNBQTJDakQ7Z0NBQ3ZELElBQUlBLFFBQVFBLEtBQUt3RixjQUFjLElBQUl4RixLQUFLeUYsTUFBTSxNQUFLckUsaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRSxHQUFFO29DQUMzRCw4QkFBOEI7b0NBQzlCakM7c0ZBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTTs4RkFBQ1ksQ0FBQUEsSUFBS0EsRUFBRXJELEVBQUUsS0FBSzNELEtBQUt3RixjQUFjOzs7b0NBRXRFLGtDQUFrQztvQ0FDbEMsSUFBSTdELHVCQUF1QjNCLEtBQUt3RixjQUFjLEVBQUU7d0NBQzlDNUQsc0JBQXNCO29DQUN4QjtvQ0FFQSwrQkFBK0I7b0NBQy9CQztzRkFBWWdFLENBQUFBOzRDQUNWLE1BQU1tQyxVQUFVO2dEQUFFLEdBQUduQyxJQUFJOzRDQUFDOzRDQUMxQixPQUFPbUMsT0FBTyxDQUFDaEksS0FBS3dGLGNBQWMsQ0FBQzs0Q0FDbkMsT0FBT3dDO3dDQUNUOztvQ0FFQSw2Q0FBNkM7b0NBQzdDaEQsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVkseUJBQXlCO3dDQUM1REMsUUFBUTs0Q0FDTkMsTUFBTTs0Q0FDTmdELFFBQVE7NENBQ1I1QyxnQkFBZ0J4RixLQUFLd0YsY0FBYzs0Q0FDbkN2RixXQUFXNEQsS0FBS0MsR0FBRzt3Q0FDckI7b0NBQ0Y7Z0NBQ0Y7NEJBQ0Y7O3dCQUVBdkMsVUFBVStDO3dCQUVWLE9BQU9BO29CQUNULEVBQUUsT0FBTzVCLE9BQU87d0JBQ2RELFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0NBO3dCQUNoRGpCLGVBQWU7d0JBQ2ZtQixxQkFBcUJJLE9BQU8sR0FBRyxPQUFPLG9DQUFvQzt3QkFDMUUsT0FBTztvQkFDVDtnQkFDRjs7WUFFQSxzRUFBc0U7WUFDdEUsSUFBSXFGLDJCQUEyQjtZQUUvQix3Q0FBd0M7WUFDeEMsTUFBTUM7eURBQWlCO29CQUNyQixJQUFJRCwwQkFBMEI7d0JBQzVCNUYsUUFBUVEsR0FBRyxDQUFDO3dCQUNaO29CQUNGO29CQUVBb0YsMkJBQTJCO29CQUUzQixrRkFBa0Y7b0JBQ2xGckg7aUVBQVc7NEJBQ1QrQyxzQkFDR3dFLElBQUk7eUVBQUNDLENBQUFBO29DQUNKL0YsUUFBUVEsR0FBRyxDQUFDLHlDQUF5Q3VGLFNBQVMsWUFBWTtnQ0FDNUU7d0VBQ0NDLEtBQUs7eUVBQUMvRixDQUFBQTtvQ0FDTEQsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0E7b0NBQ2hERSxxQkFBcUJJLE9BQU8sR0FBRyxPQUFPLG9DQUFvQztnQ0FDNUU7d0VBQ0MwRixPQUFPO3lFQUFDO29DQUNQTCwyQkFBMkI7Z0NBQzdCOzt3QkFDSjtnRUFBRyxPQUFPLDBDQUEwQztnQkFDdEQ7O1lBRUEsbUNBQW1DO1lBQ25DQztZQUVBLHFGQUFxRjtZQUNyRjswQ0FBTztvQkFDTCwrRUFBK0U7b0JBQy9FLElBQUksQ0FBQ2xILE1BQU07d0JBQ1RxQixRQUFRUSxHQUFHLENBQUM7d0JBQ1osSUFBSTNCLFFBQVE7NEJBQ1YsSUFBSTtnQ0FDRkEsT0FBT3FCLFVBQVU7Z0NBQ2pCQyxxQkFBcUJJLE9BQU8sR0FBRyxPQUFPLHVDQUF1Qzs0QkFDL0UsRUFBRSxPQUFPTixPQUFPO2dDQUNkRCxRQUFRQyxLQUFLLENBQUMsK0JBQStCQTs0QkFDL0M7d0JBQ0Y7b0JBQ0YsT0FBTzt3QkFDTEQsUUFBUVEsR0FBRyxDQUFDO29CQUNkO2dCQUNGOztRQUNGLG9FQUFvRTtRQUNwRSx1REFBdUQ7UUFDdkQ7aUNBQUc7UUFBQzdCO1FBQU1pQjtRQUFpQkc7S0FBZ0I7SUFFM0MsMENBQTBDO0lBQzFDLE1BQU1zRixvQkFBb0J6SSxrREFBV0E7dURBQUM7Z0JBQU9zSixnRkFBZTtZQUMxRGxHLFFBQVFRLEdBQUcsQ0FBQyxnREFBZ0QwRjtZQUM1RCxJQUFJO2dCQUNGLHFDQUFxQztnQkFDckMsSUFBSSxDQUFDdkgsTUFBTTtvQkFDVHFCLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPLEVBQUU7Z0JBQ1g7Z0JBRUEsMENBQTBDO2dCQUMxQ0QsUUFBUVEsR0FBRyxDQUFDLDhCQUE4QjdCLEtBQUt3QyxJQUFJLElBQUk7Z0JBRXZELGtDQUFrQztnQkFDbEMsSUFBSXpCLGFBQWFVLGFBQWFHLE9BQU8sRUFBRTtvQkFDckNQLFFBQVFRLEdBQUcsQ0FBQztvQkFDWixPQUFPbEQ7Z0JBQ1Q7Z0JBRUEsNkVBQTZFO2dCQUM3RSxJQUFJLENBQUM0SSxnQkFBZ0I1SSxjQUFjc0QsTUFBTSxHQUFHLEdBQUc7b0JBQzdDWixRQUFRUSxHQUFHLENBQUM7b0JBQ1osT0FBT2xEO2dCQUNUO2dCQUVBLDBEQUEwRDtnQkFDMUQ4QyxhQUFhRyxPQUFPLEdBQUc7Z0JBRXZCLG9EQUFvRDtnQkFDcEQsTUFBTWMsTUFBTUQsS0FBS0MsR0FBRztnQkFDcEIsSUFBSSxDQUFDNkUsZ0JBQ0Q3SSxhQUFhQyxhQUFhLENBQUNDLElBQUksSUFDL0JGLGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSSxDQUFDcUQsTUFBTSxHQUFHLEtBQ3pDUyxNQUFNaEUsYUFBYUMsYUFBYSxDQUFDRSxTQUFTLEdBQUdLLGtCQUFrQjtvQkFDakVtQyxRQUFRUSxHQUFHLENBQUM7b0JBQ1osT0FBT25ELGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSTtnQkFDeEM7Z0JBRUF5QyxRQUFRUSxHQUFHLENBQUM7Z0JBRVosTUFBTWUsZUFBZTNCO2dCQUNyQkksUUFBUVEsR0FBRyxDQUFDLGdDQUFnQ2UsZUFBZSxlQUFlO2dCQUMxRSxJQUFJLENBQUNBLGNBQWM7b0JBQ2pCdkIsUUFBUUMsS0FBSyxDQUFDO29CQUNkLE9BQU8sRUFBRTtnQkFDWDtnQkFFQU4sYUFBYTtnQkFFYixJQUFJO29CQUNGSyxRQUFRUSxHQUFHLENBQUM7b0JBQ1pSLFFBQVFRLEdBQUcsQ0FBQyw0QkFBNEIsR0FBVyxPQUFSdkQsU0FBUTtvQkFDbkQsTUFBTWtKLFdBQVcsTUFBTUMsTUFBTSxHQUFXLE9BQVJuSixTQUFRLDZDQUEyQzt3QkFDakZvSixTQUFTOzRCQUFFQyxlQUFlLFVBQXVCLE9BQWIvRTt3QkFBZTtvQkFDckQ7b0JBQ0F2QixRQUFRUSxHQUFHLENBQUMsdUJBQXVCMkYsU0FBU3ZELE1BQU0sRUFBRXVELFNBQVNJLFVBQVU7b0JBRXZFLElBQUksQ0FBQ0osU0FBU0ssRUFBRSxFQUFFO3dCQUNoQixJQUFJTCxTQUFTdkQsTUFBTSxLQUFLLEtBQUs7NEJBQzNCNUMsUUFBUUMsS0FBSyxDQUFDOzRCQUNkRjs0QkFDQSxPQUFPLEVBQUU7d0JBQ1g7d0JBQ0EsTUFBTSxJQUFJMEcsTUFBTSwrQkFBK0MsT0FBaEJOLFNBQVN2RCxNQUFNO29CQUNoRTtvQkFFQSxNQUFNckYsT0FBTyxNQUFNNEksU0FBU08sSUFBSTtvQkFDaEMxRyxRQUFRUSxHQUFHLENBQUMsaUNBQWlDakQ7b0JBRTdDLElBQUlBLEtBQUtvSixPQUFPLEVBQUU7NEJBS1dwSjt3QkFKM0IsZ0NBQWdDO3dCQUNoQyx3RkFBd0Y7d0JBQ3hGeUMsUUFBUVEsR0FBRyxDQUFDLDBDQUEwQ29HLEtBQUtDLFNBQVMsQ0FBQ3RKO3dCQUVyRSxNQUFNNEgscUJBQXFCNUgsRUFBQUEsYUFBQUEsS0FBS0EsSUFBSSxjQUFUQSxpQ0FBQUEsV0FBV0QsYUFBYSxLQUFJLEVBQUU7d0JBQ3pEMEMsUUFBUVEsR0FBRyxDQUFDLGdDQUFnQzJFO3dCQUU1QyxJQUFJLENBQUNKLE1BQU1DLE9BQU8sQ0FBQ0cscUJBQXFCOzRCQUN0Q25GLFFBQVFDLEtBQUssQ0FBQyxxREFBcUQxQzs0QkFDbkUsT0FBTyxFQUFFO3dCQUNYO3dCQUVBeUMsUUFBUVEsR0FBRyxDQUFDLGFBQXVDLE9BQTFCMkUsbUJBQW1CdkUsTUFBTSxFQUFDO3dCQUVuRCwrQ0FBK0M7d0JBQy9DWixRQUFRUSxHQUFHLENBQUM7d0JBQ1osTUFBTXNHLHlCQUF5QjNCLG1CQUFtQjNCLEdBQUc7a0dBQUNhLENBQUFBO2dDQUNwRCx3Q0FBd0M7Z0NBQ3hDLElBQUlBLGFBQWEzRyxRQUFRLElBQUkyRyxhQUFhM0csUUFBUSxDQUFDa0QsTUFBTSxHQUFHLEdBQUc7b0NBQzdEWixRQUFRUSxHQUFHLENBQUMsWUFBbUM2RCxPQUF2QkEsYUFBYW5ELEVBQUUsRUFBQyxTQUFvQyxPQUE3Qm1ELGFBQWEzRyxRQUFRLENBQUNrRCxNQUFNLEVBQUM7b0NBQzVFLDRCQUE0QjtvQ0FDNUIsTUFBTTRELGNBQWNILGFBQWEzRyxRQUFRLENBQUMsRUFBRSxFQUFFLHFFQUFxRTtvQ0FDbkhzQyxRQUFRUSxHQUFHLENBQUMsb0JBQW9CZ0U7b0NBRWhDLHdFQUF3RTtvQ0FDeEUsTUFBTXVDLGlCQUFpQjsyQ0FBSTFDLGFBQWEzRyxRQUFRO3FDQUFDLENBQUN3RyxJQUFJOzZIQUFDLENBQUNDLEdBQUdDLElBQ3pELElBQUloRCxLQUFLK0MsRUFBRU4sU0FBUyxJQUFJLElBQUl6QyxLQUFLZ0QsRUFBRVAsU0FBUzs7b0NBRTlDN0QsUUFBUVEsR0FBRyxDQUFDLG1CQUFrRTZELE9BQS9DMEMsZUFBZW5HLE1BQU0sRUFBQywyQkFBeUMsT0FBaEJ5RCxhQUFhbkQsRUFBRSxHQUFJNkY7b0NBRWpHLGlDQUFpQztvQ0FDakMzSDs4R0FBWWdFLENBQUFBLE9BQVM7Z0RBQ25CLEdBQUdBLElBQUk7Z0RBQ1AsQ0FBQ2lCLGFBQWFuRCxFQUFFLENBQUMsRUFBRTZGOzRDQUNyQjs7b0NBRUEsc0RBQXNEO29DQUN0RCxNQUFNLEVBQUVySixRQUFRLEVBQUUsR0FBR3NKLDZCQUE2QixHQUFHM0M7b0NBRXJELDJEQUEyRDtvQ0FDM0QsT0FBTzt3Q0FDTCxHQUFHMkMsMkJBQTJCO3dDQUM5QnhDO29DQUNGO2dDQUNGLE9BQU87b0NBQ0x4RSxRQUFRUSxHQUFHLENBQUMsWUFBNEIsT0FBaEI2RCxhQUFhbkQsRUFBRSxFQUFDO2dDQUMxQztnQ0FFQSxPQUFPbUQ7NEJBQ1Q7O3dCQUVBLHlDQUF5Qzt3QkFDekNyRSxRQUFRUSxHQUFHLENBQUMsMEJBQTBCc0csdUJBQXVCdEQsR0FBRzsyRUFBQ2UsQ0FBQUE7b0NBRWpEQTt1Q0FGdUQ7b0NBQ3JFckQsSUFBSXFELEVBQUVyRCxFQUFFO29DQUNSc0UsWUFBWSxHQUFFakIsa0JBQUFBLEVBQUVpQixZQUFZLGNBQWRqQixzQ0FBQUEsZ0JBQWdCZixHQUFHO3VGQUFDaUMsQ0FBQUEsSUFBTTtnREFDdEN6QyxRQUFReUMsRUFBRXpDLE1BQU07Z0RBQ2hCMEMsVUFBVUQsRUFBRUMsUUFBUTtnREFDcEIvRyxNQUFNOEcsRUFBRTlHLElBQUk7Z0RBQ1pzSSxRQUFReEIsRUFBRXdCLE1BQU07NENBQ2xCOztnQ0FDRjs7O3dCQUVBLCtDQUErQzt3QkFDL0MsTUFBTTVGLE1BQU1ELEtBQUtDLEdBQUc7d0JBQ3BCaEUsYUFBYUMsYUFBYSxDQUFDQyxJQUFJLEdBQUd1Sjt3QkFDbEN6SixhQUFhQyxhQUFhLENBQUNFLFNBQVMsR0FBRzZEO3dCQUV2Qyw4RkFBOEY7d0JBQzlGcEMsaUJBQWlCNkg7d0JBRWpCLGlFQUFpRTt3QkFDakV2RSxPQUFPQyxhQUFhLENBQUMsSUFBSUMsWUFBWSx5QkFBeUI7NEJBQzVEQyxRQUFRO2dDQUFFQyxNQUFNO2dDQUFpQm5GLFdBQVc0RCxLQUFLQyxHQUFHOzRCQUFHO3dCQUN6RDt3QkFFQSxPQUFPOEQ7b0JBQ1QsT0FBTzt3QkFDTG5GLFFBQVFDLEtBQUssQ0FBQyx5Q0FBeUMxQzt3QkFDdkQsT0FBTyxFQUFFO29CQUNYO2dCQUNGLEVBQUUsT0FBTzBDLE9BQU87b0JBQ2RELFFBQVFDLEtBQUssQ0FBQyxnQ0FBZ0NBO29CQUM5QyxPQUFPLEVBQUU7Z0JBQ1gsU0FBVTtvQkFDUk4sYUFBYTtvQkFDYlMsYUFBYUcsT0FBTyxHQUFHO2dCQUN6QjtZQUNGLEVBQUUsT0FBT04sT0FBTztnQkFDZEQsUUFBUUMsS0FBSyxDQUFDLCtCQUErQkE7Z0JBQzdDLE9BQU8sRUFBRTtZQUNYO1FBQ0YsdUVBQXVFO1FBQ3ZFLHVEQUF1RDtRQUN2RDtzREFBRztRQUFDdEI7UUFBTWlCO1FBQWlCRztRQUFpQkw7S0FBVTtJQUV0RCwwREFBMEQ7SUFDMUQsTUFBTXdILGVBQWV0SyxrREFBV0E7a0RBQUMsT0FBT21HO1lBQ3RDLElBQUk7b0JBeUJFMUY7Z0JBeEJKLHFDQUFxQztnQkFDckMsSUFBSSxDQUFDc0IsTUFBTTtvQkFDVHFCLFFBQVFDLEtBQUssQ0FBQztvQkFDZDtnQkFDRjtnQkFFQSwrQkFBK0I7Z0JBQy9CLE1BQU1vQixNQUFNRCxLQUFLQyxHQUFHO2dCQUNwQixJQUFJaEUsYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxJQUNyQzFCLE1BQU1oRSxhQUFhSyxRQUFRLENBQUNxRixlQUFlLENBQUN2RixTQUFTLEdBQUdLLGtCQUFrQjtvQkFDNUVtQyxRQUFRUSxHQUFHLENBQUMsNENBQTRDdUM7b0JBQ3hELE9BQU8xRixhQUFhSyxRQUFRLENBQUNxRixlQUFlLENBQUN4RixJQUFJO2dCQUNuRDtnQkFFQSw4REFBOEQ7Z0JBQzlELGlHQUFpRztnQkFFakcsTUFBTWdFLGVBQWUzQjtnQkFDckIsSUFBSSxDQUFDMkIsY0FBYztvQkFDakJ2QixRQUFRQyxLQUFLLENBQUM7b0JBQ2Q7Z0JBQ0Y7Z0JBRUEsaUVBQWlFO2dCQUNqRSxLQUFJNUMsd0NBQUFBLGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsY0FBckMxRiw0REFBQUEsc0NBQXVDOEosT0FBTyxFQUFFO29CQUNsRG5ILFFBQVFRLEdBQUcsQ0FBQztvQkFDWjtnQkFDRjtnQkFFQSx5QkFBeUI7Z0JBQ3pCbkQsYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxHQUFHO29CQUFFb0UsU0FBUztnQkFBSztnQkFFeEQsSUFBSTtvQkFDRm5ILFFBQVFRLEdBQUcsQ0FBQyw0Q0FBNEN1QztvQkFDeEQsTUFBTW9ELFdBQVcsTUFBTUMsTUFBTSxHQUFpQ3JELE9BQTlCOUYsU0FBUSx3QkFBcUMsT0FBZjhGLGdCQUFlLGNBQVk7d0JBQ3ZGc0QsU0FBUzs0QkFBRUMsZUFBZSxVQUF1QixPQUFiL0U7d0JBQWU7b0JBQ3JEO29CQUVBLElBQUksQ0FBQzRFLFNBQVNLLEVBQUUsRUFBRTt3QkFDaEIsSUFBSUwsU0FBU3ZELE1BQU0sS0FBSyxLQUFLOzRCQUMzQjVDLFFBQVFDLEtBQUssQ0FBQzs0QkFDZEY7NEJBQ0E7d0JBQ0Y7d0JBQ0EsTUFBTSxJQUFJMEcsTUFBTSwrQkFBK0MsT0FBaEJOLFNBQVN2RCxNQUFNO29CQUNoRTtvQkFFQSxNQUFNckYsT0FBTyxNQUFNNEksU0FBU08sSUFBSTtvQkFFaEMsSUFBSW5KLEtBQUtvSixPQUFPLEVBQUU7d0JBQ2hCLDRDQUE0Qzt3QkFDNUMzRyxRQUFRUSxHQUFHLENBQUMsNkNBQTZDdUMsZ0JBQWdCeEYsS0FBS0EsSUFBSTt3QkFFbEYsa0JBQWtCO3dCQUNsQkYsYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxHQUFHOzRCQUN0Q3hGLE1BQU1BLEtBQUtBLElBQUk7NEJBQ2ZDLFdBQVc2RDs0QkFDWDhGLFNBQVM7d0JBQ1g7d0JBRUEsaUNBQWlDO3dCQUNqQyw2RUFBNkU7d0JBQzdFLDJEQUEyRDt3QkFDM0QsTUFBTUosaUJBQWlCOytCQUFJeEosS0FBS0EsSUFBSTt5QkFBQyxDQUFDMkcsSUFBSTtxRkFBQyxDQUFDQyxHQUFHQyxJQUM3QyxJQUFJaEQsS0FBSytDLEVBQUVOLFNBQVMsSUFBSSxJQUFJekMsS0FBS2dELEVBQUVQLFNBQVM7O3dCQUc5QzdELFFBQVFRLEdBQUcsQ0FBQyw4QkFBOEJ1Rzt3QkFFMUMzSDtzRUFBWWdFLENBQUFBLE9BQVM7b0NBQ25CLEdBQUdBLElBQUk7b0NBQ1AsQ0FBQ0wsZUFBZSxFQUFFZ0U7Z0NBQ3BCOzt3QkFFQSwwQ0FBMEM7d0JBQzFDLElBQUl4SixLQUFLQSxJQUFJLElBQUlBLEtBQUtBLElBQUksQ0FBQ3FELE1BQU0sR0FBRyxHQUFHOzRCQUNyQyxNQUFNNEQsY0FBY2pILEtBQUtBLElBQUksQ0FBQ0EsS0FBS0EsSUFBSSxDQUFDcUQsTUFBTSxHQUFHLEVBQUU7NEJBQ25EM0I7MEVBQWlCbUUsQ0FBQUE7b0NBQ2YsT0FBT0EsS0FBS0ksR0FBRztrRkFBQ29CLENBQUFBOzRDQUNkLElBQUlBLEtBQUsxRCxFQUFFLEtBQUs2QixrQkFBbUIsRUFBQzZCLEtBQUtKLFdBQVcsSUFBSSxJQUFJcEQsS0FBS29ELFlBQVlYLFNBQVMsSUFBSSxJQUFJekMsS0FBS3dELEtBQUtKLFdBQVcsQ0FBQ1gsU0FBUyxJQUFJO2dEQUMvSCxPQUFPO29EQUFFLEdBQUdlLElBQUk7b0RBQUVKO2dEQUFZOzRDQUNoQzs0Q0FDQSxPQUFPSTt3Q0FDVDs7Z0NBQ0Y7O3dCQUNGO3dCQUVBLE9BQU9ySCxLQUFLQSxJQUFJO29CQUNsQjtnQkFDRixFQUFFLE9BQU8wQyxPQUFPO29CQUNkRCxRQUFRQyxLQUFLLENBQUMsMkJBQTJCQTtnQkFDM0MsU0FBVTt3QkFFSjVDO29CQURKLCtDQUErQztvQkFDL0MsS0FBSUEseUNBQUFBLGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsY0FBckMxRiw2REFBQUEsdUNBQXVDOEosT0FBTyxFQUFFO3dCQUNsRDlKLGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsQ0FBQ29FLE9BQU8sR0FBRztvQkFDbEQ7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9sSCxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsMEJBQTBCQTtZQUMxQztRQUNGLGtFQUFrRTtRQUNsRSx1REFBdUQ7UUFDdkQ7aURBQUc7UUFBQ3RCO1FBQU1pQjtRQUFpQkc7S0FBZ0I7SUFFM0Msa0JBQWtCO0lBQ2xCLE1BQU1xSCxjQUFjeEssa0RBQVdBO2lEQUFDLFNBQUNtRyxnQkFBZ0JnQjtnQkFBU0QsK0VBQWMsUUFBUXVELDRFQUFXO1lBQ3pGLElBQUksQ0FBQ3hJLFVBQVUsQ0FBQ0UsYUFBYTtZQUU3Qix5Q0FBeUM7WUFDekMsTUFBTWtFLFNBQVMsUUFBbUIsT0FBWDdCLEtBQUtDLEdBQUc7WUFFL0IsMERBQTBEO1lBQzFELE1BQU0yQyxjQUFjO2dCQUNsQjlDLElBQUkrQjtnQkFDSkY7Z0JBQ0FnQjtnQkFDQUQ7Z0JBQ0F1RDtnQkFDQWxFLFVBQVV4RSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0ySSxRQUFRLEtBQUkzSSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU13QyxJQUFJLE1BQUssV0FBVyxPQUFPeEMsaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRTtnQkFDckV1QyxnQkFBZ0I5RSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0ySSxRQUFRLEtBQUkzSSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU13QyxJQUFJLE1BQUssV0FBV3hDLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUUsR0FBRztnQkFDdkUyQyxXQUFXLElBQUl6QyxPQUFPbUcsV0FBVztnQkFDakNDLFFBQVE3SSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0ySSxRQUFRLEtBQUkzSSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU13QyxJQUFJLE1BQUssV0FBVyxPQUFPO29CQUFFRCxFQUFFLEVBQUV2QyxpQkFBQUEsMkJBQUFBLEtBQU11QyxFQUFFO29CQUFFVCxRQUFRLEVBQUU5QixpQkFBQUEsMkJBQUFBLEtBQU04QixRQUFRO2dCQUFDO2dCQUNwR2dILGNBQWM5SSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0ySSxRQUFRLEtBQUkzSSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU13QyxJQUFJLE1BQUssV0FBVztvQkFBRUQsRUFBRSxFQUFFdkMsaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRTtvQkFBRVQsVUFBVTlCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTThCLFFBQVEsTUFBSTlCLGlCQUFBQSwyQkFBQUEsS0FBTStJLEtBQUs7b0JBQUVBLEtBQUssRUFBRS9JLGlCQUFBQSwyQkFBQUEsS0FBTStJLEtBQUs7Z0JBQUMsSUFBSTtnQkFDMUksZ0RBQWdEO2dCQUNoRDlELFFBQVE7WUFDVjtZQUVBLGlEQUFpRDtZQUNqRHhFO3lEQUFZZ0UsQ0FBQUEsT0FBUzt3QkFDbkIsR0FBR0EsSUFBSTt3QkFDUCxDQUFDTCxlQUFlLEVBQUU7K0JBQUtLLElBQUksQ0FBQ0wsZUFBZSxJQUFJLEVBQUU7NEJBQUdpQjt5QkFBWTtvQkFDbEU7O1lBRUEsNkNBQTZDO1lBQzdDL0U7eURBQWlCbUUsQ0FBQUE7b0JBQ2YsT0FBT0EsS0FBS0ksR0FBRztpRUFBQ29CLENBQUFBOzRCQUNkLElBQUlBLEtBQUsxRCxFQUFFLEtBQUs2QixnQkFBZ0I7Z0NBQzlCLE9BQU87b0NBQUUsR0FBRzZCLElBQUk7b0NBQUVKLGFBQWFSO2dDQUFZOzRCQUM3Qzs0QkFDQSxPQUFPWTt3QkFDVDs7Z0JBQ0Y7O1lBRUEsa0NBQWtDO1lBQ2xDLE1BQU0rQyxjQUFjO2dCQUNsQjVFO2dCQUNBZ0I7Z0JBQ0FEO2dCQUNBdUQ7Z0JBQ0FwRTtZQUNGO1lBR0FwRSxPQUFPK0ksSUFBSSxDQUFDLGdCQUFnQkQ7eURBQWEsQ0FBQ3hCO29CQUN4QyxJQUFJQSxTQUFTUSxPQUFPLEVBQUU7b0JBQ3BCLHVFQUF1RTtvQkFDdkUsNEVBQTRFO29CQUM5RSxPQUFPO3dCQUNMM0csUUFBUUMsS0FBSyxDQUFDLDBCQUEwQmtHLFNBQVNsRyxLQUFLO3dCQUN0RCx3REFBd0Q7d0JBQ3hEYjtxRUFBWWdFLENBQUFBO2dDQUNWLE1BQU1DLHVCQUF1QkQsSUFBSSxDQUFDTCxlQUFlLElBQUksRUFBRTtnQ0FDdkQsT0FBTztvQ0FDTCxHQUFHSyxJQUFJO29DQUNQLENBQUNMLGVBQWUsRUFBRU0scUJBQXFCRyxHQUFHO2lGQUFDcUUsQ0FBQUEsTUFDekNBLElBQUkzRyxFQUFFLEtBQUsrQixTQUFTO2dEQUFFLEdBQUc0RSxHQUFHO2dEQUFFQyxRQUFROzRDQUFLLElBQUlEOztnQ0FFbkQ7NEJBQ0Y7O29CQUNGO2dCQUNGOztRQUNGO2dEQUFHO1FBQUNoSjtRQUFRRTtRQUFhSjtLQUFLO0lBRTlCLHNCQUFzQjtJQUN0QixNQUFNb0oscUJBQXFCbkwsa0RBQVdBO3dEQUFDLGVBQU9vTDtnQkFBZ0JDLHlFQUFRO1lBQ3BFLElBQUk7Z0JBQ0ZqSSxRQUFRUSxHQUFHLENBQUMsdUNBQXVDd0g7Z0JBQ25EaEksUUFBUVEsR0FBRyxDQUFDLHFCQUFxQndILGVBQWVwSCxNQUFNLEdBQUcsSUFBSSxVQUFVO2dCQUN2RVosUUFBUVEsR0FBRyxDQUFDLHVCQUF1QnlIO2dCQUVuQyxzRkFBc0Y7Z0JBQ3RGLElBQUl0SixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU13QyxJQUFJLE1BQUssa0JBQWtCNkcsZUFBZXBILE1BQU0sR0FBRyxHQUFHO29CQUM5RFosUUFBUVEsR0FBRyxDQUFDO29CQUNaLDZEQUE2RDtvQkFDN0QsTUFBTWUsZUFBZTNCO29CQUNyQixJQUFJMkIsY0FBYzt3QkFDaEIsSUFBSTs0QkFDRixNQUFNMkcsc0JBQXNCRixlQUFleEUsR0FBRztvR0FBQyxPQUFPdEM7b0NBQ3BELE1BQU1pRixXQUFXLE1BQU1DLE1BQU0sR0FBb0JsRixPQUFqQmpFLFNBQVEsV0FBWSxPQUFIaUUsS0FBTTt3Q0FDckRtRixTQUFTOzRDQUFFQyxlQUFlLFVBQXVCLE9BQWIvRTt3Q0FBZTtvQ0FDckQ7b0NBQ0EsSUFBSTRFLFNBQVNLLEVBQUUsRUFBRTt3Q0FDZixNQUFNMkIsV0FBVyxNQUFNaEMsU0FBU08sSUFBSTt3Q0FDcEMsT0FBT3lCLFNBQVN4QixPQUFPLEdBQUd3QixTQUFTNUssSUFBSSxHQUFHNEs7b0NBQzVDO29DQUNBLE9BQU87Z0NBQ1Q7OzRCQUVBLE1BQU0zQyxlQUFlLE1BQU00QyxRQUFRQyxHQUFHLENBQUNIOzRCQUN2Q2xJLFFBQVFRLEdBQUcsQ0FBQyw2QkFBNkJnRjs0QkFDekMsTUFBTThDLG9CQUFvQjlDLGFBQWE3QixNQUFNO2tHQUFDOEIsQ0FBQUEsSUFBS0EsS0FBS0EsRUFBRThDLFNBQVM7OzRCQUNuRXZJLFFBQVFRLEdBQUcsQ0FBQywwQkFBMEI4SDs0QkFFdEMsSUFBSUEsa0JBQWtCMUgsTUFBTSxHQUFHLEdBQUc7Z0NBQ2hDLE1BQU00SCxZQUFZO3VDQUFJLElBQUlDLElBQUlILGtCQUFrQjlFLEdBQUc7d0ZBQUNpQyxDQUFBQSxJQUFLQSxFQUFFOEMsU0FBUzs7aUNBQUc7Z0NBQ3ZFdkksUUFBUVEsR0FBRyxDQUFDLHlCQUF5QmdJO2dDQUNyQyxJQUFJQSxVQUFVNUgsTUFBTSxHQUFHLEdBQUc7b0NBQ3hCOEgsTUFBTTtvQ0FDTixPQUFPO2dDQUNUOzRCQUNGO3dCQUNGLEVBQUUsT0FBT0MsaUJBQWlCOzRCQUN4QjNJLFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0MwSTs0QkFDaERELE1BQU0sd0JBQXdCQyxnQkFBZ0I5RixPQUFPOzRCQUNyRCxPQUFPO3dCQUNUO29CQUNGO2dCQUNGO2dCQUVBLDRDQUE0QztnQkFDNUMsTUFBTStGLHdCQUF3QlosZUFBZTFFLElBQUk7MEZBQUNwQyxDQUFBQTt3QkFDaEQseUZBQXlGO3dCQUN6RixPQUFPLE9BQU9BLE9BQU8sWUFBWUEsR0FBR29HLFFBQVE7b0JBQzlDOztnQkFFQXRILFFBQVFRLEdBQUcsQ0FBQywrQkFBK0JvSTtnQkFFM0MsNkNBQTZDO2dCQUM3QyxNQUFNckgsZUFBZTNCO2dCQUNyQkksUUFBUVEsR0FBRyxDQUFDLHFCQUFxQmUsZUFBZSxRQUFRO2dCQUV4RCxJQUFJLENBQUNBLGNBQWM7b0JBQ2pCdkIsUUFBUUMsS0FBSyxDQUFDO29CQUNkLE9BQU87Z0JBQ1Q7Z0JBRUEsTUFBTWtHLFdBQVcsTUFBTUMsTUFBTSxHQUFXLE9BQVJuSixTQUFRLHdCQUFzQjtvQkFDNUQ0TCxRQUFRO29CQUNSeEMsU0FBUzt3QkFDUCxnQkFBZ0I7d0JBQ2hCQyxlQUFlLFVBQXVCLE9BQWIvRTtvQkFDM0I7b0JBQ0F1SCxNQUFNbEMsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQmxFLE1BQU1xRixlQUFlcEgsTUFBTSxHQUFHLElBQUksVUFBVTt3QkFDNUNxSDt3QkFDQUQsZ0JBQWdCQSxlQUFleEUsR0FBRzs0RUFBQ3RDLENBQUFBLEtBQU0sT0FBT0EsT0FBTyxXQUFXQSxHQUFHQSxFQUFFLEdBQUdBOzt3QkFDMUU2SCxnQkFBZ0IsS0FBSywwQ0FBMEM7b0JBQ2pFO2dCQUNGO2dCQUVBL0ksUUFBUVEsR0FBRyxDQUFDLG9CQUFvQjJGLFNBQVN2RCxNQUFNLEVBQUV1RCxTQUFTSSxVQUFVO2dCQUNwRSxNQUFNaEosT0FBTyxNQUFNNEksU0FBU08sSUFBSTtnQkFDaEMxRyxRQUFRUSxHQUFHLENBQUMsc0JBQXNCakQ7Z0JBRWxDLElBQUlBLEtBQUtvSixPQUFPLEVBQUU7b0JBQ2hCLHNDQUFzQztvQkFDdEMsTUFBTXFDLG1CQUFtQnpMLEtBQUtBLElBQUk7b0JBQ2xDeUMsUUFBUVEsR0FBRyxDQUFDLGdDQUFnQ3dJO29CQUU1QyxvQ0FBb0M7b0JBQ3BDL0o7d0VBQWlCbUUsQ0FBQUE7NEJBQ2Ysb0VBQW9FOzRCQUNwRSxJQUFJQSxLQUFLRSxJQUFJO2dGQUFDaUIsQ0FBQUEsSUFBS0EsRUFBRXJELEVBQUUsS0FBSzhILGlCQUFpQjlILEVBQUU7Z0ZBQUc7Z0NBQ2hELE9BQU9rQzs0QkFDVDs0QkFDQSxPQUFPO2dDQUFDNEY7bUNBQXFCNUY7NkJBQUs7d0JBQ3BDOztvQkFFQSxtRUFBbUU7b0JBQ25FN0U7d0VBQVc7NEJBQ1RnRSxPQUFPQyxhQUFhLENBQUMsSUFBSUMsWUFBWSx5QkFBeUI7Z0NBQzVEQyxRQUFRO29DQUNOQyxNQUFNO29DQUNOSSxnQkFBZ0JpRyxpQkFBaUI5SCxFQUFFO29DQUNuQzFELFdBQVc0RCxLQUFLQyxHQUFHO2dDQUNyQjs0QkFDRjt3QkFDRjt1RUFBRztvQkFFSCxPQUFPMkg7Z0JBQ1QsT0FBTztvQkFDTGhKLFFBQVFDLEtBQUssQ0FBQywyQkFBMkIxQyxLQUFLMEMsS0FBSyxJQUFJO29CQUl2RCx5REFBeUQ7b0JBQ3pELElBQUlrRyxTQUFTdkQsTUFBTSxLQUFLLEtBQUs7d0JBQzNCNUMsUUFBUUMsS0FBSyxDQUFDO3dCQUNkRjtvQkFDRjtnQkFDRjtnQkFFQSxPQUFPO1lBQ1QsRUFBRSxPQUFPRSxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsZ0NBQWdDQTtnQkFDOUMsT0FBTztZQUNUO1FBQ0Y7dURBQUc7UUFBQ0Y7S0FBZ0I7SUFFcEIsb0RBQW9EO0lBQ3BELE1BQU1rSiwwQkFBMEJyTSxrREFBV0E7NkRBQUMsT0FBT3NNO1lBQ2pELElBQUk7Z0JBQ0ZsSixRQUFRUSxHQUFHLENBQUMsZ0RBQWdEMEk7Z0JBRTVELElBQUksQ0FBQ0EsYUFBYSxDQUFDQSxVQUFVaEksRUFBRSxFQUFFO29CQUMvQmxCLFFBQVFDLEtBQUssQ0FBQyxxQkFBcUJpSjtvQkFDbkMsT0FBTztnQkFDVDtnQkFFQSw0RUFBNEU7Z0JBQzVFLE1BQU1DLHVCQUF1QjdMLGNBQWNnSCxJQUFJOzhGQUFDTSxDQUFBQTs0QkFHdkNBO3dCQUZQLElBQUlBLEtBQUtqQyxJQUFJLEtBQUssY0FBYyxPQUFPO3dCQUV2QyxRQUFPaUMscUJBQUFBLEtBQUtZLFlBQVksY0FBakJaLHlDQUFBQSxtQkFBbUJ0QixJQUFJO3NHQUFDbUMsQ0FBQUEsSUFBS0EsRUFBRXpDLE1BQU0sS0FBS2tHLFVBQVVoSSxFQUFFOztvQkFDL0Q7O2dCQUVBLElBQUlpSSxzQkFBc0I7b0JBQ3hCbkosUUFBUVEsR0FBRyxDQUFDLGtDQUFrQzJJO29CQUM5Q2hLLHNCQUFzQmdLLHFCQUFxQmpJLEVBQUU7b0JBQzdDLE9BQU9pSTtnQkFDVDtnQkFFQW5KLFFBQVFRLEdBQUcsQ0FBQyxzQ0FBc0MwSSxVQUFVekksUUFBUTtnQkFDcEUsMENBQTBDO2dCQUMxQyxNQUFNMkksa0JBQWtCLE1BQU1yQixtQkFBbUI7b0JBQUNtQixVQUFVaEksRUFBRTtpQkFBQztnQkFFL0QsSUFBSWtJLGlCQUFpQjtvQkFDbkJwSixRQUFRUSxHQUFHLENBQUMscUNBQXFDNEk7b0JBQ2pELDhFQUE4RTtvQkFDOUVuSzs2RUFBaUJtRSxDQUFBQTs0QkFDZixvRUFBb0U7NEJBQ3BFLElBQUlBLEtBQUtFLElBQUk7cUZBQUNpQixDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLa0ksZ0JBQWdCbEksRUFBRTtxRkFBRztnQ0FDL0MsT0FBT2tDOzRCQUNUOzRCQUNBLE9BQU87Z0NBQUNnRzttQ0FBb0JoRzs2QkFBSzt3QkFDbkM7O29CQUVBLCtGQUErRjtvQkFDL0Y3RTs2RUFBVzs0QkFDVFksc0JBQXNCaUssZ0JBQWdCbEksRUFBRTs0QkFFeEMsbUVBQW1FOzRCQUNuRXFCLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHlCQUF5QjtnQ0FDNURDLFFBQVE7b0NBQ05DLE1BQU07b0NBQ05JLGdCQUFnQnFHLGdCQUFnQmxJLEVBQUU7b0NBQ2xDMUQsV0FBVzRELEtBQUtDLEdBQUc7Z0NBQ3JCOzRCQUNGO3dCQUNGOzRFQUFHO2dCQUNMLE9BQU87b0JBQ0xyQixRQUFRQyxLQUFLLENBQUM7Z0JBQ2hCO2dCQUVBLE9BQU9tSjtZQUNULEVBQUUsT0FBT25KLE9BQU87Z0JBQ2RELFFBQVFDLEtBQUssQ0FBQywyQ0FBMkNBO2dCQUN6RCxPQUFPO1lBQ1Q7UUFDRix1RUFBdUU7UUFDdkUsdURBQXVEO1FBQ3ZEOzREQUFHO1FBQUM4SDtRQUFvQjVJO1FBQXVCWTtLQUFnQjtJQUkvRCxxREFBcUQ7SUFDckQsTUFBTXNKLGtCQUFrQnpNLGtEQUFXQTtxREFBQztnQkFBT3NKLGdGQUFlO1lBQ3hELElBQUk7Z0JBQ0YscUNBQXFDO2dCQUNyQyxJQUFJLENBQUN2SCxNQUFNO29CQUNUcUIsUUFBUUMsS0FBSyxDQUFDO29CQUNkO2dCQUNGO2dCQUVBLGtCQUFrQjtnQkFDbEIsTUFBTW9CLE1BQU1ELEtBQUtDLEdBQUc7Z0JBQ3BCLElBQUksQ0FBQzZFLGdCQUNEN0ksYUFBYUksV0FBVyxDQUFDRixJQUFJLEtBQUssUUFDbEM4RCxNQUFNaEUsYUFBYUksV0FBVyxDQUFDRCxTQUFTLEdBQUdLLGtCQUFrQjtvQkFDL0RtQyxRQUFRUSxHQUFHLENBQUM7b0JBQ1osT0FBT25ELGFBQWFJLFdBQVcsQ0FBQ0YsSUFBSTtnQkFDdEM7Z0JBRUEsTUFBTWdFLGVBQWUzQjtnQkFDckIsSUFBSSxDQUFDMkIsY0FBYztvQkFDakJ2QixRQUFRQyxLQUFLLENBQUM7b0JBQ2Q7Z0JBQ0Y7Z0JBRUEsSUFBSTtvQkFDRkQsUUFBUVEsR0FBRyxDQUFDO29CQUNaLE1BQU0yRixXQUFXLE1BQU1DLE1BQU0sR0FBVyxPQUFSbkosU0FBUSwwQkFBd0I7d0JBQzlEb0osU0FBUzs0QkFBRUMsZUFBZSxVQUF1QixPQUFiL0U7d0JBQWU7b0JBQ3JEO29CQUVBLElBQUksQ0FBQzRFLFNBQVNLLEVBQUUsRUFBRTt3QkFDaEIsSUFBSUwsU0FBU3ZELE1BQU0sS0FBSyxLQUFLOzRCQUMzQjVDLFFBQVFDLEtBQUssQ0FBQzs0QkFDZEY7NEJBQ0E7d0JBQ0Y7d0JBQ0EsTUFBTSxJQUFJMEcsTUFBTSx3REFBcUUsT0FBaEJOLFNBQVN2RCxNQUFNO29CQUN0RjtvQkFFQSxNQUFNckYsT0FBTyxNQUFNNEksU0FBU08sSUFBSTtvQkFDaEMxRyxRQUFRUSxHQUFHLENBQUMsMkNBQTJDakQ7b0JBRXZELElBQUlBLEtBQUtvSixPQUFPLEVBQUU7d0JBQ2hCM0csUUFBUVEsR0FBRyxDQUFDLDJDQUEyQ2pELEtBQUtBLElBQUk7d0JBRWhFLG9DQUFvQzt3QkFDcENGLGFBQWFJLFdBQVcsQ0FBQ0YsSUFBSSxHQUFHQSxLQUFLQSxJQUFJLENBQUN1SCxXQUFXO3dCQUNyRHpILGFBQWFJLFdBQVcsQ0FBQ0QsU0FBUyxHQUFHNkQ7d0JBRXJDLG1CQUFtQjt3QkFDbkJoQyxlQUFlOUIsS0FBS0EsSUFBSSxDQUFDdUgsV0FBVzt3QkFFcEMsdURBQXVEO3dCQUN2RCxJQUFJdkgsS0FBS0EsSUFBSSxDQUFDRCxhQUFhLElBQUl5SCxNQUFNQyxPQUFPLENBQUN6SCxLQUFLQSxJQUFJLENBQUNELGFBQWEsR0FBRzs0QkFDckUsMERBQTBEOzRCQUMxRCxNQUFNZ00sa0JBQWtCL0wsS0FBS0EsSUFBSSxDQUFDRCxhQUFhLENBQUNrRyxHQUFHOzZGQUFDZSxDQUFBQSxJQUFLQSxFQUFFeEIsY0FBYzs7NEJBRXpFLHFGQUFxRjs0QkFDckYsdUNBQXVDOzRCQUN2QyxJQUFJekYsY0FBY3NELE1BQU0sS0FBSyxLQUN6QixDQUFDMEksZ0JBQWdCQyxLQUFLOzZFQUFDckksQ0FBQUEsS0FBTTVELGNBQWNnRyxJQUFJO3FGQUFDaUIsQ0FBQUEsSUFBS0EsRUFBRXJELEVBQUUsS0FBS0E7OzZFQUFNO2dDQUN0RWxCLFFBQVFRLEdBQUcsQ0FBQztnQ0FDWixNQUFNNkUsa0JBQWtCOzRCQUMxQixPQUFPO2dDQUNMLGtFQUFrRTtnQ0FDbEVwRztpRkFBaUJtRSxDQUFBQTt3Q0FDZixPQUFPQSxLQUFLSSxHQUFHO3lGQUFDb0IsQ0FBQUE7Z0RBQ2QsZ0RBQWdEO2dEQUNoRCxNQUFNSyxhQUFhMUgsS0FBS0EsSUFBSSxDQUFDRCxhQUFhLENBQUNnSCxJQUFJOzRHQUM3Q1ksQ0FBQUEsT0FBUUEsS0FBS25DLGNBQWMsS0FBSzZCLEtBQUsxRCxFQUFFOztnREFHekMsSUFBSStELFlBQVk7b0RBQ2QsT0FBTzt3REFBRSxHQUFHTCxJQUFJO3dEQUFFbkgsYUFBYXdILFdBQVd4SCxXQUFXO29EQUFDO2dEQUN4RDtnREFDQSxPQUFPbUg7NENBQ1Q7O29DQUNGOzs0QkFDRjt3QkFDRjt3QkFFQSxPQUFPckgsS0FBS0EsSUFBSSxDQUFDdUgsV0FBVztvQkFDOUI7Z0JBQ0YsRUFBRSxPQUFPN0UsT0FBTztvQkFDZEQsUUFBUUMsS0FBSyxDQUFDLCtCQUErQkE7Z0JBQy9DO1lBQ0YsRUFBRSxPQUFPQSxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsNkJBQTZCQTtZQUM3QztRQUNGLDJGQUEyRjtRQUMzRix1REFBdUQ7UUFDdkQ7b0RBQUc7UUFBQ3RCO1FBQU1pQjtRQUFpQkc7S0FBZ0I7SUFFM0MscUVBQXFFO0lBQ3JFLE1BQU1xRix1QkFBdUJ0SSw2Q0FBTUEsQ0FBQztJQUVwQyx1Q0FBdUM7SUFDdkNILGdEQUFTQTtrQ0FBQztZQUNSLElBQUlnQyxNQUFNO2dCQUNScUIsUUFBUVEsR0FBRyxDQUFDO2dCQUNaNEUscUJBQXFCN0UsT0FBTyxHQUFHO1lBQ2pDO1FBQ0Y7aUNBQUc7UUFBQzVCLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUU7S0FBQyxHQUFHLDJFQUEyRTtJQUUzRix5RUFBeUU7SUFDekV2RSxnREFBU0E7a0NBQUM7WUFDUiw2REFBNkQ7WUFDN0QsSUFBSSxDQUFDZ0MsTUFBTTtnQkFDVHFCLFFBQVFRLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsbUNBQW1DO1lBQ25DUixRQUFRUSxHQUFHLENBQUMsbUNBQW1DN0IsS0FBS3dDLElBQUksSUFBSTtZQUU1RCxrQkFBa0I7WUFDbEIsTUFBTUksZUFBZTNCO1lBQ3JCLElBQUksQ0FBQzJCLGNBQWM7Z0JBQ2pCdkIsUUFBUVEsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxrQ0FBa0M7WUFDbEMsSUFBSWQsYUFBYVUsYUFBYUcsT0FBTyxFQUFFO2dCQUNyQ1AsUUFBUVEsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSw0Q0FBNEM7WUFDNUMsSUFBSTRFLHFCQUFxQjdFLE9BQU8sRUFBRTtnQkFDaENQLFFBQVFRLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsc0NBQXNDO1lBQ3RDLE1BQU1nSjswREFBa0I7b0JBQ3RCLElBQUk5SixhQUFhVSxhQUFhRyxPQUFPLEVBQUU7d0JBQ3JDUCxRQUFRUSxHQUFHLENBQUM7d0JBQ1o7b0JBQ0Y7b0JBRUEseUJBQXlCO29CQUN6QkosYUFBYUcsT0FBTyxHQUFHO29CQUN2QlAsUUFBUVEsR0FBRyxDQUFDO29CQUVaLElBQUk7d0JBQ0YsaUNBQWlDO3dCQUNqQ1IsUUFBUVEsR0FBRyxDQUFDO3dCQUNaLE1BQU1pSixvQkFBb0IsTUFBTXBFLGtCQUFrQjt3QkFDbERyRixRQUFRUSxHQUFHLENBQUMseUJBQXlCaUosQ0FBQUEsOEJBQUFBLHdDQUFBQSxrQkFBbUI3SSxNQUFNLEtBQUksR0FBRzt3QkFFckUsMkNBQTJDO3dCQUMzQ1osUUFBUVEsR0FBRyxDQUFDO3dCQUNaLE1BQU02SSxnQkFBZ0I7d0JBQ3RCckosUUFBUVEsR0FBRyxDQUFDO3dCQUVaLHdCQUF3Qjt3QkFDeEI0RSxxQkFBcUI3RSxPQUFPLEdBQUc7d0JBQy9CUCxRQUFRUSxHQUFHLENBQUM7b0JBQ2QsRUFBRSxPQUFPUCxPQUFPO3dCQUNkRCxRQUFRQyxLQUFLLENBQUMsMkJBQTJCQTtvQkFDM0MsU0FBVTt3QkFDUkcsYUFBYUcsT0FBTyxHQUFHO29CQUN6QjtnQkFDRjs7WUFFQSxzRUFBc0U7WUFDdEVQLFFBQVFRLEdBQUcsQ0FBQztZQUNaLE1BQU1rSixvQkFBb0IzTDs0REFBUztvQkFDakNpQyxRQUFRUSxHQUFHLENBQUM7b0JBQ1pnSjtnQkFDRjsyREFBRyxPQUFPLHNDQUFzQztZQUVoRCwrQkFBK0I7WUFDL0JFO1lBRUEsMEVBQTBFO1lBQzFFLHVFQUF1RTtZQUV2RTswQ0FBTztnQkFDTCxtQkFBbUI7Z0JBQ3JCOztRQUNGLDRHQUE0RztRQUM1Ryx1REFBdUQ7UUFDdkQ7aUNBQUc7UUFBQy9LO1FBQU1pQjtRQUFpQnlGO1FBQW1CM0Y7S0FBVTtJQUV4RCx1REFBdUQ7SUFDdkQvQyxnREFBU0E7a0NBQUM7WUFDUixJQUFJdUMsc0JBQXNCNUIsY0FBY3NELE1BQU0sR0FBRyxHQUFHO2dCQUNsRCxNQUFNK0kscUJBQXFCck0sY0FBY2dHLElBQUk7aUVBQUNpQixDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLaEM7O2dCQUM1RCxJQUFJLENBQUN5SyxvQkFBb0I7b0JBQ3ZCM0osUUFBUTRKLElBQUksQ0FBQyxxRUFBcUUxSztvQkFDbEZDLHNCQUFzQjtnQkFDeEI7WUFDRjtRQUNGO2lDQUFHO1FBQUNEO1FBQW9CNUI7S0FBYztJQUV0Qyx5RUFBeUU7SUFDekVYLGdEQUFTQTtrQ0FBQztZQUNSLGdFQUFnRTtZQUNoRSxJQUFJLENBQUNnQyxNQUFNO1lBRVgsMkVBQTJFO1lBQzNFLE1BQU1rTCxxQkFBcUJDOzZEQUFZO29CQUNyQywyREFBMkQ7b0JBQzNELElBQUlwSyxhQUFhVSxhQUFhRyxPQUFPLEVBQUU7b0JBRXZDLE1BQU1nQixlQUFlM0I7b0JBRXJCLDhDQUE4QztvQkFDOUMsSUFBSSxDQUFDMkIsZ0JBQWdCeEMsZUFBZUYsUUFBUTt3QkFDMUNtQixRQUFRUSxHQUFHLENBQUM7d0JBQ1osSUFBSTs0QkFDRjNCLE9BQU9xQixVQUFVO3dCQUNuQixFQUFFLE9BQU9ELE9BQU87NEJBQ2RELFFBQVFDLEtBQUssQ0FBQywrQkFBK0JBO3dCQUMvQzt3QkFDQWpCLGVBQWU7b0JBQ2pCO29CQUVBLCtFQUErRTtvQkFDL0UsSUFBSXVDLGdCQUFnQixDQUFDeEMsZUFBZSxDQUFDRixRQUFRO3dCQUMzQyxNQUFNd0MsTUFBTUQsS0FBS0MsR0FBRzt3QkFDcEIsbUVBQW1FO3dCQUNuRSxJQUFJQSxNQUFNaEIsbUJBQW1CRSxPQUFPLEdBQUcsUUFBUTs0QkFDN0NQLFFBQVFRLEdBQUcsQ0FBQzs0QkFDWix3REFBd0Q7NEJBQ3hETCxxQkFBcUJJLE9BQU8sR0FBRzs0QkFDL0JGLG1CQUFtQkUsT0FBTyxHQUFHYzt3QkFDL0I7b0JBQ0Y7Z0JBQ0Y7NERBQUcsU0FBUyw2QkFBNkI7WUFFekM7MENBQU8sSUFBTTBJLGNBQWNGOztRQUM3Qix1REFBdUQ7UUFDdkQ7aUNBQUc7UUFBQ2xMO1FBQU1pQjtLQUFnQjtJQUUxQiw4QkFBOEI7SUFDOUIsTUFBTW9LLGtCQUFrQnBOLGtEQUFXQTtxREFBQztZQUNsQyxxQ0FBcUM7WUFDckMsSUFBSSxDQUFDK0IsTUFBTTtZQUVYWTs2REFBZTZELENBQUFBO29CQUNiLE1BQU02RyxXQUFXLENBQUM3RztvQkFDbEIsOENBQThDO29CQUM5QyxJQUFJNkcsVUFBVTt3QkFDWnhLLGVBQWU7b0JBQ2pCO29CQUNBLE9BQU93SztnQkFDVDs7UUFDRjtvREFBRztRQUFDdEw7S0FBSztJQUVULDZCQUE2QjtJQUM3QixNQUFNdUwsa0JBQWtCdE4sa0RBQVdBO3FEQUFDO1lBQ2xDLHFDQUFxQztZQUNyQyxJQUFJLENBQUMrQixNQUFNO1lBRVhjOzZEQUFlMkQsQ0FBQUE7b0JBQ2IsTUFBTTZHLFdBQVcsQ0FBQzdHO29CQUNsQiw4Q0FBOEM7b0JBQzlDLElBQUk2RyxVQUFVO3dCQUNaMUssZUFBZTtvQkFDakI7b0JBQ0EsT0FBTzBLO2dCQUNUOztRQUNGO29EQUFHO1FBQUN0TDtLQUFLO0lBRVQsc0RBQXNEO0lBQ3REcUIsUUFBUVEsR0FBRyxDQUFDLGlEQUFpRCxPQUFPdUgsdUJBQXVCO0lBRTNGLG9DQUFvQztJQUNwQyxNQUFNb0Msd0JBQXdCdk4sa0RBQVdBOzJEQUFDLE9BQU9tRyxnQkFBZ0J1QztZQUMvRCxJQUFJO2dCQUNGLElBQUksQ0FBQ3ZDLGtCQUFrQixDQUFDdUMsZUFBZTtvQkFDckN0RixRQUFRQyxLQUFLLENBQUM7b0JBQ2QsT0FBTztnQkFDVDtnQkFFQSxNQUFNc0IsZUFBZTNCO2dCQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUNqQnZCLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPO2dCQUNUO2dCQUVBRCxRQUFRUSxHQUFHLENBQUMsNEJBQXdEdUMsT0FBNUJ1QyxlQUFjLG1CQUE2QixPQUFmdkM7Z0JBRXBFLE1BQU1vRCxXQUFXLE1BQU1DLE1BQU0sR0FBaUNyRCxPQUE5QjlGLFNBQVEsd0JBQXFDLE9BQWY4RixnQkFBZSxrQkFBZ0I7b0JBQzNGOEYsUUFBUTtvQkFDUnhDLFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQkMsZUFBZSxVQUF1QixPQUFiL0U7b0JBQzNCO29CQUNBdUgsTUFBTWxDLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRXZCO29CQUFjO2dCQUN2QztnQkFFQXRGLFFBQVFRLEdBQUcsQ0FBQyxvQkFBdUMyRixPQUFuQkEsU0FBU3ZELE1BQU0sRUFBQyxLQUF1QixPQUFwQnVELFNBQVNJLFVBQVU7Z0JBRXRFLElBQUksQ0FBQ0osU0FBU0ssRUFBRSxFQUFFO29CQUNoQixJQUFJTCxTQUFTdkQsTUFBTSxLQUFLLEtBQUs7d0JBQzNCNUMsUUFBUUMsS0FBSyxDQUFDO3dCQUNkRjt3QkFDQSxPQUFPO29CQUNUO29CQUVBLHdCQUF3QjtvQkFDeEIsTUFBTXFLLFlBQVksTUFBTWpFLFNBQVNrRSxJQUFJO29CQUNyQ3JLLFFBQVFDLEtBQUssQ0FBQyxRQUF3QixPQUFoQmtHLFNBQVN2RCxNQUFNLEVBQUMsZ0NBQThCd0g7b0JBQ3BFLE1BQU0sSUFBSTNELE1BQU0sbUNBQW1ELE9BQWhCTixTQUFTdkQsTUFBTTtnQkFDcEU7Z0JBRUEsTUFBTXJGLE9BQU8sTUFBTTRJLFNBQVNPLElBQUk7Z0JBRWhDLElBQUluSixLQUFLb0osT0FBTyxFQUFFO29CQUNoQiwrQ0FBK0M7b0JBQy9DMUg7MkVBQWlCbUUsQ0FBQUE7NEJBQ2YsT0FBT0EsS0FBS0ksR0FBRzttRkFBQ29CLENBQUFBO29DQUNkLElBQUlBLEtBQUsxRCxFQUFFLEtBQUs2QixnQkFBZ0I7d0NBQzlCLHdDQUF3Qzt3Q0FDeEMsTUFBTXVILG9CQUFvQjFGLEtBQUtZLFlBQVksQ0FBQ2xDLElBQUk7aUhBQUNtQyxDQUFBQSxJQUFLQSxFQUFFekMsTUFBTSxLQUFLc0M7O3dDQUVuRSxJQUFJZ0YsbUJBQW1COzRDQUNyQixPQUFPMUY7d0NBQ1Q7d0NBRUEsZ0NBQWdDO3dDQUNoQyxPQUFPOzRDQUNMLEdBQUdBLElBQUk7NENBQ1BZLGNBQWM7bURBQUlaLEtBQUtZLFlBQVk7Z0RBQUVqSSxLQUFLQSxJQUFJOzZDQUFDO3dDQUNqRDtvQ0FDRjtvQ0FDQSxPQUFPcUg7Z0NBQ1Q7O3dCQUNGOztvQkFFQSxrRkFBa0Y7b0JBQ2xGLElBQUkvRixVQUFVRSxhQUFhO3dCQUN6QixNQUFNd0wsZ0JBQWdCOzRCQUNwQnhIOzRCQUNBZ0IsU0FBUyxHQUEyQixPQUF4QnhHLEtBQUtBLElBQUksQ0FBQ29CLElBQUksQ0FBQzhCLFFBQVEsRUFBQzs0QkFDcENxRCxhQUFhO3dCQUNmO3dCQUVBakYsT0FBTytJLElBQUksQ0FBQyxnQkFBZ0IyQztvQkFDOUI7b0JBRUEsT0FBT2hOLEtBQUtBLElBQUk7Z0JBQ2xCO2dCQUVBLE9BQU87WUFDVCxFQUFFLE9BQU8wQyxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsbUNBQW1DQTtnQkFDakQsT0FBTztZQUNUO1FBQ0Y7MERBQUc7UUFBQ3BCO1FBQVFFO1FBQWFhO1FBQWlCRztLQUFnQjtJQUUxRCwrQ0FBK0M7SUFDL0MsTUFBTXlLLGlDQUFpQzVOLGtEQUFXQTtvRUFBQyxPQUFPbUcsZ0JBQWdCeUM7WUFDeEUsSUFBSTtnQkFDRixJQUFJLENBQUN6QyxrQkFBa0IsQ0FBQ3lDLGdCQUFnQixDQUFDQSxhQUFhNUUsTUFBTSxFQUFFO29CQUM1RFosUUFBUUMsS0FBSyxDQUFDO29CQUNkLE9BQU87Z0JBQ1Q7Z0JBRUFELFFBQVFRLEdBQUcsQ0FBQyxlQUE2RHVDLE9BQTlDeUMsYUFBYTVFLE1BQU0sRUFBQyw0QkFBeUMsT0FBZm1DO2dCQUV6RSxxQ0FBcUM7Z0JBQ3JDLE1BQU0wSCxVQUFVLEVBQUU7Z0JBQ2xCLE1BQU1DLFNBQVMsRUFBRTtnQkFFakIsb0NBQW9DO2dCQUNwQyxLQUFLLE1BQU1DLGVBQWVuRixhQUFjO29CQUN0QyxJQUFJO3dCQUNGLE1BQU1PLFNBQVMsTUFBTW9FLHNCQUFzQnBILGdCQUFnQjRILFlBQVl6SixFQUFFO3dCQUN6RSxJQUFJNkUsUUFBUTs0QkFDVjBFLFFBQVExSixJQUFJLENBQUNnRjt3QkFDZixPQUFPOzRCQUNMMkUsT0FBTzNKLElBQUksQ0FBQztnQ0FBRXBDLE1BQU1nTTtnQ0FBYTFLLE9BQU87NEJBQWtDO3dCQUM1RTtvQkFDRixFQUFFLE9BQU9BLE9BQU87d0JBQ2RELFFBQVFDLEtBQUssQ0FBQyxrQ0FBaUQsT0FBZjBLLFlBQVl6SixFQUFFLEVBQUMsTUFBSWpCO3dCQUNuRXlLLE9BQU8zSixJQUFJLENBQUM7NEJBQUVwQyxNQUFNZ007NEJBQWExSyxPQUFPQSxNQUFNNEMsT0FBTyxJQUFJO3dCQUFvQjtvQkFDL0U7Z0JBQ0Y7Z0JBRUEsT0FBTztvQkFDTDhELFNBQVM4RCxRQUFRN0osTUFBTSxHQUFHO29CQUMxQmdLLE9BQU9IO29CQUNQQyxRQUFRQTtvQkFDUkcsT0FBT3JGLGFBQWE1RSxNQUFNO29CQUMxQmtLLGNBQWNMLFFBQVE3SixNQUFNO29CQUM1Qm1LLFlBQVlMLE9BQU85SixNQUFNO2dCQUMzQjtZQUNGLEVBQUUsT0FBT1gsT0FBTztnQkFDZEQsUUFBUUMsS0FBSyxDQUFDLDhDQUE4Q0E7Z0JBQzVELE9BQU87b0JBQ0wwRyxTQUFTO29CQUNUaUUsT0FBTyxFQUFFO29CQUNURixRQUFRO3dCQUFDOzRCQUFFekssT0FBT0EsTUFBTTRDLE9BQU8sSUFBSTt3QkFBb0I7cUJBQUU7b0JBQ3pEZ0ksT0FBT3JGLGFBQWE1RSxNQUFNO29CQUMxQmtLLGNBQWM7b0JBQ2RDLFlBQVl2RixhQUFhNUUsTUFBTTtnQkFDakM7WUFDRjtRQUNGO21FQUFHO1FBQUN1SjtLQUFzQjtJQUUxQixzREFBc0Q7SUFDdEQsTUFBTWEsNkJBQTZCcE8sa0RBQVdBO2dFQUFDLE9BQU9tRyxnQkFBZ0J1QztZQUNwRXRGLFFBQVFRLEdBQUcsQ0FBQyx1Q0FBdUM7Z0JBQUV1QztnQkFBZ0J1QztnQkFBZXRDLE1BQU0sRUFBRXJFLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUU7WUFBQztZQUVyRyxJQUFJO2dCQUNGLElBQUksQ0FBQzZCLGtCQUFrQixDQUFDdUMsZUFBZTtvQkFDckN0RixRQUFRQyxLQUFLLENBQUM7b0JBQ2QsT0FBTztnQkFDVDtnQkFFQSxNQUFNc0IsZUFBZTNCO2dCQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUNqQnZCLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPO2dCQUNUO2dCQUVBLE1BQU1rRyxXQUFXLE1BQU1DLE1BQU0sR0FBaUNyRCxPQUE5QjlGLFNBQVEsd0JBQXFEcUksT0FBL0J2QyxnQkFBZSxrQkFBOEIsT0FBZHVDLGdCQUFpQjtvQkFDNUd1RCxRQUFRO29CQUNSeEMsU0FBUzt3QkFDUEMsZUFBZSxVQUF1QixPQUFiL0U7b0JBQzNCO2dCQUNGO2dCQUVBLElBQUksQ0FBQzRFLFNBQVNLLEVBQUUsRUFBRTtvQkFDaEIsSUFBSUwsU0FBU3ZELE1BQU0sS0FBSyxLQUFLO3dCQUMzQjVDLFFBQVFDLEtBQUssQ0FBQzt3QkFDZEY7d0JBQ0EsT0FBTztvQkFDVDtvQkFDQSxNQUFNLElBQUkwRyxNQUFNLGlDQUFpRCxPQUFoQk4sU0FBU3ZELE1BQU07Z0JBQ2xFO2dCQUVBLE1BQU1yRixPQUFPLE1BQU00SSxTQUFTTyxJQUFJO2dCQUVoQyxJQUFJbkosS0FBS29KLE9BQU8sRUFBRTtvQkFDaEIscURBQXFEO29CQUNyRCxJQUFJckIsbUJBQWtCM0csaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRSxHQUFFO3dCQUM5QmxCLFFBQVFRLEdBQUcsQ0FBQyxzQkFBcUMsT0FBZnVDLGdCQUFlO3dCQUVqRCx5QkFBeUI7d0JBQ3pCOUQ7b0ZBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTTs0RkFBQ2lCLENBQUFBLE9BQVFBLEtBQUsxRCxFQUFFLEtBQUs2Qjs7O3dCQUV6RCxtQkFBbUI7d0JBQ25CM0Q7b0ZBQVlnRSxDQUFBQTtnQ0FDVixNQUFNbUMsVUFBVTtvQ0FBRSxHQUFHbkMsSUFBSTtnQ0FBQztnQ0FDMUIsT0FBT21DLE9BQU8sQ0FBQ3hDLGVBQWU7Z0NBQzlCLE9BQU93Qzs0QkFDVDs7d0JBRUEsZUFBZTt3QkFDZmxJLGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSSxHQUFHO3dCQUNsQ0YsYUFBYUMsYUFBYSxDQUFDRSxTQUFTLEdBQUc7d0JBQ3ZDLElBQUlILGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsRUFBRTs0QkFDekMsT0FBTzFGLGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWU7d0JBQzlDO3dCQUVBLGtDQUFrQzt3QkFDbEMsSUFBSTdELHVCQUF1QjZELGdCQUFnQjs0QkFDekM1RCxzQkFBc0I7d0JBQ3hCO3dCQUVBLGdEQUFnRDt3QkFDaERaO29GQUFXO2dDQUNUOEcsa0JBQWtCOzRCQUNwQjttRkFBRzt3QkFFSHJGLFFBQVFRLEdBQUcsQ0FBQyxZQUEyQixPQUFmdUMsZ0JBQWU7b0JBQ3pDLE9BQU87NEJBZXdCeEYsaUJBQUFBO3dCQWQ3QixnREFBZ0Q7d0JBQ2hEMEI7b0ZBQWlCbUUsQ0FBQUE7Z0NBQ2YsT0FBT0EsS0FBS0ksR0FBRzs0RkFBQ29CLENBQUFBO3dDQUNkLElBQUlBLEtBQUsxRCxFQUFFLEtBQUs2QixnQkFBZ0I7NENBQzlCLE9BQU87Z0RBQ0wsR0FBRzZCLElBQUk7Z0RBQ1BZLGNBQWNaLEtBQUtZLFlBQVksQ0FBQzdCLE1BQU07NEdBQUM4QixDQUFBQSxJQUFLQSxFQUFFekMsTUFBTSxLQUFLc0M7OzRDQUMzRDt3Q0FDRjt3Q0FDQSxPQUFPVjtvQ0FDVDs7NEJBQ0Y7O3dCQUVBLHdFQUF3RTt3QkFDeEUsSUFBSS9GLFVBQVVFLGlCQUFleEIsYUFBQUEsS0FBS0EsSUFBSSxjQUFUQSxrQ0FBQUEsa0JBQUFBLFdBQVdvQixJQUFJLGNBQWZwQixzQ0FBQUEsZ0JBQWlCa0QsUUFBUSxHQUFFOzRCQUN0RCxNQUFNOEosZ0JBQWdCO2dDQUNwQnhIO2dDQUNBZ0IsU0FBUyxHQUEyQixPQUF4QnhHLEtBQUtBLElBQUksQ0FBQ29CLElBQUksQ0FBQzhCLFFBQVEsRUFBQztnQ0FDcENxRCxhQUFhOzRCQUNmOzRCQUVBakYsT0FBTytJLElBQUksQ0FBQyxnQkFBZ0IyQzt3QkFDOUI7b0JBQ0Y7b0JBRUEsT0FBT2hOLEtBQUtBLElBQUk7Z0JBQ2xCO2dCQUVBLE9BQU87WUFDVCxFQUFFLE9BQU8wQyxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsaUNBQWlDQTtnQkFDL0MsT0FBTztZQUNUO1FBQ0Y7K0RBQUc7UUFBQ3BCO1FBQVFFO1FBQWFKO1FBQU1PO1FBQW9CVTtRQUFpQkc7UUFBaUJzSjtLQUFnQjtJQUVyRyw4QkFBOEI7SUFDOUIsTUFBTTRCLHFCQUFxQnJPLGtEQUFXQTt3REFBQyxPQUFPbUcsZ0JBQWdCbUk7WUFDNUQsSUFBSTtnQkFDRixJQUFJLENBQUN2TSxNQUFNO2dCQUVYLE1BQU00QyxlQUFlM0I7Z0JBQ3JCLElBQUksQ0FBQzJCLGNBQWM7Z0JBRW5CLE1BQU00RSxXQUFXLE1BQU1DLE1BQU0sR0FBVyxPQUFSbkosU0FBUSw2QkFBMkI7b0JBQ2pFNEwsUUFBUTtvQkFDUnhDLFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQkMsZUFBZSxVQUF1QixPQUFiL0U7b0JBQzNCO29CQUNBdUgsTUFBTWxDLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRTlEO3dCQUFnQm1JO29CQUFVO2dCQUNuRDtnQkFFQSxJQUFJL0UsU0FBU0ssRUFBRSxFQUFFO29CQUNmLHlDQUF5QztvQkFDekN2SDt3RUFBaUJtRSxDQUFBQSxPQUFRQSxLQUFLSSxHQUFHO2dGQUFDb0IsQ0FBQUEsT0FDaENBLEtBQUsxRCxFQUFFLEtBQUs2QixpQkFBaUI7d0NBQUUsR0FBRzZCLElBQUk7d0NBQUVuSCxhQUFhO29DQUFFLElBQUltSDs7O29CQUc3RCxnQ0FBZ0M7b0JBQ2hDdkY7d0VBQWUrRCxDQUFBQTs0QkFDYixNQUFNd0IsT0FBT3RILGNBQWNnSCxJQUFJO3FGQUFDQyxDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLNkI7OzRCQUM5QyxNQUFNb0ksZ0JBQWdCdkcsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNbkgsV0FBVyxLQUFJOzRCQUMzQyxPQUFPMk4sS0FBS0MsR0FBRyxDQUFDLEdBQUdqSSxPQUFPK0g7d0JBQzVCOztvQkFFQSxNQUFNOUIsZ0JBQWdCO2dCQUN4QjtZQUNGLEVBQUUsT0FBT3BKLE9BQU87Z0JBQ2RELFFBQVFDLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ25EO1FBQ0Y7dURBQUc7UUFBQ3RCO1FBQU1pQjtRQUFpQnlKO1FBQWlCL0w7S0FBYztJQUUxRCwwQ0FBMEM7SUFDMUMsTUFBTWdPLG1CQUFtQjFPLGtEQUFXQTtzREFBQztZQUNuQyxJQUFJO2dCQUNGLElBQUksQ0FBQytCLE1BQU07Z0JBRVgsTUFBTTRDLGVBQWUzQjtnQkFDckIsSUFBSSxDQUFDMkIsY0FBYztnQkFFbkIsTUFBTTRFLFdBQVcsTUFBTUMsTUFBTSxHQUFXLE9BQVJuSixTQUFRLGdDQUE4QjtvQkFDcEU0TCxRQUFRO29CQUNSeEMsU0FBUzt3QkFDUCxnQkFBZ0I7d0JBQ2hCQyxlQUFlLFVBQXVCLE9BQWIvRTtvQkFDM0I7Z0JBQ0Y7Z0JBRUEsSUFBSTRFLFNBQVNLLEVBQUUsRUFBRTtvQkFDZm5ILGVBQWU7b0JBQ2ZKO3NFQUFpQm1FLENBQUFBLE9BQVFBLEtBQUtJLEdBQUc7OEVBQUNvQixDQUFBQSxPQUFTO3dDQUFFLEdBQUdBLElBQUk7d0NBQUVuSCxhQUFhO29DQUFFOzs7Z0JBQ3ZFO1lBQ0YsRUFBRSxPQUFPd0MsT0FBTztnQkFDZEQsUUFBUUMsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDakQ7UUFDRjtxREFBRztRQUFDdEI7UUFBTWlCO0tBQWdCO0lBRTFCLG1CQUFtQjtJQUNuQixNQUFNMkwsaUJBQWlCM08sa0RBQVdBO29EQUFDLE9BQU80TyxZQUFZekk7WUFDcEQsSUFBSTtnQkFDRixJQUFJLENBQUN5SSxjQUFjQSxXQUFXNUssTUFBTSxLQUFLLEdBQUc7b0JBQzFDWixRQUFRQyxLQUFLLENBQUM7b0JBQ2QsT0FBTztnQkFDVDtnQkFFQSxNQUFNc0IsZUFBZTNCO2dCQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUNqQnZCLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPO2dCQUNUO2dCQUVBLE1BQU13SyxVQUFVLEVBQUU7Z0JBQ2xCLE1BQU1DLFNBQVMsRUFBRTtnQkFFakIsK0JBQStCO2dCQUMvQixLQUFLLE1BQU1RLGFBQWFNLFdBQVk7b0JBQ2xDLElBQUk7d0JBQ0YsTUFBTXJGLFdBQVcsTUFBTUMsTUFBTSxHQUE0QjhFLE9BQXpCak8sU0FBUSxtQkFBMkIsT0FBVmlPLFlBQWE7NEJBQ3BFckMsUUFBUTs0QkFDUnhDLFNBQVM7Z0NBQ1BDLGVBQWUsVUFBdUIsT0FBYi9FOzRCQUMzQjt3QkFDRjt3QkFFQSxJQUFJLENBQUM0RSxTQUFTSyxFQUFFLEVBQUU7NEJBQ2hCLElBQUlMLFNBQVN2RCxNQUFNLEtBQUssS0FBSztnQ0FDM0I1QyxRQUFRQyxLQUFLLENBQUM7Z0NBQ2RGO2dDQUNBLE9BQU87NEJBQ1Q7NEJBQ0EsTUFBTSxJQUFJMEcsTUFBTSw0QkFBNEMsT0FBaEJOLFNBQVN2RCxNQUFNO3dCQUM3RDt3QkFFQSxNQUFNckYsT0FBTyxNQUFNNEksU0FBU08sSUFBSTt3QkFFaEMsSUFBSW5KLEtBQUtvSixPQUFPLEVBQUU7NEJBQ2hCOEQsUUFBUTFKLElBQUksQ0FBQ3hELEtBQUtBLElBQUk7NEJBRXRCLG1DQUFtQzs0QkFDbkM2Qjs0RUFBWWdFLENBQUFBO29DQUNWLE1BQU1hLGtCQUFrQjt3Q0FBRSxHQUFHYixJQUFJO29DQUFDO29DQUVsQywrQkFBK0I7b0NBQy9CcUksT0FBT0MsSUFBSSxDQUFDekgsaUJBQWlCakQsT0FBTztvRkFBQzJLLENBQUFBOzRDQUNuQyxrREFBa0Q7NENBQ2xEMUgsZUFBZSxDQUFDMEgsT0FBTyxHQUFHMUgsZUFBZSxDQUFDMEgsT0FBTyxDQUFDbkksR0FBRzs0RkFBQ3FFLENBQUFBO29EQUNwRCxJQUFJQSxJQUFJM0csRUFBRSxLQUFLZ0ssV0FBVzt3REFDeEIsT0FBTzs0REFDTCxHQUFHckQsR0FBRzs0REFDTixHQUFHdEssS0FBS0EsSUFBSTs0REFDWnFPLFdBQVc7d0RBQ2I7b0RBQ0Y7b0RBQ0EsT0FBTy9EO2dEQUNUOzt3Q0FDRjs7b0NBRUEsT0FBTzVEO2dDQUNUOzt3QkFDRixPQUFPOzRCQUNMeUcsT0FBTzNKLElBQUksQ0FBQztnQ0FBRW1LO2dDQUFXakwsT0FBTzs0QkFBMkI7d0JBQzdEO29CQUNGLEVBQUUsT0FBT0EsT0FBTzt3QkFDZEQsUUFBUUMsS0FBSyxDQUFDLDJCQUFxQyxPQUFWaUwsV0FBVSxNQUFJakw7d0JBQ3ZEeUssT0FBTzNKLElBQUksQ0FBQzs0QkFBRW1LOzRCQUFXakwsT0FBT0EsTUFBTTRDLE9BQU8sSUFBSTt3QkFBb0I7b0JBQ3ZFO2dCQUNGO2dCQUVBLHlEQUF5RDtnQkFDekQsSUFBSUUsa0JBQWtCMEgsUUFBUTdKLE1BQU0sR0FBRyxHQUFHO29CQUN4Q1osUUFBUVEsR0FBRyxDQUFDLGdEQUFnRHVDO29CQUM1RCxJQUFJMUYsYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxFQUFFO3dCQUN6QyxPQUFPMUYsYUFBYUssUUFBUSxDQUFDcUYsZUFBZTtvQkFDOUM7b0JBRUEsNEZBQTRGO29CQUM1RixJQUFJO3dCQUNGL0MsUUFBUVEsR0FBRyxDQUFDO3dCQUNaLE1BQU0wRyxhQUFhbkU7d0JBRW5CLGdGQUFnRjt3QkFDaEYsTUFBTXNDLGtCQUFrQjtvQkFDMUIsRUFBRSxPQUFPd0csYUFBYTt3QkFDcEI3TCxRQUFRQyxLQUFLLENBQUMsK0NBQStDNEw7b0JBQy9EO2dCQUNGO2dCQUVBLE9BQU87b0JBQ0xsRixTQUFTOEQsUUFBUTdKLE1BQU0sR0FBRztvQkFDMUJrTCxTQUFTckI7b0JBQ1RDLFFBQVFBO29CQUNSRyxPQUFPVyxXQUFXNUssTUFBTTtvQkFDeEJrSyxjQUFjTCxRQUFRN0osTUFBTTtvQkFDNUJtSyxZQUFZTCxPQUFPOUosTUFBTTtnQkFDM0I7WUFDRixFQUFFLE9BQU9YLE9BQU87Z0JBQ2RELFFBQVFDLEtBQUssQ0FBQyw2QkFBNkJBO2dCQUMzQyxPQUFPO29CQUNMMEcsU0FBUztvQkFDVG1GLFNBQVMsRUFBRTtvQkFDWHBCLFFBQVE7d0JBQUM7NEJBQUV6SyxPQUFPQSxNQUFNNEMsT0FBTyxJQUFJO3dCQUFvQjtxQkFBRTtvQkFDekRnSSxPQUFPVyxXQUFXNUssTUFBTTtvQkFDeEJrSyxjQUFjO29CQUNkQyxZQUFZUyxXQUFXNUssTUFBTTtnQkFDL0I7WUFDRjtRQUNGO21EQUFHO1FBQUNoQjtRQUFpQkc7UUFBaUJtSDtRQUFjN0I7S0FBa0I7SUFFdEUsMkVBQTJFO0lBQzNFLE1BQU0wRyxlQUFlbFAsOENBQU9BOzhDQUFDLElBQU87Z0JBQ2xDUztnQkFDQUk7Z0JBQ0FEO2dCQUNBeUI7Z0JBQ0FJO2dCQUNBRTtnQkFDQVQ7Z0JBQ0FXO2dCQUNBUDtnQkFDQStIO2dCQUNBRTtnQkFDQVc7Z0JBQ0FrQjtnQkFDQWU7Z0JBQ0FFO2dCQUNBN0U7Z0JBQ0FnRTtnQkFDQTRCO2dCQUNBSztnQkFDQW5CO2dCQUNBSztnQkFDQVE7Z0JBQ0FPO1lBQ0Y7NkNBQUk7UUFDRmpPO1FBQ0FJO1FBQ0FEO1FBQ0F5QjtRQUNBSTtRQUNBRTtRQUNBVDtRQUNBVztRQUNBUDtRQUNBK0g7UUFDQUU7UUFDQVc7UUFDQWtCO1FBQ0FlO1FBQ0FFO1FBQ0E3RTtRQUNBZ0U7UUFDQTRCO1FBQ0FLO1FBQ0FuQjtRQUNBSztRQUNBUTtRQUNBTztLQUNEO0lBRUQscUJBQ0UsOERBQUMvTSxZQUFZd04sUUFBUTtRQUFDQyxPQUFPRjtrQkFDMUJyTjs7Ozs7O0FBR1AsRUFBRTtHQWgyRFdEOztRQUNjMUIsaURBQU9BOzs7S0FEckIwQjtBQWsyRE4sTUFBTXlOLFVBQVU7O0lBQ3JCLE1BQU1DLFVBQVUxUCxpREFBVUEsQ0FBQytCO0lBRTNCLElBQUksQ0FBQzJOLFNBQVM7UUFDWixNQUFNLElBQUkxRixNQUFNO0lBQ2xCO0lBRUEsT0FBTzBGO0FBQ1QsRUFBRTtJQVJXRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxQcm9ncmFtYWNhb1xcSElHSCBUSURFIFNZU1RFTVNcXGhpZ2gtdGlkZS1zeXN0ZW1zLWZyb250ZW5kXFxzcmNcXGNvbnRleHRzXFxDaGF0Q29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZU1lbW8sIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJy4vQXV0aENvbnRleHQnO1xyXG5pbXBvcnQgaW8gZnJvbSAnc29ja2V0LmlvLWNsaWVudCc7XHJcblxyXG5jb25zdCBBUElfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo1MDAwJztcclxuXHJcbi8vIENhY2hlIHBhcmEgZXZpdGFyIHJlcXVpc2nDp8O1ZXMgZHVwbGljYWRhc1xyXG5jb25zdCByZXF1ZXN0Q2FjaGUgPSB7XHJcbiAgY29udmVyc2F0aW9uczogeyBkYXRhOiBudWxsLCB0aW1lc3RhbXA6IDAgfSxcclxuICB1bnJlYWRDb3VudDogeyBkYXRhOiBudWxsLCB0aW1lc3RhbXA6IDAgfSxcclxuICBtZXNzYWdlczoge30sIC8vIENhY2hlIHBhcmEgbWVuc2FnZW5zIHBvciBjb252ZXJzYVxyXG4gIHRva2VuQ2hlY2s6IHsgdGltZXN0YW1wOiAwLCB2YWxpZDogZmFsc2UgfSAvLyBDYWNoZSBwYXJhIHZlcmlmaWNhw6fDo28gZGUgdG9rZW5cclxufTtcclxuXHJcbi8vIFRlbXBvIGRlIGV4cGlyYcOnw6NvIGRvIGNhY2hlIGVtIG1pbGlzc2VndW5kb3NcclxuY29uc3QgQ0FDSEVfRVhQSVJBVElPTiA9IDMwMDAwOyAvLyAzMCBzZWd1bmRvcyBwYXJhIGRhZG9zIG5vcm1haXNcclxuY29uc3QgVE9LRU5fQ0hFQ0tfRVhQSVJBVElPTiA9IDYwMDAwOyAvLyAxIG1pbnV0byBwYXJhIHZlcmlmaWNhw6fDo28gZGUgdG9rZW5cclxuXHJcbi8vIEZ1bsOnw6NvIGRlIGRlYm91bmNlIHBhcmEgbGltaXRhciBhIGZyZXF1w6puY2lhIGRlIGNoYW1hZGFzXHJcbmNvbnN0IGRlYm91bmNlID0gKGZ1bmMsIHdhaXQpID0+IHtcclxuICBsZXQgdGltZW91dDtcclxuICByZXR1cm4gZnVuY3Rpb24gZXhlY3V0ZWRGdW5jdGlvbiguLi5hcmdzKSB7XHJcbiAgICBjb25zdCBsYXRlciA9ICgpID0+IHtcclxuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpO1xyXG4gICAgICBmdW5jKC4uLmFyZ3MpO1xyXG4gICAgfTtcclxuICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KTtcclxuICAgIHRpbWVvdXQgPSBzZXRUaW1lb3V0KGxhdGVyLCB3YWl0KTtcclxuICB9O1xyXG59O1xyXG5cclxuY29uc3QgQ2hhdENvbnRleHQgPSBjcmVhdGVDb250ZXh0KG51bGwpO1xyXG5cclxuZXhwb3J0IGNvbnN0IENoYXRQcm92aWRlciA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICBjb25zdCB7IHVzZXIsIGxvZ291dCB9ID0gdXNlQXV0aCgpO1xyXG4gIGNvbnN0IFtzb2NrZXQsIHNldFNvY2tldF0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbaXNDb25uZWN0ZWQsIHNldElzQ29ubmVjdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY29udmVyc2F0aW9ucywgc2V0Q29udmVyc2F0aW9uc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2FjdGl2ZUNvbnZlcnNhdGlvbiwgc2V0QWN0aXZlQ29udmVyc2F0aW9uXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGUoe30pO1xyXG4gIGNvbnN0IFt1bnJlYWRDb3VudCwgc2V0VW5yZWFkQ291bnRdID0gdXNlU3RhdGUoMCk7XHJcbiAgY29uc3QgW2lzUGFuZWxPcGVuLCBzZXRJc1BhbmVsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTW9kYWxPcGVuLCBzZXRJc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8gT2J0ZXIgbyB0b2tlbiBhdHVhbCBkbyBsb2NhbFN0b3JhZ2UgLSBtZW1vaXphZG8gcGFyYSBldml0YXIgY2hhbWFkYXMgZGVzbmVjZXNzw6FyaWFzXHJcbiAgY29uc3QgZ2V0Q3VycmVudFRva2VuID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuICAgIH1cclxuICAgIHJldHVybiBudWxsO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gRnVuw6fDo28gcGFyYSBsaWRhciBjb20gZXJyb3MgZGUgYXV0ZW50aWNhw6fDo29cclxuICBjb25zdCBoYW5kbGVBdXRoRXJyb3IgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvIGRlIGF1dGVudGljYcOnw6NvIGRldGVjdGFkby4gUmVkaXJlY2lvbmFuZG8gcGFyYSBsb2dpbi4uLicpO1xyXG4gICAgLy8gTGltcGFyIGRhZG9zIGxvY2Fpc1xyXG4gICAgc2V0Q29udmVyc2F0aW9ucyhbXSk7XHJcbiAgICBzZXRNZXNzYWdlcyh7fSk7XHJcbiAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24obnVsbCk7XHJcbiAgICBzZXRVbnJlYWRDb3VudCgwKTtcclxuICAgIHNldElzUGFuZWxPcGVuKGZhbHNlKTtcclxuICAgIHNldElzTW9kYWxPcGVuKGZhbHNlKTtcclxuXHJcbiAgICAvLyBEZXNjb25lY3RhciBzb2NrZXQgc2UgZXhpc3RpclxyXG4gICAgaWYgKHNvY2tldCkge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIHNvY2tldC5kaXNjb25uZWN0KCk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBkZXNjb25lY3RhciBzb2NrZXQ6JywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBMaW1wYXIgY2FjaGVcclxuICAgIHJlcXVlc3RDYWNoZS5jb252ZXJzYXRpb25zLmRhdGEgPSBudWxsO1xyXG4gICAgcmVxdWVzdENhY2hlLnVucmVhZENvdW50LmRhdGEgPSBudWxsO1xyXG4gICAgcmVxdWVzdENhY2hlLm1lc3NhZ2VzID0ge307IC8vIExpbXBhciBjYWNoZSBkZSBtZW5zYWdlbnNcclxuXHJcbiAgICAvLyBSZWRpcmVjaW9uYXIgcGFyYSBsb2dpblxyXG4gICAgaWYgKGxvZ291dCkge1xyXG4gICAgICBsb2dvdXQoKTtcclxuICAgIH1cclxuICB9LCBbc29ja2V0LCBsb2dvdXRdKTtcclxuXHJcbiAgLy8gVXNhciByZWZlcsOqbmNpYXMgcGFyYSBjb250cm9sYXIgZXN0YWRvcyBxdWUgbsOjbyBkZXZlbSBjYXVzYXIgcmUtcmVuZGVyaXphw6fDtWVzXHJcbiAgY29uc3Qgc29ja2V0SW5pdGlhbGl6ZWRSZWYgPSB1c2VSZWYoZmFsc2UpO1xyXG4gIGNvbnN0IGlzTG9hZGluZ1JlZiA9IHVzZVJlZihmYWxzZSk7XHJcbiAgY29uc3QgbGFzdEluaXRBdHRlbXB0UmVmID0gdXNlUmVmKDApO1xyXG4gIGNvbnN0IHJlY29ubmVjdEF0dGVtcHRzUmVmID0gdXNlUmVmKDApO1xyXG5cclxuICAvLyBSZWRlZmluaXIgYSByZWZlcsOqbmNpYSBxdWFuZG8gbyB1c3XDoXJpbyBtdWRhclxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBTZSBvIHVzdcOhcmlvIGZvciBudWxsIChsb2dvdXQpLCBkZXNjb25lY3RhciBvIHNvY2tldFxyXG4gICAgaWYgKCF1c2VyICYmIHNvY2tldCkge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIHNvY2tldC5kaXNjb25uZWN0KCk7XHJcbiAgICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xyXG4gICAgICAgIHNldFNvY2tldChudWxsKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGRlc2NvbmVjdGFyIHNvY2tldCBhcMOzcyBsb2dvdXQ6JywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmVzZXRhciBlc3RhZG8gcXVhbmRvIG8gdXN1w6FyaW8gbXVkYVxyXG4gICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgbGFzdEluaXRBdHRlbXB0UmVmLmN1cnJlbnQgPSAwO1xyXG4gICAgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCA9IDA7XHJcbiAgfSwgW3VzZXIsIHNvY2tldF0pO1xyXG5cclxuICAvLyBMaW1wYXIgZXN0YWRvIGRvIGNoYXQgcXVhbmRvIG8gdXN1w6FyaW8gbXVkYVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBTZSBvIHVzdcOhcmlvIG11ZG91IChub3ZvIGxvZ2luKSwgbGltcGFyIHRvZG8gbyBlc3RhZG8gZG8gY2hhdFxyXG4gICAgaWYgKHVzZXIpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VzdcOhcmlvIGxvZ2FkbywgbGltcGFuZG8gZXN0YWRvIGRvIGNoYXQgcGFyYSBub3ZvIHVzdcOhcmlvOicsIHVzZXIuZnVsbE5hbWUpO1xyXG4gICAgICAvLyBMaW1wYXIgY29udmVyc2FzIGUgbWVuc2FnZW5zIHBhcmEgbyBub3ZvIHVzdcOhcmlvXHJcbiAgICAgIHNldENvbnZlcnNhdGlvbnMoW10pO1xyXG4gICAgICBzZXRNZXNzYWdlcyh7fSk7XHJcbiAgICAgIHNldEFjdGl2ZUNvbnZlcnNhdGlvbihudWxsKTtcclxuICAgICAgc2V0VW5yZWFkQ291bnQoMCk7XHJcbiAgICAgIHNldElzUGFuZWxPcGVuKGZhbHNlKTtcclxuICAgICAgc2V0SXNNb2RhbE9wZW4oZmFsc2UpO1xyXG4gICAgICBcclxuICAgICAgLy8gTGltcGFyIGNhY2hlXHJcbiAgICAgIHJlcXVlc3RDYWNoZS5jb252ZXJzYXRpb25zLmRhdGEgPSBudWxsO1xyXG4gICAgICByZXF1ZXN0Q2FjaGUudW5yZWFkQ291bnQuZGF0YSA9IG51bGw7XHJcbiAgICAgIHJlcXVlc3RDYWNoZS5tZXNzYWdlcyA9IHt9O1xyXG4gICAgICByZXF1ZXN0Q2FjaGUudG9rZW5DaGVjay50aW1lc3RhbXAgPSAwO1xyXG4gICAgICByZXF1ZXN0Q2FjaGUudG9rZW5DaGVjay52YWxpZCA9IGZhbHNlO1xyXG4gICAgICBcclxuICAgICAgY29uc29sZS5sb2coJ0NhY2hlIGxpbXBvIHBhcmEgbm92byB1c3XDoXJpbycpO1xyXG4gICAgICBcclxuICAgICAgLy8gTGltcGFyIGRhZG9zIHJlc2lkdWFpcyBkbyBsb2NhbFN0b3JhZ2UgcmVsYWNpb25hZG9zIGFvIGNoYXRcclxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgLy8gTGltcGFyIHF1YWxxdWVyIGNhY2hlIGVzcGVjw61maWNvIGRvIGNoYXQgcXVlIHBvc3NhIGVzdGFyIG5vIGxvY2FsU3RvcmFnZVxyXG4gICAgICAgIGNvbnN0IGtleXNUb1JlbW92ZSA9IFtdO1xyXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbG9jYWxTdG9yYWdlLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgICAgICBjb25zdCBrZXkgPSBsb2NhbFN0b3JhZ2Uua2V5KGkpO1xyXG4gICAgICAgICAgaWYgKGtleSAmJiAoa2V5LnN0YXJ0c1dpdGgoJ2NoYXRfJykgfHwga2V5LnN0YXJ0c1dpdGgoJ2NvbnZlcnNhdGlvbl8nKSB8fCBrZXkuc3RhcnRzV2l0aCgnbWVzc2FnZV8nKSkpIHtcclxuICAgICAgICAgICAga2V5c1RvUmVtb3ZlLnB1c2goa2V5KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAga2V5c1RvUmVtb3ZlLmZvckVhY2goa2V5ID0+IGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSkpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdEYWRvcyByZXNpZHVhaXMgZG8gY2hhdCByZW1vdmlkb3MgZG8gbG9jYWxTdG9yYWdlJyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbdXNlcj8uaWRdKTsgLy8gRXhlY3V0YXIgcXVhbmRvIG8gSUQgZG8gdXN1w6FyaW8gbXVkYXJcclxuXHJcbiAgLy8gSW5pY2lhbGl6YXIgY29uZXjDo28gV2ViU29ja2V0XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFNlIG7Do28gaMOhIHVzdcOhcmlvLCBuw6NvIGluaWNpYWxpemFyXHJcbiAgICBpZiAoIXVzZXIpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1VzdcOhcmlvIG7Do28gbG9nYWRvLCBuw6NvIGluaWNpYWxpemFuZG8gV2ViU29ja2V0Jyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGllbnRlcyBhZ29yYSBwb2RlbSB1c2FyIG8gY2hhdFxyXG4gICAgY29uc29sZS5sb2coJ0luaWNpYWxpemFuZG8gY2hhdCBwYXJhIHVzdcOhcmlvOicsIHVzZXIucm9sZSB8fCAnVVNFUicpO1xyXG5cclxuICAgIC8vIFNlIGrDoSBleGlzdGUgdW0gc29ja2V0IGNvbmVjdGFkbywgbsOjbyBmYXplciBuYWRhXHJcbiAgICBpZiAoc29ja2V0ICYmIGlzQ29ubmVjdGVkKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdXZWJTb2NrZXQgasOhIGVzdMOhIGNvbmVjdGFkbywgbsOjbyDDqSBuZWNlc3PDoXJpbyByZWluaWNpYWxpemFyJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBTZSBqw6EgZXN0w6EgaW5pY2lhbGl6YWRvIGUgYSDDumx0aW1hIHRlbnRhdGl2YSBmb2kgcmVjZW50ZSwgbsOjbyB0ZW50YXIgbm92YW1lbnRlXHJcbiAgICBpZiAoc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCAmJiBEYXRlLm5vdygpIC0gbGFzdEluaXRBdHRlbXB0UmVmLmN1cnJlbnQgPCA2MDAwMCkge1xyXG4gICAgICBjb25zb2xlLmxvZygnU29ja2V0IGrDoSBpbmljaWFsaXphZG8gcmVjZW50ZW1lbnRlLCBhZ3VhcmRhbmRvLi4uJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBNYXJjYXIgY29tbyBpbmljaWFsaXphZG8gaW1lZGlhdGFtZW50ZSBwYXJhIGV2aXRhciBtw7psdGlwbGFzIHRlbnRhdGl2YXNcclxuICAgIHNvY2tldEluaXRpYWxpemVkUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG4gICAgbGFzdEluaXRBdHRlbXB0UmVmLmN1cnJlbnQgPSBEYXRlLm5vdygpO1xyXG5cclxuICAgIC8vIEZ1bsOnw6NvIGFzc8OtbmNyb25hIHBhcmEgaW5pY2lhbGl6YXIgbyBXZWJTb2NrZXRcclxuICAgIGNvbnN0IGluaXRpYWxpemVXZWJTb2NrZXQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2FkbyBhbnRlcyBkZSBpbmljaWFsaXphciBvIFdlYlNvY2tldFxyXG4gICAgICBpZiAoIXVzZXIpIHJldHVybjtcclxuXHJcbiAgICAgIC8vIENsaWVudGVzIGFnb3JhIHBvZGVtIHVzYXIgbyBjaGF0XHJcbiAgICAgIGNvbnNvbGUubG9nKCdJbmljaWFsaXphbmRvIFdlYlNvY2tldCBwYXJhOicsIHVzZXIucm9sZSB8fCAnVVNFUicpO1xyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSByZXR1cm47XHJcblxyXG4gICAgICAvLyBFdml0YXIgcmVjb25leMO1ZXMgZGVzbmVjZXNzw6FyaWFzIC0gdmVyaWZpY2HDp8OjbyBhZGljaW9uYWxcclxuICAgICAgaWYgKHNvY2tldCkge1xyXG4gICAgICAgIGlmIChpc0Nvbm5lY3RlZCkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBqw6EgY29uZWN0YWRvLCBpZ25vcmFuZG8gaW5pY2lhbGl6YcOnw6NvJyk7XHJcbiAgICAgICAgICByZXR1cm4gc29ja2V0OyAvLyBSZXRvcm5hciBvIHNvY2tldCBleGlzdGVudGVcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgLy8gU2UgbyBzb2NrZXQgZXhpc3RlIG1hcyBuw6NvIGVzdMOhIGNvbmVjdGFkbywgdGVudGFyIHJlY29uZWN0YXIgZW0gdmV6IGRlIGNyaWFyIHVtIG5vdm9cclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdTb2NrZXQgZXhpc3RlIG1hcyBuw6NvIGVzdMOhIGNvbmVjdGFkbywgdGVudGFuZG8gcmVjb25lY3RhcicpO1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgLy8gVGVudGFyIHJlY29uZWN0YXIgbyBzb2NrZXQgZXhpc3RlbnRlIGVtIHZleiBkZSBjcmlhciB1bSBub3ZvXHJcbiAgICAgICAgICAgIGlmICghc29ja2V0LmNvbm5lY3RlZCAmJiBzb2NrZXQuY29ubmVjdCkge1xyXG4gICAgICAgICAgICAgIHNvY2tldC5jb25uZWN0KCk7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1RlbnRhdGl2YSBkZSByZWNvbmV4w6NvIGluaWNpYWRhJyk7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIHNvY2tldDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyByZWNvbmVjdGFyIHNvY2tldCBleGlzdGVudGU6JywgZXJyb3IpO1xyXG4gICAgICAgICAgICAvLyBTw7MgZGVzY29uZWN0YXIgc2UgYSByZWNvbmV4w6NvIGZhbGhhclxyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgIHNvY2tldC5kaXNjb25uZWN0KCk7XHJcbiAgICAgICAgICAgIH0gY2F0Y2ggKGRpc2Nvbm5lY3RFcnJvcikge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gZGVzY29uZWN0YXIgc29ja2V0IGFww7NzIGZhbGhhIG5hIHJlY29uZXjDo286JywgZGlzY29ubmVjdEVycm9yKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gTWFyY2FyIGNvbW8gaW5pY2lhbGl6YWRvIHBhcmEgZXZpdGFyIG3Dumx0aXBsYXMgaW5pY2lhbGl6YcOnw7Vlc1xyXG4gICAgICBzb2NrZXRJbml0aWFsaXplZFJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuXHJcbiAgICAgIC8vIExpbWl0YXIgYSBmcmVxdcOqbmNpYSBkZSByZWNvbmV4w7Vlc1xyXG4gICAgICBjb25zdCBtYXhSZWNvbm5lY3RBdHRlbXB0cyA9IDU7XHJcbiAgICAgIGNvbnN0IHJlY29ubmVjdERlbGF5ID0gMzAwMDsgLy8gMyBzZWd1bmRvc1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ0luaWNpYWxpemFuZG8gV2ViU29ja2V0Li4uJyk7XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIC8vIFZlcmlmaWNhciBzZSBvIHRva2VuIMOpIHbDoWxpZG8gYW50ZXMgZGUgaW5pY2lhbGl6YXIgbyBXZWJTb2NrZXRcclxuICAgICAgICAvLyBVc2FyIHVtYSB2ZXJpZmljYcOnw6NvIG1haXMgc2ltcGxlcyBwYXJhIGV2aXRhciBtw7psdGlwbGFzIHJlcXVpc2nDp8O1ZXNcclxuICAgICAgICAvLyBBc3N1bWlyIHF1ZSBvIHRva2VuIMOpIHbDoWxpZG8gc2UgbyB1c3XDoXJpbyBlc3TDoSBsb2dhZG9cclxuICAgICAgICBjb25zb2xlLmxvZygnVG9rZW4gdsOhbGlkbywgaW5pY2lhbGl6YW5kbyBXZWJTb2NrZXQgcGFyYSBvIHVzdcOhcmlvOicsIHVzZXIuZnVsbE5hbWUpO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygnSW5pY2lhbGl6YW5kbyBXZWJTb2NrZXQgY29tIGF1dGVudGljYcOnw6NvJyk7XHJcblxyXG4gICAgICAgIGNvbnN0IHNvY2tldEluc3RhbmNlID0gaW8oQVBJX1VSTCwge1xyXG4gICAgICAgICAgcGF0aDogJy9zb2NrZXQuaW8nLFxyXG4gICAgICAgICAgYXV0aDogeyB0b2tlbjogY3VycmVudFRva2VuIH0sXHJcbiAgICAgICAgICB0cmFuc3BvcnRzOiBbJ3dlYnNvY2tldCcsICdwb2xsaW5nJ10sXHJcbiAgICAgICAgICByZWNvbm5lY3Rpb25BdHRlbXB0czogbWF4UmVjb25uZWN0QXR0ZW1wdHMsXHJcbiAgICAgICAgICByZWNvbm5lY3Rpb25EZWxheTogcmVjb25uZWN0RGVsYXksXHJcbiAgICAgICAgICB0aW1lb3V0OiAxMDAwMCwgLy8gMTAgc2VndW5kb3NcclxuICAgICAgICAgIGF1dG9Db25uZWN0OiB0cnVlLFxyXG4gICAgICAgICAgcmVjb25uZWN0aW9uOiB0cnVlXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIHNvY2tldEluc3RhbmNlLm9uKCdjb25uZWN0JywgKCkgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBjb25uZWN0ZWQnKTtcclxuICAgICAgICAgIHNldElzQ29ubmVjdGVkKHRydWUpO1xyXG4gICAgICAgICAgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCA9IDA7XHJcblxyXG4gICAgICAgICAgLy8gRGlzcGFyYXIgZXZlbnRvIHBlcnNvbmFsaXphZG8gcGFyYSBub3RpZmljYXIgY29tcG9uZW50ZXMgc29icmUgYSBjb25leMOjb1xyXG4gICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgIGRldGFpbDogeyB0eXBlOiAnY29ubmVjdGlvbicsIHN0YXR1czogJ2Nvbm5lY3RlZCcsIHRpbWVzdGFtcDogRGF0ZS5ub3coKSB9XHJcbiAgICAgICAgICB9KSk7XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIHNvY2tldEluc3RhbmNlLm9uKCdkaXNjb25uZWN0JywgKCkgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBkaXNjb25uZWN0ZWQnKTtcclxuICAgICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcclxuXHJcbiAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGVyc29uYWxpemFkbyBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIGRlc2NvbmV4w6NvXHJcbiAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2NoYXQ6d2Vic29ja2V0OnVwZGF0ZScsIHtcclxuICAgICAgICAgICAgZGV0YWlsOiB7IHR5cGU6ICdjb25uZWN0aW9uJywgc3RhdHVzOiAnZGlzY29ubmVjdGVkJywgdGltZXN0YW1wOiBEYXRlLm5vdygpIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2Nvbm5lY3RfZXJyb3InLCAoZXJyb3IpID0+IHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1dlYlNvY2tldCBjb25uZWN0aW9uIGVycm9yOicsIGVycm9yKTtcclxuICAgICAgICAgIHJlY29ubmVjdEF0dGVtcHRzUmVmLmN1cnJlbnQrKztcclxuXHJcbiAgICAgICAgICAvLyBTZSBvIGVycm8gZm9yIGRlIGF1dGVudGljYcOnw6NvLCBuw6NvIHRlbnRhciByZWNvbmVjdGFyXHJcbiAgICAgICAgICBpZiAoZXJyb3IubWVzc2FnZSAmJiBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdBdXRoZW50aWNhdGlvbiBlcnJvcicpKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gZGUgYXV0ZW50aWNhw6fDo28gbm8gV2ViU29ja2V0LCBuw6NvIHRlbnRhbmRvIHJlY29uZWN0YXInKTtcclxuICAgICAgICAgICAgc29ja2V0SW5zdGFuY2UuZGlzY29ubmVjdCgpO1xyXG4gICAgICAgICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XHJcbiAgICAgICAgICAgIHNvY2tldEluaXRpYWxpemVkUmVmLmN1cnJlbnQgPSBmYWxzZTsgLy8gUGVybWl0aXIgbm92YSB0ZW50YXRpdmEgbm8gZnV0dXJvXHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBpZiAocmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCA+PSBtYXhSZWNvbm5lY3RBdHRlbXB0cykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdNw6F4aW1vIGRlIHRlbnRhdGl2YXMgZGUgcmVjb25leMOjbyBhdGluZ2lkbycpO1xyXG4gICAgICAgICAgICBzb2NrZXRJbnN0YW5jZS5kaXNjb25uZWN0KCk7XHJcbiAgICAgICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcclxuICAgICAgICAgICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IGZhbHNlOyAvLyBQZXJtaXRpciBub3ZhIHRlbnRhdGl2YSBubyBmdXR1cm9cclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gRXZlbnRvIHBhcmEgcmVjZWJlciBub3ZhcyBtZW5zYWdlbnNcclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vbignbWVzc2FnZTpuZXcnLCAobWVzc2FnZSkgPT4ge1xyXG4gICAgICAgICAgaWYgKCFtZXNzYWdlIHx8ICFtZXNzYWdlLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ01lbnNhZ2VtIGludsOhbGlkYSByZWNlYmlkYTonLCBtZXNzYWdlKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIFVzYXIgdW1hIGZ1bsOnw6NvIGFuw7RuaW1hIHBhcmEgZXZpdGFyIGRlcGVuZMOqbmNpYXMgY2lyY3VsYXJlc1xyXG4gICAgICAgICAgY29uc3QgdXNlcklkID0gdXNlcj8uaWQ7XHJcblxyXG4gICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgbWVuc2FnZW0gdGVtIHVtIHRlbXBJZCBhc3NvY2lhZG8gKHBhcmEgc3Vic3RpdHVpciBtZW5zYWdlbSB0ZW1wb3LDoXJpYSlcclxuICAgICAgICAgIC8vIE8gYmFja2VuZCBwb2RlIG7Do28gcmV0b3JuYXIgbyB0ZW1wSWQsIGVudMOjbyBwcmVjaXNhbW9zIHZlcmlmaWNhciBzZSDDqSB1bWEgbWVuc2FnZW0gZG8gdXN1w6FyaW8gYXR1YWxcclxuICAgICAgICAgIGNvbnN0IHRlbXBJZCA9IG1lc3NhZ2UudGVtcElkO1xyXG4gICAgICAgICAgY29uc3QgaXNDdXJyZW50VXNlck1lc3NhZ2UgPSBtZXNzYWdlLnNlbmRlcklkID09PSB1c2VySWQ7XHJcblxyXG5cclxuICAgICAgICAgIC8vIEF0dWFsaXphciBtZW5zYWdlbnMgZGEgY29udmVyc2FcclxuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25NZXNzYWdlcyA9IHByZXZbbWVzc2FnZS5jb252ZXJzYXRpb25JZF0gfHwgW107XHJcblxyXG4gICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgYSBtZW5zYWdlbSBqw6EgZXhpc3RlIHBhcmEgZXZpdGFyIGR1cGxpY2HDp8Ojb1xyXG4gICAgICAgICAgICBpZiAoY29udmVyc2F0aW9uTWVzc2FnZXMuc29tZShtID0+IG0uaWQgPT09IG1lc3NhZ2UuaWQpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIHByZXY7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIFNlIHRpdmVyIHVtIHRlbXBJZCwgc3Vic3RpdHVpciBhIG1lbnNhZ2VtIHRlbXBvcsOhcmlhIHBlbGEgbWVuc2FnZW0gcmVhbFxyXG4gICAgICAgICAgICBpZiAodGVtcElkICYmIGNvbnZlcnNhdGlvbk1lc3NhZ2VzLnNvbWUobSA9PiBtLmlkID09PSB0ZW1wSWQpKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICBbbWVzc2FnZS5jb252ZXJzYXRpb25JZF06IGNvbnZlcnNhdGlvbk1lc3NhZ2VzLm1hcChtID0+XHJcbiAgICAgICAgICAgICAgICAgIG0uaWQgPT09IHRlbXBJZCA/IG1lc3NhZ2UgOiBtXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gU2UgZm9yIHVtYSBtZW5zYWdlbSBkbyB1c3XDoXJpbyBhdHVhbCwgdmVyaWZpY2FyIHNlIGjDoSBtZW5zYWdlbnMgdGVtcG9yw6FyaWFzIHJlY2VudGVzXHJcbiAgICAgICAgICAgIGNvbnN0IGlzQ3VycmVudFVzZXJNZXNzYWdlID0gbWVzc2FnZS5zZW5kZXJJZCA9PT0gdXNlcklkIHx8IG1lc3NhZ2Uuc2VuZGVyQ2xpZW50SWQgPT09IHVzZXJJZDtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIGlmIChpc0N1cnJlbnRVc2VyTWVzc2FnZSAmJiAhdGVtcElkKSB7XHJcbiAgICAgICAgICAgICAgLy8gUHJvY3VyYXIgcG9yIG1lbnNhZ2VucyB0ZW1wb3LDoXJpYXMgcmVjZW50ZXMgKG5vcyDDumx0aW1vcyAxMCBzZWd1bmRvcylcclxuICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xyXG4gICAgICAgICAgICAgIGNvbnN0IHRlbXBNZXNzYWdlcyA9IGNvbnZlcnNhdGlvbk1lc3NhZ2VzLmZpbHRlcihtID0+IHtcclxuICAgICAgICAgICAgICAgIGlmICghbS5pc1RlbXApIHJldHVybiBmYWxzZTtcclxuICAgICAgICAgICAgICAgIGlmICghKG0uc2VuZGVySWQgPT09IHVzZXJJZCB8fCBtLnNlbmRlckNsaWVudElkID09PSB1c2VySWQpKSByZXR1cm4gZmFsc2U7XHJcbiAgICAgICAgICAgICAgICBpZiAoKG5vdyAtIG5ldyBEYXRlKG0uY3JlYXRlZEF0KSkgPj0gMTAwMDApIHJldHVybiBmYWxzZTsgLy8gMTAgc2VndW5kb3NcclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgLy8gUGFyYSBtZW5zYWdlbnMgY29tIGFuZXhvcywgdmVyaWZpY2FyIHNlIGFtYmFzIHPDo28gQVRUQUNITUVOVFxyXG4gICAgICAgICAgICAgICAgaWYgKG1lc3NhZ2UuY29udGVudFR5cGUgPT09ICdBVFRBQ0hNRU5UJyAmJiBtLmNvbnRlbnRUeXBlID09PSAnQVRUQUNITUVOVCcpIHtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIC8vIFBhcmEgbWVuc2FnZW5zIGRlIHRleHRvLCB2ZXJpZmljYXIgc2UgbyBjb250ZcO6ZG8gw6kgaWd1YWxcclxuICAgICAgICAgICAgICAgIGlmIChtZXNzYWdlLmNvbnRlbnRUeXBlID09PSAnVEVYVCcgJiYgbS5jb250ZW50VHlwZSA9PT0gJ1RFWFQnICYmIG0uY29udGVudCA9PT0gbWVzc2FnZS5jb250ZW50KSB7XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICAgIGlmICh0ZW1wTWVzc2FnZXMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgICAgLy8gU3Vic3RpdHVpciBhIHByaW1laXJhIG1lbnNhZ2VtIHRlbXBvcsOhcmlhIGVuY29udHJhZGFcclxuICAgICAgICAgICAgICAgIGNvbnN0IHRlbXBNZXNzYWdlID0gdGVtcE1lc3NhZ2VzWzBdO1xyXG5cclxuICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgIFttZXNzYWdlLmNvbnZlcnNhdGlvbklkXTogY29udmVyc2F0aW9uTWVzc2FnZXMubWFwKG0gPT5cclxuICAgICAgICAgICAgICAgICAgICBtLmlkID09PSB0ZW1wTWVzc2FnZS5pZCA/IG1lc3NhZ2UgOiBtXHJcbiAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBDYXNvIGNvbnRyw6FyaW8sIGFkaWNpb25hciBhIG5vdmEgbWVuc2FnZW1cclxuICAgICAgICAgICAgLy8gTWFudGVyIGEgb3JkZW0gY3Jvbm9sw7NnaWNhIChtYWlzIGFudGlnYXMgcHJpbWVpcm8pXHJcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRNZXNzYWdlcyA9IFsuLi5jb252ZXJzYXRpb25NZXNzYWdlcywgbWVzc2FnZV0uc29ydCgoYSwgYikgPT5cclxuICAgICAgICAgICAgICBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkgLSBuZXcgRGF0ZShiLmNyZWF0ZWRBdClcclxuICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICBbbWVzc2FnZS5jb252ZXJzYXRpb25JZF06IHVwZGF0ZWRNZXNzYWdlc1xyXG4gICAgICAgICAgICB9O1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgLy8gQXR1YWxpemFyIGxpc3RhIGRlIGNvbnZlcnNhcyAobW92ZXIgcGFyYSBvIHRvcG8pXHJcbiAgICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4ge1xyXG4gICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgYSBjb252ZXJzYSBleGlzdGVcclxuICAgICAgICAgICAgY29uc3QgY29udmVyc2F0aW9uID0gcHJldi5maW5kKGMgPT4gYy5pZCA9PT0gbWVzc2FnZS5jb252ZXJzYXRpb25JZCk7XHJcbiAgICAgICAgICAgIGlmICghY29udmVyc2F0aW9uKSByZXR1cm4gcHJldjtcclxuXHJcbiAgICAgICAgICAgIC8vIFNlIGEgY29udmVyc2EgasOhIHRpdmVyIHVtYSBtZW5zYWdlbSB0ZW1wb3LDoXJpYSBjb20gbyBtZXNtbyB0ZW1wSWQsIG7Do28gbW92ZXIgcGFyYSBvIHRvcG9cclxuICAgICAgICAgICAgaWYgKHRlbXBJZCAmJiBjb252ZXJzYXRpb24ubGFzdE1lc3NhZ2UgJiYgY29udmVyc2F0aW9uLmxhc3RNZXNzYWdlLmlkID09PSB0ZW1wSWQpIHtcclxuICAgICAgICAgICAgICAvLyBBcGVuYXMgYXR1YWxpemFyIGEgw7psdGltYSBtZW5zYWdlbSBzZW0gcmVvcmRlbmFyXHJcbiAgICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKGMgPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKGMuaWQgPT09IG1lc3NhZ2UuY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uYywgbGFzdE1lc3NhZ2U6IG1lc3NhZ2UgfTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHJldHVybiBjO1xyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBTZSBmb3IgdW1hIG1lbnNhZ2VtIGRvIHVzdcOhcmlvIGF0dWFsIGUgbsOjbyB0aXZlciB0ZW1wSWQsIHZlcmlmaWNhciBzZSBow6EgdW1hIG1lbnNhZ2VtIHRlbXBvcsOhcmlhIHJlY2VudGVcclxuICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50VXNlck1lc3NhZ2UgPSBtZXNzYWdlLnNlbmRlcklkID09PSB1c2VySWQgfHwgbWVzc2FnZS5zZW5kZXJDbGllbnRJZCA9PT0gdXNlcklkO1xyXG4gICAgICAgICAgICBpZiAoaXNDdXJyZW50VXNlck1lc3NhZ2UgJiYgIXRlbXBJZCAmJiBjb252ZXJzYXRpb24ubGFzdE1lc3NhZ2UgJiYgY29udmVyc2F0aW9uLmxhc3RNZXNzYWdlLmlzVGVtcCkge1xyXG4gICAgICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIMO6bHRpbWEgbWVuc2FnZW0gw6kgdGVtcG9yw6FyaWEgZSB0ZW0gbyBtZXNtbyBjb250ZcO6ZG9cclxuICAgICAgICAgICAgICBpZiAoY29udmVyc2F0aW9uLmxhc3RNZXNzYWdlLmNvbnRlbnQgPT09IG1lc3NhZ2UuY29udGVudCAmJlxyXG4gICAgICAgICAgICAgICAgICAoY29udmVyc2F0aW9uLmxhc3RNZXNzYWdlLnNlbmRlcklkID09PSB1c2VySWQgfHwgY29udmVyc2F0aW9uLmxhc3RNZXNzYWdlLnNlbmRlckNsaWVudElkID09PSB1c2VySWQpKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBBcGVuYXMgYXR1YWxpemFyIGEgw7psdGltYSBtZW5zYWdlbSBzZW0gcmVvcmRlbmFyXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gcHJldi5tYXAoYyA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChjLmlkID09PSBtZXNzYWdlLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uYywgbGFzdE1lc3NhZ2U6IG1lc3NhZ2UgfTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICByZXR1cm4gYztcclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gQ2FzbyBjb250csOhcmlvLCBtb3ZlciBwYXJhIG8gdG9wb1xyXG4gICAgICAgICAgICBjb25zdCB1cGRhdGVkQ29udmVyc2F0aW9ucyA9IHByZXYuZmlsdGVyKGMgPT4gYy5pZCAhPT0gbWVzc2FnZS5jb252ZXJzYXRpb25JZCk7XHJcbiAgICAgICAgICAgIHJldHVybiBbXHJcbiAgICAgICAgICAgICAgeyAuLi5jb252ZXJzYXRpb24sIGxhc3RNZXNzYWdlOiBtZXNzYWdlIH0sXHJcbiAgICAgICAgICAgICAgLi4udXBkYXRlZENvbnZlcnNhdGlvbnNcclxuICAgICAgICAgICAgXTtcclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIC8vIEluY3JlbWVudGFyIGNvbnRhZG9yIGRlIG7Do28gbGlkYXMgc2UgYSBtZW5zYWdlbSBuw6NvIGZvciBkbyB1c3XDoXJpbyBhdHVhbFxyXG4gICAgICAgICAgY29uc3QgaXNGcm9tQ3VycmVudFVzZXIgPSBtZXNzYWdlLnNlbmRlcklkID09PSB1c2VySWQgfHwgbWVzc2FnZS5zZW5kZXJDbGllbnRJZCA9PT0gdXNlcklkO1xyXG4gICAgICAgICAgaWYgKCFpc0Zyb21DdXJyZW50VXNlcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnW1dlYlNvY2tldF0gTm92YSBtZW5zYWdlbSBkZSBvdXRybyB1c3XDoXJpbywgaW5jcmVtZW50YW5kbyBjb250YWRvcicpO1xyXG4gICAgICAgICAgICBzZXRVbnJlYWRDb3VudChwcmV2ID0+IHtcclxuICAgICAgICAgICAgICBjb25zdCBuZXdDb3VudCA9IHByZXYgKyAxO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbV2ViU29ja2V0XSBDb250YWRvciBhdHVhbGl6YWRvIGRlJywgcHJldiwgJ3BhcmEnLCBuZXdDb3VudCk7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIG5ld0NvdW50O1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIC8vIEF0dWFsaXphciBjb250YWRvciBkYSBjb252ZXJzYSBlc3BlY8OtZmljYVxyXG4gICAgICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4gcHJldi5tYXAoY29udiA9PiB7XHJcbiAgICAgICAgICAgICAgaWYgKGNvbnYuaWQgPT09IG1lc3NhZ2UuY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG5ld1VucmVhZENvdW50ID0gKGNvbnYudW5yZWFkQ291bnQgfHwgMCkgKyAxO1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coYFtXZWJTb2NrZXRdIENvbnZlcnNhICR7Y29udi5pZH0gY29udGFkb3I6ICR7Y29udi51bnJlYWRDb3VudCB8fCAwfSAtPiAke25ld1VucmVhZENvdW50fWApO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgdW5yZWFkQ291bnQ6IG5ld1VucmVhZENvdW50IH07XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHJldHVybiBjb252O1xyXG4gICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnW1dlYlNvY2tldF0gTWVuc2FnZW0gZG8gcHLDs3ByaW8gdXN1w6FyaW8sIG7Do28gaW5jcmVtZW50YW5kbyBjb250YWRvcicpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIERpc3BhcmFyIGV2ZW50byBwZXJzb25hbGl6YWRvIHBhcmEgbm90aWZpY2FyIGNvbXBvbmVudGVzIHNvYnJlIGEgbm92YSBtZW5zYWdlbVxyXG4gICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgIGRldGFpbDogeyB0eXBlOiAnbWVzc2FnZScsIGNvbnZlcnNhdGlvbklkOiBtZXNzYWdlLmNvbnZlcnNhdGlvbklkLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfVxyXG4gICAgICAgICAgfSkpO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBFdmVudG8gcGFyYSBhdHVhbGl6YcOnw6NvIGRlIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ3VucmVhZDp1cGRhdGUnLCAoZGF0YSkgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1tXZWJTb2NrZXRdIEF0dWFsaXphw6fDo28gZGUgbWVuc2FnZW5zIG7Do28gbGlkYXMgcmVjZWJpZGE6JywgZGF0YSk7XHJcbiAgICAgICAgICBpZiAoZGF0YSAmJiB0eXBlb2YgZGF0YS50b3RhbFVucmVhZCA9PT0gJ251bWJlcicpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFtXZWJTb2NrZXRdIEF0dWFsaXphbmRvIGNvbnRhZG9yIHRvdGFsIHBhcmE6ICR7ZGF0YS50b3RhbFVucmVhZH1gKTtcclxuICAgICAgICAgICAgc2V0VW5yZWFkQ291bnQoZGF0YS50b3RhbFVucmVhZCk7XHJcblxyXG4gICAgICAgICAgICAvLyBBdHVhbGl6YXIgYXMgY29udmVyc2FzIGNvbSBhcyBjb250YWdlbnMgZGUgbsOjbyBsaWRhc1xyXG4gICAgICAgICAgICBpZiAoZGF0YS5jb252ZXJzYXRpb25zICYmIEFycmF5LmlzQXJyYXkoZGF0YS5jb252ZXJzYXRpb25zKSkge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbV2ViU29ja2V0XSBBdHVhbGl6YW5kbyAke2RhdGEuY29udmVyc2F0aW9ucy5sZW5ndGh9IGNvbnZlcnNhcyBjb20gY29udGFkb3Jlc2ApO1xyXG4gICAgICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gcHJldi5tYXAoY29udiA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIC8vIFByb2N1cmFyIGVzdGEgY29udmVyc2Egbm9zIGRhZG9zIGRlIG7Do28gbGlkYXNcclxuICAgICAgICAgICAgICAgICAgY29uc3QgdW5yZWFkSW5mbyA9IGRhdGEuY29udmVyc2F0aW9ucy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgIGl0ZW0gPT4gaXRlbS5jb252ZXJzYXRpb25JZCA9PT0gY29udi5pZFxyXG4gICAgICAgICAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgICAgICAgICAgaWYgKHVucmVhZEluZm8pIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgW1dlYlNvY2tldF0gQ29udmVyc2EgJHtjb252LmlkfSBhZ29yYSB0ZW0gJHt1bnJlYWRJbmZvLnVucmVhZENvdW50fSBtZW5zYWdlbnMgbsOjbyBsaWRhc2ApO1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7IC4uLmNvbnYsIHVucmVhZENvdW50OiB1bnJlYWRJbmZvLnVucmVhZENvdW50IH07XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgLy8gUmVzZXRhciBjb250YWRvciBzZSBuw6NvIGVzdGl2ZXIgbmEgbGlzdGEgZGUgbsOjbyBsaWRhc1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm4geyAuLi5jb252LCB1bnJlYWRDb3VudDogMCB9O1xyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGVyc29uYWxpemFkbyBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIGF0dWFsaXphw6fDo28gZGUgbsOjbyBsaWRhc1xyXG4gICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgIGRldGFpbDogeyB0eXBlOiAndW5yZWFkJywgdGltZXN0YW1wOiBEYXRlLm5vdygpIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gRXZlbnRvIHBhcmEgYXR1YWxpemHDp8OjbyBkZSBsaXN0YSBkZSBjb252ZXJzYXNcclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vbignY29udmVyc2F0aW9uczp1cGRhdGUnLCAoZGF0YSkgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0F0dWFsaXphw6fDo28gZGUgbGlzdGEgZGUgY29udmVyc2FzIHJlY2ViaWRhIHZpYSBXZWJTb2NrZXQ6JywgZGF0YSk7XHJcbiAgICAgICAgICAvLyBWZXJpZmljYXIgc2Ugb3MgZGFkb3MgZXN0w6NvIG5vIGZvcm1hdG8gZXNwZXJhZG9cclxuICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgIC8vIFBvZGUgdmlyIGNvbW8gYXJyYXkgZGlyZXRvIG91IGNvbW8gb2JqZXRvIGNvbSBwcm9wcmllZGFkZSBjb252ZXJzYXRpb25zXHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbnNBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogZGF0YS5jb252ZXJzYXRpb25zIHx8IFtdO1xyXG5cclxuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoY29udmVyc2F0aW9uc0FycmF5KSAmJiBjb252ZXJzYXRpb25zQXJyYXkubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBdHVhbGl6YW5kbyAke2NvbnZlcnNhdGlvbnNBcnJheS5sZW5ndGh9IGNvbnZlcnNhcyB2aWEgV2ViU29ja2V0YCk7XHJcbiAgICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhjb252ZXJzYXRpb25zQXJyYXkpO1xyXG5cclxuICAgICAgICAgICAgICAvLyBBdHVhbGl6YXIgYSBmbGFnIHBhcmEgaW5kaWNhciBxdWUgb3MgZGFkb3MgZm9yYW0gY2FycmVnYWRvc1xyXG4gICAgICAgICAgICAgIGluaXRpYWxEYXRhTG9hZGVkUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG5cclxuICAgICAgICAgICAgICAvLyBBdHVhbGl6YXIgbyBjYWNoZVxyXG4gICAgICAgICAgICAgIHJlcXVlc3RDYWNoZS5jb252ZXJzYXRpb25zLmRhdGEgPSBjb252ZXJzYXRpb25zQXJyYXk7XHJcbiAgICAgICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMudGltZXN0YW1wID0gRGF0ZS5ub3coKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gRGlzcGFyYXIgZXZlbnRvIHBlcnNvbmFsaXphZG8gcGFyYSBub3RpZmljYXIgY29tcG9uZW50ZXMgc29icmUgYSBhdHVhbGl6YcOnw6NvXHJcbiAgICAgICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgICAgICBkZXRhaWw6IHsgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfVxyXG4gICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGb3JtYXRvIGludsOhbGlkbyBvdSBhcnJheSB2YXppbyBkZSBjb252ZXJzYXMgcmVjZWJpZG8gdmlhIFdlYlNvY2tldDonLCBkYXRhKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gU2UgcmVjZWJlbW9zIHVtIGFycmF5IHZhemlvLCBmb3LDp2FyIGNhcnJlZ2FtZW50byBkYXMgY29udmVyc2FzXHJcbiAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoY29udmVyc2F0aW9uc0FycmF5KSAmJiBjb252ZXJzYXRpb25zQXJyYXkubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQXJyYXkgdmF6aW8gcmVjZWJpZG8sIGZvcsOnYW5kbyBjYXJyZWdhbWVudG8gZGUgY29udmVyc2FzLi4uJyk7XHJcbiAgICAgICAgICAgICAgICBsb2FkQ29udmVyc2F0aW9ucyh0cnVlKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gRXZlbnRvIHBhcmEgbm90aWZpY2FyIHF1ZSB1bWEgbm92YSBjb252ZXJzYSBmb2kgY3JpYWRhXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2NvbnZlcnNhdGlvbjpjcmVhdGVkJywgKGRhdGEpID0+IHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdOb3ZhIGNvbnZlcnNhIGNyaWFkYSByZWNlYmlkYSB2aWEgV2ViU29ja2V0OicsIGRhdGEpO1xyXG4gICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5pZCkge1xyXG4gICAgICAgICAgICAvLyBBZGljaW9uYXIgYSBub3ZhIGNvbnZlcnNhIGFvIGluw61jaW8gZGEgbGlzdGFcclxuICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgYSBjb252ZXJzYSBqw6EgZXhpc3RlXHJcbiAgICAgICAgICAgICAgaWYgKHByZXYuc29tZShjID0+IGMuaWQgPT09IGRhdGEuaWQpKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gcHJldjtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFtkYXRhLCAuLi5wcmV2XTtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vIEV2ZW50byBwYXJhIHF1YW5kbyB1bSBwYXJ0aWNpcGFudGUgw6kgcmVtb3ZpZG9cclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vbigncGFydGljaXBhbnQ6cmVtb3ZlZCcsIChkYXRhKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnUGFydGljaXBhbnRlIHJlbW92aWRvIHJlY2ViaWRvIHZpYSBXZWJTb2NrZXQ6JywgZGF0YSk7XHJcbiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvbnZlcnNhdGlvbklkICYmIGRhdGEucGFydGljaXBhbnRJZCkge1xyXG4gICAgICAgICAgICAvLyBTZSBvIHVzdcOhcmlvIGF0dWFsIGZvaSByZW1vdmlkbywgcmVtb3ZlciBhIGNvbnZlcnNhIGRhIGxpc3RhXHJcbiAgICAgICAgICAgIGlmIChkYXRhLnBhcnRpY2lwYW50SWQgPT09IHVzZXI/LmlkKSB7XHJcbiAgICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHByZXYuZmlsdGVyKGMgPT4gYy5pZCAhPT0gZGF0YS5jb252ZXJzYXRpb25JZCkpO1xyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIC8vIFNlIGVyYSBhIGNvbnZlcnNhIGF0aXZhLCBsaW1wYXJcclxuICAgICAgICAgICAgICBpZiAoYWN0aXZlQ29udmVyc2F0aW9uID09PSBkYXRhLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24obnVsbCk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIC8vIExpbXBhciBtZW5zYWdlbnMgZGEgY29udmVyc2FcclxuICAgICAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7IC4uLnByZXYgfTtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSB1cGRhdGVkW2RhdGEuY29udmVyc2F0aW9uSWRdO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHVwZGF0ZWQ7XHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgLy8gQXR1YWxpemFyIGEgbGlzdGEgZGUgcGFydGljaXBhbnRlcyBkYSBjb252ZXJzYVxyXG4gICAgICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiBwcmV2Lm1hcChjb252ID0+IHtcclxuICAgICAgICAgICAgICAgIGlmIChjb252LmlkID09PSBkYXRhLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uY29udixcclxuICAgICAgICAgICAgICAgICAgICBwYXJ0aWNpcGFudHM6IGNvbnYucGFydGljaXBhbnRzLmZpbHRlcihwID0+IFxyXG4gICAgICAgICAgICAgICAgICAgICAgKHAudXNlcklkICE9PSBkYXRhLnBhcnRpY2lwYW50SWQgJiYgcC5jbGllbnRJZCAhPT0gZGF0YS5wYXJ0aWNpcGFudElkKVxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHJldHVybiBjb252O1xyXG4gICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBFdmVudG8gcGFyYSBxdWFuZG8gbyB1c3XDoXJpbyBzYWkgZGUgdW1hIGNvbnZlcnNhXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2NvbnZlcnNhdGlvbjpsZWZ0JywgKGRhdGEpID0+IHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVc3XDoXJpbyBzYWl1IGRhIGNvbnZlcnNhIHZpYSBXZWJTb2NrZXQ6JywgZGF0YSk7XHJcbiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvbnZlcnNhdGlvbklkICYmIGRhdGEudXNlcklkID09PSB1c2VyPy5pZCkge1xyXG4gICAgICAgICAgICAvLyBSZW1vdmVyIGEgY29udmVyc2EgZGEgbGlzdGFcclxuICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHByZXYuZmlsdGVyKGMgPT4gYy5pZCAhPT0gZGF0YS5jb252ZXJzYXRpb25JZCkpO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgLy8gU2UgZXJhIGEgY29udmVyc2EgYXRpdmEsIGxpbXBhclxyXG4gICAgICAgICAgICBpZiAoYWN0aXZlQ29udmVyc2F0aW9uID09PSBkYXRhLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgc2V0QWN0aXZlQ29udmVyc2F0aW9uKG51bGwpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAvLyBMaW1wYXIgbWVuc2FnZW5zIGRhIGNvbnZlcnNhXHJcbiAgICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7IC4uLnByZXYgfTtcclxuICAgICAgICAgICAgICBkZWxldGUgdXBkYXRlZFtkYXRhLmNvbnZlcnNhdGlvbklkXTtcclxuICAgICAgICAgICAgICByZXR1cm4gdXBkYXRlZDtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGFyYSBhdHVhbGl6YXIgYSBpbnRlcmZhY2VcclxuICAgICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgICAgZGV0YWlsOiB7IFxyXG4gICAgICAgICAgICAgICAgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLCBcclxuICAgICAgICAgICAgICAgIGFjdGlvbjogJ2xlZnQnLFxyXG4gICAgICAgICAgICAgICAgY29udmVyc2F0aW9uSWQ6IGRhdGEuY29udmVyc2F0aW9uSWQsXHJcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCkgXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIHNldFNvY2tldChzb2NrZXRJbnN0YW5jZSk7XHJcblxyXG4gICAgICAgIHJldHVybiBzb2NrZXRJbnN0YW5jZTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGluaWNpYWxpemFyIFdlYlNvY2tldDonLCBlcnJvcik7XHJcbiAgICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xyXG4gICAgICAgIHNvY2tldEluaXRpYWxpemVkUmVmLmN1cnJlbnQgPSBmYWxzZTsgLy8gUGVybWl0aXIgbm92YSB0ZW50YXRpdmEgbm8gZnV0dXJvXHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgLy8gVXNhciB1bWEgdmFyacOhdmVsIHBhcmEgY29udHJvbGFyIHNlIGrDoSBlc3RhbW9zIHRlbnRhbmRvIGluaWNpYWxpemFyXHJcbiAgICBsZXQgaW5pdGlhbGl6YXRpb25JblByb2dyZXNzID0gZmFsc2U7XHJcblxyXG4gICAgLy8gRnVuw6fDo28gcGFyYSBpbmljaWFsaXphciBjb20gc2VndXJhbsOnYVxyXG4gICAgY29uc3Qgc2FmZUluaXRpYWxpemUgPSAoKSA9PiB7XHJcbiAgICAgIGlmIChpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnSsOhIGV4aXN0ZSB1bWEgaW5pY2lhbGl6YcOnw6NvIGVtIGFuZGFtZW50bywgaWdub3JhbmRvJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MgPSB0cnVlO1xyXG5cclxuICAgICAgLy8gVXNhciB1bSB0aW1lb3V0IHBhcmEgZ2FyYW50aXIgcXVlIG7Do28gdGVudGFtb3MgaW5pY2lhbGl6YXIgbXVpdG8gZnJlcXVlbnRlbWVudGVcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgaW5pdGlhbGl6ZVdlYlNvY2tldCgpXHJcbiAgICAgICAgICAudGhlbihyZXN1bHQgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnSW5pY2lhbGl6YcOnw6NvIGRvIFdlYlNvY2tldCBjb25jbHXDrWRhOicsIHJlc3VsdCA/ICdzdWNlc3NvJyA6ICdmYWxoYScpO1xyXG4gICAgICAgICAgfSlcclxuICAgICAgICAgIC5jYXRjaChlcnJvciA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gaW5pY2lhbGl6YXIgV2ViU29ja2V0OicsIGVycm9yKTtcclxuICAgICAgICAgICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IGZhbHNlOyAvLyBQZXJtaXRpciBub3ZhIHRlbnRhdGl2YSBubyBmdXR1cm9cclxuICAgICAgICAgIH0pXHJcbiAgICAgICAgICAuZmluYWxseSgoKSA9PiB7XHJcbiAgICAgICAgICAgIGluaXRpYWxpemF0aW9uSW5Qcm9ncmVzcyA9IGZhbHNlO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgIH0sIDIwMDApOyAvLyBFc3BlcmFyIDIgc2VndW5kb3MgYW50ZXMgZGUgaW5pY2lhbGl6YXJcclxuICAgIH07XHJcblxyXG4gICAgLy8gQ2hhbWFyIGEgZnVuw6fDo28gZGUgaW5pY2lhbGl6YcOnw6NvXHJcbiAgICBzYWZlSW5pdGlhbGl6ZSgpO1xyXG5cclxuICAgIC8vIENsZWFudXAgZnVuY3Rpb24gLSBzw7MgZGVzY29uZWN0YXIgcXVhbmRvIG8gY29tcG9uZW50ZSBmb3IgZGVzbW9udGFkbyBjb21wbGV0YW1lbnRlXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAvLyBWZXJpZmljYXIgc2UgZXN0YW1vcyByZWFsbWVudGUgZGVzbW9udGFuZG8gbyBjb21wb25lbnRlICh1c3XDoXJpbyBmZXogbG9nb3V0KVxyXG4gICAgICBpZiAoIXVzZXIpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnVXN1w6FyaW8gZmV6IGxvZ291dCwgZGVzY29uZWN0YW5kbyBzb2NrZXQnKTtcclxuICAgICAgICBpZiAoc29ja2V0KSB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBzb2NrZXQuZGlzY29ubmVjdCgpO1xyXG4gICAgICAgICAgICBzb2NrZXRJbml0aWFsaXplZFJlZi5jdXJyZW50ID0gZmFsc2U7IC8vIFBlcm1pdGlyIG5vdmEgdGVudGF0aXZhIGFww7NzIGNsZWFudXBcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gZGVzY29uZWN0YXIgc29ja2V0OicsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NvbXBvbmVudGUgc2VuZG8gcmVtb250YWRvLCBtYW50ZW5kbyBzb2NrZXQgY29uZWN0YWRvJyk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgLy8gUmVtb3ZlbmRvIHNvY2tldCBlIGlzQ29ubmVjdGVkIGRhcyBkZXBlbmTDqm5jaWFzIHBhcmEgZXZpdGFyIGxvb3BzXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBDYXJyZWdhciBjb252ZXJzYXMgZG8gdXN1w6FyaW8gY29tIGNhY2hlXHJcbiAgY29uc3QgbG9hZENvbnZlcnNhdGlvbnMgPSB1c2VDYWxsYmFjayhhc3luYyAoZm9yY2VSZWZyZXNoID0gdHJ1ZSkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ2xvYWRDb252ZXJzYXRpb25zIGNoYW1hZG8gY29tIGZvcmNlUmVmcmVzaCA9JywgZm9yY2VSZWZyZXNoKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2Fkb1xyXG4gICAgICBpZiAoIXVzZXIpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdVc3XDoXJpbyBuw6NvIGxvZ2FkbyBhbyBjYXJyZWdhciBjb252ZXJzYXMnKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENsaWVudGVzIGFnb3JhIHBvZGVtIGNhcnJlZ2FyIGNvbnZlcnNhc1xyXG4gICAgICBjb25zb2xlLmxvZygnQ2FycmVnYW5kbyBjb252ZXJzYXMgcGFyYTonLCB1c2VyLnJvbGUgfHwgJ1VTRVInKTtcclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBqw6EgZXN0w6EgY2FycmVnYW5kb1xyXG4gICAgICBpZiAoaXNMb2FkaW5nIHx8IGlzTG9hZGluZ1JlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0rDoSBlc3TDoSBjYXJyZWdhbmRvIGNvbnZlcnNhcywgcmV0b3JuYW5kbyBlc3RhZG8gYXR1YWwnKTtcclxuICAgICAgICByZXR1cm4gY29udmVyc2F0aW9ucztcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmVyaWZpY2FyIHNlIGrDoSB0ZW1vcyBjb252ZXJzYXMgY2FycmVnYWRhcyBlIG7Do28gw6kgdW1hIGF0dWFsaXphw6fDo28gZm9yw6dhZGFcclxuICAgICAgaWYgKCFmb3JjZVJlZnJlc2ggJiYgY29udmVyc2F0aW9ucy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0rDoSB0ZW1vcyBjb252ZXJzYXMgY2FycmVnYWRhcyBlIG7Do28gw6kgdW1hIGF0dWFsaXphw6fDo28gZm9yw6dhZGEsIHJldG9ybmFuZG8gZXN0YWRvIGF0dWFsJyk7XHJcbiAgICAgICAgcmV0dXJuIGNvbnZlcnNhdGlvbnM7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIGNhcnJlZ2FuZG8gcGFyYSBldml0YXIgY2hhbWFkYXMgc2ltdWx0w6JuZWFzXHJcbiAgICAgIGlzTG9hZGluZ1JlZi5jdXJyZW50ID0gdHJ1ZTtcclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBjYWNoZSBhcGVuYXMgc2UgbsOjbyBmb3IgcmVmcmVzaCBmb3LDp2Fkb1xyXG4gICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xyXG4gICAgICBpZiAoIWZvcmNlUmVmcmVzaCAmJlxyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YSAmJlxyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YS5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICBub3cgLSByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy50aW1lc3RhbXAgPCBDQUNIRV9FWFBJUkFUSU9OKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VzYW5kbyBjYWNoZSBwYXJhIGNvbnZlcnNhcycpO1xyXG4gICAgICAgIHJldHVybiByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy5kYXRhO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmxvZygnQnVzY2FuZG8gY29udmVyc2FzIGF0dWFsaXphZGFzIGRhIEFQSScpO1xyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiBhbyBjYXJyZWdhciBjb252ZXJzYXM6JywgY3VycmVudFRva2VuID8gJ0Rpc3BvbsOtdmVsJyA6ICdOw6NvIGRpc3BvbsOtdmVsJyk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbCBhbyBjYXJyZWdhciBjb252ZXJzYXMnKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0J1c2NhbmRvIGNvbnZlcnNhcyBkYSBBUEkuLi4nKTtcclxuICAgICAgICBjb25zb2xlLmxvZygnRmF6ZW5kbyByZXF1aXNpw6fDo28gcGFyYTonLCBgJHtBUElfVVJMfS9jaGF0L2NvbnZlcnNhdGlvbnM/aW5jbHVkZU1lc3NhZ2VzPXRydWVgKTtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvY29udmVyc2F0aW9ucz9pbmNsdWRlTWVzc2FnZXM9dHJ1ZWAsIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBjb25zb2xlLmxvZygnU3RhdHVzIGRhIHJlc3Bvc3RhOicsIHJlc3BvbnNlLnN0YXR1cywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdUb2tlbiBleHBpcmFkbyBvdSBpbnbDoWxpZG8gYW8gY2FycmVnYXIgY29udmVyc2FzJyk7XHJcbiAgICAgICAgICAgIGhhbmRsZUF1dGhFcnJvcigpO1xyXG4gICAgICAgICAgICByZXR1cm4gW107XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gY2FycmVnYXIgY29udmVyc2FzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1Jlc3Bvc3RhIGRhIEFQSSBkZSBjb252ZXJzYXM6JywgZGF0YSk7XHJcblxyXG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcclxuICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBow6EgZGFkb3MgdsOhbGlkb3NcclxuICAgICAgICAgIC8vIEEgQVBJIHJldG9ybmEgeyBzdWNjZXNzOiB0cnVlLCBkYXRhOiB7IGNvbnZlcnNhdGlvbnM6IFsuLi5dLCB0b3RhbCwgbGltaXQsIG9mZnNldCB9IH1cclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdFc3RydXR1cmEgY29tcGxldGEgZGEgcmVzcG9zdGEgZGEgQVBJOicsIEpTT04uc3RyaW5naWZ5KGRhdGEpKTtcclxuXHJcbiAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25zQXJyYXkgPSBkYXRhLmRhdGE/LmNvbnZlcnNhdGlvbnMgfHwgW107XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQXJyYXkgZGUgY29udmVyc2FzIGV4dHJhw61kbzonLCBjb252ZXJzYXRpb25zQXJyYXkpO1xyXG5cclxuICAgICAgICAgIGlmICghQXJyYXkuaXNBcnJheShjb252ZXJzYXRpb25zQXJyYXkpKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3Bvc3RhIGRhIEFQSSBuw6NvIGNvbnTDqW0gdW0gYXJyYXkgZGUgY29udmVyc2FzOicsIGRhdGEpO1xyXG4gICAgICAgICAgICByZXR1cm4gW107XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgY29uc29sZS5sb2coYFJlY2ViaWRhcyAke2NvbnZlcnNhdGlvbnNBcnJheS5sZW5ndGh9IGNvbnZlcnNhcyBkYSBBUElgKTtcclxuXHJcbiAgICAgICAgICAvLyBQcm9jZXNzYXIgYXMgw7psdGltYXMgbWVuc2FnZW5zIGRhcyBjb252ZXJzYXNcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdQcm9jZXNzYW5kbyDDumx0aW1hcyBtZW5zYWdlbnMgZGFzIGNvbnZlcnNhcy4uLicpO1xyXG4gICAgICAgICAgY29uc3QgcHJvY2Vzc2VkQ29udmVyc2F0aW9ucyA9IGNvbnZlcnNhdGlvbnNBcnJheS5tYXAoY29udmVyc2F0aW9uID0+IHtcclxuICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgY29udmVyc2EgdGVtIG1lbnNhZ2Vuc1xyXG4gICAgICAgICAgICBpZiAoY29udmVyc2F0aW9uLm1lc3NhZ2VzICYmIGNvbnZlcnNhdGlvbi5tZXNzYWdlcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYENvbnZlcnNhICR7Y29udmVyc2F0aW9uLmlkfSB0ZW0gJHtjb252ZXJzYXRpb24ubWVzc2FnZXMubGVuZ3RofSBtZW5zYWdlbnNgKTtcclxuICAgICAgICAgICAgICAvLyBFeHRyYWlyIGEgw7psdGltYSBtZW5zYWdlbVxyXG4gICAgICAgICAgICAgIGNvbnN0IGxhc3RNZXNzYWdlID0gY29udmVyc2F0aW9uLm1lc3NhZ2VzWzBdOyAvLyBBIHByaW1laXJhIG1lbnNhZ2VtIMOpIGEgbWFpcyByZWNlbnRlIChvcmRlbmFkYSBwb3IgY3JlYXRlZElkIGRlc2MpXHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1VsdGltYSBtZW5zYWdlbTonLCBsYXN0TWVzc2FnZSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIElNUE9SVEFOVEU6IFRhbWLDqW0gc2FsdmFyIHRvZGFzIGFzIG1lbnNhZ2VucyBkZXN0YSBjb252ZXJzYSBubyBlc3RhZG9cclxuICAgICAgICAgICAgICBjb25zdCBzb3J0ZWRNZXNzYWdlcyA9IFsuLi5jb252ZXJzYXRpb24ubWVzc2FnZXNdLnNvcnQoKGEsIGIpID0+XHJcbiAgICAgICAgICAgICAgICBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkgLSBuZXcgRGF0ZShiLmNyZWF0ZWRBdClcclxuICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBERUJVRzogU2FsdmFuZG8gJHtzb3J0ZWRNZXNzYWdlcy5sZW5ndGh9IG1lbnNhZ2VucyBkYSBjb252ZXJzYSAke2NvbnZlcnNhdGlvbi5pZH1gLCBzb3J0ZWRNZXNzYWdlcyk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIEF0dWFsaXphciBlc3RhZG8gZGFzIG1lbnNhZ2Vuc1xyXG4gICAgICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICBbY29udmVyc2F0aW9uLmlkXTogc29ydGVkTWVzc2FnZXNcclxuICAgICAgICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIFJlbW92ZXIgbyBhcnJheSBkZSBtZW5zYWdlbnMgcGFyYSBldml0YXIgZHVwbGljYcOnw6NvXHJcbiAgICAgICAgICAgICAgY29uc3QgeyBtZXNzYWdlcywgLi4uY29udmVyc2F0aW9uV2l0aG91dE1lc3NhZ2VzIH0gPSBjb252ZXJzYXRpb247XHJcblxyXG4gICAgICAgICAgICAgIC8vIEFkaWNpb25hciBhIMO6bHRpbWEgbWVuc2FnZW0gY29tbyBwcm9wcmllZGFkZSBsYXN0TWVzc2FnZVxyXG4gICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAuLi5jb252ZXJzYXRpb25XaXRob3V0TWVzc2FnZXMsXHJcbiAgICAgICAgICAgICAgICBsYXN0TWVzc2FnZVxyXG4gICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYENvbnZlcnNhICR7Y29udmVyc2F0aW9uLmlkfSBuw6NvIHRlbSBtZW5zYWdlbnNgKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnNhdGlvbjtcclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIC8vIExvZyBwYXJhIGRlYnVnIGRvcyBkYWRvcyBkYXMgY29udmVyc2FzXHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQ29udmVyc2FzIHByb2Nlc3NhZGFzOicsIHByb2Nlc3NlZENvbnZlcnNhdGlvbnMubWFwKGMgPT4gKHtcclxuICAgICAgICAgICAgaWQ6IGMuaWQsXHJcbiAgICAgICAgICAgIHBhcnRpY2lwYW50czogYy5wYXJ0aWNpcGFudHM/Lm1hcChwID0+ICh7XHJcbiAgICAgICAgICAgICAgdXNlcklkOiBwLnVzZXJJZCxcclxuICAgICAgICAgICAgICBjbGllbnRJZDogcC5jbGllbnRJZCxcclxuICAgICAgICAgICAgICB1c2VyOiBwLnVzZXIsXHJcbiAgICAgICAgICAgICAgY2xpZW50OiBwLmNsaWVudFxyXG4gICAgICAgICAgICB9KSlcclxuICAgICAgICAgIH0pKSk7XHJcblxyXG4gICAgICAgICAgLy8gQXR1YWxpemFyIGNhY2hlIGNvbSBhcyBjb252ZXJzYXMgcHJvY2Vzc2FkYXNcclxuICAgICAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XHJcbiAgICAgICAgICByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy5kYXRhID0gcHJvY2Vzc2VkQ29udmVyc2F0aW9ucztcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS5jb252ZXJzYXRpb25zLnRpbWVzdGFtcCA9IG5vdztcclxuXHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgZXN0YWRvIC0gZ2FyYW50aXIgcXVlIGVzdGFtb3MgYXR1YWxpemFuZG8gbyBlc3RhZG8gbWVzbW8gc2UgbyBhcnJheSBlc3RpdmVyIHZhemlvXHJcbiAgICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByb2Nlc3NlZENvbnZlcnNhdGlvbnMpO1xyXG5cclxuICAgICAgICAgIC8vIERpc3BhcmFyIGV2ZW50byBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIGF0dWFsaXphw6fDo29cclxuICAgICAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudCgnY2hhdDp3ZWJzb2NrZXQ6dXBkYXRlJywge1xyXG4gICAgICAgICAgICBkZXRhaWw6IHsgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfVxyXG4gICAgICAgICAgfSkpO1xyXG5cclxuICAgICAgICAgIHJldHVybiBjb252ZXJzYXRpb25zQXJyYXk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3Bvc3RhIGRhIEFQSSBuw6NvIGZvaSBiZW0tc3VjZWRpZGE6JywgZGF0YSk7XHJcbiAgICAgICAgICByZXR1cm4gW107XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgY29udmVyc2F0aW9uczonLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgaXNMb2FkaW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gbG9hZENvbnZlcnNhdGlvbnM6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gW107XHJcbiAgICB9XHJcbiAgLy8gUmVtb3ZlbmRvIGNvbnZlcnNhdGlvbnMgZGFzIGRlcGVuZMOqbmNpYXMgcGFyYSBldml0YXIgbG9vcHMgaW5maW5pdG9zXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvciwgaXNMb2FkaW5nXSk7XHJcblxyXG4gIC8vIENhcnJlZ2FyIG1lbnNhZ2VucyBkZSB1bWEgY29udmVyc2EgY29tIGNhY2hlIGFwcmltb3JhZG9cclxuICBjb25zdCBsb2FkTWVzc2FnZXMgPSB1c2VDYWxsYmFjayhhc3luYyAoY29udmVyc2F0aW9uSWQpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2Fkb1xyXG4gICAgICBpZiAoIXVzZXIpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdVc3XDoXJpbyBuw6NvIGxvZ2FkbyBhbyBjYXJyZWdhciBtZW5zYWdlbnMnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBjYWNoZSBkZSBtZW5zYWdlbnNcclxuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0gJiZcclxuICAgICAgICAgIG5vdyAtIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0udGltZXN0YW1wIDwgQ0FDSEVfRVhQSVJBVElPTikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2FuZG8gY2FjaGUgcGFyYSBtZW5zYWdlbnMgZGEgY29udmVyc2E6JywgY29udmVyc2F0aW9uSWQpO1xyXG4gICAgICAgIHJldHVybiByZXF1ZXN0Q2FjaGUubWVzc2FnZXNbY29udmVyc2F0aW9uSWRdLmRhdGE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBqw6EgdGVtIG1lbnNhZ2VucyBjb21wbGV0YXMgY2FycmVnYWRhcyBubyBjYWNoZVxyXG4gICAgICAvLyBOw6NvIHVzYXIgbyBlc3RhZG8gbWVzc2FnZXMgYXF1aSBwb2lzIHBvZGUgY29udGVyIGFwZW5hcyBhIMO6bHRpbWEgbWVuc2FnZW0gZG8gbG9hZENvbnZlcnNhdGlvbnNcclxuXHJcbiAgICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgICBpZiAoIWN1cnJlbnRUb2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIG7Do28gZGlzcG9uw612ZWwgYW8gY2FycmVnYXIgbWVuc2FnZW5zJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBFdml0YXIgbcO6bHRpcGxhcyByZXF1aXNpw6fDtWVzIHNpbXVsdMOibmVhcyBwYXJhIGEgbWVzbWEgY29udmVyc2FcclxuICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0/LmxvYWRpbmcpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnSsOhIGV4aXN0ZSB1bWEgcmVxdWlzacOnw6NvIGVtIGFuZGFtZW50byBwYXJhIGVzdGEgY29udmVyc2EnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIGNhcnJlZ2FuZG9cclxuICAgICAgcmVxdWVzdENhY2hlLm1lc3NhZ2VzW2NvbnZlcnNhdGlvbklkXSA9IHsgbG9hZGluZzogdHJ1ZSB9O1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQnVzY2FuZG8gbWVuc2FnZW5zIGRhIEFQSSBwYXJhIGNvbnZlcnNhOicsIGNvbnZlcnNhdGlvbklkKTtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvY29udmVyc2F0aW9ucy8ke2NvbnZlcnNhdGlvbklkfS9tZXNzYWdlc2AsIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIGV4cGlyYWRvIG91IGludsOhbGlkbyBhbyBjYXJyZWdhciBtZW5zYWdlbnMnKTtcclxuICAgICAgICAgICAgaGFuZGxlQXV0aEVycm9yKCk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRXJybyBhbyBjYXJyZWdhciBtZW5zYWdlbnM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgICAgLy8gRGVidWcgcGFyYSB2ZXJpZmljYXIgbWVuc2FnZW5zIGNhcnJlZ2FkYXNcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdERUJVRzogTWVuc2FnZW5zIGNhcnJlZ2FkYXMgcGFyYSBjb252ZXJzYScsIGNvbnZlcnNhdGlvbklkLCBkYXRhLmRhdGEpO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgY2FjaGVcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0gPSB7XHJcbiAgICAgICAgICAgIGRhdGE6IGRhdGEuZGF0YSxcclxuICAgICAgICAgICAgdGltZXN0YW1wOiBub3csXHJcbiAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlXHJcbiAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgIC8vIEF0dWFsaXphciBlc3RhZG8gZGFzIG1lbnNhZ2Vuc1xyXG4gICAgICAgICAgLy8gR2FyYW50aXIgcXVlIGFzIG1lbnNhZ2VucyBlc3RlamFtIG5hIG9yZGVtIGNvcnJldGEgKG1haXMgYW50aWdhcyBwcmltZWlybylcclxuICAgICAgICAgIC8vIHBhcmEgcXVlIGEgb3JkZW5hw6fDo28gbm8gY29tcG9uZW50ZSBmdW5jaW9uZSBjb3JyZXRhbWVudGVcclxuICAgICAgICAgIGNvbnN0IHNvcnRlZE1lc3NhZ2VzID0gWy4uLmRhdGEuZGF0YV0uc29ydCgoYSwgYikgPT5cclxuICAgICAgICAgICAgbmV3IERhdGUoYS5jcmVhdGVkQXQpIC0gbmV3IERhdGUoYi5jcmVhdGVkQXQpXHJcbiAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdERUJVRzogTWVuc2FnZW5zIG9yZGVuYWRhcycsIHNvcnRlZE1lc3NhZ2VzKTtcclxuXHJcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgIFtjb252ZXJzYXRpb25JZF06IHNvcnRlZE1lc3NhZ2VzXHJcbiAgICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgICAgLy8gQXR1YWxpemFyIGEgw7psdGltYSBtZW5zYWdlbSBkYSBjb252ZXJzYVxyXG4gICAgICAgICAgaWYgKGRhdGEuZGF0YSAmJiBkYXRhLmRhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjb25zdCBsYXN0TWVzc2FnZSA9IGRhdGEuZGF0YVtkYXRhLmRhdGEubGVuZ3RoIC0gMV07XHJcbiAgICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKGNvbnYuaWQgPT09IGNvbnZlcnNhdGlvbklkICYmICghY29udi5sYXN0TWVzc2FnZSB8fCBuZXcgRGF0ZShsYXN0TWVzc2FnZS5jcmVhdGVkQXQpID4gbmV3IERhdGUoY29udi5sYXN0TWVzc2FnZS5jcmVhdGVkQXQpKSkge1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm4geyAuLi5jb252LCBsYXN0TWVzc2FnZSB9O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGNvbnY7XHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHJldHVybiBkYXRhLmRhdGE7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbWVzc2FnZXM6JywgZXJyb3IpO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIC8vIFJlbW92ZXIgZmxhZyBkZSBjYXJyZWdhbWVudG8gZW0gY2FzbyBkZSBlcnJvXHJcbiAgICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0/LmxvYWRpbmcpIHtcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0ubG9hZGluZyA9IGZhbHNlO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gbG9hZE1lc3NhZ2VzOicsIGVycm9yKTtcclxuICAgIH1cclxuICAvLyBSZW1vdmVuZG8gbWVzc2FnZXMgZGFzIGRlcGVuZMOqbmNpYXMgcGFyYSBldml0YXIgbG9vcHMgaW5maW5pdG9zXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBFbnZpYXIgbWVuc2FnZW1cclxuICBjb25zdCBzZW5kTWVzc2FnZSA9IHVzZUNhbGxiYWNrKChjb252ZXJzYXRpb25JZCwgY29udGVudCwgY29udGVudFR5cGUgPSAnVEVYVCcsIG1ldGFkYXRhID0gbnVsbCkgPT4ge1xyXG4gICAgaWYgKCFzb2NrZXQgfHwgIWlzQ29ubmVjdGVkKSByZXR1cm47XHJcblxyXG4gICAgLy8gR2VyYXIgdW0gSUQgdGVtcG9yw6FyaW8gcGFyYSBhIG1lbnNhZ2VtXHJcbiAgICBjb25zdCB0ZW1wSWQgPSBgdGVtcC0ke0RhdGUubm93KCl9YDtcclxuXHJcbiAgICAvLyBDcmlhciB1bWEgbWVuc2FnZW0gdGVtcG9yw6FyaWEgcGFyYSBleGliaXIgaW1lZGlhdGFtZW50ZVxyXG4gICAgY29uc3QgdGVtcE1lc3NhZ2UgPSB7XHJcbiAgICAgIGlkOiB0ZW1wSWQsXHJcbiAgICAgIGNvbnZlcnNhdGlvbklkLFxyXG4gICAgICBjb250ZW50LFxyXG4gICAgICBjb250ZW50VHlwZSxcclxuICAgICAgbWV0YWRhdGEsXHJcbiAgICAgIHNlbmRlcklkOiB1c2VyPy5pc0NsaWVudCB8fCB1c2VyPy5yb2xlID09PSAnQ0xJRU5UJyA/IG51bGwgOiB1c2VyPy5pZCxcclxuICAgICAgc2VuZGVyQ2xpZW50SWQ6IHVzZXI/LmlzQ2xpZW50IHx8IHVzZXI/LnJvbGUgPT09ICdDTElFTlQnID8gdXNlcj8uaWQgOiBudWxsLFxyXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgc2VuZGVyOiB1c2VyPy5pc0NsaWVudCB8fCB1c2VyPy5yb2xlID09PSAnQ0xJRU5UJyA/IG51bGwgOiB7IGlkOiB1c2VyPy5pZCwgZnVsbE5hbWU6IHVzZXI/LmZ1bGxOYW1lIH0sXHJcbiAgICAgIHNlbmRlckNsaWVudDogdXNlcj8uaXNDbGllbnQgfHwgdXNlcj8ucm9sZSA9PT0gJ0NMSUVOVCcgPyB7IGlkOiB1c2VyPy5pZCwgZnVsbE5hbWU6IHVzZXI/LmZ1bGxOYW1lIHx8IHVzZXI/LmxvZ2luLCBsb2dpbjogdXNlcj8ubG9naW4gfSA6IG51bGwsXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIHRlbXBvcsOhcmlhIHBhcmEgZXZpdGFyIGR1cGxpY2HDp8Ojb1xyXG4gICAgICBpc1RlbXA6IHRydWVcclxuICAgIH07XHJcblxyXG4gICAgLy8gQXR1YWxpemFyIG1lbnNhZ2VucyBsb2NhbG1lbnRlIGFudGVzIGRlIGVudmlhclxyXG4gICAgc2V0TWVzc2FnZXMocHJldiA9PiAoe1xyXG4gICAgICAuLi5wcmV2LFxyXG4gICAgICBbY29udmVyc2F0aW9uSWRdOiBbLi4uKHByZXZbY29udmVyc2F0aW9uSWRdIHx8IFtdKSwgdGVtcE1lc3NhZ2VdXHJcbiAgICB9KSk7XHJcblxyXG4gICAgLy8gQXR1YWxpemFyIGEgY29udmVyc2EgY29tIGEgw7psdGltYSBtZW5zYWdlbVxyXG4gICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgcmV0dXJuIHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgIGlmIChjb252LmlkID09PSBjb252ZXJzYXRpb25JZCkge1xyXG4gICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgbGFzdE1lc3NhZ2U6IHRlbXBNZXNzYWdlIH07XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBjb252O1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIEVudmlhciBhIG1lbnNhZ2VtIHZpYSBXZWJTb2NrZXRcclxuICAgIGNvbnN0IG1lc3NhZ2VEYXRhID0ge1xyXG4gICAgICBjb252ZXJzYXRpb25JZCxcclxuICAgICAgY29udGVudCxcclxuICAgICAgY29udGVudFR5cGUsXHJcbiAgICAgIG1ldGFkYXRhLFxyXG4gICAgICB0ZW1wSWQgLy8gSW5jbHVpciBvIElEIHRlbXBvcsOhcmlvIHBhcmEgcG9kZXIgc3Vic3RpdHVpciBkZXBvaXNcclxuICAgIH07XHJcblxyXG5cclxuICAgIHNvY2tldC5lbWl0KCdtZXNzYWdlOnNlbmQnLCBtZXNzYWdlRGF0YSwgKHJlc3BvbnNlKSA9PiB7XHJcbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzKSB7XHJcbiAgICAgICAgLy8gQSBtZW5zYWdlbSByZWFsIHNlcsOhIGFkaWNpb25hZGEgcGVsbyBldmVudG8gbWVzc2FnZTpuZXcgZG8gV2ViU29ja2V0XHJcbiAgICAgICAgLy8gTsOjbyBwcmVjaXNhbW9zIGZhemVyIG5hZGEgYXF1aSwgcG9pcyBvIFdlYlNvY2tldCB2YWkgYXR1YWxpemFyIGEgbWVuc2FnZW1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIG1lc3NhZ2U6JywgcmVzcG9uc2UuZXJyb3IpO1xyXG4gICAgICAgIC8vIEVtIGNhc28gZGUgZXJybywgcG9kZW1vcyBtYXJjYXIgYSBtZW5zYWdlbSBjb21vIGZhbGhhXHJcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiB7XHJcbiAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25NZXNzYWdlcyA9IHByZXZbY29udmVyc2F0aW9uSWRdIHx8IFtdO1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgW2NvbnZlcnNhdGlvbklkXTogY29udmVyc2F0aW9uTWVzc2FnZXMubWFwKG1zZyA9PlxyXG4gICAgICAgICAgICAgIG1zZy5pZCA9PT0gdGVtcElkID8geyAuLi5tc2csIGZhaWxlZDogdHJ1ZSB9IDogbXNnXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gIH0sIFtzb2NrZXQsIGlzQ29ubmVjdGVkLCB1c2VyXSk7XHJcblxyXG4gIC8vIENyaWFyIG5vdmEgY29udmVyc2FcclxuICBjb25zdCBjcmVhdGVDb252ZXJzYXRpb24gPSB1c2VDYWxsYmFjayhhc3luYyAocGFydGljaXBhbnRJZHMsIHRpdGxlID0gbnVsbCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ0NyaWFuZG8gY29udmVyc2EgY29tIHBhcnRpY2lwYW50ZXM6JywgcGFydGljaXBhbnRJZHMpO1xyXG4gICAgICBjb25zb2xlLmxvZygnVGlwbyBkZSBjb252ZXJzYTonLCBwYXJ0aWNpcGFudElkcy5sZW5ndGggPiAxID8gJ0dSVVBPJyA6ICdJTkRJVklEVUFMJyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdUw610dWxvIGRhIGNvbnZlcnNhOicsIHRpdGxlKTtcclxuICAgICAgXHJcbiAgICAgIC8vIOKchSBWQUxJREHDh8ODTzogU3lzdGVtIGFkbWluIG7Do28gcG9kZSBjcmlhciBncnVwb3MgY29tIHVzdcOhcmlvcyBkZSBlbXByZXNhcyBkaWZlcmVudGVzXHJcbiAgICAgIGlmICh1c2VyPy5yb2xlID09PSAnU1lTVEVNX0FETUlOJyAmJiBwYXJ0aWNpcGFudElkcy5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1ZhbGlkYW5kbyBlbXByZXNhcyBwYXJhIHN5c3RlbSBhZG1pbi4uLicpO1xyXG4gICAgICAgIC8vIEJ1c2NhciBpbmZvcm1hw6fDtWVzIGRvcyBwYXJ0aWNpcGFudGVzIHBhcmEgdmFsaWRhciBlbXByZXNhc1xyXG4gICAgICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgICAgIGlmIChjdXJyZW50VG9rZW4pIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHBhcnRpY2lwYW50UHJvbWlzZXMgPSBwYXJ0aWNpcGFudElkcy5tYXAoYXN5bmMgKGlkKSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS91c2Vycy8ke2lkfWAsIHtcclxuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHVzZXJEYXRhLnN1Y2Nlc3MgPyB1c2VyRGF0YS5kYXRhIDogdXNlckRhdGE7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIGNvbnN0IHBhcnRpY2lwYW50cyA9IGF3YWl0IFByb21pc2UuYWxsKHBhcnRpY2lwYW50UHJvbWlzZXMpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUGFydGljaXBhbnRlcyBjYXJyZWdhZG9zOicsIHBhcnRpY2lwYW50cyk7XHJcbiAgICAgICAgICAgIGNvbnN0IHZhbGlkUGFydGljaXBhbnRzID0gcGFydGljaXBhbnRzLmZpbHRlcihwID0+IHAgJiYgcC5jb21wYW55SWQpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUGFydGljaXBhbnRlcyB2w6FsaWRvczonLCB2YWxpZFBhcnRpY2lwYW50cyk7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICBpZiAodmFsaWRQYXJ0aWNpcGFudHMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgICAgIGNvbnN0IGNvbXBhbmllcyA9IFsuLi5uZXcgU2V0KHZhbGlkUGFydGljaXBhbnRzLm1hcChwID0+IHAuY29tcGFueUlkKSldO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdFbXByZXNhcyBlbmNvbnRyYWRhczonLCBjb21wYW5pZXMpO1xyXG4gICAgICAgICAgICAgIGlmIChjb21wYW5pZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgICAgICAgYWxlcnQoJ07Do28gw6kgcG9zc8OtdmVsIGNyaWFyIGdydXBvcyBjb20gdXN1w6FyaW9zIGRlIGVtcHJlc2FzIGRpZmVyZW50ZXMuJyk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gY2F0Y2ggKHZhbGlkYXRpb25FcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIG5hIHZhbGlkYcOnw6NvIGRlIGVtcHJlc2FzOicsIHZhbGlkYXRpb25FcnJvcik7XHJcbiAgICAgICAgICAgIGFsZXJ0KCdFcnJvIG5hIHZhbGlkYcOnw6NvOiAnICsgdmFsaWRhdGlvbkVycm9yLm1lc3NhZ2UpO1xyXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBhbGd1bSBwYXJ0aWNpcGFudGUgw6kgY2xpZW50ZVxyXG4gICAgICBjb25zdCBoYXNDbGllbnRQYXJ0aWNpcGFudHMgPSBwYXJ0aWNpcGFudElkcy5zb21lKGlkID0+IHtcclxuICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBJRCBjb3JyZXNwb25kZSBhIHVtIGNsaWVudGUgKGFzc3VtaW5kbyBxdWUgY2xpZW50ZXMgdMOqbSBpc0NsaWVudDogdHJ1ZSlcclxuICAgICAgICByZXR1cm4gdHlwZW9mIGlkID09PSAnb2JqZWN0JyAmJiBpZC5pc0NsaWVudDtcclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmxvZygnVGVtIHBhcnRpY2lwYW50ZXMgY2xpZW50ZXM6JywgaGFzQ2xpZW50UGFydGljaXBhbnRzKTtcclxuXHJcbiAgICAgIC8vIE9idGVyIG8gdG9rZW4gbWFpcyByZWNlbnRlIGRvIGxvY2FsU3RvcmFnZVxyXG4gICAgICBjb25zdCBjdXJyZW50VG9rZW4gPSBnZXRDdXJyZW50VG9rZW4oKTtcclxuICAgICAgY29uc29sZS5sb2coJ1Rva2VuIGRpc3BvbsOtdmVsOicsIGN1cnJlbnRUb2tlbiA/ICdTaW0nIDogJ07Do28nKTtcclxuXHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbC4gVXN1w6FyaW8gcHJlY2lzYSBmYXplciBsb2dpbiBub3ZhbWVudGUuJyk7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX1VSTH0vY2hhdC9jb252ZXJzYXRpb25zYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgIHR5cGU6IHBhcnRpY2lwYW50SWRzLmxlbmd0aCA+IDEgPyAnR1JPVVAnIDogJ0lORElWSURVQUwnLFxyXG4gICAgICAgICAgdGl0bGUsXHJcbiAgICAgICAgICBwYXJ0aWNpcGFudElkczogcGFydGljaXBhbnRJZHMubWFwKGlkID0+IHR5cGVvZiBpZCA9PT0gJ29iamVjdCcgPyBpZC5pZCA6IGlkKSxcclxuICAgICAgICAgIGluY2x1ZGVDbGllbnRzOiB0cnVlIC8vIFBlcm1pdGlyIGluY2x1aXIgY2xpZW50ZXMgbmFzIGNvbnZlcnNhc1xyXG4gICAgICAgIH0pXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ1Jlc3Bvc3RhIGRhIEFQSTonLCByZXNwb25zZS5zdGF0dXMsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBjb25zb2xlLmxvZygnRGFkb3MgZGEgcmVzcG9zdGE6JywgZGF0YSk7XHJcblxyXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XHJcbiAgICAgICAgLy8gRXh0cmFpciBvcyBkYWRvcyBkYSBjb252ZXJzYSBjcmlhZGFcclxuICAgICAgICBjb25zdCBjb252ZXJzYXRpb25EYXRhID0gZGF0YS5kYXRhO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDb252ZXJzYSBjcmlhZGEgY29tIHN1Y2Vzc286JywgY29udmVyc2F0aW9uRGF0YSk7XHJcblxyXG4gICAgICAgIC8vIEFkaWNpb25hciBhIG5vdmEgY29udmVyc2Egw6AgbGlzdGFcclxuICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4ge1xyXG4gICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgY29udmVyc2EgasOhIGV4aXN0ZSBuYSBsaXN0YSBwYXJhIGV2aXRhciBkdXBsaWNhw6fDo29cclxuICAgICAgICAgIGlmIChwcmV2LnNvbWUoYyA9PiBjLmlkID09PSBjb252ZXJzYXRpb25EYXRhLmlkKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gcHJldjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybiBbY29udmVyc2F0aW9uRGF0YSwgLi4ucHJldl07XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vIERpc3BhcmFyIGV2ZW50byBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIG5vdmEgY29udmVyc2FcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudCgnY2hhdDp3ZWJzb2NrZXQ6dXBkYXRlJywge1xyXG4gICAgICAgICAgICBkZXRhaWw6IHtcclxuICAgICAgICAgICAgICB0eXBlOiAnY29udmVyc2F0aW9ucycsXHJcbiAgICAgICAgICAgICAgY29udmVyc2F0aW9uSWQ6IGNvbnZlcnNhdGlvbkRhdGEuaWQsXHJcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9LCAzMDApO1xyXG5cclxuICAgICAgICByZXR1cm4gY29udmVyc2F0aW9uRGF0YTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNyaWFyIGNvbnZlcnNhOicsIGRhdGEuZXJyb3IgfHwgJ0Vycm8gZGVzY29uaGVjaWRvJyk7XHJcbiAgICAgICAgXHJcblxyXG5cclxuICAgICAgICAvLyBTZSBvIGVycm8gZm9yIGRlIGF1dGVudGljYcOnw6NvLCByZWRpcmVjaW9uYXIgcGFyYSBsb2dpblxyXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gZXhwaXJhZG8gb3UgaW52w6FsaWRvLiBSZWRpcmVjaW9uYW5kbyBwYXJhIGxvZ2luLi4uJyk7XHJcbiAgICAgICAgICBoYW5kbGVBdXRoRXJyb3IoKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgY29udmVyc2F0aW9uOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgfSwgW2hhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBDcmlhciBvdSBvYnRlciBjb252ZXJzYSBjb20gdW0gdXN1w6FyaW8gZXNwZWPDrWZpY29cclxuICBjb25zdCBjcmVhdGVPckdldENvbnZlcnNhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChvdGhlclVzZXIpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdjcmVhdGVPckdldENvbnZlcnNhdGlvbiBjaGFtYWRvIGNvbSB1c3XDoXJpbzonLCBvdGhlclVzZXIpO1xyXG5cclxuICAgICAgaWYgKCFvdGhlclVzZXIgfHwgIW90aGVyVXNlci5pZCkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VzdcOhcmlvIGludsOhbGlkbzonLCBvdGhlclVzZXIpO1xyXG4gICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBQcmltZWlybywgdmVyaWZpY2FyIHNlIGrDoSBleGlzdGUgdW1hIGNvbnZlcnNhIGluZGl2aWR1YWwgY29tIGVzdGUgdXN1w6FyaW9cclxuICAgICAgY29uc3QgZXhpc3RpbmdDb252ZXJzYXRpb24gPSBjb252ZXJzYXRpb25zLmZpbmQoY29udiA9PiB7XHJcbiAgICAgICAgaWYgKGNvbnYudHlwZSAhPT0gJ0lORElWSURVQUwnKSByZXR1cm4gZmFsc2U7XHJcblxyXG4gICAgICAgIHJldHVybiBjb252LnBhcnRpY2lwYW50cz8uc29tZShwID0+IHAudXNlcklkID09PSBvdGhlclVzZXIuaWQpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmIChleGlzdGluZ0NvbnZlcnNhdGlvbikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDb252ZXJzYSBleGlzdGVudGUgZW5jb250cmFkYTonLCBleGlzdGluZ0NvbnZlcnNhdGlvbik7XHJcbiAgICAgICAgc2V0QWN0aXZlQ29udmVyc2F0aW9uKGV4aXN0aW5nQ29udmVyc2F0aW9uLmlkKTtcclxuICAgICAgICByZXR1cm4gZXhpc3RpbmdDb252ZXJzYXRpb247XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdDcmlhbmRvIG5vdmEgY29udmVyc2EgY29tIHVzdcOhcmlvOicsIG90aGVyVXNlci5mdWxsTmFtZSk7XHJcbiAgICAgIC8vIFNlIG7Do28gZXhpc3RpciwgY3JpYXIgdW1hIG5vdmEgY29udmVyc2FcclxuICAgICAgY29uc3QgbmV3Q29udmVyc2F0aW9uID0gYXdhaXQgY3JlYXRlQ29udmVyc2F0aW9uKFtvdGhlclVzZXIuaWRdKTtcclxuXHJcbiAgICAgIGlmIChuZXdDb252ZXJzYXRpb24pIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnTm92YSBjb252ZXJzYSBjcmlhZGEgY29tIHN1Y2Vzc286JywgbmV3Q29udmVyc2F0aW9uKTtcclxuICAgICAgICAvLyBHYXJhbnRpciBxdWUgYSBjb252ZXJzYSBzZWphIGFkaWNpb25hZGEgw6AgbGlzdGEgYW50ZXMgZGUgZGVmaW5pciBjb21vIGF0aXZhXHJcbiAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIGNvbnZlcnNhIGrDoSBleGlzdGUgbmEgbGlzdGEgcGFyYSBldml0YXIgZHVwbGljYcOnw6NvXHJcbiAgICAgICAgICBpZiAocHJldi5zb21lKGMgPT4gYy5pZCA9PT0gbmV3Q29udmVyc2F0aW9uLmlkKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gcHJldjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybiBbbmV3Q29udmVyc2F0aW9uLCAuLi5wcmV2XTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gRGVmaW5pciBhIGNvbnZlcnNhIGNvbW8gYXRpdmEgYXDDs3MgdW0gcGVxdWVubyBkZWxheSBwYXJhIGdhcmFudGlyIHF1ZSBhIGxpc3RhIGZvaSBhdHVhbGl6YWRhXHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24obmV3Q29udmVyc2F0aW9uLmlkKTtcclxuXHJcbiAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGFyYSBub3RpZmljYXIgY29tcG9uZW50ZXMgc29icmUgYSBub3ZhIGNvbnZlcnNhXHJcbiAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2NoYXQ6d2Vic29ja2V0OnVwZGF0ZScsIHtcclxuICAgICAgICAgICAgZGV0YWlsOiB7XHJcbiAgICAgICAgICAgICAgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLFxyXG4gICAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkOiBuZXdDb252ZXJzYXRpb24uaWQsXHJcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9LCAzMDApO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhbGhhIGFvIGNyaWFyIG5vdmEgY29udmVyc2EsIHJldG9ybm8gbnVsbyBvdSBpbmRlZmluaWRvJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBuZXdDb252ZXJzYXRpb247XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBvciBnZXR0aW5nIGNvbnZlcnNhdGlvbjonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG4gIC8vIFJlbW92ZW5kbyBjb252ZXJzYXRpb25zIGRhcyBkZXBlbmTDqm5jaWFzIHBhcmEgZXZpdGFyIGxvb3BzIGluZmluaXRvc1xyXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcclxuICB9LCBbY3JlYXRlQ29udmVyc2F0aW9uLCBzZXRBY3RpdmVDb252ZXJzYXRpb24sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuXHJcblxyXG4gIC8vIENhcnJlZ2FyIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzIGNvbSBjYWNoZVxyXG4gIGNvbnN0IGxvYWRVbnJlYWRDb3VudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChmb3JjZVJlZnJlc2ggPSBmYWxzZSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gdXN1w6FyaW8gZXN0w6EgbG9nYWRvXHJcbiAgICAgIGlmICghdXNlcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VzdcOhcmlvIG7Do28gbG9nYWRvIGFvIGNhcnJlZ2FyIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBWZXJpZmljYXIgY2FjaGVcclxuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICAgICAgaWYgKCFmb3JjZVJlZnJlc2ggJiZcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS51bnJlYWRDb3VudC5kYXRhICE9PSBudWxsICYmXHJcbiAgICAgICAgICBub3cgLSByZXF1ZXN0Q2FjaGUudW5yZWFkQ291bnQudGltZXN0YW1wIDwgQ0FDSEVfRVhQSVJBVElPTikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2FuZG8gY2FjaGUgcGFyYSBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhcycpO1xyXG4gICAgICAgIHJldHVybiByZXF1ZXN0Q2FjaGUudW5yZWFkQ291bnQuZGF0YTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbCBhbyBjYXJyZWdhciBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhcycpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQnVzY2FuZG8gY29udGFnZW0gZGUgbWVuc2FnZW5zIG7Do28gbGlkYXMgZGEgQVBJLi4uJyk7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L21lc3NhZ2VzL3VucmVhZGAsIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIGV4cGlyYWRvIG91IGludsOhbGlkbyBhbyBjYXJyZWdhciBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhcycpO1xyXG4gICAgICAgICAgICBoYW5kbGVBdXRoRXJyb3IoKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGNhcnJlZ2FyIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1Jlc3Bvc3RhIGRhIEFQSSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhczonLCBkYXRhKTtcclxuXHJcbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0RhZG9zIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzIHJlY2ViaWRvczonLCBkYXRhLmRhdGEpO1xyXG5cclxuICAgICAgICAgIC8vIEF0dWFsaXphciBjYWNoZSBjb20gbyB0b3RhbFVucmVhZFxyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLnVucmVhZENvdW50LmRhdGEgPSBkYXRhLmRhdGEudG90YWxVbnJlYWQ7XHJcbiAgICAgICAgICByZXF1ZXN0Q2FjaGUudW5yZWFkQ291bnQudGltZXN0YW1wID0gbm93O1xyXG5cclxuICAgICAgICAgIC8vIEF0dWFsaXphciBlc3RhZG9cclxuICAgICAgICAgIHNldFVucmVhZENvdW50KGRhdGEuZGF0YS50b3RhbFVucmVhZCk7XHJcblxyXG4gICAgICAgICAgLy8gQXR1YWxpemFyIGFzIGNvbnZlcnNhcyBjb20gYXMgY29udGFnZW5zIGRlIG7Do28gbGlkYXNcclxuICAgICAgICAgIGlmIChkYXRhLmRhdGEuY29udmVyc2F0aW9ucyAmJiBBcnJheS5pc0FycmF5KGRhdGEuZGF0YS5jb252ZXJzYXRpb25zKSkge1xyXG4gICAgICAgICAgICAvLyBQcmltZWlybywgdmVyaWZpY2FyIHNlIGrDoSB0ZW1vcyBhcyBjb252ZXJzYXMgY2FycmVnYWRhc1xyXG4gICAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25JZHMgPSBkYXRhLmRhdGEuY29udmVyc2F0aW9ucy5tYXAoYyA9PiBjLmNvbnZlcnNhdGlvbklkKTtcclxuXHJcbiAgICAgICAgICAgIC8vIFNlIG7Do28gdGVtb3MgY29udmVyc2FzIGNhcnJlZ2FkYXMgb3Ugc2UgdGVtb3MgbWVub3MgY29udmVyc2FzIGRvIHF1ZSBhcyBuw6NvIGxpZGFzLFxyXG4gICAgICAgICAgICAvLyBmb3LDp2FyIHVtYSBhdHVhbGl6YcOnw6NvIGRhcyBjb252ZXJzYXNcclxuICAgICAgICAgICAgaWYgKGNvbnZlcnNhdGlvbnMubGVuZ3RoID09PSAwIHx8XHJcbiAgICAgICAgICAgICAgICAhY29udmVyc2F0aW9uSWRzLmV2ZXJ5KGlkID0+IGNvbnZlcnNhdGlvbnMuc29tZShjID0+IGMuaWQgPT09IGlkKSkpIHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnRm9yw6dhbmRvIGF0dWFsaXphw6fDo28gZGFzIGNvbnZlcnNhcyBwb3JxdWUgaMOhIG1lbnNhZ2VucyBuw6NvIGxpZGFzIGVtIGNvbnZlcnNhcyBuw6NvIGNhcnJlZ2FkYXMnKTtcclxuICAgICAgICAgICAgICBhd2FpdCBsb2FkQ29udmVyc2F0aW9ucyh0cnVlKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAvLyBBdHVhbGl6YXIgYXMgY29udmVyc2FzIGV4aXN0ZW50ZXMgY29tIGFzIGNvbnRhZ2VucyBkZSBuw6NvIGxpZGFzXHJcbiAgICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBwcmV2Lm1hcChjb252ID0+IHtcclxuICAgICAgICAgICAgICAgICAgLy8gUHJvY3VyYXIgZXN0YSBjb252ZXJzYSBub3MgZGFkb3MgZGUgbsOjbyBsaWRhc1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCB1bnJlYWRJbmZvID0gZGF0YS5kYXRhLmNvbnZlcnNhdGlvbnMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICBpdGVtID0+IGl0ZW0uY29udmVyc2F0aW9uSWQgPT09IGNvbnYuaWRcclxuICAgICAgICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgICAgICAgIGlmICh1bnJlYWRJbmZvKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgdW5yZWFkQ291bnQ6IHVucmVhZEluZm8udW5yZWFkQ291bnQgfTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICByZXR1cm4gY29udjtcclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuIGRhdGEuZGF0YS50b3RhbFVucmVhZDtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB1bnJlYWQgY291bnQ6JywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBsb2FkVW5yZWFkQ291bnQ6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIC8vIFJlbW92ZW5kbyBjb252ZXJzYXRpb25zIGUgbG9hZENvbnZlcnNhdGlvbnMgZGFzIGRlcGVuZMOqbmNpYXMgcGFyYSBldml0YXIgbG9vcHMgaW5maW5pdG9zXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBSZWZlcsOqbmNpYSBwYXJhIGNvbnRyb2xhciBzZSBvcyBkYWRvcyBpbmljaWFpcyBqw6EgZm9yYW0gY2FycmVnYWRvc1xyXG4gIGNvbnN0IGluaXRpYWxEYXRhTG9hZGVkUmVmID0gdXNlUmVmKGZhbHNlKTtcclxuXHJcbiAgLy8gUmVzZXRhciBhIGZsYWcgcXVhbmRvIG8gdXN1w6FyaW8gbXVkYVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAodXNlcikge1xyXG4gICAgICBjb25zb2xlLmxvZygnVXN1w6FyaW8gYWx0ZXJhZG8sIHJlc2V0YW5kbyBmbGFnIGRlIGRhZG9zIGNhcnJlZ2Fkb3MnKTtcclxuICAgICAgaW5pdGlhbERhdGFMb2FkZWRSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgfVxyXG4gIH0sIFt1c2VyPy5pZF0pOyAvLyBEZXBlbmRlbmRvIGFwZW5hcyBkbyBJRCBkbyB1c3XDoXJpbyBwYXJhIGV2aXRhciByZS1yZW5kZXJzIGRlc25lY2Vzc8Ohcmlvc1xyXG5cclxuICAvLyBFZmVpdG8gcGFyYSBjYXJyZWdhciBkYWRvcyBpbmljaWFpcyBlIGNvbmZpZ3VyYXIgYXR1YWxpemHDp8OjbyBwZXJpw7NkaWNhXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2FkbyBhbnRlcyBkZSBjYXJyZWdhciBkYWRvc1xyXG4gICAgaWYgKCF1c2VyKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdVc3XDoXJpbyBuw6NvIGxvZ2FkbywgbsOjbyBjYXJyZWdhbmRvIGRhZG9zIGluaWNpYWlzJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGllbnRlcyBhZ29yYSBwb2RlbSB1c2FyIG8gY2hhdFxyXG4gICAgY29uc29sZS5sb2coJ0NhcnJlZ2FuZG8gZGFkb3MgaW5pY2lhaXMgcGFyYTonLCB1c2VyLnJvbGUgfHwgJ1VTRVInKTtcclxuXHJcbiAgICAvLyBWZXJpZmljYXIgdG9rZW5cclxuICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgaWYgKCFjdXJyZW50VG9rZW4pIHtcclxuICAgICAgY29uc29sZS5sb2coJ1Rva2VuIG7Do28gZGlzcG9uw612ZWwsIG7Do28gY2FycmVnYW5kbyBkYWRvcyBpbmljaWFpcycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmVyaWZpY2FyIHNlIGrDoSBlc3TDoSBjYXJyZWdhbmRvXHJcbiAgICBpZiAoaXNMb2FkaW5nIHx8IGlzTG9hZGluZ1JlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdKw6EgZXN0w6EgY2FycmVnYW5kbyBkYWRvcywgYWd1YXJkYW5kby4uLicpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmVyaWZpY2FyIHNlIG9zIGRhZG9zIGrDoSBmb3JhbSBjYXJyZWdhZG9zXHJcbiAgICBpZiAoaW5pdGlhbERhdGFMb2FkZWRSZWYuY3VycmVudCkge1xyXG4gICAgICBjb25zb2xlLmxvZygnRGFkb3MgasOhIGZvcmFtIGNhcnJlZ2Fkb3MgYW50ZXJpb3JtZW50ZSwgaWdub3JhbmRvJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIGRhZG9zIGluaWNpYWlzXHJcbiAgICBjb25zdCBsb2FkSW5pdGlhbERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmIChpc0xvYWRpbmcgfHwgaXNMb2FkaW5nUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnSsOhIGVzdMOhIGNhcnJlZ2FuZG8gZGFkb3MsIGNhbmNlbGFuZG8gY2FycmVnYW1lbnRvIGluaWNpYWwnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIGNhcnJlZ2FuZG9cclxuICAgICAgaXNMb2FkaW5nUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG4gICAgICBjb25zb2xlLmxvZygnSW5pY2lhbmRvIGNhcnJlZ2FtZW50byBkZSBkYWRvcyBkbyBjaGF0Li4uJyk7XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIC8vIFByaW1laXJvIGNhcnJlZ2FyIGFzIGNvbnZlcnNhc1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDYXJyZWdhbmRvIGNvbnZlcnNhcy4uLicpO1xyXG4gICAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbnNEYXRhID0gYXdhaXQgbG9hZENvbnZlcnNhdGlvbnModHJ1ZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NvbnZlcnNhcyBjYXJyZWdhZGFzOicsIGNvbnZlcnNhdGlvbnNEYXRhPy5sZW5ndGggfHwgMCwgJ2NvbnZlcnNhcycpO1xyXG5cclxuICAgICAgICAvLyBDYXJyZWdhciBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhc1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDYXJyZWdhbmRvIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzLi4uJyk7XHJcbiAgICAgICAgYXdhaXQgbG9hZFVucmVhZENvdW50KHRydWUpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDb250YWdlbSBkZSBuw6NvIGxpZGFzIGNhcnJlZ2FkYScpO1xyXG5cclxuICAgICAgICAvLyBNYXJjYXIgY29tbyBjYXJyZWdhZG9cclxuICAgICAgICBpbml0aWFsRGF0YUxvYWRlZFJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ2FycmVnYW1lbnRvIGRlIGRhZG9zIGNvbmNsdcOtZG8gY29tIHN1Y2Vzc28nKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIGRhZG9zOicsIGVycm9yKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBpc0xvYWRpbmdSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIENhcnJlZ2FyIGRhZG9zIGluaWNpYWlzIGNvbSBkZWJvdW5jZSBwYXJhIGV2aXRhciBtw7psdGlwbGFzIGNoYW1hZGFzXHJcbiAgICBjb25zb2xlLmxvZygnQWdlbmRhbmRvIGNhcnJlZ2FtZW50byBkZSBkYWRvcyBpbmljaWFpcy4uLicpO1xyXG4gICAgY29uc3QgZGVib3VuY2VkTG9hZERhdGEgPSBkZWJvdW5jZSgoKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdDYXJyZWdhbmRvIGRhZG9zIGluaWNpYWlzIChkZWJvdW5jZWQpLi4uJyk7XHJcbiAgICAgIGxvYWRJbml0aWFsRGF0YSgpO1xyXG4gICAgfSwgMTAwMCk7IC8vIEVzcGVyYXIgMSBzZWd1bmRvIGFudGVzIGRlIGNhcnJlZ2FyXHJcblxyXG4gICAgLy8gQ2hhbWFyIGEgZnVuw6fDo28gY29tIGRlYm91bmNlXHJcbiAgICBkZWJvdW5jZWRMb2FkRGF0YSgpO1xyXG5cclxuICAgIC8vIFJlbW92ZW1vcyBhIGF0dWFsaXphw6fDo28gcGVyacOzZGljYSB2aWEgSFRUUCBwYXJhIGV2aXRhciBmbG9vZCBubyBiYWNrZW5kXHJcbiAgICAvLyBBZ29yYSBkZXBlbmRlbW9zIGFwZW5hcyBkbyBXZWJTb2NrZXQgcGFyYSBhdHVhbGl6YcOnw7VlcyBlbSB0ZW1wbyByZWFsXHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgLy8gQ2xlYW51cCBmdW5jdGlvblxyXG4gICAgfTtcclxuICAvLyBSZW1vdmVuZG8gbG9hZFVucmVhZENvdW50IGUgaXNQYW5lbE9wZW4sIGlzTW9kYWxPcGVuIGRhcyBkZXBlbmTDqm5jaWFzIHBhcmEgZXZpdGFyIGNoYW1hZGFzIGRlc25lY2Vzc8Ohcmlhc1xyXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcclxuICB9LCBbdXNlciwgZ2V0Q3VycmVudFRva2VuLCBsb2FkQ29udmVyc2F0aW9ucywgaXNMb2FkaW5nXSk7XHJcblxyXG4gIC8vIEVmZWl0byBwYXJhIHZlcmlmaWNhciBzZSBhY3RpdmVDb252ZXJzYXRpb24gw6kgdsOhbGlkb1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoYWN0aXZlQ29udmVyc2F0aW9uICYmIGNvbnZlcnNhdGlvbnMubGVuZ3RoID4gMCkge1xyXG4gICAgICBjb25zdCBjb252ZXJzYXRpb25FeGlzdHMgPSBjb252ZXJzYXRpb25zLnNvbWUoYyA9PiBjLmlkID09PSBhY3RpdmVDb252ZXJzYXRpb24pO1xyXG4gICAgICBpZiAoIWNvbnZlcnNhdGlvbkV4aXN0cykge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignQ29udmVyc2EgYXRpdmEgbsOjbyBlbmNvbnRyYWRhIG5hIGxpc3RhIGRlIGNvbnZlcnNhcywgcmVzZXRhbmRvLi4uJywgYWN0aXZlQ29udmVyc2F0aW9uKTtcclxuICAgICAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24obnVsbCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbYWN0aXZlQ29udmVyc2F0aW9uLCBjb252ZXJzYXRpb25zXSk7XHJcblxyXG4gIC8vIEVmZWl0byBwYXJhIG1vbml0b3JhciBtdWRhbsOnYXMgbm8gdG9rZW4gZSByZWNvbmVjdGFyIHF1YW5kbyBuZWNlc3PDoXJpb1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBWZXJpZmljYXIgc2UgbyB1c3XDoXJpbyBlc3TDoSBsb2dhZG8gYW50ZXMgZGUgbW9uaXRvcmFyIG8gdG9rZW5cclxuICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG5cclxuICAgIC8vIFZlcmlmaWNhciBvIHRva2VuIGEgY2FkYSAyIG1pbnV0b3MgKGF1bWVudGFkbyBwYXJhIHJlZHV6aXIgYSBmcmVxdcOqbmNpYSlcclxuICAgIGNvbnN0IHRva2VuQ2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgLy8gRXZpdGFyIHZlcmlmaWNhw6fDtWVzIGRlc25lY2Vzc8OhcmlhcyBzZSBlc3RpdmVyIGNhcnJlZ2FuZG9cclxuICAgICAgaWYgKGlzTG9hZGluZyB8fCBpc0xvYWRpbmdSZWYuY3VycmVudCkgcmV0dXJuO1xyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcblxyXG4gICAgICAvLyBTZSBuw6NvIGjDoSB0b2tlbiBtYXMgaMOhIGNvbmV4w6NvLCBkZXNjb25lY3RhclxyXG4gICAgICBpZiAoIWN1cnJlbnRUb2tlbiAmJiBpc0Nvbm5lY3RlZCAmJiBzb2NrZXQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnVG9rZW4gbsOjbyBlbmNvbnRyYWRvLCBkZXNjb25lY3RhbmRvIFdlYlNvY2tldC4uLicpO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBzb2NrZXQuZGlzY29ubmVjdCgpO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGRlc2NvbmVjdGFyIHNvY2tldDonLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gU2UgaMOhIHRva2VuIG1hcyBuw6NvIGjDoSBjb25leMOjbywgdGVudGFyIHJlY29uZWN0YXIgKGNvbSB2ZXJpZmljYcOnw6NvIGRlIHRlbXBvKVxyXG4gICAgICBpZiAoY3VycmVudFRva2VuICYmICFpc0Nvbm5lY3RlZCAmJiAhc29ja2V0KSB7XHJcbiAgICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICAgICAgICAvLyBMaW1pdGFyIHRlbnRhdGl2YXMgZGUgcmVjb25leMOjbyAobm8gbcOheGltbyB1bWEgYSBjYWRhIDIgbWludXRvcylcclxuICAgICAgICBpZiAobm93IC0gbGFzdEluaXRBdHRlbXB0UmVmLmN1cnJlbnQgPiAxMjAwMDApIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiBlbmNvbnRyYWRvLCB0ZW50YW5kbyByZWNvbmVjdGFyIFdlYlNvY2tldC4uLicpO1xyXG4gICAgICAgICAgLy8gUGVybWl0aXIgbm92YSB0ZW50YXRpdmEgZGUgaW5pY2lhbGl6YcOnw6NvIGRvIFdlYlNvY2tldFxyXG4gICAgICAgICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgICAgbGFzdEluaXRBdHRlbXB0UmVmLmN1cnJlbnQgPSBub3c7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9LCAxMjAwMDApOyAvLyBWZXJpZmljYXIgYSBjYWRhIDIgbWludXRvc1xyXG5cclxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRva2VuQ2hlY2tJbnRlcnZhbCk7XHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW5dKTtcclxuXHJcbiAgLy8gQWJyaXIvZmVjaGFyIHBhaW5lbCBkZSBjaGF0XHJcbiAgY29uc3QgdG9nZ2xlQ2hhdFBhbmVsID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgLy8gVmVyaWZpY2FyIHNlIG8gdXN1w6FyaW8gZXN0w6EgbG9nYWRvXHJcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcclxuXHJcbiAgICBzZXRJc1BhbmVsT3BlbihwcmV2ID0+IHtcclxuICAgICAgY29uc3QgbmV3U3RhdGUgPSAhcHJldjtcclxuICAgICAgLy8gU2UgZXN0aXZlciBhYnJpbmRvIG8gcGFpbmVsLCBmZWNoYXIgbyBtb2RhbFxyXG4gICAgICBpZiAobmV3U3RhdGUpIHtcclxuICAgICAgICBzZXRJc01vZGFsT3BlbihmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIG5ld1N0YXRlO1xyXG4gICAgfSk7XHJcbiAgfSwgW3VzZXJdKTtcclxuXHJcbiAgLy8gQWJyaXIvZmVjaGFyIG1vZGFsIGRlIGNoYXRcclxuICBjb25zdCB0b2dnbGVDaGF0TW9kYWwgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICAvLyBWZXJpZmljYXIgc2UgbyB1c3XDoXJpbyBlc3TDoSBsb2dhZG9cclxuICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG5cclxuICAgIHNldElzTW9kYWxPcGVuKHByZXYgPT4ge1xyXG4gICAgICBjb25zdCBuZXdTdGF0ZSA9ICFwcmV2O1xyXG4gICAgICAvLyBTZSBlc3RpdmVyIGFicmluZG8gbyBtb2RhbCwgZmVjaGFyIG8gcGFpbmVsXHJcbiAgICAgIGlmIChuZXdTdGF0ZSkge1xyXG4gICAgICAgIHNldElzUGFuZWxPcGVuKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gbmV3U3RhdGU7XHJcbiAgICB9KTtcclxuICB9LCBbdXNlcl0pO1xyXG5cclxuICAvLyBWZXJpZmljYXIgc2UgY3JlYXRlQ29udmVyc2F0aW9uIMOpIHVtYSBmdW7Dp8OjbyB2w6FsaWRhXHJcbiAgY29uc29sZS5sb2coJ0NoYXRDb250ZXh0OiBjcmVhdGVDb252ZXJzYXRpb24gw6kgdW1hIGZ1bsOnw6NvPycsIHR5cGVvZiBjcmVhdGVDb252ZXJzYXRpb24gPT09ICdmdW5jdGlvbicpO1xyXG5cclxuICAvLyBBZGljaW9uYXIgcGFydGljaXBhbnRlIGEgdW0gZ3J1cG9cclxuICBjb25zdCBhZGRQYXJ0aWNpcGFudFRvR3JvdXAgPSB1c2VDYWxsYmFjayhhc3luYyAoY29udmVyc2F0aW9uSWQsIHBhcnRpY2lwYW50SWQpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghY29udmVyc2F0aW9uSWQgfHwgIXBhcnRpY2lwYW50SWQpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdJRCBkYSBjb252ZXJzYSBlIElEIGRvIHBhcnRpY2lwYW50ZSBzw6NvIG9icmlnYXTDs3Jpb3MnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbCBhbyBhZGljaW9uYXIgcGFydGljaXBhbnRlJyk7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGBBZGljaW9uYW5kbyBwYXJ0aWNpcGFudGUgJHtwYXJ0aWNpcGFudElkfSDDoCBjb252ZXJzYSAke2NvbnZlcnNhdGlvbklkfWApO1xyXG4gICAgICBcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L2NvbnZlcnNhdGlvbnMvJHtjb252ZXJzYXRpb25JZH0vcGFydGljaXBhbnRzYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgcGFydGljaXBhbnRJZCB9KVxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGNvbnNvbGUubG9nKGBSZXNwb3N0YSBkYSBBUEk6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdUb2tlbiBleHBpcmFkbyBvdSBpbnbDoWxpZG8gYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZScpO1xyXG4gICAgICAgICAgaGFuZGxlQXV0aEVycm9yKCk7XHJcbiAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gTG9nIGRldGFsaGFkbyBkbyBlcnJvXHJcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm8gJHtyZXNwb25zZS5zdGF0dXN9IGFvIGFkaWNpb25hciBwYXJ0aWNpcGFudGU6YCwgZXJyb3JUZXh0KTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZTogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblxyXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XHJcbiAgICAgICAgLy8gQXR1YWxpemFyIGEgY29udmVyc2EgY29tIG8gbm92byBwYXJ0aWNpcGFudGVcclxuICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4ge1xyXG4gICAgICAgICAgcmV0dXJuIHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgICAgICBpZiAoY29udi5pZCA9PT0gY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBwYXJ0aWNpcGFudGUgasOhIGV4aXN0ZVxyXG4gICAgICAgICAgICAgIGNvbnN0IHBhcnRpY2lwYW50RXhpc3RzID0gY29udi5wYXJ0aWNpcGFudHMuc29tZShwID0+IHAudXNlcklkID09PSBwYXJ0aWNpcGFudElkKTtcclxuXHJcbiAgICAgICAgICAgICAgaWYgKHBhcnRpY2lwYW50RXhpc3RzKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gY29udjtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC8vIEFkaWNpb25hciBvIG5vdm8gcGFydGljaXBhbnRlXHJcbiAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgIC4uLmNvbnYsXHJcbiAgICAgICAgICAgICAgICBwYXJ0aWNpcGFudHM6IFsuLi5jb252LnBhcnRpY2lwYW50cywgZGF0YS5kYXRhXVxyXG4gICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIGNvbnY7XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gQ3JpYXIgdW1hIG1lbnNhZ2VtIGRvIHNpc3RlbWEgcGFyYSBub3RpZmljYXIgcXVlIHVtIHBhcnRpY2lwYW50ZSBmb2kgYWRpY2lvbmFkb1xyXG4gICAgICAgIGlmIChzb2NrZXQgJiYgaXNDb25uZWN0ZWQpIHtcclxuICAgICAgICAgIGNvbnN0IHN5c3RlbU1lc3NhZ2UgPSB7XHJcbiAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkLFxyXG4gICAgICAgICAgICBjb250ZW50OiBgJHtkYXRhLmRhdGEudXNlci5mdWxsTmFtZX0gZm9pIGFkaWNpb25hZG8gYW8gZ3J1cG9gLFxyXG4gICAgICAgICAgICBjb250ZW50VHlwZTogJ1NZU1RFTSdcclxuICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgc29ja2V0LmVtaXQoJ21lc3NhZ2U6c2VuZCcsIHN5c3RlbU1lc3NhZ2UpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIGRhdGEuZGF0YTtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGFkaWNpb25hciBwYXJ0aWNpcGFudGU6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuICB9LCBbc29ja2V0LCBpc0Nvbm5lY3RlZCwgZ2V0Q3VycmVudFRva2VuLCBoYW5kbGVBdXRoRXJyb3JdKTtcclxuXHJcbiAgLy8gQWRpY2lvbmFyIG3Dumx0aXBsb3MgcGFydGljaXBhbnRlcyBhIHVtIGdydXBvXHJcbiAgY29uc3QgYWRkTXVsdGlwbGVQYXJ0aWNpcGFudHNUb0dyb3VwID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbnZlcnNhdGlvbklkLCBwYXJ0aWNpcGFudHMpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghY29udmVyc2F0aW9uSWQgfHwgIXBhcnRpY2lwYW50cyB8fCAhcGFydGljaXBhbnRzLmxlbmd0aCkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0lEIGRhIGNvbnZlcnNhIGUgbGlzdGEgZGUgcGFydGljaXBhbnRlcyBzw6NvIG9icmlnYXTDs3Jpb3MnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc29sZS5sb2coYEFkaWNpb25hbmRvICR7cGFydGljaXBhbnRzLmxlbmd0aH0gcGFydGljaXBhbnRlcyBhbyBncnVwbyAke2NvbnZlcnNhdGlvbklkfWApO1xyXG5cclxuICAgICAgLy8gQXJyYXkgcGFyYSBhcm1hemVuYXIgb3MgcmVzdWx0YWRvc1xyXG4gICAgICBjb25zdCByZXN1bHRzID0gW107XHJcbiAgICAgIGNvbnN0IGVycm9ycyA9IFtdO1xyXG5cclxuICAgICAgLy8gQWRpY2lvbmFyIHBhcnRpY2lwYW50ZXMgdW0gcG9yIHVtXHJcbiAgICAgIGZvciAoY29uc3QgcGFydGljaXBhbnQgb2YgcGFydGljaXBhbnRzKSB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGFkZFBhcnRpY2lwYW50VG9Hcm91cChjb252ZXJzYXRpb25JZCwgcGFydGljaXBhbnQuaWQpO1xyXG4gICAgICAgICAgaWYgKHJlc3VsdCkge1xyXG4gICAgICAgICAgICByZXN1bHRzLnB1c2gocmVzdWx0KTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGVycm9ycy5wdXNoKHsgdXNlcjogcGFydGljaXBhbnQsIGVycm9yOiAnRmFsaGEgYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZScgfSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm8gYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZSAke3BhcnRpY2lwYW50LmlkfTpgLCBlcnJvcik7XHJcbiAgICAgICAgICBlcnJvcnMucHVzaCh7IHVzZXI6IHBhcnRpY2lwYW50LCBlcnJvcjogZXJyb3IubWVzc2FnZSB8fCAnRXJybyBkZXNjb25oZWNpZG8nIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiByZXN1bHRzLmxlbmd0aCA+IDAsXHJcbiAgICAgICAgYWRkZWQ6IHJlc3VsdHMsXHJcbiAgICAgICAgZXJyb3JzOiBlcnJvcnMsXHJcbiAgICAgICAgdG90YWw6IHBhcnRpY2lwYW50cy5sZW5ndGgsXHJcbiAgICAgICAgc3VjY2Vzc0NvdW50OiByZXN1bHRzLmxlbmd0aCxcclxuICAgICAgICBlcnJvckNvdW50OiBlcnJvcnMubGVuZ3RoXHJcbiAgICAgIH07XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGFkaWNpb25hciBtw7psdGlwbG9zIHBhcnRpY2lwYW50ZXM6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICAgIGFkZGVkOiBbXSxcclxuICAgICAgICBlcnJvcnM6IFt7IGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdFcnJvIGRlc2NvbmhlY2lkbycgfV0sXHJcbiAgICAgICAgdG90YWw6IHBhcnRpY2lwYW50cy5sZW5ndGgsXHJcbiAgICAgICAgc3VjY2Vzc0NvdW50OiAwLFxyXG4gICAgICAgIGVycm9yQ291bnQ6IHBhcnRpY2lwYW50cy5sZW5ndGhcclxuICAgICAgfTtcclxuICAgIH1cclxuICB9LCBbYWRkUGFydGljaXBhbnRUb0dyb3VwXSk7XHJcblxyXG4gIC8vIFJlbW92ZXIgcGFydGljaXBhbnRlIGRlIHVtIGdydXBvIChvdSBzYWlyIGRvIGdydXBvKVxyXG4gIGNvbnN0IHJlbW92ZVBhcnRpY2lwYW50RnJvbUdyb3VwID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbnZlcnNhdGlvbklkLCBwYXJ0aWNpcGFudElkKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygncmVtb3ZlUGFydGljaXBhbnRGcm9tR3JvdXAgY2hhbWFkbzonLCB7IGNvbnZlcnNhdGlvbklkLCBwYXJ0aWNpcGFudElkLCB1c2VySWQ6IHVzZXI/LmlkIH0pO1xyXG4gICAgXHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoIWNvbnZlcnNhdGlvbklkIHx8ICFwYXJ0aWNpcGFudElkKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignSUQgZGEgY29udmVyc2EgZSBJRCBkbyBwYXJ0aWNpcGFudGUgc8OjbyBvYnJpZ2F0w7NyaW9zJyk7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgICBpZiAoIWN1cnJlbnRUb2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIG7Do28gZGlzcG9uw612ZWwgYW8gcmVtb3ZlciBwYXJ0aWNpcGFudGUnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L2NvbnZlcnNhdGlvbnMvJHtjb252ZXJzYXRpb25JZH0vcGFydGljaXBhbnRzLyR7cGFydGljaXBhbnRJZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gZXhwaXJhZG8gb3UgaW52w6FsaWRvIGFvIHJlbW92ZXIgcGFydGljaXBhbnRlJyk7XHJcbiAgICAgICAgICBoYW5kbGVBdXRoRXJyb3IoKTtcclxuICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gcmVtb3ZlciBwYXJ0aWNpcGFudGU6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgIC8vIFNlbXByZSByZW1vdmVyIGRhIGxpc3RhIGxvY2FsIHF1YW5kbyBvIHVzdcOhcmlvIHNhaVxyXG4gICAgICAgIGlmIChwYXJ0aWNpcGFudElkID09PSB1c2VyPy5pZCkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coYFJlbW92ZW5kbyBjb252ZXJzYSAke2NvbnZlcnNhdGlvbklkfSBkYSBsaXN0YSBsb2NhbGApO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyBSZW1vdmVyIGRhIGxpc3RhIGxvY2FsXHJcbiAgICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4gcHJldi5maWx0ZXIoY29udiA9PiBjb252LmlkICE9PSBjb252ZXJzYXRpb25JZCkpO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyBMaW1wYXIgbWVuc2FnZW5zXHJcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZCA9IHsgLi4ucHJldiB9O1xyXG4gICAgICAgICAgICBkZWxldGUgdXBkYXRlZFtjb252ZXJzYXRpb25JZF07XHJcbiAgICAgICAgICAgIHJldHVybiB1cGRhdGVkO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC8vIExpbXBhciBjYWNoZVxyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YSA9IG51bGw7XHJcbiAgICAgICAgICByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy50aW1lc3RhbXAgPSAwO1xyXG4gICAgICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0pIHtcclxuICAgICAgICAgICAgZGVsZXRlIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC8vIFNlIGVyYSBhIGNvbnZlcnNhIGF0aXZhLCBsaW1wYXJcclxuICAgICAgICAgIGlmIChhY3RpdmVDb252ZXJzYXRpb24gPT09IGNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgIHNldEFjdGl2ZUNvbnZlcnNhdGlvbihudWxsKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gRm9yw6dhciByZWNhcnJlZ2FtZW50byBkYXMgY29udmVyc2FzIGFww7NzIHNhaXJcclxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBsb2FkQ29udmVyc2F0aW9ucyh0cnVlKTtcclxuICAgICAgICAgIH0sIDUwMCk7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIGNvbnNvbGUubG9nKGBDb252ZXJzYSAke2NvbnZlcnNhdGlvbklkfSByZW1vdmlkYSBkYSBsaXN0YSBsb2NhbGApO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgYSBjb252ZXJzYSByZW1vdmVuZG8gbyBwYXJ0aWNpcGFudGVcclxuICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgICAgICAgIHJldHVybiBwcmV2Lm1hcChjb252ID0+IHtcclxuICAgICAgICAgICAgICBpZiAoY29udi5pZCA9PT0gY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgIC4uLmNvbnYsXHJcbiAgICAgICAgICAgICAgICAgIHBhcnRpY2lwYW50czogY29udi5wYXJ0aWNpcGFudHMuZmlsdGVyKHAgPT4gcC51c2VySWQgIT09IHBhcnRpY2lwYW50SWQpXHJcbiAgICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICByZXR1cm4gY29udjtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAvLyBDcmlhciB1bWEgbWVuc2FnZW0gZG8gc2lzdGVtYSBwYXJhIG5vdGlmaWNhciBxdWUgdW0gcGFydGljaXBhbnRlIHNhaXVcclxuICAgICAgICAgIGlmIChzb2NrZXQgJiYgaXNDb25uZWN0ZWQgJiYgZGF0YS5kYXRhPy51c2VyPy5mdWxsTmFtZSkge1xyXG4gICAgICAgICAgICBjb25zdCBzeXN0ZW1NZXNzYWdlID0ge1xyXG4gICAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkLFxyXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IGAke2RhdGEuZGF0YS51c2VyLmZ1bGxOYW1lfSBzYWl1IGRvIGdydXBvYCxcclxuICAgICAgICAgICAgICBjb250ZW50VHlwZTogJ1NZU1RFTSdcclxuICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgIHNvY2tldC5lbWl0KCdtZXNzYWdlOnNlbmQnLCBzeXN0ZW1NZXNzYWdlKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiBkYXRhLmRhdGE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyByZW1vdmVyIHBhcnRpY2lwYW50ZTonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG4gIH0sIFtzb2NrZXQsIGlzQ29ubmVjdGVkLCB1c2VyLCBhY3RpdmVDb252ZXJzYXRpb24sIGdldEN1cnJlbnRUb2tlbiwgaGFuZGxlQXV0aEVycm9yLCBsb2FkVW5yZWFkQ291bnRdKTtcclxuXHJcbiAgLy8gTWFyY2FyIG1lbnNhZ2VucyBjb21vIGxpZGFzXHJcbiAgY29uc3QgbWFya01lc3NhZ2VzQXNSZWFkID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbnZlcnNhdGlvbklkLCBtZXNzYWdlSWQpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG4gICAgICBcclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSByZXR1cm47XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvbWVzc2FnZXMvbWFyay1yZWFkYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgY29udmVyc2F0aW9uSWQsIG1lc3NhZ2VJZCB9KVxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIC8vIEF0dWFsaXphciBjb250YWRvciBsb2NhbCBpbWVkaWF0YW1lbnRlXHJcbiAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHByZXYubWFwKGNvbnYgPT4gXHJcbiAgICAgICAgICBjb252LmlkID09PSBjb252ZXJzYXRpb25JZCA/IHsgLi4uY29udiwgdW5yZWFkQ291bnQ6IDAgfSA6IGNvbnZcclxuICAgICAgICApKTtcclxuICAgICAgICBcclxuICAgICAgICAvLyBSZWNhbGN1bGFyIHRvdGFsIGRlIG7Do28gbGlkYXNcclxuICAgICAgICBzZXRVbnJlYWRDb3VudChwcmV2ID0+IHtcclxuICAgICAgICAgIGNvbnN0IGNvbnYgPSBjb252ZXJzYXRpb25zLmZpbmQoYyA9PiBjLmlkID09PSBjb252ZXJzYXRpb25JZCk7XHJcbiAgICAgICAgICBjb25zdCBjdXJyZW50VW5yZWFkID0gY29udj8udW5yZWFkQ291bnQgfHwgMDtcclxuICAgICAgICAgIHJldHVybiBNYXRoLm1heCgwLCBwcmV2IC0gY3VycmVudFVucmVhZCk7XHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgYXdhaXQgbG9hZFVucmVhZENvdW50KHRydWUpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBtYXJraW5nIG1lc3NhZ2VzIGFzIHJlYWQ6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGxvYWRVbnJlYWRDb3VudCwgY29udmVyc2F0aW9uc10pO1xyXG5cclxuICAvLyBSZXNldGFyIGNvbnRhZG9yIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzXHJcbiAgY29uc3QgcmVzZXRVbnJlYWRDb3VudCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG4gICAgICBcclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSByZXR1cm47XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvbWVzc2FnZXMvcmVzZXQtdW5yZWFkYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgc2V0VW5yZWFkQ291bnQoMCk7XHJcbiAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHByZXYubWFwKGNvbnYgPT4gKHsgLi4uY29udiwgdW5yZWFkQ291bnQ6IDAgfSkpKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVzZXR0aW5nIHVucmVhZCBjb3VudDonLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfSwgW3VzZXIsIGdldEN1cnJlbnRUb2tlbl0pO1xyXG5cclxuICAvLyBBcGFnYXIgbWVuc2FnZW5zXHJcbiAgY29uc3QgZGVsZXRlTWVzc2FnZXMgPSB1c2VDYWxsYmFjayhhc3luYyAobWVzc2FnZUlkcywgY29udmVyc2F0aW9uSWQpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghbWVzc2FnZUlkcyB8fCBtZXNzYWdlSWRzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0lEcyBkYXMgbWVuc2FnZW5zIHPDo28gb2JyaWdhdMOzcmlvcycpO1xyXG4gICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBjdXJyZW50VG9rZW4gPSBnZXRDdXJyZW50VG9rZW4oKTtcclxuICAgICAgaWYgKCFjdXJyZW50VG9rZW4pIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdUb2tlbiBuw6NvIGRpc3BvbsOtdmVsIGFvIGFwYWdhciBtZW5zYWdlbnMnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcmVzdWx0cyA9IFtdO1xyXG4gICAgICBjb25zdCBlcnJvcnMgPSBbXTtcclxuXHJcbiAgICAgIC8vIEFwYWdhciBtZW5zYWdlbnMgdW1hIHBvciB1bWFcclxuICAgICAgZm9yIChjb25zdCBtZXNzYWdlSWQgb2YgbWVzc2FnZUlkcykge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvbWVzc2FnZXMvJHttZXNzYWdlSWR9YCwge1xyXG4gICAgICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gZXhwaXJhZG8gb3UgaW52w6FsaWRvIGFvIGFwYWdhciBtZW5zYWdlbScpO1xyXG4gICAgICAgICAgICAgIGhhbmRsZUF1dGhFcnJvcigpO1xyXG4gICAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRXJybyBhbyBhcGFnYXIgbWVuc2FnZW06ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblxyXG4gICAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgICAgICByZXN1bHRzLnB1c2goZGF0YS5kYXRhKTtcclxuXHJcbiAgICAgICAgICAgIC8vIEF0dWFsaXphciBvIGVzdGFkbyBkYXMgbWVuc2FnZW5zXHJcbiAgICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRNZXNzYWdlcyA9IHsgLi4ucHJldiB9O1xyXG5cclxuICAgICAgICAgICAgICAvLyBQZXJjb3JyZXIgdG9kYXMgYXMgY29udmVyc2FzXHJcbiAgICAgICAgICAgICAgT2JqZWN0LmtleXModXBkYXRlZE1lc3NhZ2VzKS5mb3JFYWNoKGNvbnZJZCA9PiB7XHJcbiAgICAgICAgICAgICAgICAvLyBBdHVhbGl6YXIgYSBtZW5zYWdlbSBuYSBjb252ZXJzYSBjb3JyZXNwb25kZW50ZVxyXG4gICAgICAgICAgICAgICAgdXBkYXRlZE1lc3NhZ2VzW2NvbnZJZF0gPSB1cGRhdGVkTWVzc2FnZXNbY29udklkXS5tYXAobXNnID0+IHtcclxuICAgICAgICAgICAgICAgICAgaWYgKG1zZy5pZCA9PT0gbWVzc2FnZUlkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLm1zZyxcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLmRhdGEuZGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgIGlzRGVsZXRlZDogdHJ1ZVxyXG4gICAgICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIG1zZztcclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICByZXR1cm4gdXBkYXRlZE1lc3NhZ2VzO1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGVycm9ycy5wdXNoKHsgbWVzc2FnZUlkLCBlcnJvcjogJ0ZhbGhhIGFvIGFwYWdhciBtZW5zYWdlbScgfSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm8gYW8gYXBhZ2FyIG1lbnNhZ2VtICR7bWVzc2FnZUlkfTpgLCBlcnJvcik7XHJcbiAgICAgICAgICBlcnJvcnMucHVzaCh7IG1lc3NhZ2VJZCwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgJ0Vycm8gZGVzY29uaGVjaWRvJyB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIExpbXBhciBvIGNhY2hlIGRlIG1lbnNhZ2VucyBwYXJhIGZvcsOnYXIgdW1hIG5vdmEgYnVzY2FcclxuICAgICAgaWYgKGNvbnZlcnNhdGlvbklkICYmIHJlc3VsdHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdMaW1wYW5kbyBjYWNoZSBkZSBtZW5zYWdlbnMgcGFyYSBhIGNvbnZlcnNhOicsIGNvbnZlcnNhdGlvbklkKTtcclxuICAgICAgICBpZiAocmVxdWVzdENhY2hlLm1lc3NhZ2VzW2NvbnZlcnNhdGlvbklkXSkge1xyXG4gICAgICAgICAgZGVsZXRlIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF07XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBSZWNhcnJlZ2FyIGFzIG1lbnNhZ2VucyBkYSBjb252ZXJzYSBwYXJhIGdhcmFudGlyIHF1ZSBlc3RhbW9zIHNpbmNyb25pemFkb3MgY29tIG8gYmFja2VuZFxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnUmVjYXJyZWdhbmRvIG1lbnNhZ2VucyBhcMOzcyBleGNsdXPDo28nKTtcclxuICAgICAgICAgIGF3YWl0IGxvYWRNZXNzYWdlcyhjb252ZXJzYXRpb25JZCk7XHJcblxyXG4gICAgICAgICAgLy8gVGFtYsOpbSByZWNhcnJlZ2FyIGEgbGlzdGEgZGUgY29udmVyc2FzIHBhcmEgZ2FyYW50aXIgcXVlIHR1ZG8gZXN0w6EgYXR1YWxpemFkb1xyXG4gICAgICAgICAgYXdhaXQgbG9hZENvbnZlcnNhdGlvbnModHJ1ZSk7XHJcbiAgICAgICAgfSBjYXRjaCAocmVsb2FkRXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gcmVjYXJyZWdhciBtZW5zYWdlbnMgYXDDs3MgZXhjbHVzw6NvOicsIHJlbG9hZEVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgc3VjY2VzczogcmVzdWx0cy5sZW5ndGggPiAwLFxyXG4gICAgICAgIGRlbGV0ZWQ6IHJlc3VsdHMsXHJcbiAgICAgICAgZXJyb3JzOiBlcnJvcnMsXHJcbiAgICAgICAgdG90YWw6IG1lc3NhZ2VJZHMubGVuZ3RoLFxyXG4gICAgICAgIHN1Y2Nlc3NDb3VudDogcmVzdWx0cy5sZW5ndGgsXHJcbiAgICAgICAgZXJyb3JDb3VudDogZXJyb3JzLmxlbmd0aFxyXG4gICAgICB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBhcGFnYXIgbWVuc2FnZW5zOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBkZWxldGVkOiBbXSxcclxuICAgICAgICBlcnJvcnM6IFt7IGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdFcnJvIGRlc2NvbmhlY2lkbycgfV0sXHJcbiAgICAgICAgdG90YWw6IG1lc3NhZ2VJZHMubGVuZ3RoLFxyXG4gICAgICAgIHN1Y2Nlc3NDb3VudDogMCxcclxuICAgICAgICBlcnJvckNvdW50OiBtZXNzYWdlSWRzLmxlbmd0aFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gIH0sIFtnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvciwgbG9hZE1lc3NhZ2VzLCBsb2FkQ29udmVyc2F0aW9uc10pO1xyXG5cclxuICAvLyBNZW1vaXphciBvIHZhbG9yIGRvIGNvbnRleHRvIHBhcmEgZXZpdGFyIHJlLXJlbmRlcml6YcOnw7VlcyBkZXNuZWNlc3PDoXJpYXNcclxuICBjb25zdCBjb250ZXh0VmFsdWUgPSB1c2VNZW1vKCgpID0+ICh7XHJcbiAgICBjb252ZXJzYXRpb25zLFxyXG4gICAgbWVzc2FnZXMsXHJcbiAgICB1bnJlYWRDb3VudCxcclxuICAgIGFjdGl2ZUNvbnZlcnNhdGlvbixcclxuICAgIGlzUGFuZWxPcGVuLFxyXG4gICAgaXNNb2RhbE9wZW4sXHJcbiAgICBpc0Nvbm5lY3RlZCxcclxuICAgIGlzTG9hZGluZyxcclxuICAgIHNldEFjdGl2ZUNvbnZlcnNhdGlvbixcclxuICAgIGxvYWRNZXNzYWdlcyxcclxuICAgIHNlbmRNZXNzYWdlLFxyXG4gICAgY3JlYXRlQ29udmVyc2F0aW9uLFxyXG4gICAgY3JlYXRlT3JHZXRDb252ZXJzYXRpb24sXHJcbiAgICB0b2dnbGVDaGF0UGFuZWwsXHJcbiAgICB0b2dnbGVDaGF0TW9kYWwsXHJcbiAgICBsb2FkQ29udmVyc2F0aW9ucyxcclxuICAgIGxvYWRVbnJlYWRDb3VudCxcclxuICAgIG1hcmtNZXNzYWdlc0FzUmVhZCxcclxuICAgIHJlc2V0VW5yZWFkQ291bnQsXHJcbiAgICBhZGRQYXJ0aWNpcGFudFRvR3JvdXAsXHJcbiAgICBhZGRNdWx0aXBsZVBhcnRpY2lwYW50c1RvR3JvdXAsXHJcbiAgICByZW1vdmVQYXJ0aWNpcGFudEZyb21Hcm91cCxcclxuICAgIGRlbGV0ZU1lc3NhZ2VzXHJcbiAgfSksIFtcclxuICAgIGNvbnZlcnNhdGlvbnMsXHJcbiAgICBtZXNzYWdlcyxcclxuICAgIHVucmVhZENvdW50LFxyXG4gICAgYWN0aXZlQ29udmVyc2F0aW9uLFxyXG4gICAgaXNQYW5lbE9wZW4sXHJcbiAgICBpc01vZGFsT3BlbixcclxuICAgIGlzQ29ubmVjdGVkLFxyXG4gICAgaXNMb2FkaW5nLFxyXG4gICAgc2V0QWN0aXZlQ29udmVyc2F0aW9uLFxyXG4gICAgbG9hZE1lc3NhZ2VzLFxyXG4gICAgc2VuZE1lc3NhZ2UsXHJcbiAgICBjcmVhdGVDb252ZXJzYXRpb24sXHJcbiAgICBjcmVhdGVPckdldENvbnZlcnNhdGlvbixcclxuICAgIHRvZ2dsZUNoYXRQYW5lbCxcclxuICAgIHRvZ2dsZUNoYXRNb2RhbCxcclxuICAgIGxvYWRDb252ZXJzYXRpb25zLFxyXG4gICAgbG9hZFVucmVhZENvdW50LFxyXG4gICAgbWFya01lc3NhZ2VzQXNSZWFkLFxyXG4gICAgcmVzZXRVbnJlYWRDb3VudCxcclxuICAgIGFkZFBhcnRpY2lwYW50VG9Hcm91cCxcclxuICAgIGFkZE11bHRpcGxlUGFydGljaXBhbnRzVG9Hcm91cCxcclxuICAgIHJlbW92ZVBhcnRpY2lwYW50RnJvbUdyb3VwLFxyXG4gICAgZGVsZXRlTWVzc2FnZXNcclxuICBdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxDaGF0Q29udGV4dC5Qcm92aWRlciB2YWx1ZT17Y29udGV4dFZhbHVlfT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9DaGF0Q29udGV4dC5Qcm92aWRlcj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZUNoYXQgPSAoKSA9PiB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQ2hhdENvbnRleHQpO1xyXG5cclxuICBpZiAoIWNvbnRleHQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcigndXNlQ2hhdCBkZXZlIHNlciB1c2FkbyBkZW50cm8gZGUgdW0gQ2hhdFByb3ZpZGVyJyk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gY29udGV4dDtcclxufTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlTWVtbyIsInVzZVJlZiIsInVzZUF1dGgiLCJpbyIsIkFQSV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsInJlcXVlc3RDYWNoZSIsImNvbnZlcnNhdGlvbnMiLCJkYXRhIiwidGltZXN0YW1wIiwidW5yZWFkQ291bnQiLCJtZXNzYWdlcyIsInRva2VuQ2hlY2siLCJ2YWxpZCIsIkNBQ0hFX0VYUElSQVRJT04iLCJUT0tFTl9DSEVDS19FWFBJUkFUSU9OIiwiZGVib3VuY2UiLCJmdW5jIiwid2FpdCIsInRpbWVvdXQiLCJleGVjdXRlZEZ1bmN0aW9uIiwiYXJncyIsImxhdGVyIiwiY2xlYXJUaW1lb3V0Iiwic2V0VGltZW91dCIsIkNoYXRDb250ZXh0IiwiQ2hhdFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ1c2VyIiwibG9nb3V0Iiwic29ja2V0Iiwic2V0U29ja2V0IiwiaXNDb25uZWN0ZWQiLCJzZXRJc0Nvbm5lY3RlZCIsInNldENvbnZlcnNhdGlvbnMiLCJhY3RpdmVDb252ZXJzYXRpb24iLCJzZXRBY3RpdmVDb252ZXJzYXRpb24iLCJzZXRNZXNzYWdlcyIsInNldFVucmVhZENvdW50IiwiaXNQYW5lbE9wZW4iLCJzZXRJc1BhbmVsT3BlbiIsImlzTW9kYWxPcGVuIiwic2V0SXNNb2RhbE9wZW4iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJnZXRDdXJyZW50VG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiaGFuZGxlQXV0aEVycm9yIiwiY29uc29sZSIsImVycm9yIiwiZGlzY29ubmVjdCIsInNvY2tldEluaXRpYWxpemVkUmVmIiwiaXNMb2FkaW5nUmVmIiwibGFzdEluaXRBdHRlbXB0UmVmIiwicmVjb25uZWN0QXR0ZW1wdHNSZWYiLCJjdXJyZW50IiwibG9nIiwiZnVsbE5hbWUiLCJrZXlzVG9SZW1vdmUiLCJpIiwibGVuZ3RoIiwia2V5Iiwic3RhcnRzV2l0aCIsInB1c2giLCJmb3JFYWNoIiwicmVtb3ZlSXRlbSIsImlkIiwicm9sZSIsIkRhdGUiLCJub3ciLCJpbml0aWFsaXplV2ViU29ja2V0IiwiY3VycmVudFRva2VuIiwiY29ubmVjdGVkIiwiY29ubmVjdCIsImRpc2Nvbm5lY3RFcnJvciIsIm1heFJlY29ubmVjdEF0dGVtcHRzIiwicmVjb25uZWN0RGVsYXkiLCJzb2NrZXRJbnN0YW5jZSIsInBhdGgiLCJhdXRoIiwidG9rZW4iLCJ0cmFuc3BvcnRzIiwicmVjb25uZWN0aW9uQXR0ZW1wdHMiLCJyZWNvbm5lY3Rpb25EZWxheSIsImF1dG9Db25uZWN0IiwicmVjb25uZWN0aW9uIiwib24iLCJ3aW5kb3ciLCJkaXNwYXRjaEV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJkZXRhaWwiLCJ0eXBlIiwic3RhdHVzIiwibWVzc2FnZSIsImluY2x1ZGVzIiwiY29udmVyc2F0aW9uSWQiLCJ1c2VySWQiLCJ0ZW1wSWQiLCJpc0N1cnJlbnRVc2VyTWVzc2FnZSIsInNlbmRlcklkIiwicHJldiIsImNvbnZlcnNhdGlvbk1lc3NhZ2VzIiwic29tZSIsIm0iLCJtYXAiLCJzZW5kZXJDbGllbnRJZCIsInRlbXBNZXNzYWdlcyIsImZpbHRlciIsImlzVGVtcCIsImNyZWF0ZWRBdCIsImNvbnRlbnRUeXBlIiwiY29udGVudCIsInRlbXBNZXNzYWdlIiwidXBkYXRlZE1lc3NhZ2VzIiwic29ydCIsImEiLCJiIiwiY29udmVyc2F0aW9uIiwiZmluZCIsImMiLCJsYXN0TWVzc2FnZSIsInVwZGF0ZWRDb252ZXJzYXRpb25zIiwiaXNGcm9tQ3VycmVudFVzZXIiLCJuZXdDb3VudCIsImNvbnYiLCJuZXdVbnJlYWRDb3VudCIsInRvdGFsVW5yZWFkIiwiQXJyYXkiLCJpc0FycmF5IiwidW5yZWFkSW5mbyIsIml0ZW0iLCJjb252ZXJzYXRpb25zQXJyYXkiLCJpbml0aWFsRGF0YUxvYWRlZFJlZiIsImxvYWRDb252ZXJzYXRpb25zIiwicGFydGljaXBhbnRJZCIsInVwZGF0ZWQiLCJwYXJ0aWNpcGFudHMiLCJwIiwiY2xpZW50SWQiLCJhY3Rpb24iLCJpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MiLCJzYWZlSW5pdGlhbGl6ZSIsInRoZW4iLCJyZXN1bHQiLCJjYXRjaCIsImZpbmFsbHkiLCJmb3JjZVJlZnJlc2giLCJyZXNwb25zZSIsImZldGNoIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJzdGF0dXNUZXh0Iiwib2siLCJFcnJvciIsImpzb24iLCJzdWNjZXNzIiwiSlNPTiIsInN0cmluZ2lmeSIsInByb2Nlc3NlZENvbnZlcnNhdGlvbnMiLCJzb3J0ZWRNZXNzYWdlcyIsImNvbnZlcnNhdGlvbldpdGhvdXRNZXNzYWdlcyIsImNsaWVudCIsImxvYWRNZXNzYWdlcyIsImxvYWRpbmciLCJzZW5kTWVzc2FnZSIsIm1ldGFkYXRhIiwiaXNDbGllbnQiLCJ0b0lTT1N0cmluZyIsInNlbmRlciIsInNlbmRlckNsaWVudCIsImxvZ2luIiwibWVzc2FnZURhdGEiLCJlbWl0IiwibXNnIiwiZmFpbGVkIiwiY3JlYXRlQ29udmVyc2F0aW9uIiwicGFydGljaXBhbnRJZHMiLCJ0aXRsZSIsInBhcnRpY2lwYW50UHJvbWlzZXMiLCJ1c2VyRGF0YSIsIlByb21pc2UiLCJhbGwiLCJ2YWxpZFBhcnRpY2lwYW50cyIsImNvbXBhbnlJZCIsImNvbXBhbmllcyIsIlNldCIsImFsZXJ0IiwidmFsaWRhdGlvbkVycm9yIiwiaGFzQ2xpZW50UGFydGljaXBhbnRzIiwibWV0aG9kIiwiYm9keSIsImluY2x1ZGVDbGllbnRzIiwiY29udmVyc2F0aW9uRGF0YSIsImNyZWF0ZU9yR2V0Q29udmVyc2F0aW9uIiwib3RoZXJVc2VyIiwiZXhpc3RpbmdDb252ZXJzYXRpb24iLCJuZXdDb252ZXJzYXRpb24iLCJsb2FkVW5yZWFkQ291bnQiLCJjb252ZXJzYXRpb25JZHMiLCJldmVyeSIsImxvYWRJbml0aWFsRGF0YSIsImNvbnZlcnNhdGlvbnNEYXRhIiwiZGVib3VuY2VkTG9hZERhdGEiLCJjb252ZXJzYXRpb25FeGlzdHMiLCJ3YXJuIiwidG9rZW5DaGVja0ludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwidG9nZ2xlQ2hhdFBhbmVsIiwibmV3U3RhdGUiLCJ0b2dnbGVDaGF0TW9kYWwiLCJhZGRQYXJ0aWNpcGFudFRvR3JvdXAiLCJlcnJvclRleHQiLCJ0ZXh0IiwicGFydGljaXBhbnRFeGlzdHMiLCJzeXN0ZW1NZXNzYWdlIiwiYWRkTXVsdGlwbGVQYXJ0aWNpcGFudHNUb0dyb3VwIiwicmVzdWx0cyIsImVycm9ycyIsInBhcnRpY2lwYW50IiwiYWRkZWQiLCJ0b3RhbCIsInN1Y2Nlc3NDb3VudCIsImVycm9yQ291bnQiLCJyZW1vdmVQYXJ0aWNpcGFudEZyb21Hcm91cCIsIm1hcmtNZXNzYWdlc0FzUmVhZCIsIm1lc3NhZ2VJZCIsImN1cnJlbnRVbnJlYWQiLCJNYXRoIiwibWF4IiwicmVzZXRVbnJlYWRDb3VudCIsImRlbGV0ZU1lc3NhZ2VzIiwibWVzc2FnZUlkcyIsIk9iamVjdCIsImtleXMiLCJjb252SWQiLCJpc0RlbGV0ZWQiLCJyZWxvYWRFcnJvciIsImRlbGV0ZWQiLCJjb250ZXh0VmFsdWUiLCJQcm92aWRlciIsInZhbHVlIiwidXNlQ2hhdCIsImNvbnRleHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.js\n"));

/***/ })

});