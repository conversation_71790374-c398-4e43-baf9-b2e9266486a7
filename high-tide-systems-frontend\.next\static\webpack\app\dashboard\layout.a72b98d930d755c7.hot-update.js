"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Verificação especial para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return isAdmin() && user.role === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return isAdmin() && user.role === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \".concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    activeModuleObject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col justify-center items-center mt-8\",\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleGroup(submenu.id),\n                                            className: \"\\n                      group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\\n                      \".concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const itemKey = \"\".concat(activeModule, \".\").concat(item.id);\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300\\n                          \".concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 304,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const submenuKey = \"\".concat(activeModule, \".\").concat(submenu.id);\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\\n                \".concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 440,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 452,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 430,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 472,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 466,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 498,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 488,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"n2nPwalu72ypjb7vu/RAHHj5oME=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9TaWRlYmFyL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0U7QUFDNEQ7QUFDOUU7QUFDdUI7QUFDYjtBQUNQO0FBQzJCO0FBQ1o7QUFDUTtBQUNUO0FBQzBFO0FBQ3BFO0FBRXJFLE1BQU0wQixjQUFjO1FBQUMsRUFBRUMsUUFBUSxFQUFFQyxLQUFLLEVBQUVDLE1BQU1DLElBQUksRUFBRTtJQUNsRCx5RUFBeUU7SUFDekUsTUFBTUMsa0JBQWtCO1FBQ3RCLElBQUksSUFBNkIsRUFBRTtZQUNqQ0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekI7SUFDRjtJQUNBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDQztZQUNDQyxTQUFTUDtZQUNUUSxPQUFPO2dCQUFFQyxZQUFZO2dCQUFRQyxRQUFRO2dCQUFRQyxTQUFTO1lBQUU7WUFDeEROLFdBQVU7WUFDVk8sY0FBVztzQkFFWCw0RUFBQ1I7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNRO29CQUNDQyxLQUFJO29CQUNKQyxLQUFJO29CQUNKVixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNdEI7S0F6Qk1WO0FBNkJOLHlDQUF5QztBQUN6QyxNQUFNcUIsd0JBQXdCO0lBQzVCLFFBQVE7SUFDUixzQkFBc0I7SUFDdEIsbUJBQW1CO0lBQ25CLGVBQWU7SUFDZixxQkFBcUI7UUFBQztRQUEwQjtLQUErQjtJQUMvRSxxQkFBcUI7SUFDckIsZUFBZTtJQUNmLGNBQWM7SUFDZCxrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLG9CQUFvQjtJQUVwQixPQUFPO0lBQ1Asb0JBQW9CO0lBQ3BCLDZCQUE2QjtJQUM3QixrQkFBa0I7SUFFbEIsYUFBYTtJQUNiLHNCQUFzQjtJQUN0QixzQkFBc0I7SUFDdEIsc0JBQXNCO0lBQ3RCLHFCQUFxQjtJQUNyQixzQkFBc0I7SUFFdEIsS0FBSztJQUNMLGdCQUFnQjtJQUNoQixjQUFjO0lBQ2QsZ0JBQWdCO0lBQ2hCLGtCQUFrQjtJQUNsQixpQkFBaUI7SUFDakIsZUFBZTtJQUNmLGVBQWU7SUFFZixVQUFVO0lBQ1Ysa0JBQWtCO0lBQ2xCLGtCQUFrQjtJQUNsQixvQkFBb0I7SUFDcEIscUJBQXFCO0lBQ3JCLDJCQUEyQjtJQUUzQixjQUFjO0lBQ2Qsc0JBQXNCO0lBQ3RCLDJCQUEyQjtJQUMzQiwyQkFBMkI7SUFDM0IsdUJBQXVCO0lBQ3ZCLHVCQUF1QjtJQUN2QixpQ0FBaUM7SUFDakMsb0NBQW9DO0FBQ3RDO0FBRUEsTUFBTUMsVUFBVTtRQUFDLEVBQ2ZDLFlBQVksRUFDWkMsaUJBQWlCLEVBQ2pCQyxlQUFlLEVBQ2ZDLHdCQUF3QixFQUN4QkMsbUJBQW1CLEVBQ25CQyxhQUFhLEVBQ2Q7UUErSm1GeEM7O0lBOUpsRixNQUFNLEVBQUV5QyxHQUFHLEVBQUVDLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEdBQUcxQyxxRUFBY0E7SUFDbEQsTUFBTSxFQUFFMkMsSUFBSSxFQUFFLEdBQUcxQyw4REFBT0E7SUFDeEIsTUFBTTJDLFdBQVcvQyw0REFBV0E7SUFDNUIsTUFBTSxFQUFFZ0QscUJBQXFCLEVBQUVDLFdBQVdDLGtCQUFrQixFQUFFLEdBQUc3Qyx5RkFBd0JBO0lBRXpGLHFFQUFxRTtJQUNyRSxNQUFNLENBQUM4QyxnQkFBZ0JDLGtCQUFrQixHQUFHOUQsK0NBQVFBOzRCQUFDO1lBQ25ELDBFQUEwRTtZQUMxRSxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLE1BQU0rRCxhQUFhQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ3hDLE9BQU9GLGFBQWFHLEtBQUtDLEtBQUssQ0FBQ0osY0FBYyxDQUFDO1lBQ2hEO1lBQ0EsT0FBTyxDQUFDO1FBQ1Y7O0lBRUEsNERBQTREO0lBQzVELE1BQU1LLHFCQUFxQnpELDhEQUFPQSxDQUFDMEQsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUt4QjtJQUN0RCxNQUFNeUIsYUFBYUosK0JBQUFBLHlDQUFBQSxtQkFBb0J6QyxJQUFJO0lBRTNDLHVFQUF1RTtJQUN2RSxNQUFNOEMsMEJBQTBCMUUsa0RBQVdBO3dEQUFDLENBQUMwQixVQUFVaUQ7WUFDckQsZ0ZBQWdGO1lBQ2hGLElBQUlBLGNBQWMsY0FBY0EsY0FBYyxtQkFDNUNBLGNBQWMsWUFBWUEsY0FBYyxlQUN4Q0EsY0FBYyxnQkFBZ0JBLGNBQWMsY0FBYztnQkFDMUQsT0FBTztZQUNUO1lBRUEsNERBQTREO1lBQzVELElBQUlBLGNBQWMsY0FBYztnQkFDOUIsT0FBT25CLGFBQWFDLEtBQUttQixJQUFJLEtBQUs7WUFDcEM7WUFFQSw4REFBOEQ7WUFDOUQsSUFBSUQsY0FBYyxlQUFlO2dCQUMvQixPQUFPbkIsYUFBYUMsS0FBS21CLElBQUksS0FBSztZQUNwQztZQUVBLE1BQU1DLGdCQUFnQixHQUFlRixPQUFaakQsVUFBUyxLQUFhLE9BQVZpRDtZQUNyQyxNQUFNRyxxQkFBcUJoQyxxQkFBcUIsQ0FBQytCLGNBQWM7WUFFL0QscURBQXFEO1lBQ3JELElBQUksQ0FBQ0Msb0JBQW9CLE9BQU87WUFFaEMsb0NBQW9DO1lBQ3BDLElBQUl0QixXQUFXLE9BQU87WUFFdEIsaUNBQWlDO1lBQ2pDLElBQUl1QixNQUFNQyxPQUFPLENBQUNGLHFCQUFxQjtnQkFDckMsaUZBQWlGO2dCQUNqRixPQUFPQSxtQkFBbUJHLElBQUk7b0VBQUNDLENBQUFBLE9BQVE1QixJQUFJNEI7O1lBQzdDLE9BQU87Z0JBQ0wsb0RBQW9EO2dCQUNwRCxPQUFPNUIsSUFBSXdCO1lBQ2I7UUFDRjt1REFBRztRQUFDeEI7UUFBS0U7UUFBU0MsS0FBS21CLElBQUk7S0FBQztJQUU1QixnRkFBZ0Y7SUFDaEYsTUFBTU8saUNBQWlDbkYsa0RBQVdBOytEQUFDLENBQUMwQixVQUFVaUQ7WUFDNUQsNkRBQTZEO1lBQzdELElBQUlqRCxhQUFhLGFBQWEsT0FBTztZQUVyQywyRkFBMkY7WUFDM0YsSUFBSW1DLG9CQUFvQjtnQkFDdEIsT0FBTztZQUNUO1lBRUEsZ0RBQWdEO1lBQ2hELElBQUksQ0FBQ0YsdUJBQXVCO2dCQUMxQixPQUFPO1lBQ1Q7WUFFQSxPQUFRZ0I7Z0JBQ04sS0FBSztvQkFDSCxNQUFNUyxnQkFBZ0J6QixzQkFBc0J5QixhQUFhLEtBQUs7b0JBQzlELE9BQU9BO2dCQUNULEtBQUs7b0JBQ0gsTUFBTUMsbUJBQW1CMUIsc0JBQXNCMEIsZ0JBQWdCLEtBQUs7b0JBQ3BFLE9BQU9BO2dCQUNULEtBQUs7b0JBQ0gsTUFBTUMsZ0JBQWdCM0Isc0JBQXNCMkIsYUFBYSxLQUFLO29CQUM5RCxPQUFPQTtnQkFDVCxLQUFLO29CQUNILE1BQU1DLG1CQUFtQjVCLHNCQUFzQjRCLGdCQUFnQixLQUFLO29CQUNwRSxPQUFPQTtnQkFDVDtvQkFDRSxPQUFPO1lBQ1g7UUFDRjs4REFBRztRQUFDNUI7UUFBdUJFO0tBQW1CO0lBRTlDLDhDQUE4QztJQUM5QyxNQUFNMkIsY0FBYyxDQUFDQztRQUNuQjFCLGtCQUFrQjJCLENBQUFBLE9BQVM7Z0JBQ3pCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0QsUUFBUSxFQUFFLENBQUNDLElBQUksQ0FBQ0QsUUFBUTtZQUMzQjtJQUNGO0lBRUEsd0RBQXdEO0lBQ3hELE1BQU1FLGdCQUFnQjNGLGtEQUFXQTs4Q0FBQyxDQUFDMEIsVUFBVWtFO1lBQzNDLE9BQU9BLFdBQVdYLElBQUk7c0RBQUNZLENBQUFBLE9BQVEzQyxnQkFBZ0J4QixVQUFVbUUsS0FBS3JCLEVBQUU7O1FBQ2xFOzZDQUFHO1FBQUN0QjtLQUFnQjtJQUVwQiwwREFBMEQ7SUFDMURoRCxnREFBU0E7NkJBQUM7WUFDUixJQUFJOEMsZ0JBQWdCbkMscUVBQWMsQ0FBQ21DLGFBQWEsRUFBRTtnQkFDaEQsTUFBTThDLG9CQUFvQjtvQkFBRSxHQUFHaEMsY0FBYztnQkFBQztnQkFFOUNqRCxxRUFBYyxDQUFDbUMsYUFBYSxDQUFDK0MsT0FBTzt5Q0FBQ0MsQ0FBQUE7d0JBQ25DLElBQUlBLFFBQVFDLElBQUksS0FBSyxXQUFXTixjQUFjM0MsY0FBY2dELFFBQVFFLEtBQUssR0FBRzs0QkFDMUVKLGlCQUFpQixDQUFDRSxRQUFReEIsRUFBRSxDQUFDLEdBQUc7d0JBQ2xDO29CQUNGOztnQkFFQVQsa0JBQWtCK0I7WUFDcEI7UUFDQSx1REFBdUQ7UUFDekQ7NEJBQUc7UUFBQzlDO1FBQWNVO1FBQVVpQztLQUFjO0lBRTFDLGlFQUFpRTtJQUNqRSxNQUFNUSw4QkFBOEJuRyxrREFBV0E7NERBQUMsQ0FBQzBCLFVBQVVpRDtZQUN6RCxPQUFPRCx3QkFBd0JoRCxVQUFVaUQ7UUFDM0M7MkRBQUc7UUFBQ0Q7S0FBd0I7SUFFNUIscUJBQ0UsOERBQUMwQjtRQUNDakUsV0FBVyxnSUFDUixPQUR3SWtCLGdCQUFnQixrQkFBa0I7UUFFN0tYLGNBQVc7OzBCQUdYLDhEQUFDekIsdUVBQWdCQTtnQkFBQ2tCLFdBQVU7Z0JBQWFrRSxhQUFhckQ7O29CQUVuRHFCLG9DQUNDLDhEQUFDNUM7d0JBQ0NDLFVBQVVzQjt3QkFDVnJCLE9BQU9zQjt3QkFDUHJCLE1BQU02Qzs7Ozs7O2tDQUtWLDhEQUFDakQsOEVBQWVBO3dCQUFDd0IsY0FBY0E7Ozs7OztvQkFHOUJhLHNCQUFzQmIsaUJBQWlCLDZCQUN0Qyw4REFBQ2Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDbUU7Z0NBQUtuRSxXQUFVOzBDQUFnRDs7Ozs7Ozs7Ozs7O2tDQUtwRSw4REFBQ29FO3dCQUNDcEUsV0FBVTt3QkFDVnFFLG1CQUFnQjtrQ0FHZixDQUFFM0MsQ0FBQUEsc0JBQXNCYixpQkFBaUIsV0FBVSxLQUFNQSxrQkFBZ0JuQywrQkFBQUEscUVBQWMsQ0FBQ21DLGFBQWEsY0FBNUJuQyxtREFBQUEsNkJBQThCNEYsR0FBRyxDQUFDLENBQUNUOzRCQUMzRyxnREFBZ0Q7NEJBQ2hELElBQUlBLFFBQVFDLElBQUksS0FBSyxTQUFTO2dDQUM1QixrRUFBa0U7Z0NBQ2xFLE1BQU1TLG1CQUFtQlYsUUFBUUUsS0FBSyxDQUFDakIsSUFBSSxDQUFDWSxDQUFBQSxPQUMxQ00sNEJBQTRCbkQsY0FBYzZDLEtBQUtyQixFQUFFO2dDQUduRCxJQUFJLENBQUNrQyxrQkFBa0I7b0NBQ3JCLE9BQU8sTUFBTSx3REFBd0Q7Z0NBQ3ZFO2dDQUVBLE1BQU1DLGtCQUFrQjdDLGNBQWMsQ0FBQ2tDLFFBQVF4QixFQUFFLENBQUMsSUFBSTtnQ0FDdEQsTUFBTW9DLGtCQUFrQmpCLGNBQWMzQyxjQUFjZ0QsUUFBUUUsS0FBSztnQ0FFakUscUJBQ0UsOERBQUNoRTtvQ0FBcUJDLFdBQVU7O3NEQUU5Qiw4REFBQ0M7NENBQ0NDLFNBQVMsSUFBTW1ELFlBQVlRLFFBQVF4QixFQUFFOzRDQUNyQ3JDLFdBQVcsb0pBS1IsT0FIQ3lFLGtCQUNFLGVBQXFENUQsT0FBdENBLGNBQWEsMkJBQXNDLE9BQWJBLGNBQWEsNEJBQ2xFLDZFQUNIOzRDQUVINkQsaUJBQWVGOzs4REFFZiw4REFBQ0w7b0RBQUtuRSxXQUFVOzhEQUF5QjZELFFBQVFyRSxLQUFLOzs7Ozs7OERBQ3RELDhEQUFDTztvREFBSUMsV0FBVTs4REFDWndFLGdDQUNDLDhEQUFDdkcsa0tBQVdBO3dEQUFDMEcsTUFBTTt3REFBSUMsZUFBWTs7Ozs7a0ZBRW5DLDhEQUFDMUcsa0tBQVlBO3dEQUFDeUcsTUFBTTt3REFBSUMsZUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBTXpDSixpQ0FDQyw4REFBQ3pFOzRDQUFJQyxXQUFVO3NEQUNaNkQsUUFBUUUsS0FBSyxDQUFDTyxHQUFHLENBQUNaLENBQUFBO2dEQUNqQixNQUFNbUIsZUFBZTlELGdCQUFnQkYsY0FBYzZDLEtBQUtyQixFQUFFO2dEQUU1QyxpREFBaUQ7Z0RBQ3pFLElBQUksQ0FBQzJCLDRCQUE0Qm5ELGNBQWM2QyxLQUFLckIsRUFBRSxHQUFHO29EQUN2RCxPQUFPLE1BQU0sd0NBQXdDO2dEQUN2RDtnREFFQSxnRUFBZ0U7Z0RBQ2hFLElBQUksQ0FBQ1csK0JBQStCbkMsY0FBYzZDLEtBQUtyQixFQUFFLEdBQUc7b0RBQzFELE9BQU8sTUFBTSw0REFBNEQ7Z0RBQzNFO2dEQUVVLHlDQUF5QztnREFDekMsTUFBTXlDLFVBQVUsR0FBbUJwQixPQUFoQjdDLGNBQWEsS0FBVyxPQUFSNkMsS0FBS3JCLEVBQUU7Z0RBQzFDLE1BQU0wQywwQkFBMEI1Riw4RUFBbUJBLENBQUMwQixjQUFjNkMsS0FBS3JCLEVBQUU7Z0RBQ3pFLE1BQU0yQyxzQkFBc0I1RixpRkFBc0JBLENBQUN5QixjQUFjNkMsS0FBS3JCLEVBQUU7Z0RBRXhFLDZCQUE2QjtnREFDN0IsTUFBTTRDLGdCQUFnQixnSkFPbkIsT0FMQ0osZUFDRSxtQ0FBNkVoRSxPQUExQ0EsY0FBYSwrQkFDbkNBLE9BRGdFQSxjQUFhLDJEQUNoRSxPQUFiQSxjQUFhLDRHQUUxQiw2RUFDSDtnREFHSCxzREFBc0Q7Z0RBQ3RELElBQUlrRSx5QkFBeUI7b0RBQzNCLHFCQUNFLDhEQUFDL0Ysd0VBQWtCQTt3REFFakJnQixXQUFXaUY7d0RBQ1hDLGdCQUFjTCxlQUFlLFNBQVNNO3dEQUN0QzNGLE9BQU93RixvQkFBb0J4RixLQUFLO3dEQUNoQzRGLFNBQVNKLG9CQUFvQkksT0FBTzt3REFDcEMzRixNQUFNdUYsb0JBQW9CdkYsSUFBSTt3REFDOUI0RixVQUFTOzs0REFFUjNCLEtBQUtqRSxJQUFJLGtCQUNSLDhEQUFDTTtnRUFBSUMsV0FBVyx1Q0FJYixPQUhDNkUsZUFDRSxlQUFxRGhFLE9BQXRDQSxjQUFhLDJCQUFzQyxPQUFiQSxjQUFhLGdCQUNsRSw0REFBOEdBLE9BQWxEQSxjQUFhLHVDQUFrRCxPQUFiQSxjQUFhLDhDQUM5SDswRUFFRCw0RUFBQzZDLEtBQUtqRSxJQUFJO29FQUNSa0YsTUFBTTtvRUFDTkMsZUFBWTs7Ozs7Ozs7Ozs7MEVBSWxCLDhEQUFDVDtnRUFBS25FLFdBQVcsaUNBQXVFLE9BQXRDNkUsZUFBZSxvQkFBb0I7MEVBQU9uQixLQUFLbEUsS0FBSzs7Ozs7Ozt1REFyQmpHa0UsS0FBS3JCLEVBQUU7Ozs7O2dEQXdCbEI7Z0RBRUEsb0RBQW9EO2dEQUNwRCxxQkFDRSw4REFBQ3BDO29EQUVDQyxTQUFTLElBQU1jLHlCQUF5QkgsY0FBYzZDLEtBQUtyQixFQUFFO29EQUM3RHJDLFdBQVdpRjtvREFDWEMsZ0JBQWNMLGVBQWUsU0FBU007O3dEQUVyQ3pCLEtBQUtqRSxJQUFJLGtCQUNSLDhEQUFDTTs0REFBSUMsV0FBVyxxQ0FJYixPQUhDNkUsZUFDRSxlQUFxRGhFLE9BQXRDQSxjQUFhLDJCQUFzQyxPQUFiQSxjQUFhLGdCQUNsRSw0REFBOEdBLE9BQWxEQSxjQUFhLHVDQUFrRCxPQUFiQSxjQUFhLDhDQUM5SDtzRUFFRCw0RUFBQzZDLEtBQUtqRSxJQUFJO2dFQUNSa0YsTUFBTTtnRUFDTkMsZUFBWTs7Ozs7Ozs7Ozs7c0VBSWxCLDhEQUFDVDs0REFBS25FLFdBQVcsaUNBQXVFLE9BQXRDNkUsZUFBZSxvQkFBb0I7c0VBQU9uQixLQUFLbEUsS0FBSzs7Ozs7OzttREFsQmpHa0UsS0FBS3JCLEVBQUU7Ozs7OzRDQXFCbEI7Ozs7Ozs7bUNBN0dJd0IsUUFBUXhCLEVBQUU7Ozs7OzRCQWtIeEIsT0FBTztnQ0FDTCxvREFBb0Q7Z0NBQ3BELE1BQU1pRCxXQUFXdkUsZ0JBQWdCRixjQUFjZ0QsUUFBUXhCLEVBQUU7Z0NBRXpELGlEQUFpRDtnQ0FDakQsTUFBTWtELGdCQUFnQmhELHdCQUF3QjFCLGNBQWNnRCxRQUFReEIsRUFBRTtnQ0FFdEUsSUFBSSxDQUFDa0QsZUFBZTtvQ0FDbEIsT0FBTyxNQUFNLHdDQUF3QztnQ0FDdkQ7Z0NBRUEsbUVBQW1FO2dDQUNuRSxNQUFNQywwQkFBMEJ4QywrQkFBK0JuQyxjQUFjZ0QsUUFBUXhCLEVBQUU7Z0NBRXZGLElBQUksQ0FBQ21ELHlCQUF5QjtvQ0FDNUIsT0FBTyxNQUFNLDREQUE0RDtnQ0FDM0U7Z0NBRUEsNENBQTRDO2dDQUM1QyxNQUFNQyxhQUFhLEdBQW1CNUIsT0FBaEJoRCxjQUFhLEtBQWMsT0FBWGdELFFBQVF4QixFQUFFO2dDQUNoRCxNQUFNcUQsNkJBQTZCdkcsOEVBQW1CQSxDQUFDMEIsY0FBY2dELFFBQVF4QixFQUFFO2dDQUMvRSxNQUFNMkMsc0JBQXNCNUYsaUZBQXNCQSxDQUFDeUIsY0FBY2dELFFBQVF4QixFQUFFO2dDQUUzRSw2Q0FBNkM7Z0NBQzdDLE1BQU1zRCxrQkFBa0IsNEhBT3JCLE9BTENMLFdBQ0UsbUNBQTZFekUsT0FBMUNBLGNBQWEsK0JBQ25DQSxPQURnRUEsY0FBYSxpREFDaEUsT0FBYkEsY0FBYSxrR0FFMUIsNkVBQ0g7Z0NBR0gsc0RBQXNEO2dDQUN0RCxJQUFJNkUsNEJBQTRCO29DQUM5QixxQkFDRSw4REFBQzFHLHdFQUFrQkE7d0NBRWpCZ0IsV0FBVzJGO3dDQUNYVCxnQkFBY0ksV0FBVyxTQUFTSDt3Q0FDbEMzRixPQUFPd0Ysb0JBQW9CeEYsS0FBSzt3Q0FDaEM0RixTQUFTSixvQkFBb0JJLE9BQU87d0NBQ3BDM0YsTUFBTXVGLG9CQUFvQnZGLElBQUk7d0NBQzlCNEYsVUFBUzs7NENBRVJ4QixRQUFRcEUsSUFBSSxrQkFDWCw4REFBQ007Z0RBQUlDLFdBQVcsMkJBSWIsT0FIRHNGLFdBQ0ksZUFBcUR6RSxPQUF0Q0EsY0FBYSwyQkFBc0MsT0FBYkEsY0FBYSxnQkFDbEUsNERBQThHQSxPQUFsREEsY0FBYSx1Q0FBa0QsT0FBYkEsY0FBYSw4Q0FDOUg7MERBRUQsNEVBQUNnRCxRQUFRcEUsSUFBSTtvREFDWGtGLE1BQU07b0RBQ05DLGVBQVk7Ozs7Ozs7Ozs7OzBEQUlsQiw4REFBQ1Q7Z0RBQUtuRSxXQUFXLHlCQUEyRCxPQUFsQ3NGLFdBQVcsb0JBQW9COzBEQUFPekIsUUFBUXJFLEtBQUs7Ozs7Ozs7dUNBckJ4RnFFLFFBQVF4QixFQUFFOzs7OztnQ0F3QnJCO2dDQUVBLG9EQUFvRDtnQ0FDcEQscUJBQ0UsOERBQUNwQztvQ0FFQ0MsU0FBUyxJQUFNYyx5QkFBeUJILGNBQWNnRCxRQUFReEIsRUFBRTtvQ0FDaEVyQyxXQUFXMkY7b0NBQ1hULGdCQUFjSSxXQUFXLFNBQVNIOzt3Q0FFakN0QixRQUFRcEUsSUFBSSxrQkFDWCw4REFBQ007NENBQUlDLFdBQVcsMkJBSWIsT0FIQ3NGLFdBQ0UsZUFBcUR6RSxPQUF0Q0EsY0FBYSwyQkFBc0MsT0FBYkEsY0FBYSxnQkFDbEUsNERBQThHQSxPQUFsREEsY0FBYSx1Q0FBa0QsT0FBYkEsY0FBYSw4Q0FDOUg7c0RBRUQsNEVBQUNnRCxRQUFRcEUsSUFBSTtnREFDWGtGLE1BQU07Z0RBQ05DLGVBQVk7Ozs7Ozs7Ozs7O3NEQUlsQiw4REFBQ1Q7NENBQUtuRSxXQUFXLHlCQUEyRCxPQUFsQ3NGLFdBQVcsb0JBQW9CO3NEQUFPekIsUUFBUXJFLEtBQUs7Ozs7Ozs7bUNBbEJ4RnFFLFFBQVF4QixFQUFFOzs7Ozs0QkFxQnJCO3dCQUNGOzs7Ozs7Ozs7Ozs7MEJBS0osOERBQUN0QztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQ0NDLFNBQVNlO29CQUNUakIsV0FBVywrR0FFMERhLE9BQTFDQSxjQUFhLCtCQUNZQSxPQURpQkEsY0FBYSw4RUFDYUEsT0FBM0NBLGNBQWEsZ0NBQTJDLE9BQWJBLGNBQWE7b0JBRzVHTixjQUFXOztzQ0FFWCw4REFBQ1I7NEJBQUlDLFdBQVcsOEZBRWdDYSxPQUFsQ0EsY0FBYSx1QkFDWEEsT0FEZ0NBLGNBQWEseUNBQ1BBLE9BQXRDQSxjQUFhLDJCQUFzQyxPQUFiQSxjQUFhO3NDQUlqRSw0RUFBQzdDLGtLQUFXQTtnQ0FBQzJHLE1BQU07Z0NBQUlDLGVBQVk7Ozs7Ozs7Ozs7O3NDQUVyQyw4REFBQ1Q7NEJBQUtuRSxXQUFVO3NDQUErQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLekU7R0EvWk1ZOztRQVFnQ2pDLGlFQUFjQTtRQUNqQ0MsMERBQU9BO1FBQ1BKLHdEQUFXQTtRQUNxQ0sscUZBQXdCQTs7O01BWHJGK0I7QUFpYU4saUVBQWVBLE9BQU9BLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcUHJvZ3JhbWFjYW9cXEhJR0ggVElERSBTWVNURU1TXFxoaWdoLXRpZGUtc3lzdGVtcy1mcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxkYXNoYm9hcmRcXFNpZGViYXJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VDYWxsYmFjaywgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgQ2hldnJvbkxlZnQsIENoZXZyb25Eb3duLCBDaGV2cm9uUmlnaHQsIENvbnN0cnVjdGlvbiwgSGFyZEhhdCwgSGFtbWVyLCBXcmVuY2gsIEFsZXJ0VHJpYW5nbGUgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbmltcG9ydCB7IG1vZHVsZXMsIG1vZHVsZVN1Ym1lbnVzIH0gZnJvbSAnQC9hcHAvZGFzaGJvYXJkL2NvbXBvbmVudHMnO1xyXG5pbXBvcnQgeyB1c2VQZXJtaXNzaW9ucyB9IGZyb20gJ0AvaG9va3MvdXNlUGVybWlzc2lvbnMnO1xyXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XHJcbmltcG9ydCB7IHVzZVNjaGVkdWxpbmdQcmVmZXJlbmNlcyB9IGZyb20gJ0AvaG9va3MvdXNlU2NoZWR1bGluZ1ByZWZlcmVuY2VzJztcclxuaW1wb3J0IEN1c3RvbVNjcm9sbEFyZWEgZnJvbSAnQC9jb21wb25lbnRzL3VpL0N1c3RvbVNjcm9sbEFyZWEnO1xyXG5pbXBvcnQgeyB1c2VDb25zdHJ1Y3Rpb25NZXNzYWdlIH0gZnJvbSAnQC9ob29rcy91c2VDb25zdHJ1Y3Rpb25NZXNzYWdlJztcclxuaW1wb3J0IHsgQ29uc3RydWN0aW9uQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL2NvbnN0cnVjdGlvbic7XHJcbmltcG9ydCB7IHVuZGVyQ29uc3RydWN0aW9uU3VibWVudXMsIGNvbnN0cnVjdGlvbk1lc3NhZ2VzLCBpc1VuZGVyQ29uc3RydWN0aW9uLCBnZXRDb25zdHJ1Y3Rpb25NZXNzYWdlIH0gZnJvbSAnQC91dGlscy9jb25zdHJ1Y3Rpb25VdGlscyc7XHJcbmltcG9ydCBDb21wYW55U2VsZWN0b3IgZnJvbSAnQC9jb21wb25lbnRzL2Rhc2hib2FyZC9Db21wYW55U2VsZWN0b3InO1xyXG5cclxuY29uc3QgTW9kdWxlVGl0bGUgPSAoeyBtb2R1bGVJZCwgdGl0bGUsIGljb246IEljb24gfSkgPT4ge1xyXG4gIC8vIEV4aWJpciBvIGxvZ28gbm8gbHVnYXIgZG8gdMOtdHVsbyBkbyBtw7NkdWxvLCBjb21vIGJvdMOjbyBwYXJhIC9kYXNoYm9hcmRcclxuICBjb25zdCBoYW5kbGVMb2dvQ2xpY2sgPSAoKSA9PiB7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2Rhc2hib2FyZCc7XHJcbiAgICB9XHJcbiAgfTtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IGZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgIDxidXR0b25cclxuICAgICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvQ2xpY2t9XHJcbiAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZDogJ25vbmUnLCBib3JkZXI6ICdub25lJywgcGFkZGluZzogMCB9fVxyXG4gICAgICAgIGNsYXNzTmFtZT1cImZvY3VzOm91dGxpbmUtbm9uZSBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgYXJpYS1sYWJlbD1cIklyIHBhcmEgbyBkYXNoYm9hcmRcIlxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgPGltZ1xyXG4gICAgICAgICAgICBzcmM9XCIvbG9nb19ob3Jpem9udGFsX3NlbV9mdW5kby5wbmdcIlxyXG4gICAgICAgICAgICBhbHQ9XCJIaWdoIFRpZGUgTG9nb1wiXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTIgZGFyazppbnZlcnQgZGFyazp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvYnV0dG9uPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcblxyXG5cclxuLy8gTWFwZWFtZW50byBkZSBzdWJtZW51cyBwYXJhIHBlcm1pc3PDtWVzXHJcbmNvbnN0IHN1Ym1lbnVQZXJtaXNzaW9uc01hcCA9IHtcclxuICAvLyBBZG1pblxyXG4gICdhZG1pbi5pbnRyb2R1Y3Rpb24nOiAnYWRtaW4uZGFzaGJvYXJkLnZpZXcnLFxyXG4gICdhZG1pbi5kYXNoYm9hcmQnOiAnYWRtaW4uZGFzaGJvYXJkLnZpZXcnLFxyXG4gICdhZG1pbi51c2Vycyc6ICdhZG1pbi51c2Vycy52aWV3JyxcclxuICAnYWRtaW4ucHJvZmVzc2lvbnMnOiBbJ2FkbWluLnByb2Zlc3Npb25zLnZpZXcnLCAnYWRtaW4ucHJvZmVzc2lvbi1ncm91cHMudmlldyddLFxyXG4gICdhZG1pbi5idWctcmVwb3J0cyc6ICdhZG1pbi5kYXNoYm9hcmQudmlldycsIC8vIEFwZW5hcyBTWVNURU1fQURNSU4gdGVtIGFjZXNzb1xyXG4gICdhZG1pbi5wbGFucyc6ICdhZG1pbi5kYXNoYm9hcmQudmlldycsXHJcbiAgJ2FkbWluLmxvZ3MnOiAnYWRtaW4ubG9ncy52aWV3JyxcclxuICAnYWRtaW4uc2V0dGluZ3MnOiAnYWRtaW4uY29uZmlnLmVkaXQnLFxyXG4gICdhZG1pbi5iYWNrdXAnOiAnYWRtaW4uY29uZmlnLmVkaXQnLFxyXG4gICdhZG1pbi5hZmZpbGlhdGVzJzogJ2FkbWluLmRhc2hib2FyZC52aWV3JywgLy8gQXBlbmFzIFNZU1RFTV9BRE1JTiB0ZW0gYWNlc3NvXHJcblxyXG4gIC8vIEFCQStcclxuICAnYWJhcGx1cy5hbmFtbmVzZSc6ICdhYmFwbHVzLmFuYW1uZXNlLnZpZXcnLFxyXG4gICdhYmFwbHVzLmV2b2x1Y29lcy1kaWFyaWFzJzogJ2FiYXBsdXMuZXZvbHVjb2VzLWRpYXJpYXMudmlldycsXHJcbiAgJ2FiYXBsdXMuc2Vzc2FvJzogJ2FiYXBsdXMuc2Vzc2FvLnZpZXcnLFxyXG5cclxuICAvLyBGaW5hbmNlaXJvXHJcbiAgJ2ZpbmFuY2lhbC5pbnZvaWNlcyc6ICdmaW5hbmNpYWwuaW52b2ljZXMudmlldycsXHJcbiAgJ2ZpbmFuY2lhbC5wYXltZW50cyc6ICdmaW5hbmNpYWwucGF5bWVudHMudmlldycsXHJcbiAgJ2ZpbmFuY2lhbC5leHBlbnNlcyc6ICdmaW5hbmNpYWwuZXhwZW5zZXMudmlldycsXHJcbiAgJ2ZpbmFuY2lhbC5yZXBvcnRzJzogJ2ZpbmFuY2lhbC5yZXBvcnRzLnZpZXcnLFxyXG4gICdmaW5hbmNpYWwuY2FzaGZsb3cnOiAnZmluYW5jaWFsLnJlcG9ydHMudmlldycsXHJcblxyXG4gIC8vIFJIXHJcbiAgJ2hyLmVtcGxveWVlcyc6ICdyaC5lbXBsb3llZXMudmlldycsXHJcbiAgJ2hyLnBheXJvbGwnOiAncmgucGF5cm9sbC52aWV3JyxcclxuICAnaHIuZG9jdW1lbnRzJzogJ3JoLmVtcGxveWVlcy52aWV3JyxcclxuICAnaHIuZGVwYXJ0bWVudHMnOiAncmguZW1wbG95ZWVzLnZpZXcnLFxyXG4gICdoci5hdHRlbmRhbmNlJzogJ3JoLmF0dGVuZGFuY2UudmlldycsXHJcbiAgJ2hyLmJlbmVmaXRzJzogJ3JoLmJlbmVmaXRzLnZpZXcnLFxyXG4gICdoci50cmFpbmluZyc6ICdyaC5lbXBsb3llZXMudmlldycsXHJcblxyXG4gIC8vIFBlc3NvYXNcclxuICAncGVvcGxlLmNsaWVudHMnOiAncGVvcGxlLmNsaWVudHMudmlldycsXHJcbiAgJ3Blb3BsZS5wZXJzb25zJzogJ3Blb3BsZS5wZXJzb25zLnZpZXcnLFxyXG4gICdwZW9wbGUuZG9jdW1lbnRzJzogJ3Blb3BsZS5kb2N1bWVudHMudmlldycsXHJcbiAgJ3Blb3BsZS5pbnN1cmFuY2VzJzogJ3Blb3BsZS5pbnN1cmFuY2VzLnZpZXcnLFxyXG4gICdwZW9wbGUuaW5zdXJhbmNlLWxpbWl0cyc6ICdwZW9wbGUuaW5zdXJhbmNlLWxpbWl0cy52aWV3JyxcclxuXHJcbiAgLy8gQWdlbmRhbWVudG9cclxuICAnc2NoZWR1bGVyLmNhbGVuZGFyJzogJ3NjaGVkdWxpbmcuY2FsZW5kYXIudmlldycsXHJcbiAgJ3NjaGVkdWxlci53b3JraW5nLWhvdXJzJzogJ3NjaGVkdWxpbmcud29ya2luZy1ob3Vycy52aWV3JyxcclxuICAnc2NoZWR1bGVyLnNlcnZpY2UtdHlwZXMnOiAnc2NoZWR1bGluZy5zZXJ2aWNlLXR5cGVzLnZpZXcnLFxyXG4gICdzY2hlZHVsZXIubG9jYXRpb25zJzogJ3NjaGVkdWxpbmcubG9jYXRpb25zLnZpZXcnLFxyXG4gICdzY2hlZHVsZXIub2NjdXBhbmN5JzogJ3NjaGVkdWxpbmcub2NjdXBhbmN5LnZpZXcnLFxyXG4gICdzY2hlZHVsZXIuYXBwb2ludG1lbnRzLXJlcG9ydCc6ICdzY2hlZHVsaW5nLmFwcG9pbnRtZW50cy1yZXBvcnQudmlldycsXHJcbiAgJ3NjaGVkdWxlci5hcHBvaW50bWVudHMtZGFzaGJvYXJkJzogJ3NjaGVkdWxpbmcuYXBwb2ludG1lbnRzLWRhc2hib2FyZC52aWV3JyxcclxufTtcclxuXHJcbmNvbnN0IFNpZGViYXIgPSAoe1xyXG4gIGFjdGl2ZU1vZHVsZSxcclxuICBhY3RpdmVNb2R1bGVUaXRsZSxcclxuICBpc1N1Ym1lbnVBY3RpdmUsXHJcbiAgaGFuZGxlTW9kdWxlU3VibWVudUNsaWNrLFxyXG4gIGhhbmRsZUJhY2tUb01vZHVsZXMsXHJcbiAgaXNTaWRlYmFyT3BlblxyXG59KSA9PiB7XHJcbiAgY29uc3QgeyBjYW4sIGhhc01vZHVsZSwgaXNBZG1pbiB9ID0gdXNlUGVybWlzc2lvbnMoKTtcclxuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcclxuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XHJcbiAgY29uc3QgeyBzY2hlZHVsaW5nUHJlZmVyZW5jZXMsIGlzTG9hZGluZzogcHJlZmVyZW5jZXNMb2FkaW5nIH0gPSB1c2VTY2hlZHVsaW5nUHJlZmVyZW5jZXMoKTtcclxuXHJcbiAgLy8gSW5pY2lhbGl6YXIgbyBlc3RhZG8gZGUgZ3J1cG9zIGV4cGFuZGlkb3MgYSBwYXJ0aXIgZG8gbG9jYWxTdG9yYWdlXHJcbiAgY29uc3QgW2V4cGFuZGVkR3JvdXBzLCBzZXRFeHBhbmRlZEdyb3Vwc10gPSB1c2VTdGF0ZSgoKSA9PiB7XHJcbiAgICAvLyBWZXJpZmljYXIgc2UgZXN0YW1vcyBubyBjbGllbnRlIChicm93c2VyKSBhbnRlcyBkZSBhY2Vzc2FyIGxvY2FsU3RvcmFnZVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIGNvbnN0IHNhdmVkU3RhdGUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnc2lkZWJhckV4cGFuZGVkR3JvdXBzJyk7XHJcbiAgICAgIHJldHVybiBzYXZlZFN0YXRlID8gSlNPTi5wYXJzZShzYXZlZFN0YXRlKSA6IHt9O1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHt9O1xyXG4gIH0pO1xyXG5cclxuICAvLyBFbmNvbnRyYXIgbyBvYmpldG8gZG8gbcOzZHVsbyBhdGl2byBwYXJhIGFjZXNzYXIgc2V1IMOtY29uZVxyXG4gIGNvbnN0IGFjdGl2ZU1vZHVsZU9iamVjdCA9IG1vZHVsZXMuZmluZChtID0+IG0uaWQgPT09IGFjdGl2ZU1vZHVsZSk7XHJcbiAgY29uc3QgTW9kdWxlSWNvbiA9IGFjdGl2ZU1vZHVsZU9iamVjdD8uaWNvbjtcclxuXHJcbiAgLy8gRnVuw6fDo28gcGFyYSB2ZXJpZmljYXIgc2UgbyB1c3XDoXJpbyB0ZW0gcGVybWlzc8OjbyBwYXJhIHZlciB1bSBzdWJtZW51XHJcbiAgY29uc3QgaGFzUGVybWlzc2lvbkZvclN1Ym1lbnUgPSB1c2VDYWxsYmFjaygobW9kdWxlSWQsIHN1Ym1lbnVJZCkgPT4ge1xyXG4gICAgLy8gSWdub3JhciB2ZXJpZmljYcOnw6NvIHBhcmEgZ3J1cG9zLCBwb2lzIGEgcGVybWlzc8OjbyDDqSB2ZXJpZmljYWRhIHBhcmEgY2FkYSBpdGVtXHJcbiAgICBpZiAoc3VibWVudUlkID09PSAnY2FkYXN0cm8nIHx8IHN1Ym1lbnVJZCA9PT0gJ2NvbmZpZ3VyYWNvZXMnIHx8XHJcbiAgICAgIHN1Ym1lbnVJZCA9PT0gJ2dlc3RhbycgfHwgc3VibWVudUlkID09PSAnY29udmVuaW9zJyB8fFxyXG4gICAgICBzdWJtZW51SWQgPT09ICdmaW5hbmNlaXJvJyB8fCBzdWJtZW51SWQgPT09ICdyZWxhdG9yaW9zJykge1xyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBWZXJpZmljYcOnw6NvIGVzcGVjaWFsIHBhcmEgYWZpbGlhZG9zIC0gYXBlbmFzIFNZU1RFTV9BRE1JTlxyXG4gICAgaWYgKHN1Ym1lbnVJZCA9PT0gJ2FmZmlsaWF0ZXMnKSB7XHJcbiAgICAgIHJldHVybiBpc0FkbWluKCkgJiYgdXNlci5yb2xlID09PSAnU1lTVEVNX0FETUlOJztcclxuICAgIH1cclxuXHJcbiAgICAvLyBWZXJpZmljYcOnw6NvIGVzcGVjaWFsIHBhcmEgYnVnLXJlcG9ydHMgLSBhcGVuYXMgU1lTVEVNX0FETUlOXHJcbiAgICBpZiAoc3VibWVudUlkID09PSAnYnVnLXJlcG9ydHMnKSB7XHJcbiAgICAgIHJldHVybiBpc0FkbWluKCkgJiYgdXNlci5yb2xlID09PSAnU1lTVEVNX0FETUlOJztcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBwZXJtaXNzaW9uS2V5ID0gYCR7bW9kdWxlSWR9LiR7c3VibWVudUlkfWA7XHJcbiAgICBjb25zdCByZXF1aXJlZFBlcm1pc3Npb24gPSBzdWJtZW51UGVybWlzc2lvbnNNYXBbcGVybWlzc2lvbktleV07XHJcblxyXG4gICAgLy8gU2UgbsOjbyBow6EgbWFwZWFtZW50byBkZSBwZXJtaXNzw6NvLCBwZXJtaXRpciBhY2Vzc29cclxuICAgIGlmICghcmVxdWlyZWRQZXJtaXNzaW9uKSByZXR1cm4gdHJ1ZTtcclxuXHJcbiAgICAvLyBBZG1pbmlzdHJhZG9yZXMgdMOqbSBhY2Vzc28gYSB0dWRvXHJcbiAgICBpZiAoaXNBZG1pbigpKSByZXR1cm4gdHJ1ZTtcclxuXHJcbiAgICAvLyBWZXJpZmljYXIgcGVybWlzc8OjbyBlc3BlY8OtZmljYVxyXG4gICAgaWYgKEFycmF5LmlzQXJyYXkocmVxdWlyZWRQZXJtaXNzaW9uKSkge1xyXG4gICAgICAvLyBTZSBmb3IgdW0gYXJyYXkgZGUgcGVybWlzc8O1ZXMsIHZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIHRlbSBwZWxvIG1lbm9zIHVtYSBkZWxhc1xyXG4gICAgICByZXR1cm4gcmVxdWlyZWRQZXJtaXNzaW9uLnNvbWUocGVybSA9PiBjYW4ocGVybSkpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8gU2UgZm9yIHVtYSDDum5pY2EgcGVybWlzc8OjbywgdmVyaWZpY2FyIG5vcm1hbG1lbnRlXHJcbiAgICAgIHJldHVybiBjYW4ocmVxdWlyZWRQZXJtaXNzaW9uKTtcclxuICAgIH1cclxuICB9LCBbY2FuLCBpc0FkbWluLCB1c2VyLnJvbGVdKTtcclxuXHJcbiAgLy8gRnVuw6fDo28gcGFyYSB2ZXJpZmljYXIgc2UgdW0gc3VibWVudSBkZXZlIHNlciBleGliaWRvIGJhc2VhZG8gbmFzIHByZWZlcsOqbmNpYXNcclxuICBjb25zdCBzaG91bGRTaG93U3VibWVudUJ5UHJlZmVyZW5jZXMgPSB1c2VDYWxsYmFjaygobW9kdWxlSWQsIHN1Ym1lbnVJZCkgPT4ge1xyXG4gICAgLy8gQXBlbmFzIHZlcmlmaWNhciBwcmVmZXLDqm5jaWFzIHBhcmEgbyBtw7NkdWxvIGRlIGFnZW5kYW1lbnRvXHJcbiAgICBpZiAobW9kdWxlSWQgIT09ICdzY2hlZHVsZXInKSByZXR1cm4gdHJ1ZTtcclxuXHJcbiAgICAvLyBTZSBhcyBwcmVmZXLDqm5jaWFzIGFpbmRhIGVzdMOjbyBjYXJyZWdhbmRvLCBuw6NvIG1vc3RyYXIgb3MgaXRlbnMgcXVlIHBvZGVtIHNlciBlc2NvbmRpZG9zXHJcbiAgICBpZiAocHJlZmVyZW5jZXNMb2FkaW5nKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBWZXJpZmljYXIgc2UgYXMgcHJlZmVyw6puY2lhcyBlc3TDo28gY2FycmVnYWRhc1xyXG4gICAgaWYgKCFzY2hlZHVsaW5nUHJlZmVyZW5jZXMpIHtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIHN3aXRjaCAoc3VibWVudUlkKSB7XHJcbiAgICAgIGNhc2UgJ2xvY2F0aW9ucyc6XHJcbiAgICAgICAgY29uc3Qgc2hvd0xvY2F0aW9ucyA9IHNjaGVkdWxpbmdQcmVmZXJlbmNlcy5zaG93TG9jYXRpb25zICE9PSBmYWxzZTtcclxuICAgICAgICByZXR1cm4gc2hvd0xvY2F0aW9ucztcclxuICAgICAgY2FzZSAnc2VydmljZS10eXBlcyc6XHJcbiAgICAgICAgY29uc3Qgc2hvd1NlcnZpY2VUeXBlcyA9IHNjaGVkdWxpbmdQcmVmZXJlbmNlcy5zaG93U2VydmljZVR5cGVzICE9PSBmYWxzZTtcclxuICAgICAgICByZXR1cm4gc2hvd1NlcnZpY2VUeXBlcztcclxuICAgICAgY2FzZSAnaW5zdXJhbmNlcyc6XHJcbiAgICAgICAgY29uc3Qgc2hvd0luc3VyYW5jZSA9IHNjaGVkdWxpbmdQcmVmZXJlbmNlcy5zaG93SW5zdXJhbmNlICE9PSBmYWxzZTtcclxuICAgICAgICByZXR1cm4gc2hvd0luc3VyYW5jZTtcclxuICAgICAgY2FzZSAnd29ya2luZy1ob3Vycyc6XHJcbiAgICAgICAgY29uc3Qgc2hvd1dvcmtpbmdIb3VycyA9IHNjaGVkdWxpbmdQcmVmZXJlbmNlcy5zaG93V29ya2luZ0hvdXJzICE9PSBmYWxzZTtcclxuICAgICAgICByZXR1cm4gc2hvd1dvcmtpbmdIb3VycztcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH1cclxuICB9LCBbc2NoZWR1bGluZ1ByZWZlcmVuY2VzLCBwcmVmZXJlbmNlc0xvYWRpbmddKTtcclxuXHJcbiAgLy8gRnVuw6fDo28gcGFyYSBhbHRlcm5hciBhIGV4cGFuc8OjbyBkZSB1bSBncnVwb1xyXG4gIGNvbnN0IHRvZ2dsZUdyb3VwID0gKGdyb3VwSWQpID0+IHtcclxuICAgIHNldEV4cGFuZGVkR3JvdXBzKHByZXYgPT4gKHtcclxuICAgICAgLi4ucHJldixcclxuICAgICAgW2dyb3VwSWRdOiAhcHJldltncm91cElkXVxyXG4gICAgfSkpO1xyXG4gIH07XHJcblxyXG4gIC8vIFZlcmlmaWNhciBzZSBhbGd1bSBpdGVtIGRlbnRybyBkZSB1bSBncnVwbyBlc3TDoSBhdGl2b1xyXG4gIGNvbnN0IGlzR3JvdXBBY3RpdmUgPSB1c2VDYWxsYmFjaygobW9kdWxlSWQsIGdyb3VwSXRlbXMpID0+IHtcclxuICAgIHJldHVybiBncm91cEl0ZW1zLnNvbWUoaXRlbSA9PiBpc1N1Ym1lbnVBY3RpdmUobW9kdWxlSWQsIGl0ZW0uaWQpKTtcclxuICB9LCBbaXNTdWJtZW51QWN0aXZlXSk7XHJcblxyXG4gIC8vIEV4cGFuZGlyIGF1dG9tYXRpY2FtZW50ZSBncnVwb3MgcXVlIGNvbnTDqm0gbyBpdGVtIGF0aXZvXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChhY3RpdmVNb2R1bGUgJiYgbW9kdWxlU3VibWVudXNbYWN0aXZlTW9kdWxlXSkge1xyXG4gICAgICBjb25zdCBuZXdFeHBhbmRlZEdyb3VwcyA9IHsgLi4uZXhwYW5kZWRHcm91cHMgfTtcclxuXHJcbiAgICAgIG1vZHVsZVN1Ym1lbnVzW2FjdGl2ZU1vZHVsZV0uZm9yRWFjaChzdWJtZW51ID0+IHtcclxuICAgICAgICBpZiAoc3VibWVudS50eXBlID09PSAnZ3JvdXAnICYmIGlzR3JvdXBBY3RpdmUoYWN0aXZlTW9kdWxlLCBzdWJtZW51Lml0ZW1zKSkge1xyXG4gICAgICAgICAgbmV3RXhwYW5kZWRHcm91cHNbc3VibWVudS5pZF0gPSB0cnVlO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcblxyXG4gICAgICBzZXRFeHBhbmRlZEdyb3VwcyhuZXdFeHBhbmRlZEdyb3Vwcyk7XHJcbiAgICB9XHJcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXHJcbiAgfSwgW2FjdGl2ZU1vZHVsZSwgcGF0aG5hbWUsIGlzR3JvdXBBY3RpdmVdKTtcclxuXHJcbiAgLy8gVmVyaWZpY2FyIHNlIHVtIGl0ZW0gZGUgc3VibWVudSB0ZW0gcGVybWlzc8OjbyBwYXJhIHNlciBleGliaWRvXHJcbiAgY29uc3QgaGFzUGVybWlzc2lvbkZvclN1Ym1lbnVJdGVtID0gdXNlQ2FsbGJhY2soKG1vZHVsZUlkLCBzdWJtZW51SWQpID0+IHtcclxuICAgIHJldHVybiBoYXNQZXJtaXNzaW9uRm9yU3VibWVudShtb2R1bGVJZCwgc3VibWVudUlkKTtcclxuICB9LCBbaGFzUGVybWlzc2lvbkZvclN1Ym1lbnVdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxhc2lkZVxyXG4gICAgICBjbGFzc05hbWU9e2B3LTcyIGJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgYm9yZGVyLXIgZGFyazpib3JkZXItZ3JheS03MDAgaC1zY3JlZW4gc3RpY2t5IHRvcC0wIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmbGV4IGZsZXgtY29sICR7aXNTaWRlYmFyT3BlbiA/ICd0cmFuc2xhdGUteC0wJyA6ICctdHJhbnNsYXRlLXgtZnVsbCBsZzp0cmFuc2xhdGUteC0wJ1xyXG4gICAgICAgIH1gfVxyXG4gICAgICBhcmlhLWxhYmVsPVwiTmF2ZWdhw6fDo28gbGF0ZXJhbFwiXHJcbiAgICA+XHJcbiAgICAgIHsvKiBDb250ZcO6ZG8gcHJpbmNpcGFsIGRhIHNpZGViYXIgKi99XHJcbiAgICAgIDxDdXN0b21TY3JvbGxBcmVhIGNsYXNzTmFtZT1cImZsZXgtMSBwLTVcIiBtb2R1bGVDb2xvcj17YWN0aXZlTW9kdWxlfT5cclxuICAgICAgICB7LyogVMOtdHVsbyBkbyBtw7NkdWxvIGVzdGlsaXphZG8gKGxvZ28pICovfVxyXG4gICAgICAgIHthY3RpdmVNb2R1bGVPYmplY3QgJiYgKFxyXG4gICAgICAgICAgPE1vZHVsZVRpdGxlXHJcbiAgICAgICAgICAgIG1vZHVsZUlkPXthY3RpdmVNb2R1bGV9XHJcbiAgICAgICAgICAgIHRpdGxlPXthY3RpdmVNb2R1bGVUaXRsZX1cclxuICAgICAgICAgICAgaWNvbj17TW9kdWxlSWNvbn1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIFNlbGV0b3IgZGUgZW1wcmVzYSBwYXJhIFNZU1RFTV9BRE1JTiAqL31cclxuICAgICAgICA8Q29tcGFueVNlbGVjdG9yIGFjdGl2ZU1vZHVsZT17YWN0aXZlTW9kdWxlfSAvPlxyXG5cclxuICAgICAgICB7LyogTG9hZGluZyBzdGF0ZSBlbnF1YW50byBhcyBwcmVmZXLDqm5jaWFzIGNhcnJlZ2FtICovfVxyXG4gICAgICAgIHtwcmVmZXJlbmNlc0xvYWRpbmcgJiYgYWN0aXZlTW9kdWxlID09PSAnc2NoZWR1bGVyJyAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNiB3LTYgYm9yZGVyLWItMiBib3JkZXItcHJpbWFyeS01MDBcIj48L2Rpdj5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXNtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+Q2FycmVnYW5kby4uLjwvc3Bhbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBDZW50cmFsaXphciBlIGFmYXN0YXIgb3MgYm90w7VlcyBkYXMgcMOhZ2luYXMgKi99XHJcbiAgICAgICAgPG5hdlxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS0yIGZsZXggZmxleC1jb2wganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIG10LThcIlxyXG4gICAgICAgICAgYXJpYS1sYWJlbGxlZGJ5PVwic2lkZWJhci1oZWFkaW5nXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICB7LyogTsOjbyByZW5kZXJpemFyIHN1Ym1lbnVzIGVucXVhbnRvIGFzIHByZWZlcsOqbmNpYXMgY2FycmVnYW0gcGFyYSBvIG3Ds2R1bG8gc2NoZWR1bGVyICovfVxyXG4gICAgICAgICAgeyEocHJlZmVyZW5jZXNMb2FkaW5nICYmIGFjdGl2ZU1vZHVsZSA9PT0gJ3NjaGVkdWxlcicpICYmIGFjdGl2ZU1vZHVsZSAmJiBtb2R1bGVTdWJtZW51c1thY3RpdmVNb2R1bGVdPy5tYXAoKHN1Ym1lbnUpID0+IHtcclxuICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIMOpIHVtIGdydXBvIG91IHVtIGl0ZW0gaW5kaXZpZHVhbFxyXG4gICAgICAgICAgICBpZiAoc3VibWVudS50eXBlID09PSAnZ3JvdXAnKSB7XHJcbiAgICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGFsZ3VtIGl0ZW0gZG8gZ3J1cG8gdGVtIHBlcm1pc3PDo28gcGFyYSBzZXIgZXhpYmlkb1xyXG4gICAgICAgICAgICAgIGNvbnN0IGhhc0FueVBlcm1pc3Npb24gPSBzdWJtZW51Lml0ZW1zLnNvbWUoaXRlbSA9PlxyXG4gICAgICAgICAgICAgICAgaGFzUGVybWlzc2lvbkZvclN1Ym1lbnVJdGVtKGFjdGl2ZU1vZHVsZSwgaXRlbS5pZClcclxuICAgICAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgICAgICBpZiAoIWhhc0FueVBlcm1pc3Npb24pIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBudWxsOyAvLyBOw6NvIHJlbmRlcml6YXIgbyBncnVwbyBzZSBuZW5odW0gaXRlbSB0aXZlciBwZXJtaXNzw6NvXHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICBjb25zdCBpc0dyb3VwRXhwYW5kZWQgPSBleHBhbmRlZEdyb3Vwc1tzdWJtZW51LmlkXSB8fCBmYWxzZTtcclxuICAgICAgICAgICAgICBjb25zdCBpc0FueUl0ZW1BY3RpdmUgPSBpc0dyb3VwQWN0aXZlKGFjdGl2ZU1vZHVsZSwgc3VibWVudS5pdGVtcyk7XHJcblxyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17c3VibWVudS5pZH0gY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgIHsvKiBDYWJlw6dhbGhvIGRvIGdydXBvICovfVxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlR3JvdXAoc3VibWVudS5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgXHJcbiAgICAgICAgICAgICAgICAgICAgICBncm91cCB3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTQgcHktMi41IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAke2lzQW55SXRlbUFjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGB0ZXh0LW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taWNvbiBkYXJrOnRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uLWRhcmsgZm9udC1tZWRpdW1gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgYH1cclxuICAgICAgICAgICAgICAgICAgICBhcmlhLWV4cGFuZGVkPXtpc0dyb3VwRXhwYW5kZWR9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWxlZnRcIj57c3VibWVudS50aXRsZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2lzR3JvdXBFeHBhbmRlZCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIHNpemU9ezE4fSBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBzaXplPXsxOH0gYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIEl0ZW5zIGRvIGdydXBvIC0gdmlzw612ZWlzIGFwZW5hcyBxdWFuZG8gZXhwYW5kaWRvICovfVxyXG4gICAgICAgICAgICAgICAgICB7aXNHcm91cEV4cGFuZGVkICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTQgcGwtMiBib3JkZXItbCBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7c3VibWVudS5pdGVtcy5tYXAoaXRlbSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzSXRlbUFjdGl2ZSA9IGlzU3VibWVudUFjdGl2ZShhY3RpdmVNb2R1bGUsIGl0ZW0uaWQpO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgcGVybWlzc8OjbyBhbnRlcyBkZSByZW5kZXJpemFyIG8gaXRlbVxyXG4gICAgICAgICAgICAgIGlmICghaGFzUGVybWlzc2lvbkZvclN1Ym1lbnVJdGVtKGFjdGl2ZU1vZHVsZSwgaXRlbS5pZCkpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBudWxsOyAvLyBOw6NvIHJlbmRlcml6YXIgc2UgbsOjbyB0aXZlciBwZXJtaXNzw6NvXHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBpdGVtIGRldmUgc2VyIGV4aWJpZG8gYmFzZWFkbyBuYXMgcHJlZmVyw6puY2lhc1xyXG4gICAgICAgICAgICAgIGlmICghc2hvdWxkU2hvd1N1Ym1lbnVCeVByZWZlcmVuY2VzKGFjdGl2ZU1vZHVsZSwgaXRlbS5pZCkpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBudWxsOyAvLyBOw6NvIHJlbmRlcml6YXIgc2UgbsOjbyBlc3RpdmVyIGhhYmlsaXRhZG8gbmFzIHByZWZlcsOqbmNpYXNcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBpdGVtIGVzdMOhIGVtIGNvbnN0cnXDp8Ojb1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpdGVtS2V5ID0gYCR7YWN0aXZlTW9kdWxlfS4ke2l0ZW0uaWR9YDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNJdGVtVW5kZXJDb25zdHJ1Y3Rpb24gPSBpc1VuZGVyQ29uc3RydWN0aW9uKGFjdGl2ZU1vZHVsZSwgaXRlbS5pZCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbnN0cnVjdGlvbk1lc3NhZ2UgPSBnZXRDb25zdHJ1Y3Rpb25NZXNzYWdlKGFjdGl2ZU1vZHVsZSwgaXRlbS5pZCk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBFc3RpbG8gY29tdW0gcGFyYSBvcyBpdGVuc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpdGVtQ2xhc3NOYW1lID0gYFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGdyb3VwIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBweC0zIHB5LTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAke2lzSXRlbUFjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYm9yZGVyIGRhcms6Ym9yZGVyLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYm9yZGVyLWRhcmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJnLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYmcgZGFyazpiZy1ncmF5LTcwMCBkYXJrOmJnLW9wYWNpdHktOTBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNoYWRvdy1tZCBkYXJrOnNoYWRvdy1ibGFjay8yMGBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBgO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gU2UgZXN0aXZlciBlbSBjb25zdHJ1w6fDo28sIHVzYXIgbyBDb25zdHJ1Y3Rpb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzSXRlbVVuZGVyQ29uc3RydWN0aW9uKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb25zdHJ1Y3Rpb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2l0ZW1DbGFzc05hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtY3VycmVudD17aXNJdGVtQWN0aXZlID8gJ3BhZ2UnIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17Y29uc3RydWN0aW9uTWVzc2FnZS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudD17Y29uc3RydWN0aW9uTWVzc2FnZS5jb250ZW50fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uPXtjb25zdHJ1Y3Rpb25NZXNzYWdlLmljb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uPVwicmlnaHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5pY29uICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJHtpc0l0ZW1BY3RpdmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgdGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24gZGFyazp0ZXh0LW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taWNvbi1kYXJrYFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMCBncm91cC1ob3Zlcjp0ZXh0LW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taWNvbiBkYXJrOmdyb3VwLWhvdmVyOnRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uLWRhcmsgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwYFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGl0ZW0uaWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPXsxOH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1oaWRkZW49XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtIHRleHQtbGVmdCB0ZXh0LXNtICR7aXNJdGVtQWN0aXZlID8gJ2Rhcms6dGV4dC13aGl0ZScgOiAnJ31gfT57aXRlbS50aXRsZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NvbnN0cnVjdGlvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBTZSBuw6NvIGVzdGl2ZXIgZW0gY29uc3RydcOnw6NvLCB1c2FyIG8gYm90w6NvIG5vcm1hbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU1vZHVsZVN1Ym1lbnVDbGljayhhY3RpdmVNb2R1bGUsIGl0ZW0uaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtpdGVtQ2xhc3NOYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1jdXJyZW50PXtpc0l0ZW1BY3RpdmUgPyAncGFnZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uaWNvbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJHtpc0l0ZW1BY3RpdmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYHRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uIGRhcms6dGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24tZGFya2BcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogYHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uIGRhcms6Z3JvdXAtaG92ZXI6dGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24tZGFyayB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aXRlbS5pY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPXsxOH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgZm9udC1tZWRpdW0gdGV4dC1sZWZ0IHRleHQtc20gJHtpc0l0ZW1BY3RpdmUgPyAnZGFyazp0ZXh0LXdoaXRlJyA6ICcnfWB9PntpdGVtLnRpdGxlfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAvLyBSZW5kZXJpemHDp8OjbyBkZSBpdGVucyBpbmRpdmlkdWFpcyAobsOjbyBhZ3J1cGFkb3MpXHJcbiAgICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBpc1N1Ym1lbnVBY3RpdmUoYWN0aXZlTW9kdWxlLCBzdWJtZW51LmlkKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHBlcm1pc3PDo28gYW50ZXMgZGUgcmVuZGVyaXphciBvIGl0ZW1cclxuICAgICAgICAgICAgICBjb25zdCBoYXNQZXJtaXNzaW9uID0gaGFzUGVybWlzc2lvbkZvclN1Ym1lbnUoYWN0aXZlTW9kdWxlLCBzdWJtZW51LmlkKTtcclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICBpZiAoIWhhc1Blcm1pc3Npb24pIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBudWxsOyAvLyBOw6NvIHJlbmRlcml6YXIgc2UgbsOjbyB0aXZlciBwZXJtaXNzw6NvXHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBzdWJtZW51IGRldmUgc2VyIGV4aWJpZG8gYmFzZWFkbyBuYXMgcHJlZmVyw6puY2lhc1xyXG4gICAgICAgICAgICAgIGNvbnN0IHNob3VsZFNob3dCeVByZWZlcmVuY2VzID0gc2hvdWxkU2hvd1N1Ym1lbnVCeVByZWZlcmVuY2VzKGFjdGl2ZU1vZHVsZSwgc3VibWVudS5pZCk7XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgaWYgKCFzaG91bGRTaG93QnlQcmVmZXJlbmNlcykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7IC8vIE7Do28gcmVuZGVyaXphciBzZSBuw6NvIGVzdGl2ZXIgaGFiaWxpdGFkbyBuYXMgcHJlZmVyw6puY2lhc1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gc3VibWVudSBlc3TDoSBlbSBjb25zdHJ1w6fDo29cclxuICAgICAgICAgICAgICBjb25zdCBzdWJtZW51S2V5ID0gYCR7YWN0aXZlTW9kdWxlfS4ke3N1Ym1lbnUuaWR9YDtcclxuICAgICAgICAgICAgICBjb25zdCBpc1N1Ym1lbnVVbmRlckNvbnN0cnVjdGlvbiA9IGlzVW5kZXJDb25zdHJ1Y3Rpb24oYWN0aXZlTW9kdWxlLCBzdWJtZW51LmlkKTtcclxuICAgICAgICAgICAgICBjb25zdCBjb25zdHJ1Y3Rpb25NZXNzYWdlID0gZ2V0Q29uc3RydWN0aW9uTWVzc2FnZShhY3RpdmVNb2R1bGUsIHN1Ym1lbnUuaWQpO1xyXG5cclxuICAgICAgICAgICAgICAvLyBFc3RpbG8gY29tdW0gcGFyYSBhbWJvcyBvcyB0aXBvcyBkZSBib3TDtWVzXHJcbiAgICAgICAgICAgICAgY29uc3QgYnV0dG9uQ2xhc3NOYW1lID0gYFxyXG4gICAgICAgICAgICAgICAgZ3JvdXAgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHB4LTQgcHktMyByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFxyXG4gICAgICAgICAgICAgICAgJHtpc0FjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICA/IGByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1ib3JkZXIgZGFyazpib3JkZXItbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1ib3JkZXItZGFya1xyXG4gICAgICAgICAgICAgICAgICAgICBiZy1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWJnIGRhcms6YmctZ3JheS03MDAgZGFyazpiZy1vcGFjaXR5LTkwXHJcbiAgICAgICAgICAgICAgICAgICAgIHNoYWRvdy1tZCBkYXJrOnNoYWRvdy1ibGFjay8yMGBcclxuICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS0xMDAgZGFyazpob3ZlcjpiZy1ncmF5LTcwMCdcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBgO1xyXG5cclxuICAgICAgICAgICAgICAvLyBTZSBlc3RpdmVyIGVtIGNvbnN0cnXDp8OjbywgdXNhciBvIENvbnN0cnVjdGlvbkJ1dHRvblxyXG4gICAgICAgICAgICAgIGlmIChpc1N1Ym1lbnVVbmRlckNvbnN0cnVjdGlvbikge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgPENvbnN0cnVjdGlvbkJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIGtleT17c3VibWVudS5pZH1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2J1dHRvbkNsYXNzTmFtZX1cclxuICAgICAgICAgICAgICAgICAgICBhcmlhLWN1cnJlbnQ9e2lzQWN0aXZlID8gJ3BhZ2UnIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPXtjb25zdHJ1Y3Rpb25NZXNzYWdlLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ9e2NvbnN0cnVjdGlvbk1lc3NhZ2UuY29udGVudH1cclxuICAgICAgICAgICAgICAgICAgICBpY29uPXtjb25zdHJ1Y3Rpb25NZXNzYWdlLmljb259XHJcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb249XCJyaWdodFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7c3VibWVudS5pY29uICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgXHJcbiAgICAgICAgICAgICAgICAgICAgICAke2lzQWN0aXZlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgdGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24gZGFyazp0ZXh0LW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taWNvbi1kYXJrYFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogYHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uIGRhcms6Z3JvdXAtaG92ZXI6dGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24tZGFyayB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHN1Ym1lbnUuaWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9ezIwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtIHRleHQtbGVmdCAke2lzQWN0aXZlID8gJ2Rhcms6dGV4dC13aGl0ZScgOiAnJ31gfT57c3VibWVudS50aXRsZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ29uc3RydWN0aW9uQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC8vIFNlIG7Do28gZXN0aXZlciBlbSBjb25zdHJ1w6fDo28sIHVzYXIgbyBib3TDo28gbm9ybWFsXHJcbiAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAga2V5PXtzdWJtZW51LmlkfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVNb2R1bGVTdWJtZW51Q2xpY2soYWN0aXZlTW9kdWxlLCBzdWJtZW51LmlkKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtidXR0b25DbGFzc05hbWV9XHJcbiAgICAgICAgICAgICAgICAgIGFyaWEtY3VycmVudD17aXNBY3RpdmUgPyAncGFnZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHtzdWJtZW51Lmljb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgXHJcbiAgICAgICAgICAgICAgICAgICAgICAke2lzQWN0aXZlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gYHRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uIGRhcms6dGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24tZGFya2BcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgZ3JvdXAtaG92ZXI6dGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24gZGFyazpncm91cC1ob3Zlcjp0ZXh0LW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taWNvbi1kYXJrIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMGBcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBgfT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzdWJtZW51Lmljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSB0ZXh0LWxlZnQgJHtpc0FjdGl2ZSA/ICdkYXJrOnRleHQtd2hpdGUnIDogJyd9YH0+e3N1Ym1lbnUudGl0bGV9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSl9XHJcbiAgICAgICAgPC9uYXY+XHJcbiAgICAgIDwvQ3VzdG9tU2Nyb2xsQXJlYT5cclxuXHJcbiAgICAgIHsvKiBCb3TDo28gZGUgdm9sdGFyIGZpeG8gbmEgcGFydGUgaW5mZXJpb3IgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC01IGJvcmRlci10IGJvcmRlci1ncmF5LTEwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBtdC1hdXRvXCI+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlQmFja1RvTW9kdWxlc31cclxuICAgICAgICAgIGNsYXNzTmFtZT17YFxyXG4gICAgICAgICAgICBncm91cCB3LWZ1bGwgcHktMyBweC00IHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcclxuICAgICAgICAgICAgYm9yZGVyLTIgYm9yZGVyLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYm9yZGVyIGRhcms6Ym9yZGVyLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYm9yZGVyLWRhcmtcclxuICAgICAgICAgICAgYmctdHJhbnNwYXJlbnQgZGFyazpiZy1ncmF5LTgwMCBob3ZlcjpiZy1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWJnLzEwIGRhcms6aG92ZXI6YmctbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1iZy1kYXJrLzEwXHJcbiAgICAgICAgICAgIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFxyXG4gICAgICAgICAgYH1cclxuICAgICAgICAgIGFyaWEtbGFiZWw9XCJWb2x0YXIgcGFyYSBvIGRhc2hib2FyZCBwcmluY2lwYWxcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgXHJcbiAgICAgICAgICAgIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctOCBoLTggcm91bmRlZC1mdWxsXHJcbiAgICAgICAgICAgIGJnLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYmcgZGFyazpiZy1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWJnLWRhcmsvNzBcclxuICAgICAgICAgICAgdGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24gZGFyazp0ZXh0LW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taWNvbi1kYXJrXHJcbiAgICAgICAgICAgIHNoYWRvdy1zbSBkYXJrOnNoYWRvdy1tZCBkYXJrOnNoYWRvdy1ibGFjay8yMFxyXG4gICAgICAgICAgICBncm91cC1ob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXHJcbiAgICAgICAgICBgfT5cclxuICAgICAgICAgICAgPENoZXZyb25MZWZ0IHNpemU9ezIwfSBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIGRhcms6dGV4dC1ncmF5LTIwMFwiPlZvbHRhciBhIFRlbGEgSW5pY2lhbDwvc3Bhbj5cclxuICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2FzaWRlPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBTaWRlYmFyOyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUNhbGxiYWNrIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDaGV2cm9uTGVmdCIsIkNoZXZyb25Eb3duIiwiQ2hldnJvblJpZ2h0IiwiQ29uc3RydWN0aW9uIiwiSGFyZEhhdCIsIkhhbW1lciIsIldyZW5jaCIsIkFsZXJ0VHJpYW5nbGUiLCJ1c2VQYXRobmFtZSIsIm1vZHVsZXMiLCJtb2R1bGVTdWJtZW51cyIsInVzZVBlcm1pc3Npb25zIiwidXNlQXV0aCIsInVzZVNjaGVkdWxpbmdQcmVmZXJlbmNlcyIsIkN1c3RvbVNjcm9sbEFyZWEiLCJ1c2VDb25zdHJ1Y3Rpb25NZXNzYWdlIiwiQ29uc3RydWN0aW9uQnV0dG9uIiwidW5kZXJDb25zdHJ1Y3Rpb25TdWJtZW51cyIsImNvbnN0cnVjdGlvbk1lc3NhZ2VzIiwiaXNVbmRlckNvbnN0cnVjdGlvbiIsImdldENvbnN0cnVjdGlvbk1lc3NhZ2UiLCJDb21wYW55U2VsZWN0b3IiLCJNb2R1bGVUaXRsZSIsIm1vZHVsZUlkIiwidGl0bGUiLCJpY29uIiwiSWNvbiIsImhhbmRsZUxvZ29DbGljayIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzdHlsZSIsImJhY2tncm91bmQiLCJib3JkZXIiLCJwYWRkaW5nIiwiYXJpYS1sYWJlbCIsImltZyIsInNyYyIsImFsdCIsInN1Ym1lbnVQZXJtaXNzaW9uc01hcCIsIlNpZGViYXIiLCJhY3RpdmVNb2R1bGUiLCJhY3RpdmVNb2R1bGVUaXRsZSIsImlzU3VibWVudUFjdGl2ZSIsImhhbmRsZU1vZHVsZVN1Ym1lbnVDbGljayIsImhhbmRsZUJhY2tUb01vZHVsZXMiLCJpc1NpZGViYXJPcGVuIiwiY2FuIiwiaGFzTW9kdWxlIiwiaXNBZG1pbiIsInVzZXIiLCJwYXRobmFtZSIsInNjaGVkdWxpbmdQcmVmZXJlbmNlcyIsImlzTG9hZGluZyIsInByZWZlcmVuY2VzTG9hZGluZyIsImV4cGFuZGVkR3JvdXBzIiwic2V0RXhwYW5kZWRHcm91cHMiLCJzYXZlZFN0YXRlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImFjdGl2ZU1vZHVsZU9iamVjdCIsImZpbmQiLCJtIiwiaWQiLCJNb2R1bGVJY29uIiwiaGFzUGVybWlzc2lvbkZvclN1Ym1lbnUiLCJzdWJtZW51SWQiLCJyb2xlIiwicGVybWlzc2lvbktleSIsInJlcXVpcmVkUGVybWlzc2lvbiIsIkFycmF5IiwiaXNBcnJheSIsInNvbWUiLCJwZXJtIiwic2hvdWxkU2hvd1N1Ym1lbnVCeVByZWZlcmVuY2VzIiwic2hvd0xvY2F0aW9ucyIsInNob3dTZXJ2aWNlVHlwZXMiLCJzaG93SW5zdXJhbmNlIiwic2hvd1dvcmtpbmdIb3VycyIsInRvZ2dsZUdyb3VwIiwiZ3JvdXBJZCIsInByZXYiLCJpc0dyb3VwQWN0aXZlIiwiZ3JvdXBJdGVtcyIsIml0ZW0iLCJuZXdFeHBhbmRlZEdyb3VwcyIsImZvckVhY2giLCJzdWJtZW51IiwidHlwZSIsIml0ZW1zIiwiaGFzUGVybWlzc2lvbkZvclN1Ym1lbnVJdGVtIiwiYXNpZGUiLCJtb2R1bGVDb2xvciIsInNwYW4iLCJuYXYiLCJhcmlhLWxhYmVsbGVkYnkiLCJtYXAiLCJoYXNBbnlQZXJtaXNzaW9uIiwiaXNHcm91cEV4cGFuZGVkIiwiaXNBbnlJdGVtQWN0aXZlIiwiYXJpYS1leHBhbmRlZCIsInNpemUiLCJhcmlhLWhpZGRlbiIsImlzSXRlbUFjdGl2ZSIsIml0ZW1LZXkiLCJpc0l0ZW1VbmRlckNvbnN0cnVjdGlvbiIsImNvbnN0cnVjdGlvbk1lc3NhZ2UiLCJpdGVtQ2xhc3NOYW1lIiwiYXJpYS1jdXJyZW50IiwidW5kZWZpbmVkIiwiY29udGVudCIsInBvc2l0aW9uIiwiaXNBY3RpdmUiLCJoYXNQZXJtaXNzaW9uIiwic2hvdWxkU2hvd0J5UHJlZmVyZW5jZXMiLCJzdWJtZW51S2V5IiwiaXNTdWJtZW51VW5kZXJDb25zdHJ1Y3Rpb24iLCJidXR0b25DbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});