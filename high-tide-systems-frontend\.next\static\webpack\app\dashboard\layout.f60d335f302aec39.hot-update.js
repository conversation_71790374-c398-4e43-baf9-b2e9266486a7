"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/contexts/ChatContext.js":
/*!*************************************!*\
  !*** ./src/contexts/ChatContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n// Cache para evitar requisições duplicadas\nconst requestCache = {\n    conversations: {\n        data: null,\n        timestamp: 0\n    },\n    unreadCount: {\n        data: null,\n        timestamp: 0\n    },\n    messages: {},\n    tokenCheck: {\n        timestamp: 0,\n        valid: false\n    } // Cache para verificação de token\n};\n// Tempo de expiração do cache em milissegundos\nconst CACHE_EXPIRATION = 30000; // 30 segundos para dados normais\nconst TOKEN_CHECK_EXPIRATION = 60000; // 1 minuto para verificação de token\n// Função de debounce para limitar a frequência de chamadas\nconst debounce = (func, wait)=>{\n    let timeout;\n    return function executedFunction() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n};\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const { user, logout } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversation, setActiveConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Debug para activeConversation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            console.log('[ChatContext] activeConversation mudou para:', activeConversation);\n        }\n    }[\"ChatProvider.useEffect\"], [\n        activeConversation\n    ]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPanelOpen, setIsPanelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Obter o token atual do localStorage - memoizado para evitar chamadas desnecessárias\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"ChatProvider.useCallback[getCurrentToken]\"], []);\n    // Função para lidar com erros de autenticação\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[handleAuthError]\": ()=>{\n            console.error('Erro de autenticação detectado. Redirecionando para login...');\n            // Limpar dados locais\n            setConversations([]);\n            setMessages({});\n            setActiveConversation(null);\n            setUnreadCount(0);\n            setIsPanelOpen(false);\n            setIsModalOpen(false);\n            // Desconectar socket se existir\n            if (socket) {\n                try {\n                    socket.disconnect();\n                } catch (error) {\n                    console.error('Erro ao desconectar socket:', error);\n                }\n                setIsConnected(false);\n            }\n            // Limpar cache\n            requestCache.conversations.data = null;\n            requestCache.unreadCount.data = null;\n            requestCache.messages = {}; // Limpar cache de mensagens\n            // Redirecionar para login\n            if (logout) {\n                logout();\n            }\n        }\n    }[\"ChatProvider.useCallback[handleAuthError]\"], [\n        socket,\n        logout\n    ]);\n    // Usar referências para controlar estados que não devem causar re-renderizações\n    const socketInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isLoadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const lastInitAttemptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // Redefinir a referência quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário for null (logout), desconectar o socket\n            if (!user && socket) {\n                try {\n                    socket.disconnect();\n                    setIsConnected(false);\n                    setSocket(null);\n                } catch (error) {\n                    console.error('Erro ao desconectar socket após logout:', error);\n                }\n            }\n            // Resetar estado quando o usuário muda\n            socketInitializedRef.current = false;\n            lastInitAttemptRef.current = 0;\n            reconnectAttemptsRef.current = 0;\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        socket\n    ]);\n    // Limpar estado do chat quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário mudou (novo login), limpar todo o estado do chat\n            if (user) {\n                console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);\n                // Limpar conversas e mensagens para o novo usuário\n                setConversations([]);\n                setMessages({});\n                setActiveConversation(null);\n                setUnreadCount(0);\n                setIsPanelOpen(false);\n                setIsModalOpen(false);\n                // Limpar cache\n                requestCache.conversations.data = null;\n                requestCache.unreadCount.data = null;\n                requestCache.messages = {};\n                requestCache.tokenCheck.timestamp = 0;\n                requestCache.tokenCheck.valid = false;\n                console.log('Cache limpo para novo usuário');\n                // Limpar dados residuais do localStorage relacionados ao chat\n                if (true) {\n                    // Limpar qualquer cache específico do chat que possa estar no localStorage\n                    const keysToRemove = [];\n                    for(let i = 0; i < localStorage.length; i++){\n                        const key = localStorage.key(i);\n                        if (key && (key.startsWith('chat_') || key.startsWith('conversation_') || key.startsWith('message_'))) {\n                            keysToRemove.push(key);\n                        }\n                    }\n                    keysToRemove.forEach({\n                        \"ChatProvider.useEffect\": (key)=>localStorage.removeItem(key)\n                    }[\"ChatProvider.useEffect\"]);\n                    console.log('Dados residuais do chat removidos do localStorage');\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o ID do usuário mudar\n    // Inicializar conexão WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se não há usuário, não inicializar\n            if (!user) {\n                console.log('Usuário não logado, não inicializando WebSocket');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Inicializando chat para usuário:', user.role || 'USER');\n            // Se já existe um socket conectado, não fazer nada\n            if (socket && isConnected) {\n                console.log('WebSocket já está conectado, não é necessário reinicializar');\n                return;\n            }\n            // Se já está inicializado e a última tentativa foi recente, não tentar novamente\n            if (socketInitializedRef.current && Date.now() - lastInitAttemptRef.current < 60000) {\n                console.log('Socket já inicializado recentemente, aguardando...');\n                return;\n            }\n            // Marcar como inicializado imediatamente para evitar múltiplas tentativas\n            socketInitializedRef.current = true;\n            lastInitAttemptRef.current = Date.now();\n            // Função assíncrona para inicializar o WebSocket\n            const initializeWebSocket = {\n                \"ChatProvider.useEffect.initializeWebSocket\": async ()=>{\n                    // Verificar se o usuário está logado antes de inicializar o WebSocket\n                    if (!user) return;\n                    // Clientes agora podem usar o chat\n                    console.log('Inicializando WebSocket para:', user.role || 'USER');\n                    const currentToken = getCurrentToken();\n                    if (!currentToken) return;\n                    // Evitar reconexões desnecessárias - verificação adicional\n                    if (socket) {\n                        if (isConnected) {\n                            console.log('WebSocket já conectado, ignorando inicialização');\n                            return socket; // Retornar o socket existente\n                        } else {\n                            // Se o socket existe mas não está conectado, tentar reconectar em vez de criar um novo\n                            console.log('Socket existe mas não está conectado, tentando reconectar');\n                            try {\n                                // Tentar reconectar o socket existente em vez de criar um novo\n                                if (!socket.connected && socket.connect) {\n                                    socket.connect();\n                                    console.log('Tentativa de reconexão iniciada');\n                                    return socket;\n                                }\n                            } catch (error) {\n                                console.error('Erro ao reconectar socket existente:', error);\n                                // Só desconectar se a reconexão falhar\n                                try {\n                                    socket.disconnect();\n                                } catch (disconnectError) {\n                                    console.error('Erro ao desconectar socket após falha na reconexão:', disconnectError);\n                                }\n                            }\n                        }\n                    }\n                    // Marcar como inicializado para evitar múltiplas inicializações\n                    socketInitializedRef.current = true;\n                    // Limitar a frequência de reconexões\n                    const maxReconnectAttempts = 5;\n                    const reconnectDelay = 3000; // 3 segundos\n                    console.log('Inicializando WebSocket...');\n                    try {\n                        // Verificar se o token é válido antes de inicializar o WebSocket\n                        // Usar uma verificação mais simples para evitar múltiplas requisições\n                        // Assumir que o token é válido se o usuário está logado\n                        console.log('Token válido, inicializando WebSocket para o usuário:', user.fullName);\n                        console.log('Inicializando WebSocket com autenticação');\n                        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(API_URL, {\n                            path: '/socket.io',\n                            auth: {\n                                token: currentToken\n                            },\n                            transports: [\n                                'websocket',\n                                'polling'\n                            ],\n                            reconnectionAttempts: maxReconnectAttempts,\n                            reconnectionDelay: reconnectDelay,\n                            timeout: 10000,\n                            autoConnect: true,\n                            reconnection: true\n                        });\n                        socketInstance.on('connect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket connected');\n                                setIsConnected(true);\n                                reconnectAttemptsRef.current = 0;\n                                // Disparar evento personalizado para notificar componentes sobre a conexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'connected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('disconnect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket disconnected');\n                                setIsConnected(false);\n                                // Disparar evento personalizado para notificar componentes sobre a desconexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'disconnected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('connect_error', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (error)=>{\n                                console.error('WebSocket connection error:', error);\n                                reconnectAttemptsRef.current++;\n                                // Se o erro for de autenticação, não tentar reconectar\n                                if (error.message && error.message.includes('Authentication error')) {\n                                    console.error('Erro de autenticação no WebSocket, não tentando reconectar');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                    return;\n                                }\n                                if (reconnectAttemptsRef.current >= maxReconnectAttempts) {\n                                    console.error('Máximo de tentativas de reconexão atingido');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para receber novas mensagens\n                        socketInstance.on('message:new', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (message)=>{\n                                if (!message || !message.conversationId) {\n                                    console.error('Mensagem inválida recebida:', message);\n                                    return;\n                                }\n                                // Usar uma função anônima para evitar dependências circulares\n                                const userId = user === null || user === void 0 ? void 0 : user.id;\n                                // Verificar se a mensagem tem um tempId associado (para substituir mensagem temporária)\n                                // O backend pode não retornar o tempId, então precisamos verificar se é uma mensagem do usuário atual\n                                const tempId = message.tempId;\n                                const isCurrentUserMessage = message.senderId === userId;\n                                // Atualizar mensagens da conversa\n                                setMessages({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        const conversationMessages = prev[message.conversationId] || [];\n                                        // Verificar se a mensagem já existe para evitar duplicação\n                                        if (conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === message.id\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return prev;\n                                        }\n                                        // Se tiver um tempId, substituir a mensagem temporária pela mensagem real\n                                        if (tempId && conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return {\n                                                ...prev,\n                                                [message.conversationId]: conversationMessages.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId ? message : m\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                            };\n                                        }\n                                        // Se for uma mensagem do usuário atual, verificar se há mensagens temporárias recentes\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId) {\n                                            // Procurar por mensagens temporárias recentes (nos últimos 10 segundos)\n                                            const now = new Date();\n                                            const tempMessages = conversationMessages.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket.tempMessages\": (m)=>{\n                                                    if (!m.isTemp) return false;\n                                                    if (!(m.senderId === userId || m.senderClientId === userId)) return false;\n                                                    if (now - new Date(m.createdAt) >= 10000) return false; // 10 segundos\n                                                    // Para mensagens com anexos, verificar se ambas são ATTACHMENT\n                                                    if (message.contentType === 'ATTACHMENT' && m.contentType === 'ATTACHMENT') {\n                                                        return true;\n                                                    }\n                                                    // Para mensagens de texto, verificar se o conteúdo é igual\n                                                    if (message.contentType === 'TEXT' && m.contentType === 'TEXT' && m.content === message.content) {\n                                                        return true;\n                                                    }\n                                                    return false;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket.tempMessages\"]);\n                                            if (tempMessages.length > 0) {\n                                                // Substituir a primeira mensagem temporária encontrada\n                                                const tempMessage = tempMessages[0];\n                                                return {\n                                                    ...prev,\n                                                    [message.conversationId]: conversationMessages.map({\n                                                        \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempMessage.id ? message : m\n                                                    }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                };\n                                            }\n                                        }\n                                        // Caso contrário, adicionar a nova mensagem\n                                        // Manter a ordem cronológica (mais antigas primeiro)\n                                        const updatedMessages = [\n                                            ...conversationMessages,\n                                            message\n                                        ].sort({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedMessages\"]);\n                                        return {\n                                            ...prev,\n                                            [message.conversationId]: updatedMessages\n                                        };\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Atualizar lista de conversas (mover para o topo)\n                                setConversations({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        // Verificar se a conversa existe\n                                        const conversation = prev.find({\n                                            \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                        if (!conversation) return prev;\n                                        // Se a conversa já tiver uma mensagem temporária com o mesmo tempId, não mover para o topo\n                                        if (tempId && conversation.lastMessage && conversation.lastMessage.id === tempId) {\n                                            // Apenas atualizar a última mensagem sem reordenar\n                                            return prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                    if (c.id === message.conversationId) {\n                                                        return {\n                                                            ...c,\n                                                            lastMessage: message\n                                                        };\n                                                    }\n                                                    return c;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        }\n                                        // Se for uma mensagem do usuário atual e não tiver tempId, verificar se há uma mensagem temporária recente\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId && conversation.lastMessage && conversation.lastMessage.isTemp) {\n                                            // Verificar se a última mensagem é temporária e tem o mesmo conteúdo\n                                            if (conversation.lastMessage.content === message.content && (conversation.lastMessage.senderId === userId || conversation.lastMessage.senderClientId === userId)) {\n                                                // Apenas atualizar a última mensagem sem reordenar\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                        if (c.id === message.conversationId) {\n                                                            return {\n                                                                ...c,\n                                                                lastMessage: message\n                                                            };\n                                                        }\n                                                        return c;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }\n                                        // Caso contrário, mover para o topo\n                                        const updatedConversations = prev.filter({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedConversations\": (c)=>c.id !== message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedConversations\"]);\n                                        return [\n                                            {\n                                                ...conversation,\n                                                lastMessage: message\n                                            },\n                                            ...updatedConversations\n                                        ];\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Incrementar contador de não lidas se a mensagem não for do usuário atual\n                                const isFromCurrentUser = message.senderId === userId || message.senderClientId === userId;\n                                if (!isFromCurrentUser) {\n                                    console.log('[WebSocket] Nova mensagem de outro usuário, incrementando contador');\n                                    setUnreadCount({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            const newCount = prev + 1;\n                                            console.log('[WebSocket] Contador atualizado de', prev, 'para', newCount);\n                                            return newCount;\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Atualizar contador da conversa específica\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                    if (conv.id === message.conversationId) {\n                                                        const newUnreadCount = (conv.unreadCount || 0) + 1;\n                                                        console.log(\"[WebSocket] Conversa \".concat(conv.id, \" contador: \").concat(conv.unreadCount || 0, \" -> \").concat(newUnreadCount));\n                                                        return {\n                                                            ...conv,\n                                                            unreadCount: newUnreadCount\n                                                        };\n                                                    }\n                                                    return conv;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                } else {\n                                    console.log('[WebSocket] Mensagem do próprio usuário, não incrementando contador');\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a nova mensagem\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'message',\n                                        conversationId: message.conversationId,\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de contagem de mensagens não lidas\n                        socketInstance.on('unread:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('[WebSocket] Atualização de mensagens não lidas recebida:', data);\n                                if (data && typeof data.totalUnread === 'number') {\n                                    console.log(\"[WebSocket] Atualizando contador total para: \".concat(data.totalUnread));\n                                    setUnreadCount(data.totalUnread);\n                                    // Atualizar as conversas com as contagens de não lidas\n                                    if (data.conversations && Array.isArray(data.conversations)) {\n                                        console.log(\"[WebSocket] Atualizando \".concat(data.conversations.length, \" conversas com contadores\"));\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        // Procurar esta conversa nos dados de não lidas\n                                                        const unreadInfo = data.conversations.find({\n                                                            \"ChatProvider.useEffect.initializeWebSocket.unreadInfo\": (item)=>item.conversationId === conv.id\n                                                        }[\"ChatProvider.useEffect.initializeWebSocket.unreadInfo\"]);\n                                                        if (unreadInfo) {\n                                                            console.log(\"[WebSocket] Conversa \".concat(conv.id, \" agora tem \").concat(unreadInfo.unreadCount, \" mensagens n\\xe3o lidas\"));\n                                                            return {\n                                                                ...conv,\n                                                                unreadCount: unreadInfo.unreadCount\n                                                            };\n                                                        }\n                                                        // Resetar contador se não estiver na lista de não lidas\n                                                        return {\n                                                            ...conv,\n                                                            unreadCount: 0\n                                                        };\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a atualização de não lidas\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'unread',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de lista de conversas\n                        socketInstance.on('conversations:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Atualização de lista de conversas recebida via WebSocket:', data);\n                                // Verificar se os dados estão no formato esperado\n                                if (data) {\n                                    // Pode vir como array direto ou como objeto com propriedade conversations\n                                    const conversationsArray = Array.isArray(data) ? data : data.conversations || [];\n                                    if (Array.isArray(conversationsArray) && conversationsArray.length > 0) {\n                                        console.log(\"Atualizando \".concat(conversationsArray.length, \" conversas via WebSocket\"));\n                                        setConversations(conversationsArray);\n                                        // Atualizar a flag para indicar que os dados foram carregados\n                                        initialDataLoadedRef.current = true;\n                                        // Atualizar o cache\n                                        requestCache.conversations.data = conversationsArray;\n                                        requestCache.conversations.timestamp = Date.now();\n                                        // Disparar evento personalizado para notificar componentes sobre a atualização\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    } else {\n                                        console.error('Formato inválido ou array vazio de conversas recebido via WebSocket:', data);\n                                        // Se recebemos um array vazio, forçar carregamento das conversas\n                                        if (Array.isArray(conversationsArray) && conversationsArray.length === 0) {\n                                            console.log('Array vazio recebido, forçando carregamento de conversas...');\n                                            loadConversations(true);\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para notificar que uma nova conversa foi criada\n                        socketInstance.on('conversation:created', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Nova conversa criada recebida via WebSocket:', data);\n                                if (data && data.id) {\n                                    // Adicionar a nova conversa ao início da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            // Verificar se a conversa já existe\n                                            if (prev.some({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id === data.id\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                                return prev;\n                                            }\n                                            return [\n                                                data,\n                                                ...prev\n                                            ];\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando um participante é removido\n                        socketInstance.on('participant:removed', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Participante removido recebido via WebSocket:', data);\n                                if (data && data.conversationId && data.participantId) {\n                                    // Se o usuário atual foi removido, remover a conversa da lista\n                                    if (data.participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        // Se era a conversa ativa, limpar\n                                        if (activeConversation === data.conversationId) {\n                                            setActiveConversation(null);\n                                        }\n                                        // Limpar mensagens da conversa\n                                        setMessages({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const updated = {\n                                                    ...prev\n                                                };\n                                                delete updated[data.conversationId];\n                                                return updated;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    } else {\n                                        // Atualizar a lista de participantes da conversa\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        if (conv.id === data.conversationId) {\n                                                            return {\n                                                                ...conv,\n                                                                participants: conv.participants.filter({\n                                                                    \"ChatProvider.useEffect.initializeWebSocket\": (p)=>p.userId !== data.participantId && p.clientId !== data.participantId\n                                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                            };\n                                                        }\n                                                        return conv;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando o usuário sai de uma conversa\n                        socketInstance.on('conversation:left', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Usuário saiu da conversa via WebSocket:', data);\n                                if (data && data.conversationId && data.userId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                    // Remover a conversa da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Se era a conversa ativa, limpar\n                                    if (activeConversation === data.conversationId) {\n                                        setActiveConversation(null);\n                                    }\n                                    // Limpar mensagens da conversa\n                                    setMessages({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            const updated = {\n                                                ...prev\n                                            };\n                                            delete updated[data.conversationId];\n                                            return updated;\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Disparar evento para atualizar a interface\n                                    window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                        detail: {\n                                            type: 'conversations',\n                                            action: 'left',\n                                            conversationId: data.conversationId,\n                                            timestamp: Date.now()\n                                        }\n                                    }));\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        setSocket(socketInstance);\n                        return socketInstance;\n                    } catch (error) {\n                        console.error('Erro ao inicializar WebSocket:', error);\n                        setIsConnected(false);\n                        socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                        return null;\n                    }\n                }\n            }[\"ChatProvider.useEffect.initializeWebSocket\"];\n            // Usar uma variável para controlar se já estamos tentando inicializar\n            let initializationInProgress = false;\n            // Função para inicializar com segurança\n            const safeInitialize = {\n                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                    if (initializationInProgress) {\n                        console.log('Já existe uma inicialização em andamento, ignorando');\n                        return;\n                    }\n                    initializationInProgress = true;\n                    // Usar um timeout para garantir que não tentamos inicializar muito frequentemente\n                    setTimeout({\n                        \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                            initializeWebSocket().then({\n                                \"ChatProvider.useEffect.safeInitialize\": (result)=>{\n                                    console.log('Inicialização do WebSocket concluída:', result ? 'sucesso' : 'falha');\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).catch({\n                                \"ChatProvider.useEffect.safeInitialize\": (error)=>{\n                                    console.error('Erro ao inicializar WebSocket:', error);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).finally({\n                                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                                    initializationInProgress = false;\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]);\n                        }\n                    }[\"ChatProvider.useEffect.safeInitialize\"], 2000); // Esperar 2 segundos antes de inicializar\n                }\n            }[\"ChatProvider.useEffect.safeInitialize\"];\n            // Chamar a função de inicialização\n            safeInitialize();\n            // Cleanup function - só desconectar quando o componente for desmontado completamente\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                    // Verificar se estamos realmente desmontando o componente (usuário fez logout)\n                    if (!user) {\n                        console.log('Usuário fez logout, desconectando socket');\n                        if (socket) {\n                            try {\n                                socket.disconnect();\n                                socketInitializedRef.current = false; // Permitir nova tentativa após cleanup\n                            } catch (error) {\n                                console.error('Erro ao desconectar socket:', error);\n                            }\n                        }\n                    } else {\n                        console.log('Componente sendo remontado, mantendo socket conectado');\n                    }\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo socket e isConnected das dependências para evitar loops\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Carregar conversas do usuário com cache\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n            console.log('loadConversations chamado com forceRefresh =', forceRefresh);\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar conversas');\n                    return [];\n                }\n                // Clientes agora podem carregar conversas\n                console.log('Carregando conversas para:', user.role || 'USER');\n                // Verificar se já está carregando\n                if (isLoading || isLoadingRef.current) {\n                    console.log('Já está carregando conversas, retornando estado atual');\n                    return conversations;\n                }\n                // Verificar se já temos conversas carregadas e não é uma atualização forçada\n                if (!forceRefresh && conversations.length > 0) {\n                    console.log('Já temos conversas carregadas e não é uma atualização forçada, retornando estado atual');\n                    return conversations;\n                }\n                // Marcar como carregando para evitar chamadas simultâneas\n                isLoadingRef.current = true;\n                // Verificar cache apenas se não for refresh forçado\n                const now = Date.now();\n                if (!forceRefresh && requestCache.conversations.data && requestCache.conversations.data.length > 0 && now - requestCache.conversations.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para conversas');\n                    return requestCache.conversations.data;\n                }\n                console.log('Buscando conversas atualizadas da API');\n                const currentToken = getCurrentToken();\n                console.log('Token ao carregar conversas:', currentToken ? 'Disponível' : 'Não disponível');\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar conversas');\n                    return [];\n                }\n                setIsLoading(true);\n                try {\n                    console.log('Buscando conversas da API...');\n                    console.log('Fazendo requisição para:', \"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"));\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    console.log('Status da resposta:', response.status, response.statusText);\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar conversas');\n                            handleAuthError();\n                            return [];\n                        }\n                        throw new Error(\"Erro ao carregar conversas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de conversas:', data);\n                    if (data.success) {\n                        var _data_data;\n                        // Verificar se há dados válidos\n                        // A API retorna { success: true, data: { conversations: [...], total, limit, offset } }\n                        console.log('Estrutura completa da resposta da API:', JSON.stringify(data));\n                        const conversationsArray = ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.conversations) || [];\n                        console.log('Array de conversas extraído:', conversationsArray);\n                        if (!Array.isArray(conversationsArray)) {\n                            console.error('Resposta da API não contém um array de conversas:', data);\n                            return [];\n                        }\n                        console.log(\"Recebidas \".concat(conversationsArray.length, \" conversas da API\"));\n                        // Processar as últimas mensagens das conversas\n                        console.log('Processando últimas mensagens das conversas...');\n                        const processedConversations = conversationsArray.map({\n                            \"ChatProvider.useCallback[loadConversations].processedConversations\": (conversation)=>{\n                                // Verificar se a conversa tem mensagens\n                                if (conversation.messages && conversation.messages.length > 0) {\n                                    console.log(\"Conversa \".concat(conversation.id, \" tem \").concat(conversation.messages.length, \" mensagens\"));\n                                    // Extrair a última mensagem\n                                    const lastMessage = conversation.messages[0]; // A primeira mensagem é a mais recente (ordenada por createdId desc)\n                                    console.log('Ultima mensagem:', lastMessage);\n                                    // IMPORTANTE: Também salvar todas as mensagens desta conversa no estado\n                                    const sortedMessages = [\n                                        ...conversation.messages\n                                    ].sort({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\"]);\n                                    console.log(\"DEBUG: Salvando \".concat(sortedMessages.length, \" mensagens da conversa \").concat(conversation.id), sortedMessages);\n                                    // Atualizar estado das mensagens\n                                    setMessages({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations\": (prev)=>({\n                                                ...prev,\n                                                [conversation.id]: sortedMessages\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                                    // Remover o array de mensagens para evitar duplicação\n                                    const { messages, ...conversationWithoutMessages } = conversation;\n                                    // Adicionar a última mensagem como propriedade lastMessage\n                                    return {\n                                        ...conversationWithoutMessages,\n                                        lastMessage\n                                    };\n                                } else {\n                                    console.log(\"Conversa \".concat(conversation.id, \" n\\xe3o tem mensagens\"));\n                                }\n                                return conversation;\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                        // Log para debug dos dados das conversas\n                        console.log('Conversas processadas:', processedConversations.map({\n                            \"ChatProvider.useCallback[loadConversations]\": (c)=>{\n                                var _c_participants;\n                                return {\n                                    id: c.id,\n                                    participants: (_c_participants = c.participants) === null || _c_participants === void 0 ? void 0 : _c_participants.map({\n                                        \"ChatProvider.useCallback[loadConversations]\": (p)=>({\n                                                userId: p.userId,\n                                                clientId: p.clientId,\n                                                user: p.user,\n                                                client: p.client\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations]\"]));\n                        // Atualizar cache com as conversas processadas\n                        const now = Date.now();\n                        requestCache.conversations.data = processedConversations;\n                        requestCache.conversations.timestamp = now;\n                        // Atualizar estado - garantir que estamos atualizando o estado mesmo se o array estiver vazio\n                        setConversations(processedConversations);\n                        // Disparar evento para notificar componentes sobre a atualização\n                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                            detail: {\n                                type: 'conversations',\n                                timestamp: Date.now()\n                            }\n                        }));\n                        return conversationsArray;\n                    } else {\n                        console.error('Resposta da API não foi bem-sucedida:', data);\n                        return [];\n                    }\n                } catch (error) {\n                    console.error('Error loading conversations:', error);\n                    return [];\n                } finally{\n                    setIsLoading(false);\n                    isLoadingRef.current = false;\n                }\n            } catch (error) {\n                console.error('Error in loadConversations:', error);\n                return [];\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadConversations]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError,\n        isLoading\n    ]);\n    // Carregar mensagens de uma conversa com cache aprimorado\n    const loadMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadMessages]\": async (conversationId)=>{\n            try {\n                var _requestCache_messages_conversationId;\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar mensagens');\n                    return;\n                }\n                // Verificar cache de mensagens\n                const now = Date.now();\n                if (requestCache.messages[conversationId] && now - requestCache.messages[conversationId].timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para mensagens da conversa:', conversationId);\n                    return requestCache.messages[conversationId].data;\n                }\n                // Verificar se já tem mensagens completas carregadas no cache\n                // Não usar o estado messages aqui pois pode conter apenas a última mensagem do loadConversations\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar mensagens');\n                    return;\n                }\n                // Evitar múltiplas requisições simultâneas para a mesma conversa\n                if ((_requestCache_messages_conversationId = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId === void 0 ? void 0 : _requestCache_messages_conversationId.loading) {\n                    console.log('Já existe uma requisição em andamento para esta conversa');\n                    return;\n                }\n                // Marcar como carregando\n                requestCache.messages[conversationId] = {\n                    loading: true\n                };\n                try {\n                    console.log('Buscando mensagens da API para conversa:', conversationId);\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/messages\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar mensagens');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar mensagens: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    if (data.success) {\n                        // Debug para verificar mensagens carregadas\n                        console.log('DEBUG: Mensagens carregadas para conversa', conversationId, data.data);\n                        // Atualizar cache\n                        requestCache.messages[conversationId] = {\n                            data: data.data,\n                            timestamp: now,\n                            loading: false\n                        };\n                        // Atualizar estado das mensagens\n                        // Garantir que as mensagens estejam na ordem correta (mais antigas primeiro)\n                        // para que a ordenação no componente funcione corretamente\n                        const sortedMessages = [\n                            ...data.data\n                        ].sort({\n                            \"ChatProvider.useCallback[loadMessages].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                        }[\"ChatProvider.useCallback[loadMessages].sortedMessages\"]);\n                        console.log('DEBUG: Mensagens ordenadas', sortedMessages);\n                        setMessages({\n                            \"ChatProvider.useCallback[loadMessages]\": (prev)=>({\n                                    ...prev,\n                                    [conversationId]: sortedMessages\n                                })\n                        }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        // Atualizar a última mensagem da conversa\n                        if (data.data && data.data.length > 0) {\n                            const lastMessage = data.data[data.data.length - 1];\n                            setConversations({\n                                \"ChatProvider.useCallback[loadMessages]\": (prev)=>{\n                                    return prev.map({\n                                        \"ChatProvider.useCallback[loadMessages]\": (conv)=>{\n                                            if (conv.id === conversationId && (!conv.lastMessage || new Date(lastMessage.createdAt) > new Date(conv.lastMessage.createdAt))) {\n                                                return {\n                                                    ...conv,\n                                                    lastMessage\n                                                };\n                                            }\n                                            return conv;\n                                        }\n                                    }[\"ChatProvider.useCallback[loadMessages]\"]);\n                                }\n                            }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        }\n                        return data.data;\n                    }\n                } catch (error) {\n                    console.error('Error loading messages:', error);\n                } finally{\n                    var _requestCache_messages_conversationId1;\n                    // Remover flag de carregamento em caso de erro\n                    if ((_requestCache_messages_conversationId1 = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId1 === void 0 ? void 0 : _requestCache_messages_conversationId1.loading) {\n                        requestCache.messages[conversationId].loading = false;\n                    }\n                }\n            } catch (error) {\n                console.error('Error in loadMessages:', error);\n            }\n        // Removendo messages das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadMessages]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Enviar mensagem\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[sendMessage]\": function(conversationId, content) {\n            let contentType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'TEXT', metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            if (!socket || !isConnected) return;\n            // Gerar um ID temporário para a mensagem\n            const tempId = \"temp-\".concat(Date.now());\n            // Criar uma mensagem temporária para exibir imediatamente\n            const tempMessage = {\n                id: tempId,\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                senderId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : user === null || user === void 0 ? void 0 : user.id,\n                senderClientId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? user === null || user === void 0 ? void 0 : user.id : null,\n                createdAt: new Date().toISOString(),\n                sender: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: user === null || user === void 0 ? void 0 : user.fullName\n                },\n                senderClient: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.login),\n                    login: user === null || user === void 0 ? void 0 : user.login\n                } : null,\n                // Marcar como temporária para evitar duplicação\n                isTemp: true\n            };\n            // Atualizar mensagens localmente antes de enviar\n            setMessages({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>({\n                        ...prev,\n                        [conversationId]: [\n                            ...prev[conversationId] || [],\n                            tempMessage\n                        ]\n                    })\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Atualizar a conversa com a última mensagem\n            setConversations({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                    return prev.map({\n                        \"ChatProvider.useCallback[sendMessage]\": (conv)=>{\n                            if (conv.id === conversationId) {\n                                return {\n                                    ...conv,\n                                    lastMessage: tempMessage\n                                };\n                            }\n                            return conv;\n                        }\n                    }[\"ChatProvider.useCallback[sendMessage]\"]);\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Enviar a mensagem via WebSocket\n            const messageData = {\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                tempId\n            };\n            socket.emit('message:send', messageData, {\n                \"ChatProvider.useCallback[sendMessage]\": (response)=>{\n                    if (response.success) {\n                    // A mensagem real será adicionada pelo evento message:new do WebSocket\n                    // Não precisamos fazer nada aqui, pois o WebSocket vai atualizar a mensagem\n                    } else {\n                        console.error('Error sending message:', response.error);\n                        // Em caso de erro, podemos marcar a mensagem como falha\n                        setMessages({\n                            \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                                const conversationMessages = prev[conversationId] || [];\n                                return {\n                                    ...prev,\n                                    [conversationId]: conversationMessages.map({\n                                        \"ChatProvider.useCallback[sendMessage]\": (msg)=>msg.id === tempId ? {\n                                                ...msg,\n                                                failed: true\n                                            } : msg\n                                    }[\"ChatProvider.useCallback[sendMessage]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[sendMessage]\"]);\n                    }\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n        }\n    }[\"ChatProvider.useCallback[sendMessage]\"], [\n        socket,\n        isConnected,\n        user\n    ]);\n    // Criar nova conversa\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createConversation]\": async function(participantIds) {\n            let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n            try {\n                console.log('Criando conversa com participantes:', participantIds);\n                console.log('Tipo de conversa:', participantIds.length > 1 ? 'GRUPO' : 'INDIVIDUAL');\n                console.log('Título da conversa:', title);\n                // ✅ VALIDAÇÃO: System admin não pode criar grupos com usuários de empresas diferentes\n                if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && participantIds.length > 1) {\n                    console.log('Validando empresas para system admin...');\n                    // Buscar informações dos participantes para validar empresas\n                    const currentToken = getCurrentToken();\n                    if (currentToken) {\n                        try {\n                            const participantPromises = participantIds.map({\n                                \"ChatProvider.useCallback[createConversation].participantPromises\": async (id)=>{\n                                    const response = await fetch(\"\".concat(API_URL, \"/users/\").concat(id), {\n                                        headers: {\n                                            Authorization: \"Bearer \".concat(currentToken)\n                                        }\n                                    });\n                                    if (response.ok) {\n                                        const userData = await response.json();\n                                        return userData.success ? userData.data : userData;\n                                    }\n                                    return null;\n                                }\n                            }[\"ChatProvider.useCallback[createConversation].participantPromises\"]);\n                            const participants = await Promise.all(participantPromises);\n                            console.log('Participantes carregados:', participants);\n                            const validParticipants = participants.filter({\n                                \"ChatProvider.useCallback[createConversation].validParticipants\": (p)=>p && p.companyId\n                            }[\"ChatProvider.useCallback[createConversation].validParticipants\"]);\n                            console.log('Participantes válidos:', validParticipants);\n                            if (validParticipants.length > 1) {\n                                const companies = [\n                                    ...new Set(validParticipants.map({\n                                        \"ChatProvider.useCallback[createConversation]\": (p)=>p.companyId\n                                    }[\"ChatProvider.useCallback[createConversation]\"]))\n                                ];\n                                console.log('Empresas encontradas:', companies);\n                                if (companies.length > 1) {\n                                    alert('Não é possível criar grupos com usuários de empresas diferentes.');\n                                    return null;\n                                }\n                            }\n                        } catch (validationError) {\n                            console.error('Erro na validação de empresas:', validationError);\n                            alert('Erro na validação: ' + validationError.message);\n                            return null;\n                        }\n                    }\n                }\n                // Verificar se algum participante é cliente\n                const hasClientParticipants = participantIds.some({\n                    \"ChatProvider.useCallback[createConversation].hasClientParticipants\": (id)=>{\n                        // Verificar se o ID corresponde a um cliente (assumindo que clientes têm isClient: true)\n                        return typeof id === 'object' && id.isClient;\n                    }\n                }[\"ChatProvider.useCallback[createConversation].hasClientParticipants\"]);\n                console.log('Tem participantes clientes:', hasClientParticipants);\n                // Obter o token mais recente do localStorage\n                const currentToken = getCurrentToken();\n                console.log('Token disponível:', currentToken ? 'Sim' : 'Não');\n                if (!currentToken) {\n                    console.error('Token não disponível. Usuário precisa fazer login novamente.');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        type: participantIds.length > 1 ? 'GROUP' : 'INDIVIDUAL',\n                        title,\n                        participantIds: participantIds.map({\n                            \"ChatProvider.useCallback[createConversation]\": (id)=>typeof id === 'object' ? id.id : id\n                        }[\"ChatProvider.useCallback[createConversation]\"]),\n                        includeClients: true // Permitir incluir clientes nas conversas\n                    })\n                });\n                console.log('Resposta da API:', response.status, response.statusText);\n                const data = await response.json();\n                console.log('Dados da resposta:', data);\n                if (data.success) {\n                    // Extrair os dados da conversa criada\n                    const conversationData = data.data;\n                    console.log('Conversa criada com sucesso:', conversationData);\n                    // Adicionar a nova conversa à lista\n                    setConversations({\n                        \"ChatProvider.useCallback[createConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createConversation]\": (c)=>c.id === conversationData.id\n                            }[\"ChatProvider.useCallback[createConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                conversationData,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"]);\n                    // Disparar evento para notificar componentes sobre a nova conversa\n                    setTimeout({\n                        \"ChatProvider.useCallback[createConversation]\": ()=>{\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: conversationData.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"], 300);\n                    return conversationData;\n                } else {\n                    console.error('Erro ao criar conversa:', data.error || 'Erro desconhecido');\n                    // Se o erro for de autenticação, redirecionar para login\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido. Redirecionando para login...');\n                        handleAuthError();\n                    }\n                }\n                return null;\n            } catch (error) {\n                console.error('Error creating conversation:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[createConversation]\"], [\n        handleAuthError\n    ]);\n    // Criar ou obter conversa com um usuário específico\n    const createOrGetConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createOrGetConversation]\": async (otherUser)=>{\n            try {\n                console.log('createOrGetConversation chamado com usuário:', otherUser);\n                if (!otherUser || !otherUser.id) {\n                    console.error('Usuário inválido:', otherUser);\n                    return null;\n                }\n                // Primeiro, verificar se já existe uma conversa individual com este usuário\n                const existingConversation = conversations.find({\n                    \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (conv)=>{\n                        var _conv_participants;\n                        if (conv.type !== 'INDIVIDUAL') return false;\n                        return (_conv_participants = conv.participants) === null || _conv_participants === void 0 ? void 0 : _conv_participants.some({\n                            \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (p)=>p.userId === otherUser.id\n                        }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                    }\n                }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                if (existingConversation) {\n                    console.log('Conversa existente encontrada:', existingConversation);\n                    setActiveConversation(existingConversation.id);\n                    return existingConversation;\n                }\n                console.log('Criando nova conversa com usuário:', otherUser.fullName);\n                // Se não existir, criar uma nova conversa\n                const newConversation = await createConversation([\n                    otherUser.id\n                ]);\n                if (newConversation) {\n                    console.log('Nova conversa criada com sucesso:', newConversation);\n                    // Garantir que a conversa seja adicionada à lista antes de definir como ativa\n                    setConversations({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createOrGetConversation]\": (c)=>c.id === newConversation.id\n                            }[\"ChatProvider.useCallback[createOrGetConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                newConversation,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"]);\n                    // Definir a conversa como ativa após um pequeno delay para garantir que a lista foi atualizada\n                    setTimeout({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": ()=>{\n                            setActiveConversation(newConversation.id);\n                            // Disparar evento para notificar componentes sobre a nova conversa\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: newConversation.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"], 300);\n                } else {\n                    console.error('Falha ao criar nova conversa, retorno nulo ou indefinido');\n                }\n                return newConversation;\n            } catch (error) {\n                console.error('Error creating or getting conversation:', error);\n                return null;\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[createOrGetConversation]\"], [\n        createConversation,\n        setActiveConversation,\n        handleAuthError\n    ]);\n    // Carregar contagem de mensagens não lidas com cache\n    const loadUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadUnreadCount]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                // Verificar cache\n                const now = Date.now();\n                if (!forceRefresh && requestCache.unreadCount.data !== null && now - requestCache.unreadCount.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para contagem de mensagens não lidas');\n                    return requestCache.unreadCount.data;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                try {\n                    console.log('Buscando contagem de mensagens não lidas da API...');\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/messages/unread\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar contagem de mensagens não lidas');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar contagem de mensagens n\\xe3o lidas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de mensagens não lidas:', data);\n                    if (data.success) {\n                        console.log('Dados de mensagens não lidas recebidos:', data.data);\n                        // Atualizar cache com o totalUnread\n                        requestCache.unreadCount.data = data.data.totalUnread;\n                        requestCache.unreadCount.timestamp = now;\n                        // Atualizar estado\n                        setUnreadCount(data.data.totalUnread);\n                        // Atualizar as conversas com as contagens de não lidas\n                        if (data.data.conversations && Array.isArray(data.data.conversations)) {\n                            // Primeiro, verificar se já temos as conversas carregadas\n                            const conversationIds = data.data.conversations.map({\n                                \"ChatProvider.useCallback[loadUnreadCount].conversationIds\": (c)=>c.conversationId\n                            }[\"ChatProvider.useCallback[loadUnreadCount].conversationIds\"]);\n                            // Se não temos conversas carregadas ou se temos menos conversas do que as não lidas,\n                            // forçar uma atualização das conversas\n                            if (conversations.length === 0 || !conversationIds.every({\n                                \"ChatProvider.useCallback[loadUnreadCount]\": (id)=>conversations.some({\n                                        \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>c.id === id\n                                    }[\"ChatProvider.useCallback[loadUnreadCount]\"])\n                            }[\"ChatProvider.useCallback[loadUnreadCount]\"])) {\n                                console.log('Forçando atualização das conversas porque há mensagens não lidas em conversas não carregadas');\n                                await loadConversations(true);\n                            } else {\n                                // Atualizar as conversas existentes com as contagens de não lidas\n                                setConversations({\n                                    \"ChatProvider.useCallback[loadUnreadCount]\": (prev)=>{\n                                        return prev.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount]\": (conv)=>{\n                                                // Procurar esta conversa nos dados de não lidas\n                                                const unreadInfo = data.data.conversations.find({\n                                                    \"ChatProvider.useCallback[loadUnreadCount].unreadInfo\": (item)=>item.conversationId === conv.id\n                                                }[\"ChatProvider.useCallback[loadUnreadCount].unreadInfo\"]);\n                                                if (unreadInfo) {\n                                                    return {\n                                                        ...conv,\n                                                        unreadCount: unreadInfo.unreadCount\n                                                    };\n                                                }\n                                                return conv;\n                                            }\n                                        }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                                    }\n                                }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                            }\n                        }\n                        return data.data.totalUnread;\n                    }\n                } catch (error) {\n                    console.error('Error loading unread count:', error);\n                }\n            } catch (error) {\n                console.error('Error in loadUnreadCount:', error);\n            }\n        // Removendo conversations e loadConversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadUnreadCount]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Referência para controlar se os dados iniciais já foram carregados\n    const initialDataLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Resetar a flag quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user) {\n                console.log('Usuário alterado, resetando flag de dados carregados');\n                initialDataLoadedRef.current = false;\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Dependendo apenas do ID do usuário para evitar re-renders desnecessários\n    // Efeito para carregar dados iniciais e configurar atualização periódica\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de carregar dados\n            if (!user) {\n                console.log('Usuário não logado, não carregando dados iniciais');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Carregando dados iniciais para:', user.role || 'USER');\n            // Verificar token\n            const currentToken = getCurrentToken();\n            if (!currentToken) {\n                console.log('Token não disponível, não carregando dados iniciais');\n                return;\n            }\n            // Verificar se já está carregando\n            if (isLoading || isLoadingRef.current) {\n                console.log('Já está carregando dados, aguardando...');\n                return;\n            }\n            // Verificar se os dados já foram carregados\n            if (initialDataLoadedRef.current) {\n                console.log('Dados já foram carregados anteriormente, ignorando');\n                return;\n            }\n            // Função para carregar dados iniciais\n            const loadInitialData = {\n                \"ChatProvider.useEffect.loadInitialData\": async ()=>{\n                    if (isLoading || isLoadingRef.current) {\n                        console.log('Já está carregando dados, cancelando carregamento inicial');\n                        return;\n                    }\n                    // Marcar como carregando\n                    isLoadingRef.current = true;\n                    console.log('Iniciando carregamento de dados do chat...');\n                    try {\n                        // Primeiro carregar as conversas\n                        console.log('Carregando conversas...');\n                        const conversationsData = await loadConversations(true);\n                        console.log('Conversas carregadas:', (conversationsData === null || conversationsData === void 0 ? void 0 : conversationsData.length) || 0, 'conversas');\n                        // Carregar contagem de mensagens não lidas\n                        console.log('Carregando contagem de mensagens não lidas...');\n                        await loadUnreadCount(true);\n                        console.log('Contagem de não lidas carregada');\n                        // Marcar como carregado\n                        initialDataLoadedRef.current = true;\n                        console.log('Carregamento de dados concluído com sucesso');\n                    } catch (error) {\n                        console.error('Erro ao carregar dados:', error);\n                    } finally{\n                        isLoadingRef.current = false;\n                    }\n                }\n            }[\"ChatProvider.useEffect.loadInitialData\"];\n            // Carregar dados iniciais com debounce para evitar múltiplas chamadas\n            console.log('Agendando carregamento de dados iniciais...');\n            const debouncedLoadData = debounce({\n                \"ChatProvider.useEffect.debouncedLoadData\": ()=>{\n                    console.log('Carregando dados iniciais (debounced)...');\n                    loadInitialData();\n                }\n            }[\"ChatProvider.useEffect.debouncedLoadData\"], 1000); // Esperar 1 segundo antes de carregar\n            // Chamar a função com debounce\n            debouncedLoadData();\n            // Removemos a atualização periódica via HTTP para evitar flood no backend\n            // Agora dependemos apenas do WebSocket para atualizações em tempo real\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                // Cleanup function\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo loadUnreadCount e isPanelOpen, isModalOpen das dependências para evitar chamadas desnecessárias\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        loadConversations,\n        isLoading\n    ]);\n    // Efeito para verificar se activeConversation é válido\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (activeConversation && conversations.length > 0) {\n                const conversationExists = conversations.some({\n                    \"ChatProvider.useEffect.conversationExists\": (c)=>c.id === activeConversation\n                }[\"ChatProvider.useEffect.conversationExists\"]);\n                if (!conversationExists) {\n                    console.warn('Conversa ativa não encontrada na lista de conversas, resetando...', activeConversation);\n                    setActiveConversation(null);\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        activeConversation,\n        conversations\n    ]);\n    // Efeito para monitorar mudanças no token e reconectar quando necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de monitorar o token\n            if (!user) return;\n            // Verificar o token a cada 2 minutos (aumentado para reduzir a frequência)\n            const tokenCheckInterval = setInterval({\n                \"ChatProvider.useEffect.tokenCheckInterval\": ()=>{\n                    // Evitar verificações desnecessárias se estiver carregando\n                    if (isLoading || isLoadingRef.current) return;\n                    const currentToken = getCurrentToken();\n                    // Se não há token mas há conexão, desconectar\n                    if (!currentToken && isConnected && socket) {\n                        console.log('Token não encontrado, desconectando WebSocket...');\n                        try {\n                            socket.disconnect();\n                        } catch (error) {\n                            console.error('Erro ao desconectar socket:', error);\n                        }\n                        setIsConnected(false);\n                    }\n                    // Se há token mas não há conexão, tentar reconectar (com verificação de tempo)\n                    if (currentToken && !isConnected && !socket) {\n                        const now = Date.now();\n                        // Limitar tentativas de reconexão (no máximo uma a cada 2 minutos)\n                        if (now - lastInitAttemptRef.current > 120000) {\n                            console.log('Token encontrado, tentando reconectar WebSocket...');\n                            // Permitir nova tentativa de inicialização do WebSocket\n                            socketInitializedRef.current = false;\n                            lastInitAttemptRef.current = now;\n                        }\n                    }\n                }\n            }[\"ChatProvider.useEffect.tokenCheckInterval\"], 120000); // Verificar a cada 2 minutos\n            return ({\n                \"ChatProvider.useEffect\": ()=>clearInterval(tokenCheckInterval)\n            })[\"ChatProvider.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Abrir/fechar painel de chat\n    const toggleChatPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatPanel]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsPanelOpen({\n                \"ChatProvider.useCallback[toggleChatPanel]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o painel, fechar o modal\n                    if (newState) {\n                        setIsModalOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatPanel]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatPanel]\"], [\n        user\n    ]);\n    // Abrir/fechar modal de chat\n    const toggleChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatModal]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsModalOpen({\n                \"ChatProvider.useCallback[toggleChatModal]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o modal, fechar o painel\n                    if (newState) {\n                        setIsPanelOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatModal]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatModal]\"], [\n        user\n    ]);\n    // Verificar se createConversation é uma função válida\n    console.log('ChatContext: createConversation é uma função?', typeof createConversation === 'function');\n    // Adicionar participante a um grupo\n    const addParticipantToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addParticipantToGroup]\": async (conversationId, participantId)=>{\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao adicionar participante');\n                    return null;\n                }\n                console.log(\"Adicionando participante \".concat(participantId, \" \\xe0 conversa \").concat(conversationId));\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        participantId\n                    })\n                });\n                console.log(\"Resposta da API: \".concat(response.status, \" \").concat(response.statusText));\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao adicionar participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    // Log detalhado do erro\n                    const errorText = await response.text();\n                    console.error(\"Erro \".concat(response.status, \" ao adicionar participante:\"), errorText);\n                    throw new Error(\"Erro ao adicionar participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Atualizar a conversa com o novo participante\n                    setConversations({\n                        \"ChatProvider.useCallback[addParticipantToGroup]\": (prev)=>{\n                            return prev.map({\n                                \"ChatProvider.useCallback[addParticipantToGroup]\": (conv)=>{\n                                    if (conv.id === conversationId) {\n                                        // Verificar se o participante já existe\n                                        const participantExists = conv.participants.some({\n                                            \"ChatProvider.useCallback[addParticipantToGroup].participantExists\": (p)=>p.userId === participantId\n                                        }[\"ChatProvider.useCallback[addParticipantToGroup].participantExists\"]);\n                                        if (participantExists) {\n                                            return conv;\n                                        }\n                                        // Adicionar o novo participante\n                                        return {\n                                            ...conv,\n                                            participants: [\n                                                ...conv.participants,\n                                                data.data\n                                            ]\n                                        };\n                                    }\n                                    return conv;\n                                }\n                            }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                        }\n                    }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                    // Criar uma mensagem do sistema para notificar que um participante foi adicionado\n                    if (socket && isConnected) {\n                        const systemMessage = {\n                            conversationId,\n                            content: \"\".concat(data.data.user.fullName, \" foi adicionado ao grupo\"),\n                            contentType: 'SYSTEM'\n                        };\n                        socket.emit('message:send', systemMessage);\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao adicionar participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[addParticipantToGroup]\"], [\n        socket,\n        isConnected,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Adicionar múltiplos participantes a um grupo\n    const addMultipleParticipantsToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addMultipleParticipantsToGroup]\": async (conversationId, participants)=>{\n            try {\n                if (!conversationId || !participants || !participants.length) {\n                    console.error('ID da conversa e lista de participantes são obrigatórios');\n                    return null;\n                }\n                console.log(\"Adicionando \".concat(participants.length, \" participantes ao grupo \").concat(conversationId));\n                // Array para armazenar os resultados\n                const results = [];\n                const errors = [];\n                // Adicionar participantes um por um\n                for (const participant of participants){\n                    try {\n                        const result = await addParticipantToGroup(conversationId, participant.id);\n                        if (result) {\n                            results.push(result);\n                        } else {\n                            errors.push({\n                                user: participant,\n                                error: 'Falha ao adicionar participante'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao adicionar participante \".concat(participant.id, \":\"), error);\n                        errors.push({\n                            user: participant,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    added: results,\n                    errors: errors,\n                    total: participants.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao adicionar múltiplos participantes:', error);\n                return {\n                    success: false,\n                    added: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: participants.length,\n                    successCount: 0,\n                    errorCount: participants.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[addMultipleParticipantsToGroup]\"], [\n        addParticipantToGroup\n    ]);\n    // Remover participante de um grupo (ou sair do grupo)\n    const removeParticipantFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[removeParticipantFromGroup]\": async (conversationId, participantId)=>{\n            console.log('removeParticipantFromGroup chamado:', {\n                conversationId,\n                participantId,\n                userId: user === null || user === void 0 ? void 0 : user.id\n            });\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao remover participante');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants/\").concat(participantId), {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao remover participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    throw new Error(\"Erro ao remover participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Sempre remover da lista local quando o usuário sai\n                    if (participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                        console.log(\"Removendo conversa \".concat(conversationId, \" da lista local\"));\n                        // Remover da lista local\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>prev.filter({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>conv.id !== conversationId\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar mensagens\n                        setMessages({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                const updated = {\n                                    ...prev\n                                };\n                                delete updated[conversationId];\n                                return updated;\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar cache\n                        requestCache.conversations.data = null;\n                        requestCache.conversations.timestamp = 0;\n                        if (requestCache.messages[conversationId]) {\n                            delete requestCache.messages[conversationId];\n                        }\n                        // Se era a conversa ativa, limpar\n                        if (activeConversation === conversationId) {\n                            setActiveConversation(null);\n                        }\n                        // Forçar recarregamento das conversas após sair\n                        setTimeout({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": ()=>{\n                                loadConversations(true);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], 500);\n                        console.log(\"Conversa \".concat(conversationId, \" removida da lista local\"));\n                    } else {\n                        var _data_data_user, _data_data;\n                        // Atualizar a conversa removendo o participante\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                return prev.map({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>{\n                                        if (conv.id === conversationId) {\n                                            return {\n                                                ...conv,\n                                                participants: conv.participants.filter({\n                                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (p)=>p.userId !== participantId\n                                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                                            };\n                                        }\n                                        return conv;\n                                    }\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Criar uma mensagem do sistema para notificar que um participante saiu\n                        if (socket && isConnected && ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : (_data_data_user = _data_data.user) === null || _data_data_user === void 0 ? void 0 : _data_data_user.fullName)) {\n                            const systemMessage = {\n                                conversationId,\n                                content: \"\".concat(data.data.user.fullName, \" saiu do grupo\"),\n                                contentType: 'SYSTEM'\n                            };\n                            socket.emit('message:send', systemMessage);\n                        }\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao remover participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], [\n        socket,\n        isConnected,\n        user,\n        activeConversation,\n        getCurrentToken,\n        handleAuthError,\n        loadUnreadCount\n    ]);\n    // Marcar mensagens como lidas\n    const markMessagesAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markMessagesAsRead]\": async (conversationId, messageId)=>{\n            try {\n                if (!user || !socket || !isConnected) return;\n                console.log(\"[markMessagesAsRead] Usando Socket.IO para marcar mensagem como lida: \".concat(conversationId, \", \").concat(messageId));\n                // Usar Socket.IO em vez do HTTP endpoint para obter atualizações em tempo real\n                socket.emit('message:read', {\n                    conversationId,\n                    messageId\n                }, {\n                    \"ChatProvider.useCallback[markMessagesAsRead]\": (response)=>{\n                        if (response.success) {\n                            console.log(\"[markMessagesAsRead] Marca\\xe7\\xe3o como lida bem-sucedida via Socket.IO para conversa: \".concat(conversationId));\n                        } else {\n                            console.error(\"[markMessagesAsRead] Erro ao marcar mensagem como lida via Socket.IO:\", response.error);\n                            // Fallback para HTTP endpoint se Socket.IO falhar\n                            console.log('[markMessagesAsRead] Tentando fallback via HTTP...');\n                            markMessagesAsReadHTTP(conversationId, messageId);\n                        }\n                    }\n                }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n            } catch (error) {\n                console.error('Error marking messages as read:', error);\n                // Fallback para HTTP endpoint se houver erro\n                markMessagesAsReadHTTP(conversationId, messageId);\n            }\n        }\n    }[\"ChatProvider.useCallback[markMessagesAsRead]\"], [\n        user,\n        socket,\n        isConnected\n    ]);\n    // Fallback HTTP para marcar mensagens como lidas (para casos onde Socket.IO não está disponível)\n    const markMessagesAsReadHTTP = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markMessagesAsReadHTTP]\": async (conversationId, messageId)=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/mark-read\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                if (response.ok) {\n                    console.log(\"[markMessagesAsReadHTTP] Marca\\xe7\\xe3o como lida bem-sucedida via HTTP para conversa: \".concat(conversationId));\n                    // Forçar recarregamento do contador da API\n                    await loadUnreadCount(true);\n                    console.log('[markMessagesAsReadHTTP] Contador recarregado da API');\n                }\n            } catch (error) {\n                console.error('Error marking messages as read via HTTP:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[markMessagesAsReadHTTP]\"], [\n        user,\n        getCurrentToken,\n        loadUnreadCount\n    ]);\n    // Resetar contador de mensagens não lidas\n    const resetUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[resetUnreadCount]\": async ()=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/reset-unread\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (response.ok) {\n                    setUnreadCount(0);\n                    setConversations({\n                        \"ChatProvider.useCallback[resetUnreadCount]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[resetUnreadCount]\": (conv)=>({\n                                        ...conv,\n                                        unreadCount: 0\n                                    })\n                            }[\"ChatProvider.useCallback[resetUnreadCount]\"])\n                    }[\"ChatProvider.useCallback[resetUnreadCount]\"]);\n                }\n            } catch (error) {\n                console.error('Error resetting unread count:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[resetUnreadCount]\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Apagar mensagens\n    const deleteMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[deleteMessages]\": async (messageIds, conversationId)=>{\n            try {\n                if (!messageIds || messageIds.length === 0) {\n                    console.error('IDs das mensagens são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao apagar mensagens');\n                    return null;\n                }\n                const results = [];\n                const errors = [];\n                // Apagar mensagens uma por uma\n                for (const messageId of messageIds){\n                    try {\n                        const response = await fetch(\"\".concat(API_URL, \"/chat/messages/\").concat(messageId), {\n                            method: 'DELETE',\n                            headers: {\n                                Authorization: \"Bearer \".concat(currentToken)\n                            }\n                        });\n                        if (!response.ok) {\n                            if (response.status === 401) {\n                                console.error('Token expirado ou inválido ao apagar mensagem');\n                                handleAuthError();\n                                return null;\n                            }\n                            throw new Error(\"Erro ao apagar mensagem: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        if (data.success) {\n                            results.push(data.data);\n                            // Atualizar o estado das mensagens\n                            setMessages({\n                                \"ChatProvider.useCallback[deleteMessages]\": (prev)=>{\n                                    const updatedMessages = {\n                                        ...prev\n                                    };\n                                    // Percorrer todas as conversas\n                                    Object.keys(updatedMessages).forEach({\n                                        \"ChatProvider.useCallback[deleteMessages]\": (convId)=>{\n                                            // Atualizar a mensagem na conversa correspondente\n                                            updatedMessages[convId] = updatedMessages[convId].map({\n                                                \"ChatProvider.useCallback[deleteMessages]\": (msg)=>{\n                                                    if (msg.id === messageId) {\n                                                        return {\n                                                            ...msg,\n                                                            ...data.data,\n                                                            isDeleted: true\n                                                        };\n                                                    }\n                                                    return msg;\n                                                }\n                                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                        }\n                                    }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                    return updatedMessages;\n                                }\n                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                        } else {\n                            errors.push({\n                                messageId,\n                                error: 'Falha ao apagar mensagem'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao apagar mensagem \".concat(messageId, \":\"), error);\n                        errors.push({\n                            messageId,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                // Limpar o cache de mensagens para forçar uma nova busca\n                if (conversationId && results.length > 0) {\n                    console.log('Limpando cache de mensagens para a conversa:', conversationId);\n                    if (requestCache.messages[conversationId]) {\n                        delete requestCache.messages[conversationId];\n                    }\n                    // Recarregar as mensagens da conversa para garantir que estamos sincronizados com o backend\n                    try {\n                        console.log('Recarregando mensagens após exclusão');\n                        await loadMessages(conversationId);\n                        // Também recarregar a lista de conversas para garantir que tudo está atualizado\n                        await loadConversations(true);\n                    } catch (reloadError) {\n                        console.error('Erro ao recarregar mensagens após exclusão:', reloadError);\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    deleted: results,\n                    errors: errors,\n                    total: messageIds.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao apagar mensagens:', error);\n                return {\n                    success: false,\n                    deleted: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: messageIds.length,\n                    successCount: 0,\n                    errorCount: messageIds.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[deleteMessages]\"], [\n        getCurrentToken,\n        handleAuthError,\n        loadMessages,\n        loadConversations\n    ]);\n    // Memoizar o valor do contexto para evitar re-renderizações desnecessárias\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatProvider.useMemo[contextValue]\": ()=>({\n                conversations,\n                messages,\n                unreadCount,\n                activeConversation,\n                isPanelOpen,\n                isModalOpen,\n                isConnected,\n                isLoading,\n                setActiveConversation,\n                loadMessages,\n                sendMessage,\n                createConversation,\n                createOrGetConversation,\n                toggleChatPanel,\n                toggleChatModal,\n                loadConversations,\n                loadUnreadCount,\n                markMessagesAsRead,\n                resetUnreadCount,\n                addParticipantToGroup,\n                addMultipleParticipantsToGroup,\n                removeParticipantFromGroup,\n                deleteMessages\n            })\n    }[\"ChatProvider.useMemo[contextValue]\"], [\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation,\n        isPanelOpen,\n        isModalOpen,\n        isConnected,\n        isLoading,\n        setActiveConversation,\n        loadMessages,\n        sendMessage,\n        createConversation,\n        createOrGetConversation,\n        toggleChatPanel,\n        toggleChatModal,\n        loadConversations,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        deleteMessages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ChatContext.js\",\n        lineNumber: 1948,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatProvider, \"eX7Gh7taDLClg5W9qRLTMr6wU0o=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ChatProvider;\nconst useChat = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChat deve ser usado dentro de um ChatProvider');\n    }\n    return context;\n};\n_s1(useChat, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.js\n"));

/***/ })

});