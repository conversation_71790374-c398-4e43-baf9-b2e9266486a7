"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/chat/ChatButton.js":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatButton.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useUnreadMessages__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useUnreadMessages */ \"(app-pages-browser)/./src/hooks/useUnreadMessages.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ChatButton = ()=>{\n    _s();\n    const { toggleChatPanel, resetUnreadCount, unreadCount } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Se o usuário não estiver logado, não mostrar o botão\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: toggleChatPanel,\n            className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full relative transition-colors\",\n            \"aria-label\": \"Mensagens\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    size: 20,\n                    \"aria-hidden\": \"true\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatButton.js\",\n                    lineNumber: 24,\n                    columnNumber: 7\n                }, undefined),\n                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    className: \"absolute -top-1 -right-1 min-w-[20px] h-5 flex items-center justify-center rounded-full bg-cyan-500 text-white text-xs font-bold px-1\",\n                    children: unreadCount > 99 ? '99+' : unreadCount\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatButton.js\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatButton.js\",\n            lineNumber: 19,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatButton.js\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatButton, \"mlQPDNbDAvaxDp6YoWbfWPI8Nr4=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ChatButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatButton);\nvar _c;\n$RefreshReg$(_c, \"ChatButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatButton.js\n"));

/***/ })

});