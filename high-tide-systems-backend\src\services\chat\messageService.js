// src/services/chat/messageService.js
const prisma = require('../../utils/prisma');
const { NotFoundError, ForbiddenError } = require('../../utils/errors');
const chatService = require('./chatService');

// Client select with person data
const clientSelectWithPerson = {
  id: true,
  login: true,
  email: true,
  fullName: true,
  clientPersons: {
    take: 1,
    select: {
      person: {
        select: {
          fullName: true
        }
      }
    }
  }
};

/**
 * Cria uma nova mensagem
 * @param {Object} data - Dados da mensagem
 * @returns {Promise<Object>} - Mensagem criada
 */
const createMessage = async (data) => {
  try {
    console.log('[MessageService] Creating message:', data);
    console.log('[MessageService] ContentType received:', data.contentType);
    
    // Verificar se a conversa existe
    const conversation = await prisma.conversation.findUnique({
      where: { id: data.conversationId }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é participante da conversa
    const isParticipant = await chatService.isConversationParticipant(
      data.conversationId,
      data.senderId
    );

    if (!isParticipant) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }
    
    // Verificar se é cliente para usar senderClientId
    const isClient = await prisma.client.findUnique({ where: { id: data.senderId } });

    // Se houver uma mensagem referenciada, verificar se ela existe e pertence à mesma conversa
    if (data.referencedMessageId) {
      const referencedMessage = await prisma.message.findUnique({
        where: { id: data.referencedMessageId },
        select: { conversationId: true }
      });

      if (!referencedMessage) {
        throw new NotFoundError('Mensagem referenciada não encontrada');
      }

      if (referencedMessage.conversationId !== data.conversationId) {
        throw new ForbiddenError('Mensagem referenciada não pertence a esta conversa');
      }
    }

    // Validar compartilhamento: usuário só pode compartilhar itens que pertencem aos clientes da conversa
    if (data.contentType?.startsWith('SHARED_') && data.metadata?.sharedItem) {
      await validateSharedItemOwnership(data.contentType, data.metadata.sharedItem, data.conversationId);
    }

    // Criar a mensagem
    const message = await prisma.message.create({
      data: {
        conversationId: data.conversationId,
        senderId: isClient ? null : data.senderId,
        senderClientId: isClient ? data.senderId : null,
        content: data.content && data.content.trim() !== '' ? data.content : (data.contentType === 'ATTACHMENT' ? 'Arquivo(s) anexado(s)' : ''),
        contentType: data.contentType || 'TEXT',
        referencedMessageId: data.referencedMessageId,
        metadata: data.metadata || {}
      },
      include: {
        sender: {
          select: {
            id: true,
            fullName: true,
            email: true,
            profileImageUrl: true
          }
        },
        senderClient: {
          select: clientSelectWithPerson
        },
        referencedMessage: {
          include: {
            sender: {
              select: {
                id: true,
                fullName: true
              }
            }
          }
        }
      }
    });

    // Criar status de mensagem para todos os participantes
    await createMessageStatusForParticipants(message.id, data.conversationId, data.senderId);

    return message;
  } catch (error) {
    console.error('Error creating message:', error);
    throw error;
  }
};

/**
 * Cria status de mensagem para todos os participantes de uma conversa
 * @param {string} messageId - ID da mensagem
 * @param {string} conversationId - ID da conversa
 * @param {string} senderId - ID do remetente
 * @returns {Promise<void>}
 */
const createMessageStatusForParticipants = async (messageId, conversationId, senderId) => {
  try {
    console.log('[createMessageStatusForParticipants] Criando status para mensagem:', messageId);
    
    // Verificar se o remetente é cliente
    const isClient = await prisma.client.findUnique({ where: { id: senderId } });
    console.log('[createMessageStatusForParticipants] Remetente é cliente:', !!isClient);
    
    // Buscar todos os participantes ativos da conversa
    const allParticipants = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        leftAt: null
      }
    });
    
    console.log('[createMessageStatusForParticipants] Total de participantes ativos:', allParticipants.length);
    
    // Filtrar participantes (exceto o remetente)
    const participants = allParticipants.filter(participant => {
      if (isClient) {
        return participant.clientId !== senderId;
      } else {
        return participant.userId !== senderId;
      }
    });
    
    console.log('[createMessageStatusForParticipants] Participantes que receberão status:', participants.length);

    // Usar upsert para evitar duplicatas de status de mensagem
    const statusPromises = participants.map(participant => {
      console.log('[createMessageStatusForParticipants] Criando/atualizando status para participante:', participant.id);
      return prisma.messageStatus.upsert({
        where: {
          messageId_participantId: {
            messageId,
            participantId: participant.id
          }
        },
        update: {
          status: 'SENT',
          timestamp: new Date()
        },
        create: {
          messageId,
          participantId: participant.id,
          status: 'SENT',
          timestamp: new Date()
        }
      });
    });

    await Promise.all(statusPromises);
    console.log('[createMessageStatusForParticipants] Status criados/atualizados com sucesso');
  } catch (error) {
    console.error('Error creating message status for participants:', error);
    throw error;
  }
};

/**
 * Busca mensagens de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário que está buscando as mensagens
 * @param {Object} filters - Filtros de busca
 * @returns {Promise<Array>} - Lista de mensagens
 */
const getConversationMessages = async (conversationId, userId, filters = {}) => {
  try {
    const { limit = 50, before, after } = filters;

    // Verificar se o usuário é participante da conversa (suporta clientes)
    const isParticipant = await chatService.isConversationParticipant(
      conversationId,
      userId
    );

    if (!isParticipant) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }

    // Construir a query base
    const where = {
      conversationId,
      isDeleted: false
    };

    // Adicionar filtros de paginação
    if (before) {
      where.createdAt = { lt: new Date(before) };
    } else if (after) {
      where.createdAt = { gt: new Date(after) };
    }

    // Buscar as mensagens
    const messages = await prisma.message.findMany({
      where,
      orderBy: {
        createdAt: after ? 'asc' : 'desc'
      },
      take: limit,
      include: {
        sender: {
          select: {
            id: true,
            fullName: true,
            email: true,
            profileImageUrl: true
          }
        },
        senderClient: {
          select: clientSelectWithPerson
        },
        referencedMessage: {
          include: {
            sender: {
              select: {
                id: true,
                fullName: true
              }
            },
            senderClient: {
              select: clientSelectWithPerson
            }
          }
        },
        statuses: {
          where: {
            participant: {
              userId: { not: userId } // Excluir o próprio usuário
            }
          },
          include: {
            participant: {
              select: {
                userId: true
              }
            }
          }
        }
      }
    });

    // Se a busca foi feita com 'after', inverter a ordem para manter consistência
    if (after) {
      messages.reverse();
    }

    return messages;
  } catch (error) {
    console.error('Error getting conversation messages:', error);
    throw error;
  }
};

/**
 * Atualiza o status de uma mensagem para um participante
 * @param {string} messageId - ID da mensagem
 * @param {string} participantId - ID do participante
 * @param {string} status - Novo status
 * @returns {Promise<Object>} - Status atualizado
 */
const updateMessageStatus = async (messageId, participantId, status) => {
  try {
    // Verificar se o status já existe
    const existingStatus = await prisma.messageStatus.findUnique({
      where: {
        messageId_participantId: {
          messageId,
          participantId
        }
      }
    });

    if (existingStatus) {
      // Atualizar o status existente
      return prisma.messageStatus.update({
        where: {
          id: existingStatus.id
        },
        data: {
          status,
          timestamp: new Date()
        }
      });
    } else {
      // Criar um novo status
      return prisma.messageStatus.create({
        data: {
          messageId,
          participantId,
          status
        }
      });
    }
  } catch (error) {
    console.error('Error updating message status:', error);
    throw error;
  }
};

/**
 * Marca uma mensagem como excluída
 * @param {string} messageId - ID da mensagem
 * @param {string} userId - ID do usuário que está excluindo a mensagem
 * @returns {Promise<Object>} - Mensagem atualizada
 */
const deleteMessage = async (messageId, userId) => {
  try {
    // Buscar a mensagem
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        conversation: {
          include: {
            participants: {
              where: {
                userId,
                isAdmin: true,
                leftAt: null
              }
            }
          }
        }
      }
    });

    if (!message) {
      throw new NotFoundError('Mensagem não encontrada');
    }

    // Verificar se o usuário é o remetente da mensagem ou administrador da conversa
    const isAdmin = message.conversation.participants.length > 0;
    const isSender = message.senderId === userId;

    if (!isSender && !isAdmin) {
      throw new ForbiddenError('Você não tem permissão para excluir esta mensagem');
    }

    // Marcar a mensagem como excluída
    const updatedMessage = await prisma.message.update({
      where: { id: messageId },
      data: {
        isDeleted: true,
        content: isSender ? 'Esta mensagem foi excluída pelo remetente' : 'Esta mensagem foi excluída por um administrador',
        metadata: null
      }
    });

    return updatedMessage;
  } catch (error) {
    console.error('Error deleting message:', error);
    throw error;
  }
};

const markMessagesAsRead = async (userId, conversationId, messageId) => {
  try {
    console.log('[markMessagesAsRead] Marcando como lidas para:', userId, 'conversa:', conversationId);
    
    const isClient = await prisma.client.findUnique({ where: { id: userId } });
    
    const participation = await prisma.conversationParticipant.findFirst({
      where: {
        conversationId,
        ...(isClient ? { clientId: userId } : { userId }),
        leftAt: null
      }
    });

    if (!participation) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }

    // Atualizar lastReadMessageId
    await prisma.conversationParticipant.update({
      where: { id: participation.id },
      data: { lastReadMessageId: messageId }
    });
    
    // Marcar todos os status de mensagem como READ para este participante
    const updatedStatuses = await prisma.messageStatus.updateMany({
      where: {
        participantId: participation.id,
        status: { in: ['SENT', 'DELIVERED'] }
      },
      data: {
        status: 'READ',
        timestamp: new Date()
      }
    });
    
    console.log('[markMessagesAsRead] Status atualizados:', updatedStatuses.count);

    return { success: true };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
};

const getUnreadMessages = async (userId) => {
  try {
    console.log('[getUnreadMessages] Iniciando para usuário:', userId);

    const isClient = await prisma.client.findUnique({ where: { id: userId } });
    console.log('[getUnreadMessages] É cliente:', !!isClient);

    const participations = await prisma.conversationParticipant.findMany({
      where: {
        ...(isClient ? { clientId: userId } : { userId }),
        leftAt: null
      }
    });

    console.log('[getUnreadMessages] Participações:', participations.length);

    const conversationsWithUnread = [];

    for (const participation of participations) {
      console.log(`[getUnreadMessages] Processando conversa: ${participation.conversationId}`);

      // Verificar se há mensagens não lidas nesta conversa
      let hasUnreadMessages = false;

      if (!participation.lastReadMessageId) {
        // Se nunca leu, verificar se há mensagens de outros usuários
        // Usar lógica mais simples: mensagens onde senderId != userId E senderClientId != userId
        const hasMessages = await prisma.message.findFirst({
          where: {
            conversationId: participation.conversationId,
            isDeleted: false,
            AND: [
              {
                OR: [
                  { senderId: { not: userId } },
                  { senderId: null }
                ]
              },
              {
                OR: [
                  { senderClientId: { not: userId } },
                  { senderClientId: null }
                ]
              }
            ]
          }
        });

        hasUnreadMessages = !!hasMessages;
        console.log(`[getUnreadMessages] Nunca leu - tem mensagens de outros: ${hasUnreadMessages}`);
      } else {
        // Verificar se há mensagens após a última lida
        const lastRead = await prisma.message.findUnique({
          where: { id: participation.lastReadMessageId }
        });

        if (lastRead) {
          const unreadMessage = await prisma.message.findFirst({
            where: {
              conversationId: participation.conversationId,
              isDeleted: false,
              createdAt: { gt: lastRead.createdAt },
              AND: [
                {
                  OR: [
                    { senderId: { not: userId } },
                    { senderId: null }
                  ]
                },
                {
                  OR: [
                    { senderClientId: { not: userId } },
                    { senderClientId: null }
                  ]
                }
              ]
            }
          });

          hasUnreadMessages = !!unreadMessage;
          console.log(`[getUnreadMessages] Após última lida - tem não lidas: ${hasUnreadMessages}`);
        } else {
          // Se a última mensagem lida não existe mais, considerar como tendo não lidas
          const hasMessages = await prisma.message.findFirst({
            where: {
              conversationId: participation.conversationId,
              isDeleted: false,
              AND: [
                {
                  OR: [
                    { senderId: { not: userId } },
                    { senderId: null }
                  ]
                },
                {
                  OR: [
                    { senderClientId: { not: userId } },
                    { senderClientId: null }
                  ]
                }
              ]
            }
          });

          hasUnreadMessages = !!hasMessages;
          console.log(`[getUnreadMessages] Última lida não encontrada - tem mensagens: ${hasUnreadMessages}`);
        }
      }

      if (hasUnreadMessages) {
        conversationsWithUnread.push({
          conversationId: participation.conversationId,
          hasUnread: true
        });
      }
    }

    const totalUnreadConversations = conversationsWithUnread.length;
    console.log('[getUnreadMessages] TOTAL CONVERSAS NÃO LIDAS:', totalUnreadConversations);
    console.log('[getUnreadMessages] Conversas com não lidas:', conversationsWithUnread.map(c => c.conversationId));

    return {
      conversations: conversationsWithUnread,
      totalUnreadConversations,
      // Manter compatibilidade com código existente
      totalUnread: totalUnreadConversations
    };
  } catch (error) {
    console.error('Error getting unread messages:', error);
    throw error;
  }
};

/**
 * Valida se o item compartilhado pertence aos clientes da conversa
 * @param {string} contentType - Tipo de conteúdo (SHARED_*)
 * @param {Object} sharedItem - Item sendo compartilhado
 * @param {string} conversationId - ID da conversa
 * @returns {Promise<void>}
 * @throws {ForbiddenError} - Se o item não pertencer aos clientes da conversa
 */
const validateSharedItemOwnership = async (contentType, sharedItem, conversationId) => {
  try {
    // Buscar clientes participantes da conversa
    const conversationClients = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        clientId: { not: null },
        leftAt: null
      },
      select: {
        clientId: true
      }
    });

    if (conversationClients.length === 0) {
      // Se não há clientes na conversa, permitir compartilhamento livre (apenas usuários internos)
      // Usuários podem compartilhar qualquer coisa entre si
      return;
    }

    const clientIds = conversationClients.map(cp => cp.clientId);
    
    console.log(`[validateSharedItemOwnership] Validando compartilhamento de ${contentType} com ${clientIds.length} clientes na conversa:`, clientIds);

    // Validar propriedade baseado no tipo de item
    switch (contentType) {
      case 'SHARED_APPOINTMENT':
        await validateAppointmentOwnership(sharedItem.id, clientIds);
        break;
      
      case 'SHARED_PERSON':
        await validatePersonOwnership(sharedItem.id, clientIds);
        break;
      
      case 'SHARED_CLIENT':
        await validateClientOwnership(sharedItem.id, clientIds);
        break;
      
      case 'SHARED_INSURANCE_LIMIT':
        await validateInsuranceLimitOwnership(sharedItem.id, clientIds);
        break;
      
      case 'SHARED_INSURANCE':
      case 'SHARED_SERVICE_TYPE':
      case 'SHARED_LOCATION':
      case 'SHARED_WORKING_HOURS':
      case 'SHARED_USER':
        // Estes tipos são geralmente recursos da empresa, não específicos do cliente
        // Permitir compartilhamento sem validação adicional
        break;
      
      default:
        console.warn(`[validateSharedItemOwnership] Tipo de compartilhamento não reconhecido: ${contentType}`);
    }
  } catch (error) {
    console.error('Error validating shared item ownership:', error);
    throw error;
  }
};

/**
 * Valida se o agendamento pertence aos clientes especificados
 */
const validateAppointmentOwnership = async (appointmentId, clientIds) => {
  const appointment = await prisma.scheduling.findUnique({
    where: { id: appointmentId },
    select: { 
      clientId: true,
      Person: {
        select: {
          clientPersons: {
            select: {
              clientId: true
            }
          }
        }
      }
    }
  });

  if (!appointment) {
    throw new NotFoundError('Agendamento não encontrado');
  }

  // Verificar se o agendamento pertence diretamente a um dos clientes
  if (clientIds.includes(appointment.clientId)) {
    return;
  }

  // Verificar se o agendamento é para uma pessoa relacionada a um dos clientes
  const relatedClientIds = appointment.Person?.flatMap(person =>
    person.clientPersons?.map(cp => cp.clientId) || []
  ) || [];

  const hasClientRelation = relatedClientIds.some(id => clientIds.includes(id));

  if (!hasClientRelation) {
    throw new ForbiddenError('Você não pode compartilhar este agendamento pois ele não pertence aos clientes desta conversa');
  }
};

/**
 * Valida se a pessoa pertence aos clientes especificados
 */
const validatePersonOwnership = async (personId, clientIds) => {
  const person = await prisma.person.findUnique({
    where: { id: personId },
    select: {
      clientPersons: {
        select: {
          clientId: true
        }
      }
    }
  });

  if (!person) {
    throw new NotFoundError('Pessoa não encontrada');
  }

  const personClientIds = person.clientPersons?.map(cp => cp.clientId) || [];
  const hasClientRelation = personClientIds.some(id => clientIds.includes(id));

  if (!hasClientRelation) {
    throw new ForbiddenError('Você não pode compartilhar esta pessoa pois ela não está relacionada aos clientes desta conversa');
  }
};

/**
 * Valida se o cliente pode ser compartilhado (deve estar na lista de clientes da conversa)
 */
const validateClientOwnership = async (clientId, clientIds) => {
  if (!clientIds.includes(clientId)) {
    throw new ForbiddenError('Você não pode compartilhar este cliente pois ele não participa desta conversa');
  }
};

/**
 * Valida se o limite de convênio pertence aos clientes especificados
 */
const validateInsuranceLimitOwnership = async (limitId, clientIds) => {
  const limit = await prisma.personInsuranceServiceLimit.findUnique({
    where: { id: limitId },
    select: {
      Person: {
        select: {
          clientPersons: {
            select: {
              clientId: true
            }
          }
        }
      }
    }
  });

  if (!limit) {
    throw new NotFoundError('Limite de convênio não encontrado');
  }

  const limitClientIds = limit.Person?.clientPersons?.map(cp => cp.clientId) || [];
  const hasClientRelation = limitClientIds.some(id => clientIds.includes(id));

  if (!hasClientRelation) {
    throw new ForbiddenError('Você não pode compartilhar este limite de convênio pois ele não pertence aos clientes desta conversa');
  }
};

const resetAllUnreadMessages = async (userId) => {
  try {
    const isClient = await prisma.client.findUnique({ where: { id: userId } });
    
    // Buscar todas as participações do usuário
    const participations = await prisma.conversationParticipant.findMany({
      where: {
        ...(isClient ? { clientId: userId } : { userId }),
        leftAt: null
      },
      include: {
        conversation: {
          include: {
            messages: {
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          }
        }
      }
    });

    // Atualizar cada participação com a última mensagem
    for (const participation of participations) {
      const lastMessage = participation.conversation.messages[0];
      if (lastMessage) {
        await prisma.conversationParticipant.update({
          where: { id: participation.id },
          data: { lastReadMessageId: lastMessage.id }
        });
      }
    }

    return { success: true, updated: participations.length };
  } catch (error) {
    console.error('Error resetting unread messages:', error);
    throw error;
  }
};

module.exports = {
  createMessage,
  getConversationMessages,
  updateMessageStatus,
  deleteMessage,
  getUnreadMessages,
  markMessagesAsRead,
  resetAllUnreadMessages
};