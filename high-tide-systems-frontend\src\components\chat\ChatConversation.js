'use client';

import React, { useEffect, useRef, useMemo, useCallback, useState } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, MoreVertical, UserPlus, LogOut, Users, X, Trash2 } from 'lucide-react';
import ChatInput from './ChatInput';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import MultiUserSearch from './MultiUserSearch';
import { ConfirmationDialog } from '../../components/ui';
import SharedItemMessage from './SharedItemMessage';
import AttachmentViewer from './AttachmentViewer';
import { useSharedItemNavigation } from '@/hooks/useSharedItemNavigation';

const ChatConversation = ({ conversationId, onBack, compact = false }) => {
  const {
    messages,
    loadMessages,
    conversations,
    setConversations,
    activeConversation,
    setActiveConversation,
    removeParticipantFromGroup,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    markMessagesAsRead
  } = useChat();
  const { user } = useAuth();
  const messagesEndRef = useRef(null);
  const { handleSharedItemClick } = useSharedItemNavigation();

  // Wrapper para logar o clique no item compartilhado
  const handleSharedItemClickWithLog = (contentType, itemData) => {
    console.log('[CHAT] Clique em item compartilhado:', { contentType, itemData });
    handleSharedItemClick(contentType, itemData);
  };

  // Estados para gerenciar o menu de opções e adição de participantes
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const [showAddParticipant, setShowAddParticipant] = useState(false);
  const [showParticipantsList, setShowParticipantsList] = useState(false);
  const [addingParticipants, setAddingParticipants] = useState(false);

  // Estados para diálogos de confirmação
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState({
    type: '', // 'leave-group', 'delete-conversation', 'delete-messages'
    title: '',
    message: '',
    onConfirm: () => {}
  });

  // Memoizar a conversa atual para evitar recalculos
  const conversation = useMemo(() => {
    return conversations.find(c => c.id === conversationId);
  }, [conversations, conversationId]);

  // Memoizar as mensagens da conversa para evitar recalculos
  const conversationMessages = useMemo(() => {
    return messages[conversationId] || [];
  }, [messages, conversationId]);

  // Referência para controlar se as mensagens já foram carregadas
  const messagesLoadedRef = useRef(false);

  // Carregar mensagens ao montar o componente - apenas uma vez
  useEffect(() => {
    if (conversationId && !messagesLoadedRef.current) {
      // Sempre carregar mensagens completas quando abrir uma conversa
      // O loadConversations só traz a última mensagem, precisamos de todas
      console.log('[ChatConversation] Carregando mensagens completas para conversa:', conversationId);
      loadMessages(conversationId);
      messagesLoadedRef.current = true;
    }

    // Limpar a referência quando o componente for desmontado
    return () => {
      messagesLoadedRef.current = false;
    };
  }, [conversationId, loadMessages]);

  // Referência para controlar se já marcou como lida
  const markedAsReadRef = useRef(false);

  // Marcar mensagens como lidas quando a conversa for visualizada - apenas uma vez
  useEffect(() => {
    if (conversationId && conversationMessages.length > 0 && !markedAsReadRef.current) {
      const lastMessage = conversationMessages[conversationMessages.length - 1];

      if (lastMessage) {
        markedAsReadRef.current = true; // Marcar como já processado

        // Delay para garantir que a conversa foi totalmente carregada
        const timer = setTimeout(() => {
          markMessagesAsRead(conversationId, lastMessage.id);
        }, 1000);

        return () => clearTimeout(timer);
      }
    }
  }, [conversationId, conversationMessages.length]); // Removido markMessagesAsRead para evitar loop

  // Reset da referência quando muda de conversa
  useEffect(() => {
    markedAsReadRef.current = false;
  }, [conversationId]);

  // Rolar para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationMessages]);

  // Verificar se o usuário atual é administrador do grupo
  const isGroupAdmin = useMemo(() => {
    if (!conversation || !user) return false;

    const userParticipant = conversation.participants?.find(p => p.userId === user.id);
    return userParticipant?.isAdmin || false;
  }, [conversation, user]);

  // Verificar se é uma conversa de grupo
  const isGroupChat = useMemo(() => {
    return conversation?.type === 'GROUP';
  }, [conversation]);

  // Função para confirmar saída do grupo
  const handleLeaveGroup = useCallback(() => {
    if (!conversation || !user) return;

    // Configurar o diálogo de confirmação
    setConfirmAction({
      type: 'leave-group',
      title: 'Sair do grupo',
      message: `Tem certeza que deseja sair do grupo "${conversation.title || 'Grupo'}"?`,
      onConfirm: () => {
        removeParticipantFromGroup(conversation.id, user.id)
          .then(() => {
            console.log('Saiu do grupo com sucesso');

            // Disparar evento para atualizar a lista de conversas
            setTimeout(() => {
              console.log('Forçando atualização da lista de conversas após sair do grupo');
              window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                detail: {
                  type: 'conversations',
                  action: 'delete',
                  conversationId: conversation.id,
                  timestamp: Date.now()
                }
              }));
            }, 300);

            // A navegação de volta para a lista de conversas é tratada no contexto
          })
          .catch(error => {
            console.error('Erro ao sair do grupo:', error);
            alert('Não foi possível sair do grupo. Tente novamente mais tarde.');
          });
      }
    });

    // Abrir o diálogo de confirmação
    setConfirmDialogOpen(true);
    setShowOptionsMenu(false);
  }, [conversation, user, removeParticipantFromGroup]);

  // Função para adicionar múltiplos participantes
  const handleAddParticipants = useCallback((selectedUsers) => {
    if (!conversation || !selectedUsers || selectedUsers.length === 0) return;

    setAddingParticipants(true);

    addMultipleParticipantsToGroup(conversation.id, selectedUsers)
      .then((result) => {
        console.log('Resultado da adição de participantes:', result);

        if (result.success) {
          console.log(`${result.successCount} participantes adicionados com sucesso`);

          if (result.errorCount > 0) {
            console.warn(`${result.errorCount} participantes não puderam ser adicionados`);
            // Poderia mostrar um toast com essa informação
          }
        } else {
          console.error('Falha ao adicionar participantes');
          alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');
        }

        setShowAddParticipant(false);
      })
      .catch(error => {
        console.error('Erro ao adicionar participantes:', error);
        alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');
      })
      .finally(() => {
        setAddingParticipants(false);
      });
  }, [conversation, addMultipleParticipantsToGroup]);

  // Função para apagar conversa (sair da conversa)
  const handleDeleteConversation = useCallback(() => {
    if (!conversation || !user) return;

    // Configurar o diálogo de confirmação
    setConfirmAction({
      type: 'delete-conversation',
      title: 'Apagar conversa',
      message: 'Tem certeza que deseja apagar esta conversa? Esta ação não pode ser desfeita.',
      onConfirm: () => {
        // Para apagar uma conversa, usamos a mesma função de sair do grupo
        // No backend, isso marca o participante como tendo saído da conversa
        removeParticipantFromGroup(conversation.id, user.id)
          .then(() => {
            console.log('Conversa apagada com sucesso');

            // Disparar evento para atualizar a lista de conversas
            setTimeout(() => {
              console.log('Forçando atualização da lista de conversas após apagar conversa');
              window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                detail: {
                  type: 'conversations',
                  action: 'delete',
                  conversationId: conversation.id,
                  timestamp: Date.now()
                }
              }));
            }, 300);

            // A navegação de volta para a lista de conversas é tratada no contexto
          })
          .catch(error => {
            console.error('Erro ao apagar conversa:', error);
            alert('Não foi possível apagar a conversa. Tente novamente mais tarde.');
          });
      }
    });

    // Abrir o diálogo de confirmação
    setConfirmDialogOpen(true);
    setShowOptionsMenu(false);
  }, [conversation, user, removeParticipantFromGroup]);

  // Obter o nome da conversa - memoizado para evitar recalculos
  const getConversationName = useCallback(() => {
    if (!conversation) return 'Conversa';

    if (conversation.type === 'GROUP') {
      return conversation.title || 'Grupo';
    }

    // Encontrar o outro participante (não o usuário atual)
    const otherParticipant = conversation.participants?.find(
      p => (p.userId !== user?.id && p.clientId !== user?.id)
    );

    // Priorizar fullName para usuários e clientes
    if (otherParticipant?.user?.fullName) {
      return otherParticipant.user.fullName;
    }
    if (otherParticipant?.client) {
      // Usar o nome do paciente titular se disponível
      const clientPersonName = otherParticipant.client.clientPersons?.[0]?.person?.fullName;
      if (clientPersonName && clientPersonName.trim() !== '') {
        return clientPersonName;
      }
      // Fallback para fullName do cliente ou login
      return (otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '') 
        ? otherParticipant.client.fullName 
        : otherParticipant.client.login;
    }

    return 'Usuário';
  }, [conversation, user?.id]);

  // Obter a imagem da conversa - memoizado para evitar recalculos
  const getConversationImage = useCallback(() => {
    if (!conversation) return null;

    if (conversation.type === 'GROUP') {
      return null;
    }

    const otherParticipant = conversation.participants?.find(
      p => p.userId !== user?.id
    );

    return otherParticipant?.user?.profileImageUrl;
  }, [conversation, user?.id]);

  // Obter iniciais para avatar - memoizado para evitar recalculos
  const getInitials = useCallback((name) => {
    if (!name) return 'U';

    try {
      const names = name.split(' ');
      if (names.length === 1) return names[0].charAt(0);

      return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
    } catch (error) {
      console.error('Erro ao obter iniciais:', error);
      return 'U';
    }
  }, []);

  // Formatar data da mensagem - memoizado para evitar recalculos
  const formatMessageDate = useCallback((timestamp) => {
    if (!timestamp) return '';

    try {
      return format(new Date(timestamp), 'HH:mm', { locale: ptBR });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return '';
    }
  }, []);

  // Agrupar mensagens por data - memoizado para evitar recalculos
  const groupMessagesByDate = useCallback((messages) => {
    if (!messages || messages.length === 0) {
      console.log("Nenhuma mensagem para agrupar");
      return [];
    }

    console.log(`Agrupando ${messages.length} mensagens`);

    try {
      const groups = {};

      // Ordenar mensagens por data (mais antigas primeiro)
      const sortedMessages = [...messages].sort((a, b) =>
        new Date(a.createdAt) - new Date(b.createdAt)
      );

      sortedMessages.forEach(message => {
        if (!message || !message.createdAt) {
          console.warn("Mensagem inválida encontrada:", message);
          return;
        }

        try {
          const messageDate = new Date(message.createdAt);
          const date = format(messageDate, 'dd/MM/yyyy');

          if (!groups[date]) {
            groups[date] = [];
          }

          groups[date].push(message);
        } catch (err) {
          console.error("Erro ao processar mensagem:", err, message);
        }
      });

      // Verificar se temos grupos
      if (Object.keys(groups).length === 0) {
        console.warn("Nenhum grupo criado após processamento");
        return [];
      }

      // Ordenar os grupos por data (mais antigos primeiro)
      const result = Object.entries(groups)
        .sort(([dateA], [dateB]) => {
          // Converter strings de data para objetos Date para comparação
          const [dayA, monthA, yearA] = dateA.split('/').map(Number);
          const [dayB, monthB, yearB] = dateB.split('/').map(Number);
          return new Date(yearA, monthA - 1, dayA) - new Date(yearB, monthB - 1, dayB);
        })
        .map(([date, messages]) => ({
          date,
          messages
        }));

      console.log(`Criados ${result.length} grupos de mensagens`);
      return result;
    } catch (error) {
      console.error('Erro ao agrupar mensagens:', error);
      return [];
    }
  }, []);

  // Memoizar os grupos de mensagens para evitar recalculos
  const messageGroups = useMemo(() => {
    return groupMessagesByDate(conversationMessages);
  }, [groupMessagesByDate, conversationMessages]);

  // Se a conversa não for encontrada, mostrar mensagem de erro
  if (!conversation) {
    return (
      <div className="flex flex-col h-full">
        <div className="p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 text-white rounded-t-lg">
          <button
            onClick={onBack}
            className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
            aria-label="Voltar"
          >
            <ArrowLeft size={compact ? 16 : 20} />
          </button>
          <h3 className="font-medium text-white text-lg">
            Conversa não encontrada
          </h3>
        </div>
        <div className="flex-1 flex items-center justify-center p-4 text-gray-500 dark:text-gray-400">
          <p>A conversa não foi encontrada ou foi removida.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Cabeçalho */}
      <div className="p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white">
        <button
          onClick={onBack}
          className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
          aria-label="Voltar"
        >
          <ArrowLeft size={compact ? 16 : 20} />
        </button>

        <div className="flex items-center gap-2 flex-1">
          {getConversationImage() ? (
            <img
              src={getConversationImage()}
              alt={getConversationName()}
              className={`${compact ? 'h-8 w-8' : 'h-10 w-10'} rounded-full object-cover`}
              onError={(e) => {
                e.target.onerror = null;
                e.target.style.display = 'none';
              }}
            />
          ) : (
            <div className={`${compact ? 'h-8 w-8' : 'h-10 w-10'} rounded-full bg-white/20 flex items-center justify-center text-white font-medium shadow-sm`}>
              {getInitials(getConversationName())}
            </div>
          )}

          <div>
            <h3 className={`font-medium text-white ${compact ? 'text-sm' : 'text-base'}`}>
              {getConversationName()}
            </h3>
            {conversation?.isOnline && (
              <p className="text-xs text-white/80">Online</p>
            )}
          </div>
        </div>

        <div className="relative">
          <button
            onClick={() => setShowOptionsMenu(!showOptionsMenu)}
            className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
            aria-label="Mais opções"
          >
            <MoreVertical size={compact ? 16 : 20} />
          </button>

          {/* Menu de opções */}
          {showOptionsMenu && (
            <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 w-48 z-10">
              {isGroupChat ? (
                <>
                  <button
                    onClick={() => {
                      setShowOptionsMenu(false);
                      setShowParticipantsList(true);
                    }}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Users size={16} />
                    <span>Ver participantes</span>
                  </button>

                  {isGroupAdmin && (
                    <button
                      onClick={() => {
                        setShowOptionsMenu(false);
                        setShowAddParticipant(true);
                      }}
                      className="w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <UserPlus size={16} />
                      <span>Adicionar participante</span>
                    </button>
                  )}

                  <button
                    onClick={handleLeaveGroup}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <LogOut size={16} />
                    <span>Sair do grupo</span>
                  </button>
                  
                  {isGroupAdmin && (
                    <button
                      onClick={() => {
                        setConfirmAction({
                          type: 'delete-group',
                          title: 'Deletar grupo',
                          message: `Tem certeza que deseja deletar o grupo "${conversation.title || 'Grupo'}"? Todos os participantes serão removidos e esta ação não pode ser desfeita.`,
                          onConfirm: async () => {
                            console.log('Deletando grupo:', conversation.id);
                            try {
                              const currentToken = localStorage.getItem('token');
                              const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
                              console.log('Fazendo requisição DELETE para:', `${API_URL}/chat/conversations/${conversation.id}`);
                              
                              // Remover todos os participantes para deletar o grupo
                              const participants = conversation.participants || [];
                              for (const participant of participants) {
                                const participantId = participant.userId || participant.clientId;
                                if (participantId) {
                                  await fetch(`${API_URL}/chat/conversations/${conversation.id}/participants/${participantId}`, {
                                    method: 'DELETE',
                                    headers: { 'Authorization': `Bearer ${currentToken}` }
                                  });
                                }
                              }
                              
                              const response = { ok: true }; // Simular sucesso
                              
                              console.log('Resposta:', response.status, response.statusText);
                              
                              if (response.ok) {
                                console.log('Grupo deletado com sucesso');
                                // Usar removeParticipantFromGroup para remover o usuário atual
                                await removeParticipantFromGroup(conversation.id, user.id);
                                onBack();
                              } else {
                                const errorText = await response.text();
                                console.error('Erro na resposta:', errorText);
                                alert('Erro ao deletar grupo: ' + response.status);
                              }
                            } catch (error) {
                              console.error('Erro ao deletar grupo:', error);
                              alert('Erro ao deletar grupo: ' + error.message);
                            }
                          }
                        });
                        setConfirmDialogOpen(true);
                        setShowOptionsMenu(false);
                      }}
                      className="w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-t border-gray-200 dark:border-gray-700"
                    >
                      <Trash2 size={16} />
                      <span>Deletar grupo</span>
                    </button>
                  )}
                </>
              ) : (
                // Opções para conversas individuais
                <button
                  onClick={handleDeleteConversation}
                  className="w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <Trash2 size={16} />
                  <span>Apagar conversa</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Tela de adicionar participantes */}
      {showAddParticipant && (
        <div className="absolute inset-0 bg-background z-20 flex flex-col h-full overflow-hidden" style={{borderRadius: '0.75rem'}}>
          <MultiUserSearch
            onAddUsers={handleAddParticipants}
            onClose={() => setShowAddParticipant(false)}
            title="Adicionar participantes ao grupo"
          />

          {addingParticipants && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-30">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg flex flex-col items-center">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-cyan-500 mb-4"></div>
                <p className="text-gray-700 dark:text-gray-300">Adicionando participantes...</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Lista de participantes */}
      {showParticipantsList && (
        <div className="absolute inset-0 bg-background z-20 flex flex-col" style={{borderRadius: '0.75rem'}}>
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white">
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowParticipantsList(false)}
                className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
                aria-label="Voltar"
              >
                <ArrowLeft size={20} />
              </button>
              <h3 className="font-medium text-white text-base">Participantes do grupo</h3>
            </div>

            {isGroupAdmin && (
              <button
                onClick={() => {
                  setShowParticipantsList(false);
                  setShowAddParticipant(true);
                }}
                className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
                aria-label="Adicionar participante"
              >
                <UserPlus size={20} />
              </button>
            )}
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {conversation?.participants?.map(participant => {
                // Determinar se é usuário ou cliente e obter nome correto
                const getParticipantName = () => {
                  if (participant.userId === user?.id || participant.clientId === user?.id) {
                    return 'Você';
                  }
                  
                  // Se é usuário
                  if (participant.user?.fullName) {
                    return participant.user.fullName;
                  }
                  
                  // Se é cliente
                  if (participant.client) {
                    const clientPersonName = participant.client.clientPersons?.[0]?.person?.fullName;
                    if (clientPersonName && clientPersonName.trim() !== '') {
                      return clientPersonName;
                    }
                    return (participant.client.fullName && participant.client.fullName.trim() !== '') 
                      ? participant.client.fullName 
                      : participant.client.login;
                  }
                  
                  return 'Usuário';
                };
                
                const participantName = getParticipantName();
                const isCurrentUser = participant.userId === user?.id || participant.clientId === user?.id;
                const participantId = participant.userId || participant.clientId;
                
                return (
                  <div key={participant.id} className="flex items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    {/* Avatar */}
                    <div className="relative flex-shrink-0">
                      {participant.user?.profileImageUrl ? (
                        <img
                          src={participant.user.profileImageUrl}
                          alt={participantName}
                          className="h-10 w-10 rounded-full object-cover"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.style.display = 'none';
                            e.target.parentNode.innerHTML = `<div class="h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium">${getInitials(participantName)}</div>`;
                          }}
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium">
                          {getInitials(participantName)}
                        </div>
                      )}
                    </div>

                    {/* Informações */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                        {participantName}
                      </h4>
                      <div className="flex items-center gap-2">
                        {participant.isAdmin && (
                          <span className="text-xs text-cyan-600 dark:text-cyan-400 bg-cyan-50 dark:bg-cyan-900/30 px-2 py-0.5 rounded-full">
                            Administrador
                          </span>
                        )}
                        {participant.client && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            Cliente
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Botão de remover */}
                    {isGroupAdmin && !isCurrentUser && (
                      <button
                        onClick={() => {
                          if (window.confirm(`Tem certeza que deseja remover ${participantName} do grupo?`)) {
                            removeParticipantFromGroup(conversation.id, participantId)
                              .catch(error => {
                                console.error('Erro ao remover participante:', error);
                                alert('Não foi possível remover o participante. Tente novamente mais tarde.');
                              });
                          }
                        }}
                        className="p-1.5 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                        aria-label="Remover participante"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Mensagens */}
      <div className="flex-1 overflow-y-auto p-5 space-y-5">
        {messageGroups.map(group => (
          <div key={group.date} className="space-y-3">
            <div className="flex justify-center">
              <div className="px-3 py-1 bg-cyan-100 dark:bg-cyan-900/30 rounded-full text-xs text-cyan-700 dark:text-cyan-300 shadow-sm">
                {group.date}
              </div>
            </div>

            {group.messages.map(message => {
              const isOwnMessage = message.senderId === user?.id || message.senderClientId === user?.id;
              const isGroupChat = conversation.type === 'GROUP';

              // Encontrar o nome do remetente para mensagens de grupo
              const getSenderName = () => {
                if (message.senderId === user?.id || message.senderClientId === user?.id) {
                  return 'Você';
                }

                // Se a mensagem tem sender (usuário)
                if (message.sender?.fullName) {
                  return message.sender.fullName;
                }
                
                // Se a mensagem tem senderClient (cliente)
                if (message.senderClient) {
                  // Usar o nome do paciente titular se disponível
                  const clientPersonName = message.senderClient.clientPersons?.[0]?.person?.fullName;
                  if (clientPersonName && clientPersonName.trim() !== '') {
                    return clientPersonName;
                  }
                  // Fallback para fullName do cliente ou login
                  return (message.senderClient.fullName && message.senderClient.fullName.trim() !== '') 
                    ? message.senderClient.fullName 
                    : message.senderClient.login;
                }

                return 'Usuário';
              };

              // Obter as iniciais do remetente para mensagens de grupo
              const getSenderInitials = () => {
                if (isOwnMessage) return 'Você';

                const senderName = getSenderName();
                return getInitials(senderName);
              };

              return (
                <div
                  key={message.id}
                  className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                >
                  {/* Avatar do remetente (apenas para mensagens de grupo e que não são do usuário atual) */}
                  {isGroupChat && !isOwnMessage && (
                    <div className="mr-2 flex flex-col items-center justify-start">
                      <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-xs font-medium">
                        {getSenderInitials()}
                      </div>
                    </div>
                  )}

                  <div className={`max-w-[75%] ${compact ? 'max-w-[85%]' : ''}`}>
                    {/* Nome do remetente (apenas para mensagens de grupo e que não são do usuário atual) */}
                    {isGroupChat && !isOwnMessage && (
                      <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 ml-1">
                        {getSenderName()}
                      </div>
                    )}

                    <div
                      className={`px-4 py-3 rounded-lg shadow-sm ${
                        isOwnMessage
                          ? 'bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white rounded-br-none'
                          : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none border border-cyan-100 dark:border-cyan-800'
                      }`}
                    >
                      {message.contentType && ['SHARED_APPOINTMENT', 'SHARED_PERSON', 'SHARED_CLIENT', 'SHARED_USER', 'SHARED_SERVICE_TYPE', 'SHARED_LOCATION', 'SHARED_WORKING_HOURS', 'SHARED_INSURANCE', 'SHARED_INSURANCE_LIMIT'].includes(message.contentType) ? (
                        <SharedItemMessage message={message} onItemClick={handleSharedItemClickWithLog} />
                      ) : message.contentType === 'ATTACHMENT' && message.metadata?.attachments ? (
                        <div className="space-y-2">
                          {/* Anexos */}
                          {message.metadata.attachments.map((attachment, index) => (
                            <AttachmentViewer 
                              key={attachment.id || index} 
                              attachment={attachment} 
                              compact={true}
                            />
                          ))}
                          {/* Texto da mensagem (se existir) */}
                          {message.content && (
                            <div className="mt-2">
                              {message.content}
                            </div>
                          )}
                        </div>
                      ) : (
                        message.content
                      )}
                    </div>
                    <div className={`text-xs mt-1.5 ${isOwnMessage ? 'text-right' : ''} text-gray-500 dark:text-gray-400`}>
                      {formatMessageDate(message.createdAt)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ))}

        {conversationMessages.length === 0 && (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <p className="text-center text-sm">
              Nenhuma mensagem ainda. Comece a conversar!
            </p>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <ChatInput conversationId={conversationId} compact={compact} />

      {/* Diálogo de confirmação */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={confirmAction.onConfirm}
        title={confirmAction.title}
        message={confirmAction.message}
        confirmText="Confirmar"
        cancelText="Cancelar"
        variant="warning"
      />
    </div>
  );
};

export default ChatConversation;
