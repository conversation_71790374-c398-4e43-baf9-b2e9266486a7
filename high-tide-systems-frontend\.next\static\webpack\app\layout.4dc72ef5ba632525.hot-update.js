"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ee330d5732cb\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFByb2dyYW1hY2FvXFxISUdIIFRJREUgU1lTVEVNU1xcaGlnaC10aWRlLXN5c3RlbXMtZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVlMzMwZDU3MzJjYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chat/ChatList.js":
/*!*****************************************!*\
  !*** ./src/components/chat/ChatList.js ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock,MessageCircle,MoreVertical,Plus,Search,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck,Clock,MessageCircle,MoreVertical,Plus,Search,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ChatList = (param)=>{\n    let { searchQuery = '' } = param;\n    _s();\n    const { conversations, setActiveConversation, messages, loadConversations, isLoading } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Estado para forçar re-renderização\n    const [updateTrigger, setUpdateTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Carregar conversas quando o componente é montado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatList.useEffect\": ()=>{\n            // Carregar conversas inicialmente\n            console.log('ChatList montado, verificando se é necessário carregar conversas...');\n            if (user && user.id) {\n                console.log('Usuário logado:', user.id);\n                // Carregar conversas inicialmente\n                console.log('Carregando conversas iniciais');\n                loadConversations(false); // Usar cache se disponível na primeira carga\n            } else {\n                console.log('Usuário não logado, não carregando conversas');\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatList.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o usuário mudar\n    // Adicionar listener para eventos de atualização do WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatList.useEffect\": ()=>{\n            // Função para lidar com eventos de atualização\n            const handleWebSocketUpdate = {\n                \"ChatList.useEffect.handleWebSocketUpdate\": (event)=>{\n                    const { type, action, conversationId } = event.detail;\n                    console.log(\"ChatList recebeu evento de atualiza\\xe7\\xe3o: \".concat(type), event.detail);\n                    // Forçar re-renderização para qualquer tipo de evento\n                    setUpdateTrigger({\n                        \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                    }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                    // Se for uma atualização de conversas, verificar se precisamos recarregar\n                    if (type === 'conversations') {\n                        // Se for uma ação de exclusão, não precisamos recarregar as conversas\n                        // pois o estado já foi atualizado no ChatContext\n                        if (action === 'delete') {\n                            console.log(\"ChatList: Conversa \".concat(conversationId, \" foi apagada, atualizando interface\"));\n                            // Apenas forçar re-renderização\n                            setUpdateTrigger({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        } else if (conversations.length === 0) {\n                            // Só recarregar se não tivermos conversas\n                            console.log('ChatList: Não há conversas carregadas, recarregando após evento de atualização');\n                            loadConversations(false).then({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": ()=>{\n                                    console.log('ChatList: Conversas carregadas com sucesso');\n                                    // Forçar outra re-renderização após o carregamento\n                                    setUpdateTrigger({\n                                        \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                                    }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                                }\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        } else {\n                            console.log('ChatList: Já existem conversas carregadas, apenas atualizando interface');\n                            // Apenas forçar re-renderização\n                            setUpdateTrigger({\n                                \"ChatList.useEffect.handleWebSocketUpdate\": (prev)=>prev + 1\n                            }[\"ChatList.useEffect.handleWebSocketUpdate\"]);\n                        }\n                    }\n                }\n            }[\"ChatList.useEffect.handleWebSocketUpdate\"];\n            // Adicionar listener\n            window.addEventListener('chat:websocket:update', handleWebSocketUpdate);\n            // Remover listener quando o componente for desmontado\n            return ({\n                \"ChatList.useEffect\": ()=>{\n                    window.removeEventListener('chat:websocket:update', handleWebSocketUpdate);\n                }\n            })[\"ChatList.useEffect\"];\n        }\n    }[\"ChatList.useEffect\"], [\n        loadConversations\n    ]); // Dependência: loadConversations\n    // Filtrar conversas com base na pesquisa\n    const filteredConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatList.useMemo[filteredConversations]\": ()=>{\n            // Log para debug\n            console.log(\"Recalculando filteredConversations. Trigger: \".concat(updateTrigger, \", Conversas: \").concat(conversations.length));\n            if (!searchQuery) return conversations;\n            return conversations.filter({\n                \"ChatList.useMemo[filteredConversations]\": (conversation)=>{\n                    var _conversation_title;\n                    // Para conversas individuais, pesquisar pelo nome do outro participante\n                    if (conversation.type === 'INDIVIDUAL') {\n                        var _conversation_participants, _otherParticipant_user;\n                        const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                            \"ChatList.useMemo[filteredConversations]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n                        }[\"ChatList.useMemo[filteredConversations]\"]);\n                        // Buscar pelo fullName do usuário ou cliente\n                        if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                            return otherParticipant.user.fullName.toLowerCase().includes(searchQuery.toLowerCase());\n                        }\n                        if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                            var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                            const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                            let clientName;\n                            if (clientPersonName && clientPersonName.trim() !== '') {\n                                clientName = clientPersonName;\n                            } else {\n                                clientName = otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n                            }\n                            return clientName.toLowerCase().includes(searchQuery.toLowerCase());\n                        }\n                        return false;\n                    }\n                    // Para grupos, pesquisar pelo título\n                    return (_conversation_title = conversation.title) === null || _conversation_title === void 0 ? void 0 : _conversation_title.toLowerCase().includes(searchQuery.toLowerCase());\n                }\n            }[\"ChatList.useMemo[filteredConversations]\"]);\n        }\n    }[\"ChatList.useMemo[filteredConversations]\"], [\n        conversations,\n        searchQuery,\n        user === null || user === void 0 ? void 0 : user.id,\n        updateTrigger\n    ]); // Adicionado updateTrigger como dependência\n    // Obter o nome da conversa - memoizado para evitar recalculos\n    const getConversationName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getConversationName]\": (conversation)=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return 'Conversa';\n            if (conversation.type === 'GROUP') {\n                return conversation.title || 'Grupo';\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatList.useCallback[getConversationName]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatList.useCallback[getConversationName]\"]);\n            // Retornar nome do usuário ou cliente\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                return otherParticipant.user.fullName;\n            }\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                if (clientPersonName && clientPersonName.trim() !== '') {\n                    return clientPersonName;\n                }\n                return otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n            }\n            return 'Usuário';\n        }\n    }[\"ChatList.useCallback[getConversationName]\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter a imagem da conversa - memoizado para evitar recalculos\n    const getConversationImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getConversationImage]\": (conversation)=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return null;\n            if (conversation.type === 'GROUP') {\n                return null; // Usar um ícone de grupo\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatList.useCallback[getConversationImage]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatList.useCallback[getConversationImage]\"]);\n            return otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.profileImageUrl;\n        }\n    }[\"ChatList.useCallback[getConversationImage]\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Formatar a data da última mensagem - memoizado para evitar recalculos\n    const formatLastMessageTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[formatLastMessageTime]\": (timestamp)=>{\n            if (!timestamp) return '';\n            try {\n                return (0,_barrel_optimize_names_format_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(new Date(timestamp), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_5__.ptBR\n                });\n            } catch (error) {\n                console.error('Erro ao formatar data:', error);\n                return '';\n            }\n        }\n    }[\"ChatList.useCallback[formatLastMessageTime]\"], []);\n    // Obter iniciais para avatar - memoizado para evitar recalculos\n    const getInitials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatList.useCallback[getInitials]\": (name)=>{\n            if (!name) return 'U';\n            try {\n                const names = name.split(' ');\n                if (names.length === 1) return names[0].charAt(0);\n                return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n            } catch (error) {\n                console.error('Erro ao obter iniciais:', error);\n                return 'U';\n            }\n        }\n    }[\"ChatList.useCallback[getInitials]\"], []);\n    // Log para depuração - quando o componente é renderizado\n    console.log(\"ChatList renderizando. Trigger: \".concat(updateTrigger, \", Conversas: \").concat(conversations.length, \", Filtradas: \").concat(filteredConversations.length));\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.3,\n                ease: \"easeOut\"\n            }\n        },\n        exit: {\n            opacity: 0,\n            y: -20,\n            transition: {\n                duration: 0.2\n            }\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    if (filteredConversations.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full p-6 text-gray-500 dark:text-gray-400 relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-4 right-4 flex flex-col items-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 232,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-blue-600 dark:text-blue-400 font-medium\",\n                                    children: \"Carregando...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 231,\n                            columnNumber: 23\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        scale: 0.8,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 48,\n                            className: \"mx-auto mb-3 text-blue-500 dark:text-blue-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2\",\n                            children: searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa iniciada'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: searchQuery ? 'Tente ajustar sua pesquisa' : 'Inicie uma conversa para começar a trocar mensagens'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        className: \"relative\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 flex flex-col items-end z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-blue-600 dark:text-blue-400 font-medium\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                        lineNumber: 271,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1 p-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: filteredConversations.map((conversation, index)=>{\n                        const name = getConversationName(conversation);\n                        const image = getConversationImage(conversation);\n                        // Verificar se há mensagens carregadas para esta conversa\n                        const conversationMessages = messages[conversation.id] || [];\n                        // Usar a última mensagem da conversa ou a última mensagem carregada\n                        const lastMessage = conversation.lastMessage || (conversationMessages.length > 0 ? conversationMessages[conversationMessages.length - 1] : null);\n                        // Verificar se há mensagens não lidas (usar hasUnread do backend ou fallback para unreadCount)\n                        const hasUnread = conversation.hasUnread || conversation.unreadCount > 0;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                            variants: itemVariants,\n                            initial: \"hidden\",\n                            animate: \"visible\",\n                            exit: \"exit\",\n                            onClick: ()=>setActiveConversation(conversation.id),\n                            className: \"w-full flex items-center gap-4 p-4 transition-all duration-300 text-left rounded-2xl group relative overflow-hidden \".concat(hasUnread ? 'bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/30 dark:to-blue-900/30 hover:from-cyan-100 hover:to-blue-100 dark:hover:from-cyan-800/40 dark:hover:to-blue-800/40 border-l-4 border-cyan-500 dark:border-cyan-400 shadow-md' : 'hover:bg-gradient-to-r hover:from-cyan-50 hover:to-cyan-100 dark:hover:from-cyan-900/20 dark:hover:to-cyan-800/20'),\n                            whileHover: {\n                                scale: 1.02,\n                                transition: {\n                                    duration: 0.2\n                                }\n                            },\n                            whileTap: {\n                                scale: 0.98,\n                                transition: {\n                                    duration: 0.1\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 transition-opacity duration-300 \".concat(hasUnread ? 'bg-gradient-to-r from-cyan-500/10 via-blue-500/5 to-cyan-700/10 opacity-50 group-hover:opacity-70' : 'bg-gradient-to-r from-cyan-500/5 via-transparent to-cyan-700/5 opacity-0 group-hover:opacity-100')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-shrink-0 z-10\",\n                                    children: image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image,\n                                                alt: name,\n                                                className: \"h-12 w-12 rounded-2xl object-cover transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200 shadow-lg' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 338,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 21\n                                    }, undefined) : conversation.type === 'GROUP' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white shadow-lg transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_Clock_MessageCircle_MoreVertical_Plus_Search_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white font-semibold shadow-lg transition-all duration-300 \".concat(hasUnread ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200' : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'),\n                                                children: getInitials(name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            conversation.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                lineNumber: 364,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 355,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0 z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900 dark:text-gray-100 truncate group-hover:text-cyan-600 dark:group-hover:text-cyan-400 transition-colors duration-300\",\n                                                    children: name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-2\",\n                                                    children: formatLastMessageTime(lastMessage.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        lastMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm truncate transition-colors duration-300 \".concat(hasUnread ? 'font-medium text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'),\n                                            children: lastMessage.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 384,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 italic\",\n                                            children: \"Nenhuma mensagem\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 392,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 371,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    children: hasUnread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        exit: {\n                                            scale: 0,\n                                            opacity: 0\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 500,\n                                            damping: 30\n                                        },\n                                        className: \"flex-shrink-0 z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 w-3 rounded-full bg-gradient-to-r from-cyan-500 to-cyan-600 shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                        lineNumber: 401,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, conversation.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                            lineNumber: 292,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatList.js\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatList, \"XFZkOmi5v6Y4DQyDJMfXLBkr4gg=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ChatList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatList);\nvar _c;\n$RefreshReg$(_c, \"ChatList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatList.js\n"));

/***/ })

});