const express = require('express');
const router = express.Router();
const bugReportController = require('../controllers/bugReportController');
const authenticate = require('../middlewares/auth');
const checkPermission = require('../middlewares/permissionCheck');
const { body, param, query } = require('express-validator');
const { handleValidationErrors } = require('../middlewares/validation');

// Validações
const createBugReportValidation = [
  body('title')
    .notEmpty()
    .withMessage('Título é obrigatório')
    .isLength({ max: 100 })
    .withMessage('Título deve ter no máximo 100 caracteres'),
  body('description')
    .notEmpty()
    .withMessage('Descrição é obrigatória')
    .isLength({ max: 2000 })
    .withMessage('Descrição deve ter no máximo 2000 caracteres'),
  body('location')
    .optional()
    .isLength({ max: 150 })
    .withMessage('Local deve ter no máximo 150 caracteres'),
  body('category')
    .optional()
    .isIn(['GENERAL', 'UI_UX', 'PERFORMANCE', 'FUNCTIONALITY', 'DATA', 'SECURITY', 'INTEGRATION'])
    .withMessage('Categoria inválida'),
  body('priority')
    .optional()
    .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
    .withMessage('Prioridade inválida'),
];

const updateStatusValidation = [
  param('id')
    .isUUID()
    .withMessage('ID inválido'),
  body('status')
    .optional()
    .isIn(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'])
    .withMessage('Status inválido'),
  body('priority')
    .optional()
    .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
    .withMessage('Prioridade inválida'),
  body('adminNotes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notas do admin devem ter no máximo 1000 caracteres'),
];

const listValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Página deve ser um número positivo'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite deve ser entre 1 e 100'),
  query('status')
    .optional()
    .isIn(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'])
    .withMessage('Status inválido'),
  query('priority')
    .optional()
    .isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
    .withMessage('Prioridade inválida'),
  query('category')
    .optional()
    .isIn(['GENERAL', 'UI_UX', 'PERFORMANCE', 'FUNCTIONALITY', 'DATA', 'SECURITY', 'INTEGRATION'])
    .withMessage('Categoria inválida'),
];

const getByIdValidation = [
  param('id')
    .isUUID()
    .withMessage('ID inválido'),
];

// Middleware para verificar se é SYSTEM_ADMIN
const requireSystemAdmin = (req, res, next) => {
  if (req.user.role !== 'SYSTEM_ADMIN') {
    return res.status(403).json({ 
      message: 'Acesso negado. Apenas administradores do sistema podem acessar este recurso.' 
    });
  }
  next();
};

// Rotas públicas para usuários autenticados

/**
 * @swagger
 * /bug-reports:
 *   post:
 *     summary: Criar um novo relatório de bug
 *     tags: [Bug Reports]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *             properties:
 *               title:
 *                 type: string
 *                 maxLength: 100
 *                 example: "Erro ao salvar dados do paciente"
 *               description:
 *                 type: string
 *                 maxLength: 2000
 *                 example: "Quando tento salvar os dados do paciente, aparece um erro 500"
 *               location:
 *                 type: string
 *                 maxLength: 150
 *                 example: "Módulo Pessoas > Cadastro de Pacientes"
 *               category:
 *                 type: string
 *                 enum: [GENERAL, UI_UX, PERFORMANCE, FUNCTIONALITY, DATA, SECURITY, INTEGRATION]
 *                 example: "FUNCTIONALITY"
 *               priority:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *                 example: "MEDIUM"
 *     responses:
 *       201:
 *         description: Bug reportado com sucesso
 *       400:
 *         description: Dados inválidos
 *       401:
 *         description: Não autorizado
 */
router.post('/', 
  authenticate, 
  createBugReportValidation, 
  handleValidationErrors, 
  bugReportController.create
);

/**
 * @swagger
 * /bug-reports:
 *   get:
 *     summary: Listar relatórios de bug do usuário
 *     tags: [Bug Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Itens por página
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [OPEN, IN_PROGRESS, RESOLVED, CLOSED]
 *         description: Filtrar por status
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *         description: Filtrar por prioridade
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [GENERAL, UI_UX, PERFORMANCE, FUNCTIONALITY, DATA, SECURITY, INTEGRATION]
 *         description: Filtrar por categoria
 *     responses:
 *       200:
 *         description: Lista de relatórios de bug
 *       401:
 *         description: Não autorizado
 */
router.get('/', 
  authenticate, 
  listValidation, 
  handleValidationErrors, 
  bugReportController.list
);

/**
 * @swagger
 * /bug-reports/{id}:
 *   get:
 *     summary: Obter detalhes de um relatório de bug
 *     tags: [Bug Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do relatório de bug
 *     responses:
 *       200:
 *         description: Detalhes do relatório de bug
 *       404:
 *         description: Relatório não encontrado
 *       401:
 *         description: Não autorizado
 */
router.get('/:id', 
  authenticate, 
  getByIdValidation, 
  handleValidationErrors, 
  bugReportController.getById
);

// Rotas administrativas (apenas SYSTEM_ADMIN)

/**
 * @swagger
 * /bug-reports/admin/all:
 *   get:
 *     summary: Listar todos os relatórios de bug (apenas SYSTEM_ADMIN)
 *     tags: [Bug Reports - Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Itens por página
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [OPEN, IN_PROGRESS, RESOLVED, CLOSED]
 *         description: Filtrar por status
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *         description: Filtrar por prioridade
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [GENERAL, UI_UX, PERFORMANCE, FUNCTIONALITY, DATA, SECURITY, INTEGRATION]
 *         description: Filtrar por categoria
 *       - in: query
 *         name: companyId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filtrar por empresa
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Buscar por texto
 *     responses:
 *       200:
 *         description: Lista de todos os relatórios de bug
 *       403:
 *         description: Acesso negado
 *       401:
 *         description: Não autorizado
 */
router.get('/admin/all', 
  authenticate, 
  requireSystemAdmin, 
  listValidation, 
  handleValidationErrors, 
  bugReportController.listAll
);

/**
 * @swagger
 * /bug-reports/admin/stats:
 *   get:
 *     summary: Obter estatísticas dos bugs (apenas SYSTEM_ADMIN)
 *     tags: [Bug Reports - Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: companyId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filtrar por empresa
 *     responses:
 *       200:
 *         description: Estatísticas dos bugs
 *       403:
 *         description: Acesso negado
 *       401:
 *         description: Não autorizado
 */
router.get('/admin/stats', 
  authenticate, 
  requireSystemAdmin, 
  bugReportController.getStats
);

/**
 * @swagger
 * /bug-reports/{id}/status:
 *   patch:
 *     summary: Atualizar status/prioridade de um bug (apenas SYSTEM_ADMIN)
 *     tags: [Bug Reports - Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do relatório de bug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [OPEN, IN_PROGRESS, RESOLVED, CLOSED]
 *                 example: "IN_PROGRESS"
 *               priority:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *                 example: "HIGH"
 *               adminNotes:
 *                 type: string
 *                 maxLength: 1000
 *                 example: "Bug confirmado, iniciando correção"
 *     responses:
 *       200:
 *         description: Bug atualizado com sucesso
 *       404:
 *         description: Bug não encontrado
 *       403:
 *         description: Acesso negado
 *       401:
 *         description: Não autorizado
 */
router.patch('/:id/status', 
  authenticate, 
  requireSystemAdmin, 
  updateStatusValidation, 
  handleValidationErrors, 
  bugReportController.updateStatus
);

module.exports = router;
