"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/ChatContext.js":
/*!*************************************!*\
  !*** ./src/contexts/ChatContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n// Cache para evitar requisições duplicadas\nconst requestCache = {\n    conversations: {\n        data: null,\n        timestamp: 0\n    },\n    unreadCount: {\n        data: null,\n        timestamp: 0\n    },\n    messages: {},\n    tokenCheck: {\n        timestamp: 0,\n        valid: false\n    } // Cache para verificação de token\n};\n// Tempo de expiração do cache em milissegundos\nconst CACHE_EXPIRATION = 30000; // 30 segundos para dados normais\nconst TOKEN_CHECK_EXPIRATION = 60000; // 1 minuto para verificação de token\n// Função de debounce para limitar a frequência de chamadas\nconst debounce = (func, wait)=>{\n    let timeout;\n    return function executedFunction() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n};\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const { user, logout } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversation, setActiveConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPanelOpen, setIsPanelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Obter o token atual do localStorage - memoizado para evitar chamadas desnecessárias\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"ChatProvider.useCallback[getCurrentToken]\"], []);\n    // Função para lidar com erros de autenticação\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[handleAuthError]\": ()=>{\n            console.error('Erro de autenticação detectado. Redirecionando para login...');\n            // Limpar dados locais\n            setConversations([]);\n            setMessages({});\n            setActiveConversation(null);\n            setUnreadCount(0);\n            setIsPanelOpen(false);\n            setIsModalOpen(false);\n            // Desconectar socket se existir\n            if (socket) {\n                try {\n                    socket.disconnect();\n                } catch (error) {\n                    console.error('Erro ao desconectar socket:', error);\n                }\n                setIsConnected(false);\n            }\n            // Limpar cache\n            requestCache.conversations.data = null;\n            requestCache.unreadCount.data = null;\n            requestCache.messages = {}; // Limpar cache de mensagens\n            // Redirecionar para login\n            if (logout) {\n                logout();\n            }\n        }\n    }[\"ChatProvider.useCallback[handleAuthError]\"], [\n        socket,\n        logout\n    ]);\n    // Usar referências para controlar estados que não devem causar re-renderizações\n    const socketInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isLoadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const lastInitAttemptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // Redefinir a referência quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário for null (logout), desconectar o socket\n            if (!user && socket) {\n                try {\n                    socket.disconnect();\n                    setIsConnected(false);\n                    setSocket(null);\n                } catch (error) {\n                    console.error('Erro ao desconectar socket após logout:', error);\n                }\n            }\n            // Resetar estado quando o usuário muda\n            socketInitializedRef.current = false;\n            lastInitAttemptRef.current = 0;\n            reconnectAttemptsRef.current = 0;\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        socket\n    ]);\n    // Limpar estado do chat quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário mudou (novo login), limpar todo o estado do chat\n            if (user) {\n                console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);\n                // Limpar conversas e mensagens para o novo usuário\n                setConversations([]);\n                setMessages({});\n                setActiveConversation(null);\n                setUnreadCount(0);\n                setIsPanelOpen(false);\n                setIsModalOpen(false);\n                // Limpar cache\n                requestCache.conversations.data = null;\n                requestCache.unreadCount.data = null;\n                requestCache.messages = {};\n                requestCache.tokenCheck.timestamp = 0;\n                requestCache.tokenCheck.valid = false;\n                console.log('Cache limpo para novo usuário');\n                // Limpar dados residuais do localStorage relacionados ao chat\n                if (true) {\n                    // Limpar qualquer cache específico do chat que possa estar no localStorage\n                    const keysToRemove = [];\n                    for(let i = 0; i < localStorage.length; i++){\n                        const key = localStorage.key(i);\n                        if (key && (key.startsWith('chat_') || key.startsWith('conversation_') || key.startsWith('message_'))) {\n                            keysToRemove.push(key);\n                        }\n                    }\n                    keysToRemove.forEach({\n                        \"ChatProvider.useEffect\": (key)=>localStorage.removeItem(key)\n                    }[\"ChatProvider.useEffect\"]);\n                    console.log('Dados residuais do chat removidos do localStorage');\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o ID do usuário mudar\n    // Inicializar conexão WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se não há usuário, não inicializar\n            if (!user) {\n                console.log('Usuário não logado, não inicializando WebSocket');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Inicializando chat para usuário:', user.role || 'USER');\n            // Se já existe um socket conectado, não fazer nada\n            if (socket && isConnected) {\n                console.log('WebSocket já está conectado, não é necessário reinicializar');\n                return;\n            }\n            // Se já está inicializado e a última tentativa foi recente, não tentar novamente\n            if (socketInitializedRef.current && Date.now() - lastInitAttemptRef.current < 60000) {\n                console.log('Socket já inicializado recentemente, aguardando...');\n                return;\n            }\n            // Marcar como inicializado imediatamente para evitar múltiplas tentativas\n            socketInitializedRef.current = true;\n            lastInitAttemptRef.current = Date.now();\n            // Função assíncrona para inicializar o WebSocket\n            const initializeWebSocket = {\n                \"ChatProvider.useEffect.initializeWebSocket\": async ()=>{\n                    // Verificar se o usuário está logado antes de inicializar o WebSocket\n                    if (!user) return;\n                    // Clientes agora podem usar o chat\n                    console.log('Inicializando WebSocket para:', user.role || 'USER');\n                    const currentToken = getCurrentToken();\n                    if (!currentToken) return;\n                    // Evitar reconexões desnecessárias - verificação adicional\n                    if (socket) {\n                        if (isConnected) {\n                            console.log('WebSocket já conectado, ignorando inicialização');\n                            return socket; // Retornar o socket existente\n                        } else {\n                            // Se o socket existe mas não está conectado, tentar reconectar em vez de criar um novo\n                            console.log('Socket existe mas não está conectado, tentando reconectar');\n                            try {\n                                // Tentar reconectar o socket existente em vez de criar um novo\n                                if (!socket.connected && socket.connect) {\n                                    socket.connect();\n                                    console.log('Tentativa de reconexão iniciada');\n                                    return socket;\n                                }\n                            } catch (error) {\n                                console.error('Erro ao reconectar socket existente:', error);\n                                // Só desconectar se a reconexão falhar\n                                try {\n                                    socket.disconnect();\n                                } catch (disconnectError) {\n                                    console.error('Erro ao desconectar socket após falha na reconexão:', disconnectError);\n                                }\n                            }\n                        }\n                    }\n                    // Marcar como inicializado para evitar múltiplas inicializações\n                    socketInitializedRef.current = true;\n                    // Limitar a frequência de reconexões\n                    const maxReconnectAttempts = 5;\n                    const reconnectDelay = 3000; // 3 segundos\n                    console.log('Inicializando WebSocket...');\n                    try {\n                        // Verificar se o token é válido antes de inicializar o WebSocket\n                        // Usar uma verificação mais simples para evitar múltiplas requisições\n                        // Assumir que o token é válido se o usuário está logado\n                        console.log('Token válido, inicializando WebSocket para o usuário:', user.fullName);\n                        console.log('Inicializando WebSocket com autenticação');\n                        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(API_URL, {\n                            path: '/socket.io',\n                            auth: {\n                                token: currentToken\n                            },\n                            transports: [\n                                'websocket',\n                                'polling'\n                            ],\n                            reconnectionAttempts: maxReconnectAttempts,\n                            reconnectionDelay: reconnectDelay,\n                            timeout: 10000,\n                            autoConnect: true,\n                            reconnection: true\n                        });\n                        socketInstance.on('connect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket connected');\n                                setIsConnected(true);\n                                reconnectAttemptsRef.current = 0;\n                                // Disparar evento personalizado para notificar componentes sobre a conexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'connected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('disconnect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket disconnected');\n                                setIsConnected(false);\n                                // Disparar evento personalizado para notificar componentes sobre a desconexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'disconnected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('connect_error', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (error)=>{\n                                console.error('WebSocket connection error:', error);\n                                reconnectAttemptsRef.current++;\n                                // Se o erro for de autenticação, não tentar reconectar\n                                if (error.message && error.message.includes('Authentication error')) {\n                                    console.error('Erro de autenticação no WebSocket, não tentando reconectar');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                    return;\n                                }\n                                if (reconnectAttemptsRef.current >= maxReconnectAttempts) {\n                                    console.error('Máximo de tentativas de reconexão atingido');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para receber novas mensagens\n                        socketInstance.on('message:new', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (message)=>{\n                                if (!message || !message.conversationId) {\n                                    console.error('Mensagem inválida recebida:', message);\n                                    return;\n                                }\n                                // Usar uma função anônima para evitar dependências circulares\n                                const userId = user === null || user === void 0 ? void 0 : user.id;\n                                // Verificar se a mensagem tem um tempId associado (para substituir mensagem temporária)\n                                // O backend pode não retornar o tempId, então precisamos verificar se é uma mensagem do usuário atual\n                                const tempId = message.tempId;\n                                const isCurrentUserMessage = message.senderId === userId;\n                                // Atualizar mensagens da conversa\n                                setMessages({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        const conversationMessages = prev[message.conversationId] || [];\n                                        // Verificar se a mensagem já existe para evitar duplicação\n                                        if (conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === message.id\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return prev;\n                                        }\n                                        // Se tiver um tempId, substituir a mensagem temporária pela mensagem real\n                                        if (tempId && conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return {\n                                                ...prev,\n                                                [message.conversationId]: conversationMessages.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId ? message : m\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                            };\n                                        }\n                                        // Se for uma mensagem do usuário atual, verificar se há mensagens temporárias recentes\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId) {\n                                            // Procurar por mensagens temporárias recentes (nos últimos 10 segundos)\n                                            const now = new Date();\n                                            const tempMessages = conversationMessages.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket.tempMessages\": (m)=>{\n                                                    if (!m.isTemp) return false;\n                                                    if (!(m.senderId === userId || m.senderClientId === userId)) return false;\n                                                    if (now - new Date(m.createdAt) >= 10000) return false; // 10 segundos\n                                                    // Para mensagens com anexos, verificar se ambas são ATTACHMENT\n                                                    if (message.contentType === 'ATTACHMENT' && m.contentType === 'ATTACHMENT') {\n                                                        return true;\n                                                    }\n                                                    // Para mensagens de texto, verificar se o conteúdo é igual\n                                                    if (message.contentType === 'TEXT' && m.contentType === 'TEXT' && m.content === message.content) {\n                                                        return true;\n                                                    }\n                                                    return false;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket.tempMessages\"]);\n                                            if (tempMessages.length > 0) {\n                                                // Substituir a primeira mensagem temporária encontrada\n                                                const tempMessage = tempMessages[0];\n                                                return {\n                                                    ...prev,\n                                                    [message.conversationId]: conversationMessages.map({\n                                                        \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempMessage.id ? message : m\n                                                    }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                };\n                                            }\n                                        }\n                                        // Caso contrário, adicionar a nova mensagem\n                                        // Manter a ordem cronológica (mais antigas primeiro)\n                                        const updatedMessages = [\n                                            ...conversationMessages,\n                                            message\n                                        ].sort({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedMessages\"]);\n                                        return {\n                                            ...prev,\n                                            [message.conversationId]: updatedMessages\n                                        };\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Atualizar lista de conversas (mover para o topo)\n                                setConversations({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        // Verificar se a conversa existe\n                                        const conversation = prev.find({\n                                            \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                        if (!conversation) return prev;\n                                        // Se a conversa já tiver uma mensagem temporária com o mesmo tempId, não mover para o topo\n                                        if (tempId && conversation.lastMessage && conversation.lastMessage.id === tempId) {\n                                            // Apenas atualizar a última mensagem sem reordenar\n                                            return prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                    if (c.id === message.conversationId) {\n                                                        return {\n                                                            ...c,\n                                                            lastMessage: message\n                                                        };\n                                                    }\n                                                    return c;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        }\n                                        // Se for uma mensagem do usuário atual e não tiver tempId, verificar se há uma mensagem temporária recente\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId && conversation.lastMessage && conversation.lastMessage.isTemp) {\n                                            // Verificar se a última mensagem é temporária e tem o mesmo conteúdo\n                                            if (conversation.lastMessage.content === message.content && (conversation.lastMessage.senderId === userId || conversation.lastMessage.senderClientId === userId)) {\n                                                // Apenas atualizar a última mensagem sem reordenar\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                        if (c.id === message.conversationId) {\n                                                            return {\n                                                                ...c,\n                                                                lastMessage: message\n                                                            };\n                                                        }\n                                                        return c;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }\n                                        // Caso contrário, mover para o topo\n                                        const updatedConversations = prev.filter({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedConversations\": (c)=>c.id !== message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedConversations\"]);\n                                        return [\n                                            {\n                                                ...conversation,\n                                                lastMessage: message\n                                            },\n                                            ...updatedConversations\n                                        ];\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Marcar conversa como tendo mensagens não lidas se a mensagem não for do usuário atual\n                                const isFromCurrentUser = message.senderId === userId || message.senderClientId === userId;\n                                if (!isFromCurrentUser) {\n                                    console.log('[WebSocket] Nova mensagem de outro usuário, marcando conversa como não lida');\n                                    // Verificar se a conversa já estava marcada como não lida\n                                    const conversation = conversations.find({\n                                        \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                    }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                    const wasAlreadyUnread = (conversation === null || conversation === void 0 ? void 0 : conversation.hasUnread) || false;\n                                    // Marcar conversa como tendo mensagens não lidas\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                    if (conv.id === message.conversationId) {\n                                                        console.log(\"[WebSocket] Conversa \".concat(conv.id, \" marcada como n\\xe3o lida\"));\n                                                        return {\n                                                            ...conv,\n                                                            hasUnread: true\n                                                        };\n                                                    }\n                                                    return conv;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Incrementar contador total apenas se a conversa não estava marcada como não lida\n                                    if (!wasAlreadyUnread) {\n                                        setUnreadCount({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const newCount = prev + 1;\n                                                console.log('[WebSocket] Contador de conversas não lidas atualizado de', prev, 'para', newCount);\n                                                return newCount;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                } else {\n                                    console.log('[WebSocket] Mensagem do próprio usuário, não alterando status de não lida');\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a nova mensagem\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'message',\n                                        conversationId: message.conversationId,\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de contagem de mensagens não lidas\n                        socketInstance.on('unread:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('[WebSocket] Atualização de mensagens não lidas recebida:', data);\n                                // Usar o novo campo totalUnreadConversations ou fallback para totalUnread\n                                const totalUnreadConversations = data.totalUnreadConversations || data.totalUnread || 0;\n                                if (typeof totalUnreadConversations === 'number') {\n                                    console.log(\"[WebSocket] Atualizando contador total de conversas n\\xe3o lidas para: \".concat(totalUnreadConversations));\n                                    setUnreadCount(totalUnreadConversations);\n                                    // Atualizar as conversas com status de não lidas\n                                    if (data.conversations && Array.isArray(data.conversations)) {\n                                        console.log(\"[WebSocket] Atualizando \".concat(data.conversations.length, \" conversas com status de n\\xe3o lidas\"));\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        // Procurar esta conversa nos dados de não lidas\n                                                        const unreadInfo = data.conversations.find({\n                                                            \"ChatProvider.useEffect.initializeWebSocket.unreadInfo\": (item)=>item.conversationId === conv.id\n                                                        }[\"ChatProvider.useEffect.initializeWebSocket.unreadInfo\"]);\n                                                        if (unreadInfo) {\n                                                            const hasUnread = unreadInfo.hasUnread || unreadInfo.unreadCount && unreadInfo.unreadCount > 0;\n                                                            console.log(\"[WebSocket] Conversa \".concat(conv.id, \" tem mensagens n\\xe3o lidas: \").concat(hasUnread));\n                                                            return {\n                                                                ...conv,\n                                                                hasUnread\n                                                            };\n                                                        }\n                                                        // Resetar status se não estiver na lista de não lidas\n                                                        return {\n                                                            ...conv,\n                                                            hasUnread: false\n                                                        };\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a atualização de não lidas\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'unread',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de lista de conversas\n                        socketInstance.on('conversations:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Atualização de lista de conversas recebida via WebSocket:', data);\n                                // Verificar se os dados estão no formato esperado\n                                if (data) {\n                                    // Pode vir como array direto ou como objeto com propriedade conversations\n                                    const conversationsArray = Array.isArray(data) ? data : data.conversations || [];\n                                    if (Array.isArray(conversationsArray) && conversationsArray.length > 0) {\n                                        console.log(\"Atualizando \".concat(conversationsArray.length, \" conversas via WebSocket\"));\n                                        setConversations(conversationsArray);\n                                        // Atualizar a flag para indicar que os dados foram carregados\n                                        initialDataLoadedRef.current = true;\n                                        // Atualizar o cache\n                                        requestCache.conversations.data = conversationsArray;\n                                        requestCache.conversations.timestamp = Date.now();\n                                        // Disparar evento personalizado para notificar componentes sobre a atualização\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    } else {\n                                        console.error('Formato inválido ou array vazio de conversas recebido via WebSocket:', data);\n                                        // Se recebemos um array vazio, forçar carregamento das conversas\n                                        if (Array.isArray(conversationsArray) && conversationsArray.length === 0) {\n                                            console.log('Array vazio recebido, forçando carregamento de conversas...');\n                                            loadConversations(true);\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para notificar que uma nova conversa foi criada\n                        socketInstance.on('conversation:created', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Nova conversa criada recebida via WebSocket:', data);\n                                if (data && data.id) {\n                                    // Adicionar a nova conversa ao início da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            // Verificar se a conversa já existe\n                                            if (prev.some({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id === data.id\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                                return prev;\n                                            }\n                                            return [\n                                                data,\n                                                ...prev\n                                            ];\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando um participante é removido\n                        socketInstance.on('participant:removed', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Participante removido recebido via WebSocket:', data);\n                                if (data && data.conversationId && data.participantId) {\n                                    // Se o usuário atual foi removido, remover a conversa da lista\n                                    if (data.participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        // Se era a conversa ativa, limpar\n                                        if (activeConversation === data.conversationId) {\n                                            setActiveConversation(null);\n                                        }\n                                        // Limpar mensagens da conversa\n                                        setMessages({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const updated = {\n                                                    ...prev\n                                                };\n                                                delete updated[data.conversationId];\n                                                return updated;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    } else {\n                                        // Atualizar a lista de participantes da conversa\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        if (conv.id === data.conversationId) {\n                                                            return {\n                                                                ...conv,\n                                                                participants: conv.participants.filter({\n                                                                    \"ChatProvider.useEffect.initializeWebSocket\": (p)=>p.userId !== data.participantId && p.clientId !== data.participantId\n                                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                            };\n                                                        }\n                                                        return conv;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando o usuário sai de uma conversa\n                        socketInstance.on('conversation:left', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Usuário saiu da conversa via WebSocket:', data);\n                                if (data && data.conversationId && data.userId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                    // Remover a conversa da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Se era a conversa ativa, limpar\n                                    if (activeConversation === data.conversationId) {\n                                        setActiveConversation(null);\n                                    }\n                                    // Limpar mensagens da conversa\n                                    setMessages({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            const updated = {\n                                                ...prev\n                                            };\n                                            delete updated[data.conversationId];\n                                            return updated;\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Disparar evento para atualizar a interface\n                                    window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                        detail: {\n                                            type: 'conversations',\n                                            action: 'left',\n                                            conversationId: data.conversationId,\n                                            timestamp: Date.now()\n                                        }\n                                    }));\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        setSocket(socketInstance);\n                        return socketInstance;\n                    } catch (error) {\n                        console.error('Erro ao inicializar WebSocket:', error);\n                        setIsConnected(false);\n                        socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                        return null;\n                    }\n                }\n            }[\"ChatProvider.useEffect.initializeWebSocket\"];\n            // Usar uma variável para controlar se já estamos tentando inicializar\n            let initializationInProgress = false;\n            // Função para inicializar com segurança\n            const safeInitialize = {\n                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                    if (initializationInProgress) {\n                        console.log('Já existe uma inicialização em andamento, ignorando');\n                        return;\n                    }\n                    initializationInProgress = true;\n                    // Usar um timeout para garantir que não tentamos inicializar muito frequentemente\n                    setTimeout({\n                        \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                            initializeWebSocket().then({\n                                \"ChatProvider.useEffect.safeInitialize\": (result)=>{\n                                    console.log('Inicialização do WebSocket concluída:', result ? 'sucesso' : 'falha');\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).catch({\n                                \"ChatProvider.useEffect.safeInitialize\": (error)=>{\n                                    console.error('Erro ao inicializar WebSocket:', error);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).finally({\n                                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                                    initializationInProgress = false;\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]);\n                        }\n                    }[\"ChatProvider.useEffect.safeInitialize\"], 2000); // Esperar 2 segundos antes de inicializar\n                }\n            }[\"ChatProvider.useEffect.safeInitialize\"];\n            // Chamar a função de inicialização\n            safeInitialize();\n            // Cleanup function - só desconectar quando o componente for desmontado completamente\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                    // Verificar se estamos realmente desmontando o componente (usuário fez logout)\n                    if (!user) {\n                        console.log('Usuário fez logout, desconectando socket');\n                        if (socket) {\n                            try {\n                                socket.disconnect();\n                                socketInitializedRef.current = false; // Permitir nova tentativa após cleanup\n                            } catch (error) {\n                                console.error('Erro ao desconectar socket:', error);\n                            }\n                        }\n                    } else {\n                        console.log('Componente sendo remontado, mantendo socket conectado');\n                    }\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo socket e isConnected das dependências para evitar loops\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Carregar conversas do usuário com cache\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n            console.log('loadConversations chamado com forceRefresh =', forceRefresh);\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar conversas');\n                    return [];\n                }\n                // Clientes agora podem carregar conversas\n                console.log('Carregando conversas para:', user.role || 'USER');\n                // Verificar se já está carregando\n                if (isLoading || isLoadingRef.current) {\n                    console.log('Já está carregando conversas, retornando estado atual');\n                    return conversations;\n                }\n                // Verificar se já temos conversas carregadas e não é uma atualização forçada\n                if (!forceRefresh && conversations.length > 0) {\n                    console.log('Já temos conversas carregadas e não é uma atualização forçada, retornando estado atual');\n                    return conversations;\n                }\n                // Marcar como carregando para evitar chamadas simultâneas\n                isLoadingRef.current = true;\n                // Verificar cache apenas se não for refresh forçado\n                const now = Date.now();\n                if (!forceRefresh && requestCache.conversations.data && requestCache.conversations.data.length > 0 && now - requestCache.conversations.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para conversas');\n                    return requestCache.conversations.data;\n                }\n                console.log('Buscando conversas atualizadas da API');\n                const currentToken = getCurrentToken();\n                console.log('Token ao carregar conversas:', currentToken ? 'Disponível' : 'Não disponível');\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar conversas');\n                    return [];\n                }\n                setIsLoading(true);\n                try {\n                    console.log('Buscando conversas da API...');\n                    console.log('Fazendo requisição para:', \"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"));\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    console.log('Status da resposta:', response.status, response.statusText);\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar conversas');\n                            handleAuthError();\n                            return [];\n                        }\n                        throw new Error(\"Erro ao carregar conversas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de conversas:', data);\n                    if (data.success) {\n                        var _data_data;\n                        // Verificar se há dados válidos\n                        // A API retorna { success: true, data: { conversations: [...], total, limit, offset } }\n                        console.log('Estrutura completa da resposta da API:', JSON.stringify(data));\n                        const conversationsArray = ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.conversations) || [];\n                        console.log('Array de conversas extraído:', conversationsArray);\n                        if (!Array.isArray(conversationsArray)) {\n                            console.error('Resposta da API não contém um array de conversas:', data);\n                            return [];\n                        }\n                        console.log(\"Recebidas \".concat(conversationsArray.length, \" conversas da API\"));\n                        // Processar as últimas mensagens das conversas\n                        console.log('Processando últimas mensagens das conversas...');\n                        const processedConversations = conversationsArray.map({\n                            \"ChatProvider.useCallback[loadConversations].processedConversations\": (conversation)=>{\n                                // Verificar se a conversa tem mensagens\n                                if (conversation.messages && conversation.messages.length > 0) {\n                                    console.log(\"Conversa \".concat(conversation.id, \" tem \").concat(conversation.messages.length, \" mensagens\"));\n                                    // Extrair a última mensagem\n                                    const lastMessage = conversation.messages[0]; // A primeira mensagem é a mais recente (ordenada por createdId desc)\n                                    console.log('Ultima mensagem:', lastMessage);\n                                    // IMPORTANTE: Também salvar todas as mensagens desta conversa no estado\n                                    const sortedMessages = [\n                                        ...conversation.messages\n                                    ].sort({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\"]);\n                                    console.log(\"DEBUG: Salvando \".concat(sortedMessages.length, \" mensagens da conversa \").concat(conversation.id), sortedMessages);\n                                    // Atualizar estado das mensagens\n                                    setMessages({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations\": (prev)=>({\n                                                ...prev,\n                                                [conversation.id]: sortedMessages\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                                    // Remover o array de mensagens para evitar duplicação\n                                    const { messages, ...conversationWithoutMessages } = conversation;\n                                    // Adicionar a última mensagem como propriedade lastMessage\n                                    return {\n                                        ...conversationWithoutMessages,\n                                        lastMessage\n                                    };\n                                } else {\n                                    console.log(\"Conversa \".concat(conversation.id, \" n\\xe3o tem mensagens\"));\n                                }\n                                return conversation;\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                        // Log para debug dos dados das conversas\n                        console.log('Conversas processadas:', processedConversations.map({\n                            \"ChatProvider.useCallback[loadConversations]\": (c)=>{\n                                var _c_participants;\n                                return {\n                                    id: c.id,\n                                    participants: (_c_participants = c.participants) === null || _c_participants === void 0 ? void 0 : _c_participants.map({\n                                        \"ChatProvider.useCallback[loadConversations]\": (p)=>({\n                                                userId: p.userId,\n                                                clientId: p.clientId,\n                                                user: p.user,\n                                                client: p.client\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations]\"]));\n                        // Atualizar cache com as conversas processadas\n                        const now = Date.now();\n                        requestCache.conversations.data = processedConversations;\n                        requestCache.conversations.timestamp = now;\n                        // Atualizar estado - garantir que estamos atualizando o estado mesmo se o array estiver vazio\n                        setConversations(processedConversations);\n                        // Disparar evento para notificar componentes sobre a atualização\n                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                            detail: {\n                                type: 'conversations',\n                                timestamp: Date.now()\n                            }\n                        }));\n                        return conversationsArray;\n                    } else {\n                        console.error('Resposta da API não foi bem-sucedida:', data);\n                        return [];\n                    }\n                } catch (error) {\n                    console.error('Error loading conversations:', error);\n                    return [];\n                } finally{\n                    setIsLoading(false);\n                    isLoadingRef.current = false;\n                }\n            } catch (error) {\n                console.error('Error in loadConversations:', error);\n                return [];\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadConversations]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError,\n        isLoading\n    ]);\n    // Carregar mensagens de uma conversa com cache aprimorado\n    const loadMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadMessages]\": async (conversationId)=>{\n            try {\n                var _requestCache_messages_conversationId;\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar mensagens');\n                    return;\n                }\n                // Verificar cache de mensagens\n                const now = Date.now();\n                if (requestCache.messages[conversationId] && now - requestCache.messages[conversationId].timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para mensagens da conversa:', conversationId);\n                    return requestCache.messages[conversationId].data;\n                }\n                // Verificar se já tem mensagens completas carregadas no cache\n                // Não usar o estado messages aqui pois pode conter apenas a última mensagem do loadConversations\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar mensagens');\n                    return;\n                }\n                // Evitar múltiplas requisições simultâneas para a mesma conversa\n                if ((_requestCache_messages_conversationId = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId === void 0 ? void 0 : _requestCache_messages_conversationId.loading) {\n                    console.log('Já existe uma requisição em andamento para esta conversa');\n                    return;\n                }\n                // Marcar como carregando\n                requestCache.messages[conversationId] = {\n                    loading: true\n                };\n                try {\n                    console.log('Buscando mensagens da API para conversa:', conversationId);\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/messages\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar mensagens');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar mensagens: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    if (data.success) {\n                        // Debug para verificar mensagens carregadas\n                        console.log('DEBUG: Mensagens carregadas para conversa', conversationId, data.data);\n                        // Atualizar cache\n                        requestCache.messages[conversationId] = {\n                            data: data.data,\n                            timestamp: now,\n                            loading: false\n                        };\n                        // Atualizar estado das mensagens\n                        // Garantir que as mensagens estejam na ordem correta (mais antigas primeiro)\n                        // para que a ordenação no componente funcione corretamente\n                        const sortedMessages = [\n                            ...data.data\n                        ].sort({\n                            \"ChatProvider.useCallback[loadMessages].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                        }[\"ChatProvider.useCallback[loadMessages].sortedMessages\"]);\n                        console.log('DEBUG: Mensagens ordenadas', sortedMessages);\n                        setMessages({\n                            \"ChatProvider.useCallback[loadMessages]\": (prev)=>({\n                                    ...prev,\n                                    [conversationId]: sortedMessages\n                                })\n                        }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        // Atualizar a última mensagem da conversa\n                        if (data.data && data.data.length > 0) {\n                            const lastMessage = data.data[data.data.length - 1];\n                            setConversations({\n                                \"ChatProvider.useCallback[loadMessages]\": (prev)=>{\n                                    return prev.map({\n                                        \"ChatProvider.useCallback[loadMessages]\": (conv)=>{\n                                            if (conv.id === conversationId && (!conv.lastMessage || new Date(lastMessage.createdAt) > new Date(conv.lastMessage.createdAt))) {\n                                                return {\n                                                    ...conv,\n                                                    lastMessage\n                                                };\n                                            }\n                                            return conv;\n                                        }\n                                    }[\"ChatProvider.useCallback[loadMessages]\"]);\n                                }\n                            }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        }\n                        return data.data;\n                    }\n                } catch (error) {\n                    console.error('Error loading messages:', error);\n                } finally{\n                    var _requestCache_messages_conversationId1;\n                    // Remover flag de carregamento em caso de erro\n                    if ((_requestCache_messages_conversationId1 = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId1 === void 0 ? void 0 : _requestCache_messages_conversationId1.loading) {\n                        requestCache.messages[conversationId].loading = false;\n                    }\n                }\n            } catch (error) {\n                console.error('Error in loadMessages:', error);\n            }\n        // Removendo messages das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadMessages]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Enviar mensagem\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[sendMessage]\": function(conversationId, content) {\n            let contentType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'TEXT', metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            if (!socket || !isConnected) return;\n            // Gerar um ID temporário para a mensagem\n            const tempId = \"temp-\".concat(Date.now());\n            // Criar uma mensagem temporária para exibir imediatamente\n            const tempMessage = {\n                id: tempId,\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                senderId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : user === null || user === void 0 ? void 0 : user.id,\n                senderClientId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? user === null || user === void 0 ? void 0 : user.id : null,\n                createdAt: new Date().toISOString(),\n                sender: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: user === null || user === void 0 ? void 0 : user.fullName\n                },\n                senderClient: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.login),\n                    login: user === null || user === void 0 ? void 0 : user.login\n                } : null,\n                // Marcar como temporária para evitar duplicação\n                isTemp: true\n            };\n            // Atualizar mensagens localmente antes de enviar\n            setMessages({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>({\n                        ...prev,\n                        [conversationId]: [\n                            ...prev[conversationId] || [],\n                            tempMessage\n                        ]\n                    })\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Atualizar a conversa com a última mensagem\n            setConversations({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                    return prev.map({\n                        \"ChatProvider.useCallback[sendMessage]\": (conv)=>{\n                            if (conv.id === conversationId) {\n                                return {\n                                    ...conv,\n                                    lastMessage: tempMessage\n                                };\n                            }\n                            return conv;\n                        }\n                    }[\"ChatProvider.useCallback[sendMessage]\"]);\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Enviar a mensagem via WebSocket\n            const messageData = {\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                tempId\n            };\n            socket.emit('message:send', messageData, {\n                \"ChatProvider.useCallback[sendMessage]\": (response)=>{\n                    if (response.success) {\n                    // A mensagem real será adicionada pelo evento message:new do WebSocket\n                    // Não precisamos fazer nada aqui, pois o WebSocket vai atualizar a mensagem\n                    } else {\n                        console.error('Error sending message:', response.error);\n                        // Em caso de erro, podemos marcar a mensagem como falha\n                        setMessages({\n                            \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                                const conversationMessages = prev[conversationId] || [];\n                                return {\n                                    ...prev,\n                                    [conversationId]: conversationMessages.map({\n                                        \"ChatProvider.useCallback[sendMessage]\": (msg)=>msg.id === tempId ? {\n                                                ...msg,\n                                                failed: true\n                                            } : msg\n                                    }[\"ChatProvider.useCallback[sendMessage]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[sendMessage]\"]);\n                    }\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n        }\n    }[\"ChatProvider.useCallback[sendMessage]\"], [\n        socket,\n        isConnected,\n        user\n    ]);\n    // Criar nova conversa\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createConversation]\": async function(participantIds) {\n            let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n            try {\n                console.log('Criando conversa com participantes:', participantIds);\n                console.log('Tipo de conversa:', participantIds.length > 1 ? 'GRUPO' : 'INDIVIDUAL');\n                console.log('Título da conversa:', title);\n                // ✅ VALIDAÇÃO: System admin não pode criar grupos com usuários de empresas diferentes\n                if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && participantIds.length > 1) {\n                    console.log('Validando empresas para system admin...');\n                    // Buscar informações dos participantes para validar empresas\n                    const currentToken = getCurrentToken();\n                    if (currentToken) {\n                        try {\n                            const participantPromises = participantIds.map({\n                                \"ChatProvider.useCallback[createConversation].participantPromises\": async (id)=>{\n                                    const response = await fetch(\"\".concat(API_URL, \"/users/\").concat(id), {\n                                        headers: {\n                                            Authorization: \"Bearer \".concat(currentToken)\n                                        }\n                                    });\n                                    if (response.ok) {\n                                        const userData = await response.json();\n                                        return userData.success ? userData.data : userData;\n                                    }\n                                    return null;\n                                }\n                            }[\"ChatProvider.useCallback[createConversation].participantPromises\"]);\n                            const participants = await Promise.all(participantPromises);\n                            console.log('Participantes carregados:', participants);\n                            const validParticipants = participants.filter({\n                                \"ChatProvider.useCallback[createConversation].validParticipants\": (p)=>p && p.companyId\n                            }[\"ChatProvider.useCallback[createConversation].validParticipants\"]);\n                            console.log('Participantes válidos:', validParticipants);\n                            if (validParticipants.length > 1) {\n                                const companies = [\n                                    ...new Set(validParticipants.map({\n                                        \"ChatProvider.useCallback[createConversation]\": (p)=>p.companyId\n                                    }[\"ChatProvider.useCallback[createConversation]\"]))\n                                ];\n                                console.log('Empresas encontradas:', companies);\n                                if (companies.length > 1) {\n                                    alert('Não é possível criar grupos com usuários de empresas diferentes.');\n                                    return null;\n                                }\n                            }\n                        } catch (validationError) {\n                            console.error('Erro na validação de empresas:', validationError);\n                            alert('Erro na validação: ' + validationError.message);\n                            return null;\n                        }\n                    }\n                }\n                // Verificar se algum participante é cliente\n                const hasClientParticipants = participantIds.some({\n                    \"ChatProvider.useCallback[createConversation].hasClientParticipants\": (id)=>{\n                        // Verificar se o ID corresponde a um cliente (assumindo que clientes têm isClient: true)\n                        return typeof id === 'object' && id.isClient;\n                    }\n                }[\"ChatProvider.useCallback[createConversation].hasClientParticipants\"]);\n                console.log('Tem participantes clientes:', hasClientParticipants);\n                // Obter o token mais recente do localStorage\n                const currentToken = getCurrentToken();\n                console.log('Token disponível:', currentToken ? 'Sim' : 'Não');\n                if (!currentToken) {\n                    console.error('Token não disponível. Usuário precisa fazer login novamente.');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        type: participantIds.length > 1 ? 'GROUP' : 'INDIVIDUAL',\n                        title,\n                        participantIds: participantIds.map({\n                            \"ChatProvider.useCallback[createConversation]\": (id)=>typeof id === 'object' ? id.id : id\n                        }[\"ChatProvider.useCallback[createConversation]\"]),\n                        includeClients: true // Permitir incluir clientes nas conversas\n                    })\n                });\n                console.log('Resposta da API:', response.status, response.statusText);\n                const data = await response.json();\n                console.log('Dados da resposta:', data);\n                if (data.success) {\n                    // Extrair os dados da conversa criada\n                    const conversationData = data.data;\n                    console.log('Conversa criada com sucesso:', conversationData);\n                    // Adicionar a nova conversa à lista\n                    setConversations({\n                        \"ChatProvider.useCallback[createConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createConversation]\": (c)=>c.id === conversationData.id\n                            }[\"ChatProvider.useCallback[createConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                conversationData,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"]);\n                    // Disparar evento para notificar componentes sobre a nova conversa\n                    setTimeout({\n                        \"ChatProvider.useCallback[createConversation]\": ()=>{\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: conversationData.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"], 300);\n                    return conversationData;\n                } else {\n                    console.error('Erro ao criar conversa:', data.error || 'Erro desconhecido');\n                    // Se o erro for de autenticação, redirecionar para login\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido. Redirecionando para login...');\n                        handleAuthError();\n                    }\n                }\n                return null;\n            } catch (error) {\n                console.error('Error creating conversation:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[createConversation]\"], [\n        handleAuthError\n    ]);\n    // Criar ou obter conversa com um usuário específico\n    const createOrGetConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createOrGetConversation]\": async (otherUser)=>{\n            try {\n                console.log('createOrGetConversation chamado com usuário:', otherUser);\n                if (!otherUser || !otherUser.id) {\n                    console.error('Usuário inválido:', otherUser);\n                    return null;\n                }\n                // Primeiro, verificar se já existe uma conversa individual com este usuário\n                const existingConversation = conversations.find({\n                    \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (conv)=>{\n                        var _conv_participants;\n                        if (conv.type !== 'INDIVIDUAL') return false;\n                        return (_conv_participants = conv.participants) === null || _conv_participants === void 0 ? void 0 : _conv_participants.some({\n                            \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (p)=>p.userId === otherUser.id\n                        }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                    }\n                }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                if (existingConversation) {\n                    console.log('Conversa existente encontrada:', existingConversation);\n                    setActiveConversation(existingConversation.id);\n                    return existingConversation;\n                }\n                console.log('Criando nova conversa com usuário:', otherUser.fullName);\n                // Se não existir, criar uma nova conversa\n                const newConversation = await createConversation([\n                    otherUser.id\n                ]);\n                if (newConversation) {\n                    console.log('Nova conversa criada com sucesso:', newConversation);\n                    // Garantir que a conversa seja adicionada à lista antes de definir como ativa\n                    setConversations({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createOrGetConversation]\": (c)=>c.id === newConversation.id\n                            }[\"ChatProvider.useCallback[createOrGetConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                newConversation,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"]);\n                    // Definir a conversa como ativa após um pequeno delay para garantir que a lista foi atualizada\n                    setTimeout({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": ()=>{\n                            setActiveConversation(newConversation.id);\n                            // Disparar evento para notificar componentes sobre a nova conversa\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: newConversation.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"], 300);\n                } else {\n                    console.error('Falha ao criar nova conversa, retorno nulo ou indefinido');\n                }\n                return newConversation;\n            } catch (error) {\n                console.error('Error creating or getting conversation:', error);\n                return null;\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[createOrGetConversation]\"], [\n        createConversation,\n        setActiveConversation,\n        handleAuthError\n    ]);\n    // Carregar contagem de mensagens não lidas com cache\n    const loadUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadUnreadCount]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                // Verificar cache\n                const now = Date.now();\n                if (!forceRefresh && requestCache.unreadCount.data !== null && now - requestCache.unreadCount.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para contagem de mensagens não lidas');\n                    return requestCache.unreadCount.data;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                try {\n                    console.log('Buscando contagem de mensagens não lidas da API...');\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/messages/unread\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar contagem de mensagens não lidas');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar contagem de mensagens n\\xe3o lidas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de mensagens não lidas:', data);\n                    if (data.success) {\n                        console.log('Dados de mensagens não lidas recebidos:', data.data);\n                        // Usar o novo campo totalUnreadConversations ou fallback para totalUnread\n                        const totalUnreadConversations = data.data.totalUnreadConversations || data.data.totalUnread || 0;\n                        // Atualizar cache com o totalUnreadConversations\n                        requestCache.unreadCount.data = totalUnreadConversations;\n                        requestCache.unreadCount.timestamp = now;\n                        // Atualizar estado\n                        setUnreadCount(totalUnreadConversations);\n                        // Atualizar as conversas com status de não lidas\n                        if (data.data.conversations && Array.isArray(data.data.conversations)) {\n                            console.log('[loadUnreadCount] Dados de conversas não lidas recebidos:', data.data.conversations);\n                            // Primeiro, verificar se já temos as conversas carregadas\n                            const conversationIds = data.data.conversations.map({\n                                \"ChatProvider.useCallback[loadUnreadCount].conversationIds\": (c)=>c.conversationId\n                            }[\"ChatProvider.useCallback[loadUnreadCount].conversationIds\"]);\n                            console.log('[loadUnreadCount] IDs de conversas não lidas:', conversationIds);\n                            console.log('[loadUnreadCount] Conversas atualmente carregadas:', conversations.length, conversations.map({\n                                \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>c.id\n                            }[\"ChatProvider.useCallback[loadUnreadCount]\"]));\n                            // Se não temos conversas carregadas ou se temos menos conversas do que as não lidas,\n                            // forçar uma atualização das conversas\n                            if (conversations.length === 0 || !conversationIds.every({\n                                \"ChatProvider.useCallback[loadUnreadCount]\": (id)=>conversations.some({\n                                        \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>c.id === id\n                                    }[\"ChatProvider.useCallback[loadUnreadCount]\"])\n                            }[\"ChatProvider.useCallback[loadUnreadCount]\"])) {\n                                console.log('[loadUnreadCount] Forçando atualização das conversas porque há mensagens não lidas em conversas não carregadas');\n                                await loadConversations(true);\n                                // Após carregar as conversas, precisamos atualizar com as informações de não lidas\n                                console.log('[loadUnreadCount] Atualizando conversas recém-carregadas com status de não lidas...');\n                                setConversations({\n                                    \"ChatProvider.useCallback[loadUnreadCount]\": (prev)=>{\n                                        const updated = prev.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount].updated\": (conv)=>{\n                                                const unreadInfo = data.data.conversations.find({\n                                                    \"ChatProvider.useCallback[loadUnreadCount].updated.unreadInfo\": (item)=>item.conversationId === conv.id\n                                                }[\"ChatProvider.useCallback[loadUnreadCount].updated.unreadInfo\"]);\n                                                if (unreadInfo) {\n                                                    const hasUnread = unreadInfo.hasUnread || unreadInfo.unreadCount && unreadInfo.unreadCount > 0;\n                                                    console.log(\"[loadUnreadCount] Conversa \".concat(conv.id, \": hasUnread = \").concat(hasUnread));\n                                                    return {\n                                                        ...conv,\n                                                        hasUnread\n                                                    };\n                                                }\n                                                console.log(\"[loadUnreadCount] Conversa \".concat(conv.id, \": hasUnread = false (n\\xe3o encontrada em n\\xe3o lidas)\"));\n                                                return {\n                                                    ...conv,\n                                                    hasUnread: false\n                                                };\n                                            }\n                                        }[\"ChatProvider.useCallback[loadUnreadCount].updated\"]);\n                                        console.log('[loadUnreadCount] Conversas atualizadas após loadConversations:', updated.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>({\n                                                    id: c.id,\n                                                    hasUnread: c.hasUnread\n                                                })\n                                        }[\"ChatProvider.useCallback[loadUnreadCount]\"]));\n                                        return updated;\n                                    }\n                                }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                            } else {\n                                // Atualizar as conversas existentes com status de não lidas\n                                console.log('[loadUnreadCount] Atualizando conversas com status de não lidas...');\n                                setConversations({\n                                    \"ChatProvider.useCallback[loadUnreadCount]\": (prev)=>{\n                                        const updated = prev.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount].updated\": (conv)=>{\n                                                // Procurar esta conversa nos dados de não lidas\n                                                const unreadInfo = data.data.conversations.find({\n                                                    \"ChatProvider.useCallback[loadUnreadCount].updated.unreadInfo\": (item)=>item.conversationId === conv.id\n                                                }[\"ChatProvider.useCallback[loadUnreadCount].updated.unreadInfo\"]);\n                                                if (unreadInfo) {\n                                                    const hasUnread = unreadInfo.hasUnread || unreadInfo.unreadCount && unreadInfo.unreadCount > 0;\n                                                    console.log(\"[loadUnreadCount] Conversa \".concat(conv.id, \": hasUnread = \").concat(hasUnread));\n                                                    return {\n                                                        ...conv,\n                                                        hasUnread\n                                                    };\n                                                }\n                                                console.log(\"[loadUnreadCount] Conversa \".concat(conv.id, \": hasUnread = false (n\\xe3o encontrada em n\\xe3o lidas)\"));\n                                                return {\n                                                    ...conv,\n                                                    hasUnread: false\n                                                };\n                                            }\n                                        }[\"ChatProvider.useCallback[loadUnreadCount].updated\"]);\n                                        console.log('[loadUnreadCount] Conversas atualizadas:', updated.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>({\n                                                    id: c.id,\n                                                    hasUnread: c.hasUnread\n                                                })\n                                        }[\"ChatProvider.useCallback[loadUnreadCount]\"]));\n                                        return updated;\n                                    }\n                                }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                            }\n                        }\n                        return totalUnreadConversations;\n                    }\n                } catch (error) {\n                    console.error('Error loading unread count:', error);\n                }\n            } catch (error) {\n                console.error('Error in loadUnreadCount:', error);\n            }\n        // Removendo conversations e loadConversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadUnreadCount]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Referência para controlar se os dados iniciais já foram carregados\n    const initialDataLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Resetar a flag quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user) {\n                console.log('Usuário alterado, resetando flag de dados carregados');\n                initialDataLoadedRef.current = false;\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Dependendo apenas do ID do usuário para evitar re-renders desnecessários\n    // Efeito para carregar dados iniciais e configurar atualização periódica\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de carregar dados\n            if (!user) {\n                console.log('Usuário não logado, não carregando dados iniciais');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Carregando dados iniciais para:', user.role || 'USER');\n            // Verificar token\n            const currentToken = getCurrentToken();\n            if (!currentToken) {\n                console.log('Token não disponível, não carregando dados iniciais');\n                return;\n            }\n            // Verificar se já está carregando\n            if (isLoading || isLoadingRef.current) {\n                console.log('Já está carregando dados, aguardando...');\n                return;\n            }\n            // Verificar se os dados já foram carregados\n            if (initialDataLoadedRef.current) {\n                console.log('Dados já foram carregados anteriormente, ignorando');\n                return;\n            }\n            // Função para carregar dados iniciais\n            const loadInitialData = {\n                \"ChatProvider.useEffect.loadInitialData\": async ()=>{\n                    if (isLoading || isLoadingRef.current) {\n                        console.log('Já está carregando dados, cancelando carregamento inicial');\n                        return;\n                    }\n                    // Marcar como carregando\n                    isLoadingRef.current = true;\n                    console.log('Iniciando carregamento de dados do chat...');\n                    try {\n                        // Primeiro carregar as conversas\n                        console.log('Carregando conversas...');\n                        const conversationsData = await loadConversations(true);\n                        console.log('Conversas carregadas:', (conversationsData === null || conversationsData === void 0 ? void 0 : conversationsData.length) || 0, 'conversas');\n                        // Carregar contagem de mensagens não lidas\n                        console.log('Carregando contagem de mensagens não lidas...');\n                        await loadUnreadCount(true);\n                        console.log('Contagem de não lidas carregada');\n                        // Marcar como carregado\n                        initialDataLoadedRef.current = true;\n                        console.log('Carregamento de dados concluído com sucesso');\n                    } catch (error) {\n                        console.error('Erro ao carregar dados:', error);\n                    } finally{\n                        isLoadingRef.current = false;\n                    }\n                }\n            }[\"ChatProvider.useEffect.loadInitialData\"];\n            // Carregar dados iniciais com debounce para evitar múltiplas chamadas\n            console.log('Agendando carregamento de dados iniciais...');\n            const debouncedLoadData = debounce({\n                \"ChatProvider.useEffect.debouncedLoadData\": ()=>{\n                    console.log('Carregando dados iniciais (debounced)...');\n                    loadInitialData();\n                }\n            }[\"ChatProvider.useEffect.debouncedLoadData\"], 1000); // Esperar 1 segundo antes de carregar\n            // Chamar a função com debounce\n            debouncedLoadData();\n            // Removemos a atualização periódica via HTTP para evitar flood no backend\n            // Agora dependemos apenas do WebSocket para atualizações em tempo real\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                // Cleanup function\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo loadUnreadCount e isPanelOpen, isModalOpen das dependências para evitar chamadas desnecessárias\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        loadConversations,\n        isLoading\n    ]);\n    // Efeito para verificar se activeConversation é válido\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (activeConversation && conversations.length > 0) {\n                const conversationExists = conversations.some({\n                    \"ChatProvider.useEffect.conversationExists\": (c)=>c.id === activeConversation\n                }[\"ChatProvider.useEffect.conversationExists\"]);\n                if (!conversationExists) {\n                    console.warn('Conversa ativa não encontrada na lista de conversas, resetando...', activeConversation);\n                    setActiveConversation(null);\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        activeConversation,\n        conversations\n    ]);\n    // Efeito para monitorar mudanças no token e reconectar quando necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de monitorar o token\n            if (!user) return;\n            // Verificar o token a cada 2 minutos (aumentado para reduzir a frequência)\n            const tokenCheckInterval = setInterval({\n                \"ChatProvider.useEffect.tokenCheckInterval\": ()=>{\n                    // Evitar verificações desnecessárias se estiver carregando\n                    if (isLoading || isLoadingRef.current) return;\n                    const currentToken = getCurrentToken();\n                    // Se não há token mas há conexão, desconectar\n                    if (!currentToken && isConnected && socket) {\n                        console.log('Token não encontrado, desconectando WebSocket...');\n                        try {\n                            socket.disconnect();\n                        } catch (error) {\n                            console.error('Erro ao desconectar socket:', error);\n                        }\n                        setIsConnected(false);\n                    }\n                    // Se há token mas não há conexão, tentar reconectar (com verificação de tempo)\n                    if (currentToken && !isConnected && !socket) {\n                        const now = Date.now();\n                        // Limitar tentativas de reconexão (no máximo uma a cada 2 minutos)\n                        if (now - lastInitAttemptRef.current > 120000) {\n                            console.log('Token encontrado, tentando reconectar WebSocket...');\n                            // Permitir nova tentativa de inicialização do WebSocket\n                            socketInitializedRef.current = false;\n                            lastInitAttemptRef.current = now;\n                        }\n                    }\n                }\n            }[\"ChatProvider.useEffect.tokenCheckInterval\"], 120000); // Verificar a cada 2 minutos\n            return ({\n                \"ChatProvider.useEffect\": ()=>clearInterval(tokenCheckInterval)\n            })[\"ChatProvider.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Abrir/fechar painel de chat\n    const toggleChatPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatPanel]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsPanelOpen({\n                \"ChatProvider.useCallback[toggleChatPanel]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o painel, fechar o modal\n                    if (newState) {\n                        setIsModalOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatPanel]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatPanel]\"], [\n        user\n    ]);\n    // Abrir/fechar modal de chat\n    const toggleChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatModal]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsModalOpen({\n                \"ChatProvider.useCallback[toggleChatModal]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o modal, fechar o painel\n                    if (newState) {\n                        setIsPanelOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatModal]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatModal]\"], [\n        user\n    ]);\n    // Verificar se createConversation é uma função válida\n    console.log('ChatContext: createConversation é uma função?', typeof createConversation === 'function');\n    // Adicionar participante a um grupo\n    const addParticipantToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addParticipantToGroup]\": async (conversationId, participantId)=>{\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao adicionar participante');\n                    return null;\n                }\n                console.log(\"Adicionando participante \".concat(participantId, \" \\xe0 conversa \").concat(conversationId));\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        participantId\n                    })\n                });\n                console.log(\"Resposta da API: \".concat(response.status, \" \").concat(response.statusText));\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao adicionar participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    // Log detalhado do erro\n                    const errorText = await response.text();\n                    console.error(\"Erro \".concat(response.status, \" ao adicionar participante:\"), errorText);\n                    throw new Error(\"Erro ao adicionar participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Atualizar a conversa com o novo participante\n                    setConversations({\n                        \"ChatProvider.useCallback[addParticipantToGroup]\": (prev)=>{\n                            return prev.map({\n                                \"ChatProvider.useCallback[addParticipantToGroup]\": (conv)=>{\n                                    if (conv.id === conversationId) {\n                                        // Verificar se o participante já existe\n                                        const participantExists = conv.participants.some({\n                                            \"ChatProvider.useCallback[addParticipantToGroup].participantExists\": (p)=>p.userId === participantId\n                                        }[\"ChatProvider.useCallback[addParticipantToGroup].participantExists\"]);\n                                        if (participantExists) {\n                                            return conv;\n                                        }\n                                        // Adicionar o novo participante\n                                        return {\n                                            ...conv,\n                                            participants: [\n                                                ...conv.participants,\n                                                data.data\n                                            ]\n                                        };\n                                    }\n                                    return conv;\n                                }\n                            }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                        }\n                    }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                    // Criar uma mensagem do sistema para notificar que um participante foi adicionado\n                    if (socket && isConnected) {\n                        const systemMessage = {\n                            conversationId,\n                            content: \"\".concat(data.data.user.fullName, \" foi adicionado ao grupo\"),\n                            contentType: 'SYSTEM'\n                        };\n                        socket.emit('message:send', systemMessage);\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao adicionar participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[addParticipantToGroup]\"], [\n        socket,\n        isConnected,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Adicionar múltiplos participantes a um grupo\n    const addMultipleParticipantsToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addMultipleParticipantsToGroup]\": async (conversationId, participants)=>{\n            try {\n                if (!conversationId || !participants || !participants.length) {\n                    console.error('ID da conversa e lista de participantes são obrigatórios');\n                    return null;\n                }\n                console.log(\"Adicionando \".concat(participants.length, \" participantes ao grupo \").concat(conversationId));\n                // Array para armazenar os resultados\n                const results = [];\n                const errors = [];\n                // Adicionar participantes um por um\n                for (const participant of participants){\n                    try {\n                        const result = await addParticipantToGroup(conversationId, participant.id);\n                        if (result) {\n                            results.push(result);\n                        } else {\n                            errors.push({\n                                user: participant,\n                                error: 'Falha ao adicionar participante'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao adicionar participante \".concat(participant.id, \":\"), error);\n                        errors.push({\n                            user: participant,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    added: results,\n                    errors: errors,\n                    total: participants.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao adicionar múltiplos participantes:', error);\n                return {\n                    success: false,\n                    added: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: participants.length,\n                    successCount: 0,\n                    errorCount: participants.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[addMultipleParticipantsToGroup]\"], [\n        addParticipantToGroup\n    ]);\n    // Remover participante de um grupo (ou sair do grupo)\n    const removeParticipantFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[removeParticipantFromGroup]\": async (conversationId, participantId)=>{\n            console.log('removeParticipantFromGroup chamado:', {\n                conversationId,\n                participantId,\n                userId: user === null || user === void 0 ? void 0 : user.id\n            });\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao remover participante');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants/\").concat(participantId), {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao remover participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    throw new Error(\"Erro ao remover participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Sempre remover da lista local quando o usuário sai\n                    if (participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                        console.log(\"Removendo conversa \".concat(conversationId, \" da lista local\"));\n                        // Remover da lista local\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>prev.filter({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>conv.id !== conversationId\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar mensagens\n                        setMessages({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                const updated = {\n                                    ...prev\n                                };\n                                delete updated[conversationId];\n                                return updated;\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar cache\n                        requestCache.conversations.data = null;\n                        requestCache.conversations.timestamp = 0;\n                        if (requestCache.messages[conversationId]) {\n                            delete requestCache.messages[conversationId];\n                        }\n                        // Se era a conversa ativa, limpar\n                        if (activeConversation === conversationId) {\n                            setActiveConversation(null);\n                        }\n                        // Forçar recarregamento das conversas após sair\n                        setTimeout({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": ()=>{\n                                loadConversations(true);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], 500);\n                        console.log(\"Conversa \".concat(conversationId, \" removida da lista local\"));\n                    } else {\n                        var _data_data_user, _data_data;\n                        // Atualizar a conversa removendo o participante\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                return prev.map({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>{\n                                        if (conv.id === conversationId) {\n                                            return {\n                                                ...conv,\n                                                participants: conv.participants.filter({\n                                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (p)=>p.userId !== participantId\n                                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                                            };\n                                        }\n                                        return conv;\n                                    }\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Criar uma mensagem do sistema para notificar que um participante saiu\n                        if (socket && isConnected && ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : (_data_data_user = _data_data.user) === null || _data_data_user === void 0 ? void 0 : _data_data_user.fullName)) {\n                            const systemMessage = {\n                                conversationId,\n                                content: \"\".concat(data.data.user.fullName, \" saiu do grupo\"),\n                                contentType: 'SYSTEM'\n                            };\n                            socket.emit('message:send', systemMessage);\n                        }\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao remover participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], [\n        socket,\n        isConnected,\n        user,\n        activeConversation,\n        getCurrentToken,\n        handleAuthError,\n        loadUnreadCount\n    ]);\n    // Marcar mensagens como lidas\n    const markMessagesAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markMessagesAsRead]\": async (conversationId, messageId)=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/mark-read\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                if (response.ok) {\n                    // Marcar conversa como lida localmente\n                    setConversations({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[markMessagesAsRead]\": (conv)=>conv.id === conversationId ? {\n                                        ...conv,\n                                        hasUnread: false\n                                    } : conv\n                            }[\"ChatProvider.useCallback[markMessagesAsRead]\"])\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                    // Decrementar contador de conversas não lidas se a conversa estava marcada como não lida\n                    setUnreadCount({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>{\n                            const conv = conversations.find({\n                                \"ChatProvider.useCallback[markMessagesAsRead].conv\": (c)=>c.id === conversationId\n                            }[\"ChatProvider.useCallback[markMessagesAsRead].conv\"]);\n                            const wasUnread = (conv === null || conv === void 0 ? void 0 : conv.hasUnread) || (conv === null || conv === void 0 ? void 0 : conv.unreadCount) > 0;\n                            return wasUnread ? Math.max(0, prev - 1) : prev;\n                        }\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                }\n            } catch (error) {\n                console.error('Error marking messages as read:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[markMessagesAsRead]\"], [\n        user,\n        getCurrentToken,\n        conversations\n    ]);\n    // Resetar contador de mensagens não lidas\n    const resetUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[resetUnreadCount]\": async ()=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/reset-unread\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (response.ok) {\n                    setUnreadCount(0);\n                    setConversations({\n                        \"ChatProvider.useCallback[resetUnreadCount]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[resetUnreadCount]\": (conv)=>({\n                                        ...conv,\n                                        unreadCount: 0\n                                    })\n                            }[\"ChatProvider.useCallback[resetUnreadCount]\"])\n                    }[\"ChatProvider.useCallback[resetUnreadCount]\"]);\n                }\n            } catch (error) {\n                console.error('Error resetting unread count:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[resetUnreadCount]\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Apagar mensagens\n    const deleteMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[deleteMessages]\": async (messageIds, conversationId)=>{\n            try {\n                if (!messageIds || messageIds.length === 0) {\n                    console.error('IDs das mensagens são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao apagar mensagens');\n                    return null;\n                }\n                const results = [];\n                const errors = [];\n                // Apagar mensagens uma por uma\n                for (const messageId of messageIds){\n                    try {\n                        const response = await fetch(\"\".concat(API_URL, \"/chat/messages/\").concat(messageId), {\n                            method: 'DELETE',\n                            headers: {\n                                Authorization: \"Bearer \".concat(currentToken)\n                            }\n                        });\n                        if (!response.ok) {\n                            if (response.status === 401) {\n                                console.error('Token expirado ou inválido ao apagar mensagem');\n                                handleAuthError();\n                                return null;\n                            }\n                            throw new Error(\"Erro ao apagar mensagem: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        if (data.success) {\n                            results.push(data.data);\n                            // Atualizar o estado das mensagens\n                            setMessages({\n                                \"ChatProvider.useCallback[deleteMessages]\": (prev)=>{\n                                    const updatedMessages = {\n                                        ...prev\n                                    };\n                                    // Percorrer todas as conversas\n                                    Object.keys(updatedMessages).forEach({\n                                        \"ChatProvider.useCallback[deleteMessages]\": (convId)=>{\n                                            // Atualizar a mensagem na conversa correspondente\n                                            updatedMessages[convId] = updatedMessages[convId].map({\n                                                \"ChatProvider.useCallback[deleteMessages]\": (msg)=>{\n                                                    if (msg.id === messageId) {\n                                                        return {\n                                                            ...msg,\n                                                            ...data.data,\n                                                            isDeleted: true\n                                                        };\n                                                    }\n                                                    return msg;\n                                                }\n                                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                        }\n                                    }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                    return updatedMessages;\n                                }\n                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                        } else {\n                            errors.push({\n                                messageId,\n                                error: 'Falha ao apagar mensagem'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao apagar mensagem \".concat(messageId, \":\"), error);\n                        errors.push({\n                            messageId,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                // Limpar o cache de mensagens para forçar uma nova busca\n                if (conversationId && results.length > 0) {\n                    console.log('Limpando cache de mensagens para a conversa:', conversationId);\n                    if (requestCache.messages[conversationId]) {\n                        delete requestCache.messages[conversationId];\n                    }\n                    // Recarregar as mensagens da conversa para garantir que estamos sincronizados com o backend\n                    try {\n                        console.log('Recarregando mensagens após exclusão');\n                        await loadMessages(conversationId);\n                        // Também recarregar a lista de conversas para garantir que tudo está atualizado\n                        await loadConversations(true);\n                    } catch (reloadError) {\n                        console.error('Erro ao recarregar mensagens após exclusão:', reloadError);\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    deleted: results,\n                    errors: errors,\n                    total: messageIds.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao apagar mensagens:', error);\n                return {\n                    success: false,\n                    deleted: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: messageIds.length,\n                    successCount: 0,\n                    errorCount: messageIds.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[deleteMessages]\"], [\n        getCurrentToken,\n        handleAuthError,\n        loadMessages,\n        loadConversations\n    ]);\n    // Memoizar o valor do contexto para evitar re-renderizações desnecessárias\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatProvider.useMemo[contextValue]\": ()=>({\n                conversations,\n                messages,\n                unreadCount,\n                activeConversation,\n                isPanelOpen,\n                isModalOpen,\n                isConnected,\n                isLoading,\n                setActiveConversation,\n                loadMessages,\n                sendMessage,\n                createConversation,\n                createOrGetConversation,\n                toggleChatPanel,\n                toggleChatModal,\n                loadConversations,\n                loadUnreadCount,\n                markMessagesAsRead,\n                resetUnreadCount,\n                addParticipantToGroup,\n                addMultipleParticipantsToGroup,\n                removeParticipantFromGroup,\n                deleteMessages\n            })\n    }[\"ChatProvider.useMemo[contextValue]\"], [\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation,\n        isPanelOpen,\n        isModalOpen,\n        isConnected,\n        isLoading,\n        setActiveConversation,\n        loadMessages,\n        sendMessage,\n        createConversation,\n        createOrGetConversation,\n        toggleChatPanel,\n        toggleChatModal,\n        loadConversations,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        deleteMessages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ChatContext.js\",\n        lineNumber: 1963,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatProvider, \"knvLij1shdeGVeBVK+PUiQX2mtg=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ChatProvider;\nconst useChat = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChat deve ser usado dentro de um ChatProvider');\n    }\n    return context;\n};\n_s1(useChat, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9DaGF0Q29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTRHO0FBQ3BFO0FBQ047QUFFbEMsTUFBTVUsVUFBVUMsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDQyxtQkFBbUIsSUFBSTtBQUVuRCwyQ0FBMkM7QUFDM0MsTUFBTUMsZUFBZTtJQUNuQkMsZUFBZTtRQUFFQyxNQUFNO1FBQU1DLFdBQVc7SUFBRTtJQUMxQ0MsYUFBYTtRQUFFRixNQUFNO1FBQU1DLFdBQVc7SUFBRTtJQUN4Q0UsVUFBVSxDQUFDO0lBQ1hDLFlBQVk7UUFBRUgsV0FBVztRQUFHSSxPQUFPO0lBQU0sRUFBRSxrQ0FBa0M7QUFDL0U7QUFFQSwrQ0FBK0M7QUFDL0MsTUFBTUMsbUJBQW1CLE9BQU8saUNBQWlDO0FBQ2pFLE1BQU1DLHlCQUF5QixPQUFPLHFDQUFxQztBQUUzRSwyREFBMkQ7QUFDM0QsTUFBTUMsV0FBVyxDQUFDQyxNQUFNQztJQUN0QixJQUFJQztJQUNKLE9BQU8sU0FBU0M7UUFBaUI7WUFBR0MsS0FBSCx1QkFBTzs7UUFDdEMsTUFBTUMsUUFBUTtZQUNaQyxhQUFhSjtZQUNiRixRQUFRSTtRQUNWO1FBQ0FFLGFBQWFKO1FBQ2JBLFVBQVVLLFdBQVdGLE9BQU9KO0lBQzlCO0FBQ0Y7QUFFQSxNQUFNTyw0QkFBY2hDLG9EQUFhQSxDQUFDO0FBRTNCLE1BQU1pQyxlQUFlO1FBQUMsRUFBRUMsUUFBUSxFQUFFOztJQUN2QyxNQUFNLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFLEdBQUc3QixxREFBT0E7SUFDaEMsTUFBTSxDQUFDOEIsUUFBUUMsVUFBVSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDcUMsYUFBYUMsZUFBZSxHQUFHdEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDWSxlQUFlMkIsaUJBQWlCLEdBQUd2QywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3JELE1BQU0sQ0FBQ3dDLG9CQUFvQkMsc0JBQXNCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUNnQixVQUFVMEIsWUFBWSxHQUFHMUMsK0NBQVFBLENBQUMsQ0FBQztJQUMxQyxNQUFNLENBQUNlLGFBQWE0QixlQUFlLEdBQUczQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM0QyxhQUFhQyxlQUFlLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUM4QyxhQUFhQyxlQUFlLEdBQUcvQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNnRCxXQUFXQyxhQUFhLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUUzQyxzRkFBc0Y7SUFDdEYsTUFBTWtELGtCQUFrQmhELGtEQUFXQTtxREFBQztZQUNsQyxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLE9BQU9pRCxhQUFhQyxPQUFPLENBQUM7WUFDOUI7WUFDQSxPQUFPO1FBQ1Q7b0RBQUcsRUFBRTtJQUVMLDhDQUE4QztJQUM5QyxNQUFNQyxrQkFBa0JuRCxrREFBV0E7cURBQUM7WUFDbENvRCxRQUFRQyxLQUFLLENBQUM7WUFDZCxzQkFBc0I7WUFDdEJoQixpQkFBaUIsRUFBRTtZQUNuQkcsWUFBWSxDQUFDO1lBQ2JELHNCQUFzQjtZQUN0QkUsZUFBZTtZQUNmRSxlQUFlO1lBQ2ZFLGVBQWU7WUFFZixnQ0FBZ0M7WUFDaEMsSUFBSVosUUFBUTtnQkFDVixJQUFJO29CQUNGQSxPQUFPcUIsVUFBVTtnQkFDbkIsRUFBRSxPQUFPRCxPQUFPO29CQUNkRCxRQUFRQyxLQUFLLENBQUMsK0JBQStCQTtnQkFDL0M7Z0JBQ0FqQixlQUFlO1lBQ2pCO1lBRUEsZUFBZTtZQUNmM0IsYUFBYUMsYUFBYSxDQUFDQyxJQUFJLEdBQUc7WUFDbENGLGFBQWFJLFdBQVcsQ0FBQ0YsSUFBSSxHQUFHO1lBQ2hDRixhQUFhSyxRQUFRLEdBQUcsQ0FBQyxHQUFHLDRCQUE0QjtZQUV4RCwwQkFBMEI7WUFDMUIsSUFBSWtCLFFBQVE7Z0JBQ1ZBO1lBQ0Y7UUFDRjtvREFBRztRQUFDQztRQUFRRDtLQUFPO0lBRW5CLGdGQUFnRjtJQUNoRixNQUFNdUIsdUJBQXVCckQsNkNBQU1BLENBQUM7SUFDcEMsTUFBTXNELGVBQWV0RCw2Q0FBTUEsQ0FBQztJQUM1QixNQUFNdUQscUJBQXFCdkQsNkNBQU1BLENBQUM7SUFDbEMsTUFBTXdELHVCQUF1QnhELDZDQUFNQSxDQUFDO0lBRXBDLGdEQUFnRDtJQUNoREgsZ0RBQVNBO2tDQUFDO1lBQ1IsdURBQXVEO1lBQ3ZELElBQUksQ0FBQ2dDLFFBQVFFLFFBQVE7Z0JBQ25CLElBQUk7b0JBQ0ZBLE9BQU9xQixVQUFVO29CQUNqQmxCLGVBQWU7b0JBQ2ZGLFVBQVU7Z0JBQ1osRUFBRSxPQUFPbUIsT0FBTztvQkFDZEQsUUFBUUMsS0FBSyxDQUFDLDJDQUEyQ0E7Z0JBQzNEO1lBQ0Y7WUFFQSx1Q0FBdUM7WUFDdkNFLHFCQUFxQkksT0FBTyxHQUFHO1lBQy9CRixtQkFBbUJFLE9BQU8sR0FBRztZQUM3QkQscUJBQXFCQyxPQUFPLEdBQUc7UUFDakM7aUNBQUc7UUFBQzVCO1FBQU1FO0tBQU87SUFFakIsOENBQThDO0lBQzlDbEMsZ0RBQVNBO2tDQUFDO1lBQ1IsZ0VBQWdFO1lBQ2hFLElBQUlnQyxNQUFNO2dCQUNScUIsUUFBUVEsR0FBRyxDQUFDLDhEQUE4RDdCLEtBQUs4QixRQUFRO2dCQUN2RixtREFBbUQ7Z0JBQ25EeEIsaUJBQWlCLEVBQUU7Z0JBQ25CRyxZQUFZLENBQUM7Z0JBQ2JELHNCQUFzQjtnQkFDdEJFLGVBQWU7Z0JBQ2ZFLGVBQWU7Z0JBQ2ZFLGVBQWU7Z0JBRWYsZUFBZTtnQkFDZnBDLGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSSxHQUFHO2dCQUNsQ0YsYUFBYUksV0FBVyxDQUFDRixJQUFJLEdBQUc7Z0JBQ2hDRixhQUFhSyxRQUFRLEdBQUcsQ0FBQztnQkFDekJMLGFBQWFNLFVBQVUsQ0FBQ0gsU0FBUyxHQUFHO2dCQUNwQ0gsYUFBYU0sVUFBVSxDQUFDQyxLQUFLLEdBQUc7Z0JBRWhDb0MsUUFBUVEsR0FBRyxDQUFDO2dCQUVaLDhEQUE4RDtnQkFDOUQsSUFBSSxJQUE2QixFQUFFO29CQUNqQywyRUFBMkU7b0JBQzNFLE1BQU1FLGVBQWUsRUFBRTtvQkFDdkIsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlkLGFBQWFlLE1BQU0sRUFBRUQsSUFBSzt3QkFDNUMsTUFBTUUsTUFBTWhCLGFBQWFnQixHQUFHLENBQUNGO3dCQUM3QixJQUFJRSxPQUFRQSxDQUFBQSxJQUFJQyxVQUFVLENBQUMsWUFBWUQsSUFBSUMsVUFBVSxDQUFDLG9CQUFvQkQsSUFBSUMsVUFBVSxDQUFDLFdBQVUsR0FBSTs0QkFDckdKLGFBQWFLLElBQUksQ0FBQ0Y7d0JBQ3BCO29CQUNGO29CQUNBSCxhQUFhTSxPQUFPO2tEQUFDSCxDQUFBQSxNQUFPaEIsYUFBYW9CLFVBQVUsQ0FBQ0o7O29CQUNwRGIsUUFBUVEsR0FBRyxDQUFDO2dCQUNkO1lBQ0Y7UUFDRjtpQ0FBRztRQUFDN0IsaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRTtLQUFDLEdBQUcsd0NBQXdDO0lBRXhELGdDQUFnQztJQUNoQ3ZFLGdEQUFTQTtrQ0FBQztZQUNSLHFDQUFxQztZQUNyQyxJQUFJLENBQUNnQyxNQUFNO2dCQUNUcUIsUUFBUVEsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxtQ0FBbUM7WUFDbkNSLFFBQVFRLEdBQUcsQ0FBQyxvQ0FBb0M3QixLQUFLd0MsSUFBSSxJQUFJO1lBRTdELG1EQUFtRDtZQUNuRCxJQUFJdEMsVUFBVUUsYUFBYTtnQkFDekJpQixRQUFRUSxHQUFHLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLGlGQUFpRjtZQUNqRixJQUFJTCxxQkFBcUJJLE9BQU8sSUFBSWEsS0FBS0MsR0FBRyxLQUFLaEIsbUJBQW1CRSxPQUFPLEdBQUcsT0FBTztnQkFDbkZQLFFBQVFRLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsMEVBQTBFO1lBQzFFTCxxQkFBcUJJLE9BQU8sR0FBRztZQUMvQkYsbUJBQW1CRSxPQUFPLEdBQUdhLEtBQUtDLEdBQUc7WUFFckMsaURBQWlEO1lBQ2pELE1BQU1DOzhEQUFzQjtvQkFDMUIsc0VBQXNFO29CQUN0RSxJQUFJLENBQUMzQyxNQUFNO29CQUVYLG1DQUFtQztvQkFDbkNxQixRQUFRUSxHQUFHLENBQUMsaUNBQWlDN0IsS0FBS3dDLElBQUksSUFBSTtvQkFFMUQsTUFBTUksZUFBZTNCO29CQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUVuQiwyREFBMkQ7b0JBQzNELElBQUkxQyxRQUFRO3dCQUNWLElBQUlFLGFBQWE7NEJBQ2ZpQixRQUFRUSxHQUFHLENBQUM7NEJBQ1osT0FBTzNCLFFBQVEsOEJBQThCO3dCQUMvQyxPQUFPOzRCQUNMLHVGQUF1Rjs0QkFDdkZtQixRQUFRUSxHQUFHLENBQUM7NEJBQ1osSUFBSTtnQ0FDRiwrREFBK0Q7Z0NBQy9ELElBQUksQ0FBQzNCLE9BQU8yQyxTQUFTLElBQUkzQyxPQUFPNEMsT0FBTyxFQUFFO29DQUN2QzVDLE9BQU80QyxPQUFPO29DQUNkekIsUUFBUVEsR0FBRyxDQUFDO29DQUNaLE9BQU8zQjtnQ0FDVDs0QkFDRixFQUFFLE9BQU9vQixPQUFPO2dDQUNkRCxRQUFRQyxLQUFLLENBQUMsd0NBQXdDQTtnQ0FDdEQsdUNBQXVDO2dDQUN2QyxJQUFJO29DQUNGcEIsT0FBT3FCLFVBQVU7Z0NBQ25CLEVBQUUsT0FBT3dCLGlCQUFpQjtvQ0FDeEIxQixRQUFRQyxLQUFLLENBQUMsdURBQXVEeUI7Z0NBQ3ZFOzRCQUNGO3dCQUNGO29CQUNGO29CQUVBLGdFQUFnRTtvQkFDaEV2QixxQkFBcUJJLE9BQU8sR0FBRztvQkFFL0IscUNBQXFDO29CQUNyQyxNQUFNb0IsdUJBQXVCO29CQUM3QixNQUFNQyxpQkFBaUIsTUFBTSxhQUFhO29CQUUxQzVCLFFBQVFRLEdBQUcsQ0FBQztvQkFFWixJQUFJO3dCQUNGLGlFQUFpRTt3QkFDakUsc0VBQXNFO3dCQUN0RSx3REFBd0Q7d0JBQ3hEUixRQUFRUSxHQUFHLENBQUMseURBQXlEN0IsS0FBSzhCLFFBQVE7d0JBRWxGVCxRQUFRUSxHQUFHLENBQUM7d0JBRVosTUFBTXFCLGlCQUFpQjdFLDREQUFFQSxDQUFDQyxTQUFTOzRCQUNqQzZFLE1BQU07NEJBQ05DLE1BQU07Z0NBQUVDLE9BQU9UOzRCQUFhOzRCQUM1QlUsWUFBWTtnQ0FBQztnQ0FBYTs2QkFBVTs0QkFDcENDLHNCQUFzQlA7NEJBQ3RCUSxtQkFBbUJQOzRCQUNuQjFELFNBQVM7NEJBQ1RrRSxhQUFhOzRCQUNiQyxjQUFjO3dCQUNoQjt3QkFFQVIsZUFBZVMsRUFBRSxDQUFDOzBFQUFXO2dDQUMzQnRDLFFBQVFRLEdBQUcsQ0FBQztnQ0FDWnhCLGVBQWU7Z0NBQ2ZzQixxQkFBcUJDLE9BQU8sR0FBRztnQ0FFL0IsMkVBQTJFO2dDQUMzRWdDLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHlCQUF5QjtvQ0FDNURDLFFBQVE7d0NBQUVDLE1BQU07d0NBQWNDLFFBQVE7d0NBQWFwRixXQUFXNEQsS0FBS0MsR0FBRztvQ0FBRztnQ0FDM0U7NEJBQ0Y7O3dCQUVBUSxlQUFlUyxFQUFFLENBQUM7MEVBQWM7Z0NBQzlCdEMsUUFBUVEsR0FBRyxDQUFDO2dDQUNaeEIsZUFBZTtnQ0FFZiw4RUFBOEU7Z0NBQzlFdUQsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVkseUJBQXlCO29DQUM1REMsUUFBUTt3Q0FBRUMsTUFBTTt3Q0FBY0MsUUFBUTt3Q0FBZ0JwRixXQUFXNEQsS0FBS0MsR0FBRztvQ0FBRztnQ0FDOUU7NEJBQ0Y7O3dCQUVBUSxlQUFlUyxFQUFFLENBQUM7MEVBQWlCLENBQUNyQztnQ0FDbENELFFBQVFDLEtBQUssQ0FBQywrQkFBK0JBO2dDQUM3Q0sscUJBQXFCQyxPQUFPO2dDQUU1Qix1REFBdUQ7Z0NBQ3ZELElBQUlOLE1BQU00QyxPQUFPLElBQUk1QyxNQUFNNEMsT0FBTyxDQUFDQyxRQUFRLENBQUMseUJBQXlCO29DQUNuRTlDLFFBQVFDLEtBQUssQ0FBQztvQ0FDZDRCLGVBQWUzQixVQUFVO29DQUN6QmxCLGVBQWU7b0NBQ2ZtQixxQkFBcUJJLE9BQU8sR0FBRyxPQUFPLG9DQUFvQztvQ0FDMUU7Z0NBQ0Y7Z0NBRUEsSUFBSUQscUJBQXFCQyxPQUFPLElBQUlvQixzQkFBc0I7b0NBQ3hEM0IsUUFBUUMsS0FBSyxDQUFDO29DQUNkNEIsZUFBZTNCLFVBQVU7b0NBQ3pCbEIsZUFBZTtvQ0FDZm1CLHFCQUFxQkksT0FBTyxHQUFHLE9BQU8sb0NBQW9DO2dDQUM1RTs0QkFDRjs7d0JBRUEsc0NBQXNDO3dCQUN0Q3NCLGVBQWVTLEVBQUUsQ0FBQzswRUFBZSxDQUFDTztnQ0FDaEMsSUFBSSxDQUFDQSxXQUFXLENBQUNBLFFBQVFFLGNBQWMsRUFBRTtvQ0FDdkMvQyxRQUFRQyxLQUFLLENBQUMsK0JBQStCNEM7b0NBQzdDO2dDQUNGO2dDQUVBLDhEQUE4RDtnQ0FDOUQsTUFBTUcsU0FBU3JFLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUU7Z0NBRXZCLHdGQUF3RjtnQ0FDeEYsc0dBQXNHO2dDQUN0RyxNQUFNK0IsU0FBU0osUUFBUUksTUFBTTtnQ0FDN0IsTUFBTUMsdUJBQXVCTCxRQUFRTSxRQUFRLEtBQUtIO2dDQUdsRCxrQ0FBa0M7Z0NBQ2xDNUQ7a0ZBQVlnRSxDQUFBQTt3Q0FDVixNQUFNQyx1QkFBdUJELElBQUksQ0FBQ1AsUUFBUUUsY0FBYyxDQUFDLElBQUksRUFBRTt3Q0FFL0QsMkRBQTJEO3dDQUMzRCxJQUFJTSxxQkFBcUJDLElBQUk7MEZBQUNDLENBQUFBLElBQUtBLEVBQUVyQyxFQUFFLEtBQUsyQixRQUFRM0IsRUFBRTswRkFBRzs0Q0FDdkQsT0FBT2tDO3dDQUNUO3dDQUVBLDBFQUEwRTt3Q0FDMUUsSUFBSUgsVUFBVUkscUJBQXFCQyxJQUFJOzBGQUFDQyxDQUFBQSxJQUFLQSxFQUFFckMsRUFBRSxLQUFLK0I7MEZBQVM7NENBQzdELE9BQU87Z0RBQ0wsR0FBR0csSUFBSTtnREFDUCxDQUFDUCxRQUFRRSxjQUFjLENBQUMsRUFBRU0scUJBQXFCRyxHQUFHO2tHQUFDRCxDQUFBQSxJQUNqREEsRUFBRXJDLEVBQUUsS0FBSytCLFNBQVNKLFVBQVVVOzs0Q0FFaEM7d0NBQ0Y7d0NBRUEsdUZBQXVGO3dDQUN2RixNQUFNTCx1QkFBdUJMLFFBQVFNLFFBQVEsS0FBS0gsVUFBVUgsUUFBUVksY0FBYyxLQUFLVDt3Q0FFdkYsSUFBSUUsd0JBQXdCLENBQUNELFFBQVE7NENBQ25DLHdFQUF3RTs0Q0FDeEUsTUFBTTVCLE1BQU0sSUFBSUQ7NENBQ2hCLE1BQU1zQyxlQUFlTCxxQkFBcUJNLE1BQU07MkdBQUNKLENBQUFBO29EQUMvQyxJQUFJLENBQUNBLEVBQUVLLE1BQU0sRUFBRSxPQUFPO29EQUN0QixJQUFJLENBQUVMLENBQUFBLEVBQUVKLFFBQVEsS0FBS0gsVUFBVU8sRUFBRUUsY0FBYyxLQUFLVCxNQUFLLEdBQUksT0FBTztvREFDcEUsSUFBSSxNQUFPLElBQUk1QixLQUFLbUMsRUFBRU0sU0FBUyxLQUFNLE9BQU8sT0FBTyxPQUFPLGNBQWM7b0RBRXhFLCtEQUErRDtvREFDL0QsSUFBSWhCLFFBQVFpQixXQUFXLEtBQUssZ0JBQWdCUCxFQUFFTyxXQUFXLEtBQUssY0FBYzt3REFDMUUsT0FBTztvREFDVDtvREFFQSwyREFBMkQ7b0RBQzNELElBQUlqQixRQUFRaUIsV0FBVyxLQUFLLFVBQVVQLEVBQUVPLFdBQVcsS0FBSyxVQUFVUCxFQUFFUSxPQUFPLEtBQUtsQixRQUFRa0IsT0FBTyxFQUFFO3dEQUMvRixPQUFPO29EQUNUO29EQUVBLE9BQU87Z0RBQ1Q7OzRDQUVBLElBQUlMLGFBQWE5QyxNQUFNLEdBQUcsR0FBRztnREFDM0IsdURBQXVEO2dEQUN2RCxNQUFNb0QsY0FBY04sWUFBWSxDQUFDLEVBQUU7Z0RBRW5DLE9BQU87b0RBQ0wsR0FBR04sSUFBSTtvREFDUCxDQUFDUCxRQUFRRSxjQUFjLENBQUMsRUFBRU0scUJBQXFCRyxHQUFHO3NHQUFDRCxDQUFBQSxJQUNqREEsRUFBRXJDLEVBQUUsS0FBSzhDLFlBQVk5QyxFQUFFLEdBQUcyQixVQUFVVTs7Z0RBRXhDOzRDQUNGO3dDQUNGO3dDQUVBLDRDQUE0Qzt3Q0FDNUMscURBQXFEO3dDQUNyRCxNQUFNVSxrQkFBa0I7K0NBQUlaOzRDQUFzQlI7eUNBQVEsQ0FBQ3FCLElBQUk7MEdBQUMsQ0FBQ0MsR0FBR0MsSUFDbEUsSUFBSWhELEtBQUsrQyxFQUFFTixTQUFTLElBQUksSUFBSXpDLEtBQUtnRCxFQUFFUCxTQUFTOzt3Q0FHOUMsT0FBTzs0Q0FDTCxHQUFHVCxJQUFJOzRDQUNQLENBQUNQLFFBQVFFLGNBQWMsQ0FBQyxFQUFFa0I7d0NBQzVCO29DQUNGOztnQ0FFQSxtREFBbUQ7Z0NBQ25EaEY7a0ZBQWlCbUUsQ0FBQUE7d0NBQ2YsaUNBQWlDO3dDQUNqQyxNQUFNaUIsZUFBZWpCLEtBQUtrQixJQUFJO3VHQUFDQyxDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYzs7d0NBQ25FLElBQUksQ0FBQ3NCLGNBQWMsT0FBT2pCO3dDQUUxQiwyRkFBMkY7d0NBQzNGLElBQUlILFVBQVVvQixhQUFhRyxXQUFXLElBQUlILGFBQWFHLFdBQVcsQ0FBQ3RELEVBQUUsS0FBSytCLFFBQVE7NENBQ2hGLG1EQUFtRDs0Q0FDbkQsT0FBT0csS0FBS0ksR0FBRzs4RkFBQ2UsQ0FBQUE7b0RBQ2QsSUFBSUEsRUFBRXJELEVBQUUsS0FBSzJCLFFBQVFFLGNBQWMsRUFBRTt3REFDbkMsT0FBTzs0REFBRSxHQUFHd0IsQ0FBQzs0REFBRUMsYUFBYTNCO3dEQUFRO29EQUN0QztvREFDQSxPQUFPMEI7Z0RBQ1Q7O3dDQUNGO3dDQUVBLDJHQUEyRzt3Q0FDM0csTUFBTXJCLHVCQUF1QkwsUUFBUU0sUUFBUSxLQUFLSCxVQUFVSCxRQUFRWSxjQUFjLEtBQUtUO3dDQUN2RixJQUFJRSx3QkFBd0IsQ0FBQ0QsVUFBVW9CLGFBQWFHLFdBQVcsSUFBSUgsYUFBYUcsV0FBVyxDQUFDWixNQUFNLEVBQUU7NENBQ2xHLHFFQUFxRTs0Q0FDckUsSUFBSVMsYUFBYUcsV0FBVyxDQUFDVCxPQUFPLEtBQUtsQixRQUFRa0IsT0FBTyxJQUNuRE0sQ0FBQUEsYUFBYUcsV0FBVyxDQUFDckIsUUFBUSxLQUFLSCxVQUFVcUIsYUFBYUcsV0FBVyxDQUFDZixjQUFjLEtBQUtULE1BQUssR0FBSTtnREFDeEcsbURBQW1EO2dEQUNuRCxPQUFPSSxLQUFLSSxHQUFHO2tHQUFDZSxDQUFBQTt3REFDZCxJQUFJQSxFQUFFckQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYyxFQUFFOzREQUNuQyxPQUFPO2dFQUFFLEdBQUd3QixDQUFDO2dFQUFFQyxhQUFhM0I7NERBQVE7d0RBQ3RDO3dEQUNBLE9BQU8wQjtvREFDVDs7NENBQ0Y7d0NBQ0Y7d0NBRUEsb0NBQW9DO3dDQUNwQyxNQUFNRSx1QkFBdUJyQixLQUFLTyxNQUFNOytHQUFDWSxDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYzs7d0NBQzdFLE9BQU87NENBQ0w7Z0RBQUUsR0FBR3NCLFlBQVk7Z0RBQUVHLGFBQWEzQjs0Q0FBUTsrQ0FDckM0Qjt5Q0FDSjtvQ0FDSDs7Z0NBRUEsd0ZBQXdGO2dDQUN4RixNQUFNQyxvQkFBb0I3QixRQUFRTSxRQUFRLEtBQUtILFVBQVVILFFBQVFZLGNBQWMsS0FBS1Q7Z0NBQ3BGLElBQUksQ0FBQzBCLG1CQUFtQjtvQ0FDdEIxRSxRQUFRUSxHQUFHLENBQUM7b0NBRVosMERBQTBEO29DQUMxRCxNQUFNNkQsZUFBZS9HLGNBQWNnSCxJQUFJO21HQUFDQyxDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYzs7b0NBQzVFLE1BQU00QixtQkFBbUJOLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY08sU0FBUyxLQUFJO29DQUVwRCxpREFBaUQ7b0NBQ2pEM0Y7c0ZBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS0ksR0FBRzs4RkFBQ3FCLENBQUFBO29EQUNoQyxJQUFJQSxLQUFLM0QsRUFBRSxLQUFLMkIsUUFBUUUsY0FBYyxFQUFFO3dEQUN0Qy9DLFFBQVFRLEdBQUcsQ0FBQyx3QkFBZ0MsT0FBUnFFLEtBQUszRCxFQUFFLEVBQUM7d0RBQzVDLE9BQU87NERBQUUsR0FBRzJELElBQUk7NERBQUVELFdBQVc7d0RBQUs7b0RBQ3BDO29EQUNBLE9BQU9DO2dEQUNUOzs7b0NBRUEsbUZBQW1GO29DQUNuRixJQUFJLENBQUNGLGtCQUFrQjt3Q0FDckJ0RjswRkFBZStELENBQUFBO2dEQUNiLE1BQU0wQixXQUFXMUIsT0FBTztnREFDeEJwRCxRQUFRUSxHQUFHLENBQUMsNkRBQTZENEMsTUFBTSxRQUFRMEI7Z0RBQ3ZGLE9BQU9BOzRDQUNUOztvQ0FDRjtnQ0FDRixPQUFPO29DQUNMOUUsUUFBUVEsR0FBRyxDQUFDO2dDQUNkO2dDQUVBLGlGQUFpRjtnQ0FDakYrQixPQUFPQyxhQUFhLENBQUMsSUFBSUMsWUFBWSx5QkFBeUI7b0NBQzVEQyxRQUFRO3dDQUFFQyxNQUFNO3dDQUFXSSxnQkFBZ0JGLFFBQVFFLGNBQWM7d0NBQUV2RixXQUFXNEQsS0FBS0MsR0FBRztvQ0FBRztnQ0FDM0Y7NEJBQ0Y7O3dCQUVBLDZEQUE2RDt3QkFDN0RRLGVBQWVTLEVBQUUsQ0FBQzswRUFBaUIsQ0FBQy9FO2dDQUNsQ3lDLFFBQVFRLEdBQUcsQ0FBQyw0REFBNERqRDtnQ0FFeEUsMEVBQTBFO2dDQUMxRSxNQUFNd0gsMkJBQTJCeEgsS0FBS3dILHdCQUF3QixJQUFJeEgsS0FBS3lILFdBQVcsSUFBSTtnQ0FFdEYsSUFBSSxPQUFPRCw2QkFBNkIsVUFBVTtvQ0FDaEQvRSxRQUFRUSxHQUFHLENBQUMsMEVBQWdHLE9BQXpCdUU7b0NBQ25GMUYsZUFBZTBGO29DQUVmLGlEQUFpRDtvQ0FDakQsSUFBSXhILEtBQUtELGFBQWEsSUFBSTJILE1BQU1DLE9BQU8sQ0FBQzNILEtBQUtELGFBQWEsR0FBRzt3Q0FDM0QwQyxRQUFRUSxHQUFHLENBQUMsMkJBQXFELE9BQTFCakQsS0FBS0QsYUFBYSxDQUFDc0QsTUFBTSxFQUFDO3dDQUNqRTNCOzBGQUFpQm1FLENBQUFBO2dEQUNmLE9BQU9BLEtBQUtJLEdBQUc7a0dBQUNxQixDQUFBQTt3REFDZCxnREFBZ0Q7d0RBQ2hELE1BQU1NLGFBQWE1SCxLQUFLRCxhQUFhLENBQUNnSCxJQUFJO3FIQUN4Q2MsQ0FBQUEsT0FBUUEsS0FBS3JDLGNBQWMsS0FBSzhCLEtBQUszRCxFQUFFOzt3REFHekMsSUFBSWlFLFlBQVk7NERBQ2QsTUFBTVAsWUFBWU8sV0FBV1AsU0FBUyxJQUFLTyxXQUFXMUgsV0FBVyxJQUFJMEgsV0FBVzFILFdBQVcsR0FBRzs0REFDOUZ1QyxRQUFRUSxHQUFHLENBQUMsd0JBQTREb0UsT0FBcENDLEtBQUszRCxFQUFFLEVBQUMsaUNBQXNDLE9BQVYwRDs0REFDeEUsT0FBTztnRUFBRSxHQUFHQyxJQUFJO2dFQUFFRDs0REFBVTt3REFDOUI7d0RBQ0Esc0RBQXNEO3dEQUN0RCxPQUFPOzREQUFFLEdBQUdDLElBQUk7NERBQUVELFdBQVc7d0RBQU07b0RBQ3JDOzs0Q0FDRjs7b0NBQ0Y7Z0NBQ0Y7Z0NBRUEsNEZBQTRGO2dDQUM1RnJDLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHlCQUF5QjtvQ0FDNURDLFFBQVE7d0NBQUVDLE1BQU07d0NBQVVuRixXQUFXNEQsS0FBS0MsR0FBRztvQ0FBRztnQ0FDbEQ7NEJBQ0Y7O3dCQUVBLGdEQUFnRDt3QkFDaERRLGVBQWVTLEVBQUUsQ0FBQzswRUFBd0IsQ0FBQy9FO2dDQUN6Q3lDLFFBQVFRLEdBQUcsQ0FBQyw2REFBNkRqRDtnQ0FDekUsa0RBQWtEO2dDQUNsRCxJQUFJQSxNQUFNO29DQUNSLDBFQUEwRTtvQ0FDMUUsTUFBTThILHFCQUFxQkosTUFBTUMsT0FBTyxDQUFDM0gsUUFBUUEsT0FBT0EsS0FBS0QsYUFBYSxJQUFJLEVBQUU7b0NBRWhGLElBQUkySCxNQUFNQyxPQUFPLENBQUNHLHVCQUF1QkEsbUJBQW1CekUsTUFBTSxHQUFHLEdBQUc7d0NBQ3RFWixRQUFRUSxHQUFHLENBQUMsZUFBeUMsT0FBMUI2RSxtQkFBbUJ6RSxNQUFNLEVBQUM7d0NBQ3JEM0IsaUJBQWlCb0c7d0NBRWpCLDhEQUE4RDt3Q0FDOURDLHFCQUFxQi9FLE9BQU8sR0FBRzt3Q0FFL0Isb0JBQW9CO3dDQUNwQmxELGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSSxHQUFHOEg7d0NBQ2xDaEksYUFBYUMsYUFBYSxDQUFDRSxTQUFTLEdBQUc0RCxLQUFLQyxHQUFHO3dDQUUvQywrRUFBK0U7d0NBQy9Fa0IsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVkseUJBQXlCOzRDQUM1REMsUUFBUTtnREFBRUMsTUFBTTtnREFBaUJuRixXQUFXNEQsS0FBS0MsR0FBRzs0Q0FBRzt3Q0FDekQ7b0NBQ0YsT0FBTzt3Q0FDTHJCLFFBQVFDLEtBQUssQ0FBQyx3RUFBd0UxQzt3Q0FFdEYsaUVBQWlFO3dDQUNqRSxJQUFJMEgsTUFBTUMsT0FBTyxDQUFDRyx1QkFBdUJBLG1CQUFtQnpFLE1BQU0sS0FBSyxHQUFHOzRDQUN4RVosUUFBUVEsR0FBRyxDQUFDOzRDQUNaK0Usa0JBQWtCO3dDQUNwQjtvQ0FDRjtnQ0FDRjs0QkFDRjs7d0JBRUEseURBQXlEO3dCQUN6RDFELGVBQWVTLEVBQUUsQ0FBQzswRUFBd0IsQ0FBQy9FO2dDQUN6Q3lDLFFBQVFRLEdBQUcsQ0FBQyxnREFBZ0RqRDtnQ0FDNUQsSUFBSUEsUUFBUUEsS0FBSzJELEVBQUUsRUFBRTtvQ0FDbkIsK0NBQStDO29DQUMvQ2pDO3NGQUFpQm1FLENBQUFBOzRDQUNmLG9DQUFvQzs0Q0FDcEMsSUFBSUEsS0FBS0UsSUFBSTs4RkFBQ2lCLENBQUFBLElBQUtBLEVBQUVyRCxFQUFFLEtBQUszRCxLQUFLMkQsRUFBRTs4RkFBRztnREFDcEMsT0FBT2tDOzRDQUNUOzRDQUNBLE9BQU87Z0RBQUM3RjttREFBUzZGOzZDQUFLO3dDQUN4Qjs7Z0NBQ0Y7NEJBQ0Y7O3dCQUVBLGdEQUFnRDt3QkFDaER2QixlQUFlUyxFQUFFLENBQUM7MEVBQXVCLENBQUMvRTtnQ0FDeEN5QyxRQUFRUSxHQUFHLENBQUMsaURBQWlEakQ7Z0NBQzdELElBQUlBLFFBQVFBLEtBQUt3RixjQUFjLElBQUl4RixLQUFLaUksYUFBYSxFQUFFO29DQUNyRCwrREFBK0Q7b0NBQy9ELElBQUlqSSxLQUFLaUksYUFBYSxNQUFLN0csaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRSxHQUFFO3dDQUNuQ2pDOzBGQUFpQm1FLENBQUFBLE9BQVFBLEtBQUtPLE1BQU07a0dBQUNZLENBQUFBLElBQUtBLEVBQUVyRCxFQUFFLEtBQUszRCxLQUFLd0YsY0FBYzs7O3dDQUV0RSxrQ0FBa0M7d0NBQ2xDLElBQUk3RCx1QkFBdUIzQixLQUFLd0YsY0FBYyxFQUFFOzRDQUM5QzVELHNCQUFzQjt3Q0FDeEI7d0NBRUEsK0JBQStCO3dDQUMvQkM7MEZBQVlnRSxDQUFBQTtnREFDVixNQUFNcUMsVUFBVTtvREFBRSxHQUFHckMsSUFBSTtnREFBQztnREFDMUIsT0FBT3FDLE9BQU8sQ0FBQ2xJLEtBQUt3RixjQUFjLENBQUM7Z0RBQ25DLE9BQU8wQzs0Q0FDVDs7b0NBQ0YsT0FBTzt3Q0FDTCxpREFBaUQ7d0NBQ2pEeEc7MEZBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS0ksR0FBRztrR0FBQ3FCLENBQUFBO3dEQUNoQyxJQUFJQSxLQUFLM0QsRUFBRSxLQUFLM0QsS0FBS3dGLGNBQWMsRUFBRTs0REFDbkMsT0FBTztnRUFDTCxHQUFHOEIsSUFBSTtnRUFDUGEsY0FBY2IsS0FBS2EsWUFBWSxDQUFDL0IsTUFBTTtrSEFBQ2dDLENBQUFBLElBQ3BDQSxFQUFFM0MsTUFBTSxLQUFLekYsS0FBS2lJLGFBQWEsSUFBSUcsRUFBRUMsUUFBUSxLQUFLckksS0FBS2lJLGFBQWE7OzREQUV6RTt3REFDRjt3REFDQSxPQUFPWDtvREFDVDs7O29DQUNGO2dDQUNGOzRCQUNGOzt3QkFFQSxtREFBbUQ7d0JBQ25EaEQsZUFBZVMsRUFBRSxDQUFDOzBFQUFxQixDQUFDL0U7Z0NBQ3RDeUMsUUFBUVEsR0FBRyxDQUFDLDJDQUEyQ2pEO2dDQUN2RCxJQUFJQSxRQUFRQSxLQUFLd0YsY0FBYyxJQUFJeEYsS0FBS3lGLE1BQU0sTUFBS3JFLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUUsR0FBRTtvQ0FDM0QsOEJBQThCO29DQUM5QmpDO3NGQUFpQm1FLENBQUFBLE9BQVFBLEtBQUtPLE1BQU07OEZBQUNZLENBQUFBLElBQUtBLEVBQUVyRCxFQUFFLEtBQUszRCxLQUFLd0YsY0FBYzs7O29DQUV0RSxrQ0FBa0M7b0NBQ2xDLElBQUk3RCx1QkFBdUIzQixLQUFLd0YsY0FBYyxFQUFFO3dDQUM5QzVELHNCQUFzQjtvQ0FDeEI7b0NBRUEsK0JBQStCO29DQUMvQkM7c0ZBQVlnRSxDQUFBQTs0Q0FDVixNQUFNcUMsVUFBVTtnREFBRSxHQUFHckMsSUFBSTs0Q0FBQzs0Q0FDMUIsT0FBT3FDLE9BQU8sQ0FBQ2xJLEtBQUt3RixjQUFjLENBQUM7NENBQ25DLE9BQU8wQzt3Q0FDVDs7b0NBRUEsNkNBQTZDO29DQUM3Q2xELE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHlCQUF5Qjt3Q0FDNURDLFFBQVE7NENBQ05DLE1BQU07NENBQ05rRCxRQUFROzRDQUNSOUMsZ0JBQWdCeEYsS0FBS3dGLGNBQWM7NENBQ25DdkYsV0FBVzRELEtBQUtDLEdBQUc7d0NBQ3JCO29DQUNGO2dDQUNGOzRCQUNGOzt3QkFFQXZDLFVBQVUrQzt3QkFFVixPQUFPQTtvQkFDVCxFQUFFLE9BQU81QixPQUFPO3dCQUNkRCxRQUFRQyxLQUFLLENBQUMsa0NBQWtDQTt3QkFDaERqQixlQUFlO3dCQUNmbUIscUJBQXFCSSxPQUFPLEdBQUcsT0FBTyxvQ0FBb0M7d0JBQzFFLE9BQU87b0JBQ1Q7Z0JBQ0Y7O1lBRUEsc0VBQXNFO1lBQ3RFLElBQUl1RiwyQkFBMkI7WUFFL0Isd0NBQXdDO1lBQ3hDLE1BQU1DO3lEQUFpQjtvQkFDckIsSUFBSUQsMEJBQTBCO3dCQUM1QjlGLFFBQVFRLEdBQUcsQ0FBQzt3QkFDWjtvQkFDRjtvQkFFQXNGLDJCQUEyQjtvQkFFM0Isa0ZBQWtGO29CQUNsRnZIO2lFQUFXOzRCQUNUK0Msc0JBQ0cwRSxJQUFJO3lFQUFDQyxDQUFBQTtvQ0FDSmpHLFFBQVFRLEdBQUcsQ0FBQyx5Q0FBeUN5RixTQUFTLFlBQVk7Z0NBQzVFO3dFQUNDQyxLQUFLO3lFQUFDakcsQ0FBQUE7b0NBQ0xELFFBQVFDLEtBQUssQ0FBQyxrQ0FBa0NBO29DQUNoREUscUJBQXFCSSxPQUFPLEdBQUcsT0FBTyxvQ0FBb0M7Z0NBQzVFO3dFQUNDNEYsT0FBTzt5RUFBQztvQ0FDUEwsMkJBQTJCO2dDQUM3Qjs7d0JBQ0o7Z0VBQUcsT0FBTywwQ0FBMEM7Z0JBQ3REOztZQUVBLG1DQUFtQztZQUNuQ0M7WUFFQSxxRkFBcUY7WUFDckY7MENBQU87b0JBQ0wsK0VBQStFO29CQUMvRSxJQUFJLENBQUNwSCxNQUFNO3dCQUNUcUIsUUFBUVEsR0FBRyxDQUFDO3dCQUNaLElBQUkzQixRQUFROzRCQUNWLElBQUk7Z0NBQ0ZBLE9BQU9xQixVQUFVO2dDQUNqQkMscUJBQXFCSSxPQUFPLEdBQUcsT0FBTyx1Q0FBdUM7NEJBQy9FLEVBQUUsT0FBT04sT0FBTztnQ0FDZEQsUUFBUUMsS0FBSyxDQUFDLCtCQUErQkE7NEJBQy9DO3dCQUNGO29CQUNGLE9BQU87d0JBQ0xELFFBQVFRLEdBQUcsQ0FBQztvQkFDZDtnQkFDRjs7UUFDRixvRUFBb0U7UUFDcEUsdURBQXVEO1FBQ3ZEO2lDQUFHO1FBQUM3QjtRQUFNaUI7UUFBaUJHO0tBQWdCO0lBRTNDLDBDQUEwQztJQUMxQyxNQUFNd0Ysb0JBQW9CM0ksa0RBQVdBO3VEQUFDO2dCQUFPd0osZ0ZBQWU7WUFDMURwRyxRQUFRUSxHQUFHLENBQUMsZ0RBQWdENEY7WUFDNUQsSUFBSTtnQkFDRixxQ0FBcUM7Z0JBQ3JDLElBQUksQ0FBQ3pILE1BQU07b0JBQ1RxQixRQUFRQyxLQUFLLENBQUM7b0JBQ2QsT0FBTyxFQUFFO2dCQUNYO2dCQUVBLDBDQUEwQztnQkFDMUNELFFBQVFRLEdBQUcsQ0FBQyw4QkFBOEI3QixLQUFLd0MsSUFBSSxJQUFJO2dCQUV2RCxrQ0FBa0M7Z0JBQ2xDLElBQUl6QixhQUFhVSxhQUFhRyxPQUFPLEVBQUU7b0JBQ3JDUCxRQUFRUSxHQUFHLENBQUM7b0JBQ1osT0FBT2xEO2dCQUNUO2dCQUVBLDZFQUE2RTtnQkFDN0UsSUFBSSxDQUFDOEksZ0JBQWdCOUksY0FBY3NELE1BQU0sR0FBRyxHQUFHO29CQUM3Q1osUUFBUVEsR0FBRyxDQUFDO29CQUNaLE9BQU9sRDtnQkFDVDtnQkFFQSwwREFBMEQ7Z0JBQzFEOEMsYUFBYUcsT0FBTyxHQUFHO2dCQUV2QixvREFBb0Q7Z0JBQ3BELE1BQU1jLE1BQU1ELEtBQUtDLEdBQUc7Z0JBQ3BCLElBQUksQ0FBQytFLGdCQUNEL0ksYUFBYUMsYUFBYSxDQUFDQyxJQUFJLElBQy9CRixhQUFhQyxhQUFhLENBQUNDLElBQUksQ0FBQ3FELE1BQU0sR0FBRyxLQUN6Q1MsTUFBTWhFLGFBQWFDLGFBQWEsQ0FBQ0UsU0FBUyxHQUFHSyxrQkFBa0I7b0JBQ2pFbUMsUUFBUVEsR0FBRyxDQUFDO29CQUNaLE9BQU9uRCxhQUFhQyxhQUFhLENBQUNDLElBQUk7Z0JBQ3hDO2dCQUVBeUMsUUFBUVEsR0FBRyxDQUFDO2dCQUVaLE1BQU1lLGVBQWUzQjtnQkFDckJJLFFBQVFRLEdBQUcsQ0FBQyxnQ0FBZ0NlLGVBQWUsZUFBZTtnQkFDMUUsSUFBSSxDQUFDQSxjQUFjO29CQUNqQnZCLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPLEVBQUU7Z0JBQ1g7Z0JBRUFOLGFBQWE7Z0JBRWIsSUFBSTtvQkFDRkssUUFBUVEsR0FBRyxDQUFDO29CQUNaUixRQUFRUSxHQUFHLENBQUMsNEJBQTRCLEdBQVcsT0FBUnZELFNBQVE7b0JBQ25ELE1BQU1vSixXQUFXLE1BQU1DLE1BQU0sR0FBVyxPQUFSckosU0FBUSw2Q0FBMkM7d0JBQ2pGc0osU0FBUzs0QkFBRUMsZUFBZSxVQUF1QixPQUFiakY7d0JBQWU7b0JBQ3JEO29CQUNBdkIsUUFBUVEsR0FBRyxDQUFDLHVCQUF1QjZGLFNBQVN6RCxNQUFNLEVBQUV5RCxTQUFTSSxVQUFVO29CQUV2RSxJQUFJLENBQUNKLFNBQVNLLEVBQUUsRUFBRTt3QkFDaEIsSUFBSUwsU0FBU3pELE1BQU0sS0FBSyxLQUFLOzRCQUMzQjVDLFFBQVFDLEtBQUssQ0FBQzs0QkFDZEY7NEJBQ0EsT0FBTyxFQUFFO3dCQUNYO3dCQUNBLE1BQU0sSUFBSTRHLE1BQU0sK0JBQStDLE9BQWhCTixTQUFTekQsTUFBTTtvQkFDaEU7b0JBRUEsTUFBTXJGLE9BQU8sTUFBTThJLFNBQVNPLElBQUk7b0JBQ2hDNUcsUUFBUVEsR0FBRyxDQUFDLGlDQUFpQ2pEO29CQUU3QyxJQUFJQSxLQUFLc0osT0FBTyxFQUFFOzRCQUtXdEo7d0JBSjNCLGdDQUFnQzt3QkFDaEMsd0ZBQXdGO3dCQUN4RnlDLFFBQVFRLEdBQUcsQ0FBQywwQ0FBMENzRyxLQUFLQyxTQUFTLENBQUN4Sjt3QkFFckUsTUFBTThILHFCQUFxQjlILEVBQUFBLGFBQUFBLEtBQUtBLElBQUksY0FBVEEsaUNBQUFBLFdBQVdELGFBQWEsS0FBSSxFQUFFO3dCQUN6RDBDLFFBQVFRLEdBQUcsQ0FBQyxnQ0FBZ0M2RTt3QkFFNUMsSUFBSSxDQUFDSixNQUFNQyxPQUFPLENBQUNHLHFCQUFxQjs0QkFDdENyRixRQUFRQyxLQUFLLENBQUMscURBQXFEMUM7NEJBQ25FLE9BQU8sRUFBRTt3QkFDWDt3QkFFQXlDLFFBQVFRLEdBQUcsQ0FBQyxhQUF1QyxPQUExQjZFLG1CQUFtQnpFLE1BQU0sRUFBQzt3QkFFbkQsK0NBQStDO3dCQUMvQ1osUUFBUVEsR0FBRyxDQUFDO3dCQUNaLE1BQU13Ryx5QkFBeUIzQixtQkFBbUI3QixHQUFHO2tHQUFDYSxDQUFBQTtnQ0FDcEQsd0NBQXdDO2dDQUN4QyxJQUFJQSxhQUFhM0csUUFBUSxJQUFJMkcsYUFBYTNHLFFBQVEsQ0FBQ2tELE1BQU0sR0FBRyxHQUFHO29DQUM3RFosUUFBUVEsR0FBRyxDQUFDLFlBQW1DNkQsT0FBdkJBLGFBQWFuRCxFQUFFLEVBQUMsU0FBb0MsT0FBN0JtRCxhQUFhM0csUUFBUSxDQUFDa0QsTUFBTSxFQUFDO29DQUM1RSw0QkFBNEI7b0NBQzVCLE1BQU00RCxjQUFjSCxhQUFhM0csUUFBUSxDQUFDLEVBQUUsRUFBRSxxRUFBcUU7b0NBQ25Ic0MsUUFBUVEsR0FBRyxDQUFDLG9CQUFvQmdFO29DQUVoQyx3RUFBd0U7b0NBQ3hFLE1BQU15QyxpQkFBaUI7MkNBQUk1QyxhQUFhM0csUUFBUTtxQ0FBQyxDQUFDd0csSUFBSTs2SEFBQyxDQUFDQyxHQUFHQyxJQUN6RCxJQUFJaEQsS0FBSytDLEVBQUVOLFNBQVMsSUFBSSxJQUFJekMsS0FBS2dELEVBQUVQLFNBQVM7O29DQUU5QzdELFFBQVFRLEdBQUcsQ0FBQyxtQkFBa0U2RCxPQUEvQzRDLGVBQWVyRyxNQUFNLEVBQUMsMkJBQXlDLE9BQWhCeUQsYUFBYW5ELEVBQUUsR0FBSStGO29DQUVqRyxpQ0FBaUM7b0NBQ2pDN0g7OEdBQVlnRSxDQUFBQSxPQUFTO2dEQUNuQixHQUFHQSxJQUFJO2dEQUNQLENBQUNpQixhQUFhbkQsRUFBRSxDQUFDLEVBQUUrRjs0Q0FDckI7O29DQUVBLHNEQUFzRDtvQ0FDdEQsTUFBTSxFQUFFdkosUUFBUSxFQUFFLEdBQUd3Siw2QkFBNkIsR0FBRzdDO29DQUVyRCwyREFBMkQ7b0NBQzNELE9BQU87d0NBQ0wsR0FBRzZDLDJCQUEyQjt3Q0FDOUIxQztvQ0FDRjtnQ0FDRixPQUFPO29DQUNMeEUsUUFBUVEsR0FBRyxDQUFDLFlBQTRCLE9BQWhCNkQsYUFBYW5ELEVBQUUsRUFBQztnQ0FDMUM7Z0NBRUEsT0FBT21EOzRCQUNUOzt3QkFFQSx5Q0FBeUM7d0JBQ3pDckUsUUFBUVEsR0FBRyxDQUFDLDBCQUEwQndHLHVCQUF1QnhELEdBQUc7MkVBQUNlLENBQUFBO29DQUVqREE7dUNBRnVEO29DQUNyRXJELElBQUlxRCxFQUFFckQsRUFBRTtvQ0FDUndFLFlBQVksR0FBRW5CLGtCQUFBQSxFQUFFbUIsWUFBWSxjQUFkbkIsc0NBQUFBLGdCQUFnQmYsR0FBRzt1RkFBQ21DLENBQUFBLElBQU07Z0RBQ3RDM0MsUUFBUTJDLEVBQUUzQyxNQUFNO2dEQUNoQjRDLFVBQVVELEVBQUVDLFFBQVE7Z0RBQ3BCakgsTUFBTWdILEVBQUVoSCxJQUFJO2dEQUNad0ksUUFBUXhCLEVBQUV3QixNQUFNOzRDQUNsQjs7Z0NBQ0Y7Ozt3QkFFQSwrQ0FBK0M7d0JBQy9DLE1BQU05RixNQUFNRCxLQUFLQyxHQUFHO3dCQUNwQmhFLGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSSxHQUFHeUo7d0JBQ2xDM0osYUFBYUMsYUFBYSxDQUFDRSxTQUFTLEdBQUc2RDt3QkFFdkMsOEZBQThGO3dCQUM5RnBDLGlCQUFpQitIO3dCQUVqQixpRUFBaUU7d0JBQ2pFekUsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVkseUJBQXlCOzRCQUM1REMsUUFBUTtnQ0FBRUMsTUFBTTtnQ0FBaUJuRixXQUFXNEQsS0FBS0MsR0FBRzs0QkFBRzt3QkFDekQ7d0JBRUEsT0FBT2dFO29CQUNULE9BQU87d0JBQ0xyRixRQUFRQyxLQUFLLENBQUMseUNBQXlDMUM7d0JBQ3ZELE9BQU8sRUFBRTtvQkFDWDtnQkFDRixFQUFFLE9BQU8wQyxPQUFPO29CQUNkRCxRQUFRQyxLQUFLLENBQUMsZ0NBQWdDQTtvQkFDOUMsT0FBTyxFQUFFO2dCQUNYLFNBQVU7b0JBQ1JOLGFBQWE7b0JBQ2JTLGFBQWFHLE9BQU8sR0FBRztnQkFDekI7WUFDRixFQUFFLE9BQU9OLE9BQU87Z0JBQ2RELFFBQVFDLEtBQUssQ0FBQywrQkFBK0JBO2dCQUM3QyxPQUFPLEVBQUU7WUFDWDtRQUNGLHVFQUF1RTtRQUN2RSx1REFBdUQ7UUFDdkQ7c0RBQUc7UUFBQ3RCO1FBQU1pQjtRQUFpQkc7UUFBaUJMO0tBQVU7SUFFdEQsMERBQTBEO0lBQzFELE1BQU0wSCxlQUFleEssa0RBQVdBO2tEQUFDLE9BQU9tRztZQUN0QyxJQUFJO29CQXlCRTFGO2dCQXhCSixxQ0FBcUM7Z0JBQ3JDLElBQUksQ0FBQ3NCLE1BQU07b0JBQ1RxQixRQUFRQyxLQUFLLENBQUM7b0JBQ2Q7Z0JBQ0Y7Z0JBRUEsK0JBQStCO2dCQUMvQixNQUFNb0IsTUFBTUQsS0FBS0MsR0FBRztnQkFDcEIsSUFBSWhFLGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsSUFDckMxQixNQUFNaEUsYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxDQUFDdkYsU0FBUyxHQUFHSyxrQkFBa0I7b0JBQzVFbUMsUUFBUVEsR0FBRyxDQUFDLDRDQUE0Q3VDO29CQUN4RCxPQUFPMUYsYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxDQUFDeEYsSUFBSTtnQkFDbkQ7Z0JBRUEsOERBQThEO2dCQUM5RCxpR0FBaUc7Z0JBRWpHLE1BQU1nRSxlQUFlM0I7Z0JBQ3JCLElBQUksQ0FBQzJCLGNBQWM7b0JBQ2pCdkIsUUFBUUMsS0FBSyxDQUFDO29CQUNkO2dCQUNGO2dCQUVBLGlFQUFpRTtnQkFDakUsS0FBSTVDLHdDQUFBQSxhQUFhSyxRQUFRLENBQUNxRixlQUFlLGNBQXJDMUYsNERBQUFBLHNDQUF1Q2dLLE9BQU8sRUFBRTtvQkFDbERySCxRQUFRUSxHQUFHLENBQUM7b0JBQ1o7Z0JBQ0Y7Z0JBRUEseUJBQXlCO2dCQUN6Qm5ELGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsR0FBRztvQkFBRXNFLFNBQVM7Z0JBQUs7Z0JBRXhELElBQUk7b0JBQ0ZySCxRQUFRUSxHQUFHLENBQUMsNENBQTRDdUM7b0JBQ3hELE1BQU1zRCxXQUFXLE1BQU1DLE1BQU0sR0FBaUN2RCxPQUE5QjlGLFNBQVEsd0JBQXFDLE9BQWY4RixnQkFBZSxjQUFZO3dCQUN2RndELFNBQVM7NEJBQUVDLGVBQWUsVUFBdUIsT0FBYmpGO3dCQUFlO29CQUNyRDtvQkFFQSxJQUFJLENBQUM4RSxTQUFTSyxFQUFFLEVBQUU7d0JBQ2hCLElBQUlMLFNBQVN6RCxNQUFNLEtBQUssS0FBSzs0QkFDM0I1QyxRQUFRQyxLQUFLLENBQUM7NEJBQ2RGOzRCQUNBO3dCQUNGO3dCQUNBLE1BQU0sSUFBSTRHLE1BQU0sK0JBQStDLE9BQWhCTixTQUFTekQsTUFBTTtvQkFDaEU7b0JBRUEsTUFBTXJGLE9BQU8sTUFBTThJLFNBQVNPLElBQUk7b0JBRWhDLElBQUlySixLQUFLc0osT0FBTyxFQUFFO3dCQUNoQiw0Q0FBNEM7d0JBQzVDN0csUUFBUVEsR0FBRyxDQUFDLDZDQUE2Q3VDLGdCQUFnQnhGLEtBQUtBLElBQUk7d0JBRWxGLGtCQUFrQjt3QkFDbEJGLGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsR0FBRzs0QkFDdEN4RixNQUFNQSxLQUFLQSxJQUFJOzRCQUNmQyxXQUFXNkQ7NEJBQ1hnRyxTQUFTO3dCQUNYO3dCQUVBLGlDQUFpQzt3QkFDakMsNkVBQTZFO3dCQUM3RSwyREFBMkQ7d0JBQzNELE1BQU1KLGlCQUFpQjsrQkFBSTFKLEtBQUtBLElBQUk7eUJBQUMsQ0FBQzJHLElBQUk7cUZBQUMsQ0FBQ0MsR0FBR0MsSUFDN0MsSUFBSWhELEtBQUsrQyxFQUFFTixTQUFTLElBQUksSUFBSXpDLEtBQUtnRCxFQUFFUCxTQUFTOzt3QkFHOUM3RCxRQUFRUSxHQUFHLENBQUMsOEJBQThCeUc7d0JBRTFDN0g7c0VBQVlnRSxDQUFBQSxPQUFTO29DQUNuQixHQUFHQSxJQUFJO29DQUNQLENBQUNMLGVBQWUsRUFBRWtFO2dDQUNwQjs7d0JBRUEsMENBQTBDO3dCQUMxQyxJQUFJMUosS0FBS0EsSUFBSSxJQUFJQSxLQUFLQSxJQUFJLENBQUNxRCxNQUFNLEdBQUcsR0FBRzs0QkFDckMsTUFBTTRELGNBQWNqSCxLQUFLQSxJQUFJLENBQUNBLEtBQUtBLElBQUksQ0FBQ3FELE1BQU0sR0FBRyxFQUFFOzRCQUNuRDNCOzBFQUFpQm1FLENBQUFBO29DQUNmLE9BQU9BLEtBQUtJLEdBQUc7a0ZBQUNxQixDQUFBQTs0Q0FDZCxJQUFJQSxLQUFLM0QsRUFBRSxLQUFLNkIsa0JBQW1CLEVBQUM4QixLQUFLTCxXQUFXLElBQUksSUFBSXBELEtBQUtvRCxZQUFZWCxTQUFTLElBQUksSUFBSXpDLEtBQUt5RCxLQUFLTCxXQUFXLENBQUNYLFNBQVMsSUFBSTtnREFDL0gsT0FBTztvREFBRSxHQUFHZ0IsSUFBSTtvREFBRUw7Z0RBQVk7NENBQ2hDOzRDQUNBLE9BQU9LO3dDQUNUOztnQ0FDRjs7d0JBQ0Y7d0JBRUEsT0FBT3RILEtBQUtBLElBQUk7b0JBQ2xCO2dCQUNGLEVBQUUsT0FBTzBDLE9BQU87b0JBQ2RELFFBQVFDLEtBQUssQ0FBQywyQkFBMkJBO2dCQUMzQyxTQUFVO3dCQUVKNUM7b0JBREosK0NBQStDO29CQUMvQyxLQUFJQSx5Q0FBQUEsYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxjQUFyQzFGLDZEQUFBQSx1Q0FBdUNnSyxPQUFPLEVBQUU7d0JBQ2xEaEssYUFBYUssUUFBUSxDQUFDcUYsZUFBZSxDQUFDc0UsT0FBTyxHQUFHO29CQUNsRDtnQkFDRjtZQUNGLEVBQUUsT0FBT3BILE9BQU87Z0JBQ2RELFFBQVFDLEtBQUssQ0FBQywwQkFBMEJBO1lBQzFDO1FBQ0Ysa0VBQWtFO1FBQ2xFLHVEQUF1RDtRQUN2RDtpREFBRztRQUFDdEI7UUFBTWlCO1FBQWlCRztLQUFnQjtJQUUzQyxrQkFBa0I7SUFDbEIsTUFBTXVILGNBQWMxSyxrREFBV0E7aURBQUMsU0FBQ21HLGdCQUFnQmdCO2dCQUFTRCwrRUFBYyxRQUFReUQsNEVBQVc7WUFDekYsSUFBSSxDQUFDMUksVUFBVSxDQUFDRSxhQUFhO1lBRTdCLHlDQUF5QztZQUN6QyxNQUFNa0UsU0FBUyxRQUFtQixPQUFYN0IsS0FBS0MsR0FBRztZQUUvQiwwREFBMEQ7WUFDMUQsTUFBTTJDLGNBQWM7Z0JBQ2xCOUMsSUFBSStCO2dCQUNKRjtnQkFDQWdCO2dCQUNBRDtnQkFDQXlEO2dCQUNBcEUsVUFBVXhFLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTZJLFFBQVEsS0FBSTdJLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXdDLElBQUksTUFBSyxXQUFXLE9BQU94QyxpQkFBQUEsMkJBQUFBLEtBQU11QyxFQUFFO2dCQUNyRXVDLGdCQUFnQjlFLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTZJLFFBQVEsS0FBSTdJLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXdDLElBQUksTUFBSyxXQUFXeEMsaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRSxHQUFHO2dCQUN2RTJDLFdBQVcsSUFBSXpDLE9BQU9xRyxXQUFXO2dCQUNqQ0MsUUFBUS9JLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTZJLFFBQVEsS0FBSTdJLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXdDLElBQUksTUFBSyxXQUFXLE9BQU87b0JBQUVELEVBQUUsRUFBRXZDLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUU7b0JBQUVULFFBQVEsRUFBRTlCLGlCQUFBQSwyQkFBQUEsS0FBTThCLFFBQVE7Z0JBQUM7Z0JBQ3BHa0gsY0FBY2hKLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTZJLFFBQVEsS0FBSTdJLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXdDLElBQUksTUFBSyxXQUFXO29CQUFFRCxFQUFFLEVBQUV2QyxpQkFBQUEsMkJBQUFBLEtBQU11QyxFQUFFO29CQUFFVCxVQUFVOUIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNOEIsUUFBUSxNQUFJOUIsaUJBQUFBLDJCQUFBQSxLQUFNaUosS0FBSztvQkFBRUEsS0FBSyxFQUFFakosaUJBQUFBLDJCQUFBQSxLQUFNaUosS0FBSztnQkFBQyxJQUFJO2dCQUMxSSxnREFBZ0Q7Z0JBQ2hEaEUsUUFBUTtZQUNWO1lBRUEsaURBQWlEO1lBQ2pEeEU7eURBQVlnRSxDQUFBQSxPQUFTO3dCQUNuQixHQUFHQSxJQUFJO3dCQUNQLENBQUNMLGVBQWUsRUFBRTsrQkFBS0ssSUFBSSxDQUFDTCxlQUFlLElBQUksRUFBRTs0QkFBR2lCO3lCQUFZO29CQUNsRTs7WUFFQSw2Q0FBNkM7WUFDN0MvRTt5REFBaUJtRSxDQUFBQTtvQkFDZixPQUFPQSxLQUFLSSxHQUFHO2lFQUFDcUIsQ0FBQUE7NEJBQ2QsSUFBSUEsS0FBSzNELEVBQUUsS0FBSzZCLGdCQUFnQjtnQ0FDOUIsT0FBTztvQ0FBRSxHQUFHOEIsSUFBSTtvQ0FBRUwsYUFBYVI7Z0NBQVk7NEJBQzdDOzRCQUNBLE9BQU9hO3dCQUNUOztnQkFDRjs7WUFFQSxrQ0FBa0M7WUFDbEMsTUFBTWdELGNBQWM7Z0JBQ2xCOUU7Z0JBQ0FnQjtnQkFDQUQ7Z0JBQ0F5RDtnQkFDQXRFO1lBQ0Y7WUFHQXBFLE9BQU9pSixJQUFJLENBQUMsZ0JBQWdCRDt5REFBYSxDQUFDeEI7b0JBQ3hDLElBQUlBLFNBQVNRLE9BQU8sRUFBRTtvQkFDcEIsdUVBQXVFO29CQUN2RSw0RUFBNEU7b0JBQzlFLE9BQU87d0JBQ0w3RyxRQUFRQyxLQUFLLENBQUMsMEJBQTBCb0csU0FBU3BHLEtBQUs7d0JBQ3RELHdEQUF3RDt3QkFDeERiO3FFQUFZZ0UsQ0FBQUE7Z0NBQ1YsTUFBTUMsdUJBQXVCRCxJQUFJLENBQUNMLGVBQWUsSUFBSSxFQUFFO2dDQUN2RCxPQUFPO29DQUNMLEdBQUdLLElBQUk7b0NBQ1AsQ0FBQ0wsZUFBZSxFQUFFTSxxQkFBcUJHLEdBQUc7aUZBQUN1RSxDQUFBQSxNQUN6Q0EsSUFBSTdHLEVBQUUsS0FBSytCLFNBQVM7Z0RBQUUsR0FBRzhFLEdBQUc7Z0RBQUVDLFFBQVE7NENBQUssSUFBSUQ7O2dDQUVuRDs0QkFDRjs7b0JBQ0Y7Z0JBQ0Y7O1FBQ0Y7Z0RBQUc7UUFBQ2xKO1FBQVFFO1FBQWFKO0tBQUs7SUFFOUIsc0JBQXNCO0lBQ3RCLE1BQU1zSixxQkFBcUJyTCxrREFBV0E7d0RBQUMsZUFBT3NMO2dCQUFnQkMseUVBQVE7WUFDcEUsSUFBSTtnQkFDRm5JLFFBQVFRLEdBQUcsQ0FBQyx1Q0FBdUMwSDtnQkFDbkRsSSxRQUFRUSxHQUFHLENBQUMscUJBQXFCMEgsZUFBZXRILE1BQU0sR0FBRyxJQUFJLFVBQVU7Z0JBQ3ZFWixRQUFRUSxHQUFHLENBQUMsdUJBQXVCMkg7Z0JBRW5DLHNGQUFzRjtnQkFDdEYsSUFBSXhKLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXdDLElBQUksTUFBSyxrQkFBa0IrRyxlQUFldEgsTUFBTSxHQUFHLEdBQUc7b0JBQzlEWixRQUFRUSxHQUFHLENBQUM7b0JBQ1osNkRBQTZEO29CQUM3RCxNQUFNZSxlQUFlM0I7b0JBQ3JCLElBQUkyQixjQUFjO3dCQUNoQixJQUFJOzRCQUNGLE1BQU02RyxzQkFBc0JGLGVBQWUxRSxHQUFHO29HQUFDLE9BQU90QztvQ0FDcEQsTUFBTW1GLFdBQVcsTUFBTUMsTUFBTSxHQUFvQnBGLE9BQWpCakUsU0FBUSxXQUFZLE9BQUhpRSxLQUFNO3dDQUNyRHFGLFNBQVM7NENBQUVDLGVBQWUsVUFBdUIsT0FBYmpGO3dDQUFlO29DQUNyRDtvQ0FDQSxJQUFJOEUsU0FBU0ssRUFBRSxFQUFFO3dDQUNmLE1BQU0yQixXQUFXLE1BQU1oQyxTQUFTTyxJQUFJO3dDQUNwQyxPQUFPeUIsU0FBU3hCLE9BQU8sR0FBR3dCLFNBQVM5SyxJQUFJLEdBQUc4SztvQ0FDNUM7b0NBQ0EsT0FBTztnQ0FDVDs7NEJBRUEsTUFBTTNDLGVBQWUsTUFBTTRDLFFBQVFDLEdBQUcsQ0FBQ0g7NEJBQ3ZDcEksUUFBUVEsR0FBRyxDQUFDLDZCQUE2QmtGOzRCQUN6QyxNQUFNOEMsb0JBQW9COUMsYUFBYS9CLE1BQU07a0dBQUNnQyxDQUFBQSxJQUFLQSxLQUFLQSxFQUFFOEMsU0FBUzs7NEJBQ25FekksUUFBUVEsR0FBRyxDQUFDLDBCQUEwQmdJOzRCQUV0QyxJQUFJQSxrQkFBa0I1SCxNQUFNLEdBQUcsR0FBRztnQ0FDaEMsTUFBTThILFlBQVk7dUNBQUksSUFBSUMsSUFBSUgsa0JBQWtCaEYsR0FBRzt3RkFBQ21DLENBQUFBLElBQUtBLEVBQUU4QyxTQUFTOztpQ0FBRztnQ0FDdkV6SSxRQUFRUSxHQUFHLENBQUMseUJBQXlCa0k7Z0NBQ3JDLElBQUlBLFVBQVU5SCxNQUFNLEdBQUcsR0FBRztvQ0FDeEJnSSxNQUFNO29DQUNOLE9BQU87Z0NBQ1Q7NEJBQ0Y7d0JBQ0YsRUFBRSxPQUFPQyxpQkFBaUI7NEJBQ3hCN0ksUUFBUUMsS0FBSyxDQUFDLGtDQUFrQzRJOzRCQUNoREQsTUFBTSx3QkFBd0JDLGdCQUFnQmhHLE9BQU87NEJBQ3JELE9BQU87d0JBQ1Q7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsNENBQTRDO2dCQUM1QyxNQUFNaUcsd0JBQXdCWixlQUFlNUUsSUFBSTswRkFBQ3BDLENBQUFBO3dCQUNoRCx5RkFBeUY7d0JBQ3pGLE9BQU8sT0FBT0EsT0FBTyxZQUFZQSxHQUFHc0csUUFBUTtvQkFDOUM7O2dCQUVBeEgsUUFBUVEsR0FBRyxDQUFDLCtCQUErQnNJO2dCQUUzQyw2Q0FBNkM7Z0JBQzdDLE1BQU12SCxlQUFlM0I7Z0JBQ3JCSSxRQUFRUSxHQUFHLENBQUMscUJBQXFCZSxlQUFlLFFBQVE7Z0JBRXhELElBQUksQ0FBQ0EsY0FBYztvQkFDakJ2QixRQUFRQyxLQUFLLENBQUM7b0JBQ2QsT0FBTztnQkFDVDtnQkFFQSxNQUFNb0csV0FBVyxNQUFNQyxNQUFNLEdBQVcsT0FBUnJKLFNBQVEsd0JBQXNCO29CQUM1RDhMLFFBQVE7b0JBQ1J4QyxTQUFTO3dCQUNQLGdCQUFnQjt3QkFDaEJDLGVBQWUsVUFBdUIsT0FBYmpGO29CQUMzQjtvQkFDQXlILE1BQU1sQyxLQUFLQyxTQUFTLENBQUM7d0JBQ25CcEUsTUFBTXVGLGVBQWV0SCxNQUFNLEdBQUcsSUFBSSxVQUFVO3dCQUM1Q3VIO3dCQUNBRCxnQkFBZ0JBLGVBQWUxRSxHQUFHOzRFQUFDdEMsQ0FBQUEsS0FBTSxPQUFPQSxPQUFPLFdBQVdBLEdBQUdBLEVBQUUsR0FBR0E7O3dCQUMxRStILGdCQUFnQixLQUFLLDBDQUEwQztvQkFDakU7Z0JBQ0Y7Z0JBRUFqSixRQUFRUSxHQUFHLENBQUMsb0JBQW9CNkYsU0FBU3pELE1BQU0sRUFBRXlELFNBQVNJLFVBQVU7Z0JBQ3BFLE1BQU1sSixPQUFPLE1BQU04SSxTQUFTTyxJQUFJO2dCQUNoQzVHLFFBQVFRLEdBQUcsQ0FBQyxzQkFBc0JqRDtnQkFFbEMsSUFBSUEsS0FBS3NKLE9BQU8sRUFBRTtvQkFDaEIsc0NBQXNDO29CQUN0QyxNQUFNcUMsbUJBQW1CM0wsS0FBS0EsSUFBSTtvQkFDbEN5QyxRQUFRUSxHQUFHLENBQUMsZ0NBQWdDMEk7b0JBRTVDLG9DQUFvQztvQkFDcENqSzt3RUFBaUJtRSxDQUFBQTs0QkFDZixvRUFBb0U7NEJBQ3BFLElBQUlBLEtBQUtFLElBQUk7Z0ZBQUNpQixDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLZ0ksaUJBQWlCaEksRUFBRTtnRkFBRztnQ0FDaEQsT0FBT2tDOzRCQUNUOzRCQUNBLE9BQU87Z0NBQUM4RjttQ0FBcUI5Rjs2QkFBSzt3QkFDcEM7O29CQUVBLG1FQUFtRTtvQkFDbkU3RTt3RUFBVzs0QkFDVGdFLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHlCQUF5QjtnQ0FDNURDLFFBQVE7b0NBQ05DLE1BQU07b0NBQ05JLGdCQUFnQm1HLGlCQUFpQmhJLEVBQUU7b0NBQ25DMUQsV0FBVzRELEtBQUtDLEdBQUc7Z0NBQ3JCOzRCQUNGO3dCQUNGO3VFQUFHO29CQUVILE9BQU82SDtnQkFDVCxPQUFPO29CQUNMbEosUUFBUUMsS0FBSyxDQUFDLDJCQUEyQjFDLEtBQUswQyxLQUFLLElBQUk7b0JBSXZELHlEQUF5RDtvQkFDekQsSUFBSW9HLFNBQVN6RCxNQUFNLEtBQUssS0FBSzt3QkFDM0I1QyxRQUFRQyxLQUFLLENBQUM7d0JBQ2RGO29CQUNGO2dCQUNGO2dCQUVBLE9BQU87WUFDVCxFQUFFLE9BQU9FLE9BQU87Z0JBQ2RELFFBQVFDLEtBQUssQ0FBQyxnQ0FBZ0NBO2dCQUM5QyxPQUFPO1lBQ1Q7UUFDRjt1REFBRztRQUFDRjtLQUFnQjtJQUVwQixvREFBb0Q7SUFDcEQsTUFBTW9KLDBCQUEwQnZNLGtEQUFXQTs2REFBQyxPQUFPd007WUFDakQsSUFBSTtnQkFDRnBKLFFBQVFRLEdBQUcsQ0FBQyxnREFBZ0Q0STtnQkFFNUQsSUFBSSxDQUFDQSxhQUFhLENBQUNBLFVBQVVsSSxFQUFFLEVBQUU7b0JBQy9CbEIsUUFBUUMsS0FBSyxDQUFDLHFCQUFxQm1KO29CQUNuQyxPQUFPO2dCQUNUO2dCQUVBLDRFQUE0RTtnQkFDNUUsTUFBTUMsdUJBQXVCL0wsY0FBY2dILElBQUk7OEZBQUNPLENBQUFBOzRCQUd2Q0E7d0JBRlAsSUFBSUEsS0FBS2xDLElBQUksS0FBSyxjQUFjLE9BQU87d0JBRXZDLFFBQU9rQyxxQkFBQUEsS0FBS2EsWUFBWSxjQUFqQmIseUNBQUFBLG1CQUFtQnZCLElBQUk7c0dBQUNxQyxDQUFBQSxJQUFLQSxFQUFFM0MsTUFBTSxLQUFLb0csVUFBVWxJLEVBQUU7O29CQUMvRDs7Z0JBRUEsSUFBSW1JLHNCQUFzQjtvQkFDeEJySixRQUFRUSxHQUFHLENBQUMsa0NBQWtDNkk7b0JBQzlDbEssc0JBQXNCa0sscUJBQXFCbkksRUFBRTtvQkFDN0MsT0FBT21JO2dCQUNUO2dCQUVBckosUUFBUVEsR0FBRyxDQUFDLHNDQUFzQzRJLFVBQVUzSSxRQUFRO2dCQUNwRSwwQ0FBMEM7Z0JBQzFDLE1BQU02SSxrQkFBa0IsTUFBTXJCLG1CQUFtQjtvQkFBQ21CLFVBQVVsSSxFQUFFO2lCQUFDO2dCQUUvRCxJQUFJb0ksaUJBQWlCO29CQUNuQnRKLFFBQVFRLEdBQUcsQ0FBQyxxQ0FBcUM4STtvQkFDakQsOEVBQThFO29CQUM5RXJLOzZFQUFpQm1FLENBQUFBOzRCQUNmLG9FQUFvRTs0QkFDcEUsSUFBSUEsS0FBS0UsSUFBSTtxRkFBQ2lCLENBQUFBLElBQUtBLEVBQUVyRCxFQUFFLEtBQUtvSSxnQkFBZ0JwSSxFQUFFO3FGQUFHO2dDQUMvQyxPQUFPa0M7NEJBQ1Q7NEJBQ0EsT0FBTztnQ0FBQ2tHO21DQUFvQmxHOzZCQUFLO3dCQUNuQzs7b0JBRUEsK0ZBQStGO29CQUMvRjdFOzZFQUFXOzRCQUNUWSxzQkFBc0JtSyxnQkFBZ0JwSSxFQUFFOzRCQUV4QyxtRUFBbUU7NEJBQ25FcUIsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVkseUJBQXlCO2dDQUM1REMsUUFBUTtvQ0FDTkMsTUFBTTtvQ0FDTkksZ0JBQWdCdUcsZ0JBQWdCcEksRUFBRTtvQ0FDbEMxRCxXQUFXNEQsS0FBS0MsR0FBRztnQ0FDckI7NEJBQ0Y7d0JBQ0Y7NEVBQUc7Z0JBQ0wsT0FBTztvQkFDTHJCLFFBQVFDLEtBQUssQ0FBQztnQkFDaEI7Z0JBRUEsT0FBT3FKO1lBQ1QsRUFBRSxPQUFPckosT0FBTztnQkFDZEQsUUFBUUMsS0FBSyxDQUFDLDJDQUEyQ0E7Z0JBQ3pELE9BQU87WUFDVDtRQUNGLHVFQUF1RTtRQUN2RSx1REFBdUQ7UUFDdkQ7NERBQUc7UUFBQ2dJO1FBQW9COUk7UUFBdUJZO0tBQWdCO0lBSS9ELHFEQUFxRDtJQUNyRCxNQUFNd0osa0JBQWtCM00sa0RBQVdBO3FEQUFDO2dCQUFPd0osZ0ZBQWU7WUFDeEQsSUFBSTtnQkFDRixxQ0FBcUM7Z0JBQ3JDLElBQUksQ0FBQ3pILE1BQU07b0JBQ1RxQixRQUFRQyxLQUFLLENBQUM7b0JBQ2Q7Z0JBQ0Y7Z0JBRUEsa0JBQWtCO2dCQUNsQixNQUFNb0IsTUFBTUQsS0FBS0MsR0FBRztnQkFDcEIsSUFBSSxDQUFDK0UsZ0JBQ0QvSSxhQUFhSSxXQUFXLENBQUNGLElBQUksS0FBSyxRQUNsQzhELE1BQU1oRSxhQUFhSSxXQUFXLENBQUNELFNBQVMsR0FBR0ssa0JBQWtCO29CQUMvRG1DLFFBQVFRLEdBQUcsQ0FBQztvQkFDWixPQUFPbkQsYUFBYUksV0FBVyxDQUFDRixJQUFJO2dCQUN0QztnQkFFQSxNQUFNZ0UsZUFBZTNCO2dCQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUNqQnZCLFFBQVFDLEtBQUssQ0FBQztvQkFDZDtnQkFDRjtnQkFFQSxJQUFJO29CQUNGRCxRQUFRUSxHQUFHLENBQUM7b0JBQ1osTUFBTTZGLFdBQVcsTUFBTUMsTUFBTSxHQUFXLE9BQVJySixTQUFRLDBCQUF3Qjt3QkFDOURzSixTQUFTOzRCQUFFQyxlQUFlLFVBQXVCLE9BQWJqRjt3QkFBZTtvQkFDckQ7b0JBRUEsSUFBSSxDQUFDOEUsU0FBU0ssRUFBRSxFQUFFO3dCQUNoQixJQUFJTCxTQUFTekQsTUFBTSxLQUFLLEtBQUs7NEJBQzNCNUMsUUFBUUMsS0FBSyxDQUFDOzRCQUNkRjs0QkFDQTt3QkFDRjt3QkFDQSxNQUFNLElBQUk0RyxNQUFNLHdEQUFxRSxPQUFoQk4sU0FBU3pELE1BQU07b0JBQ3RGO29CQUVBLE1BQU1yRixPQUFPLE1BQU04SSxTQUFTTyxJQUFJO29CQUNoQzVHLFFBQVFRLEdBQUcsQ0FBQywyQ0FBMkNqRDtvQkFFdkQsSUFBSUEsS0FBS3NKLE9BQU8sRUFBRTt3QkFDaEI3RyxRQUFRUSxHQUFHLENBQUMsMkNBQTJDakQsS0FBS0EsSUFBSTt3QkFFaEUsMEVBQTBFO3dCQUMxRSxNQUFNd0gsMkJBQTJCeEgsS0FBS0EsSUFBSSxDQUFDd0gsd0JBQXdCLElBQUl4SCxLQUFLQSxJQUFJLENBQUN5SCxXQUFXLElBQUk7d0JBRWhHLGlEQUFpRDt3QkFDakQzSCxhQUFhSSxXQUFXLENBQUNGLElBQUksR0FBR3dIO3dCQUNoQzFILGFBQWFJLFdBQVcsQ0FBQ0QsU0FBUyxHQUFHNkQ7d0JBRXJDLG1CQUFtQjt3QkFDbkJoQyxlQUFlMEY7d0JBRWYsaURBQWlEO3dCQUNqRCxJQUFJeEgsS0FBS0EsSUFBSSxDQUFDRCxhQUFhLElBQUkySCxNQUFNQyxPQUFPLENBQUMzSCxLQUFLQSxJQUFJLENBQUNELGFBQWEsR0FBRzs0QkFDckUwQyxRQUFRUSxHQUFHLENBQUMsNkRBQTZEakQsS0FBS0EsSUFBSSxDQUFDRCxhQUFhOzRCQUVoRywwREFBMEQ7NEJBQzFELE1BQU1rTSxrQkFBa0JqTSxLQUFLQSxJQUFJLENBQUNELGFBQWEsQ0FBQ2tHLEdBQUc7NkZBQUNlLENBQUFBLElBQUtBLEVBQUV4QixjQUFjOzs0QkFDekUvQyxRQUFRUSxHQUFHLENBQUMsaURBQWlEZ0o7NEJBQzdEeEosUUFBUVEsR0FBRyxDQUFDLHNEQUFzRGxELGNBQWNzRCxNQUFNLEVBQUV0RCxjQUFja0csR0FBRzs2RUFBQ2UsQ0FBQUEsSUFBS0EsRUFBRXJELEVBQUU7OzRCQUVuSCxxRkFBcUY7NEJBQ3JGLHVDQUF1Qzs0QkFDdkMsSUFBSTVELGNBQWNzRCxNQUFNLEtBQUssS0FDekIsQ0FBQzRJLGdCQUFnQkMsS0FBSzs2RUFBQ3ZJLENBQUFBLEtBQU01RCxjQUFjZ0csSUFBSTtxRkFBQ2lCLENBQUFBLElBQUtBLEVBQUVyRCxFQUFFLEtBQUtBOzs2RUFBTTtnQ0FDdEVsQixRQUFRUSxHQUFHLENBQUM7Z0NBQ1osTUFBTStFLGtCQUFrQjtnQ0FFeEIsbUZBQW1GO2dDQUNuRnZGLFFBQVFRLEdBQUcsQ0FBQztnQ0FDWnZCO2lGQUFpQm1FLENBQUFBO3dDQUNmLE1BQU1xQyxVQUFVckMsS0FBS0ksR0FBRztpR0FBQ3FCLENBQUFBO2dEQUN2QixNQUFNTSxhQUFhNUgsS0FBS0EsSUFBSSxDQUFDRCxhQUFhLENBQUNnSCxJQUFJO29IQUM3Q2MsQ0FBQUEsT0FBUUEsS0FBS3JDLGNBQWMsS0FBSzhCLEtBQUszRCxFQUFFOztnREFHekMsSUFBSWlFLFlBQVk7b0RBQ2QsTUFBTVAsWUFBWU8sV0FBV1AsU0FBUyxJQUFLTyxXQUFXMUgsV0FBVyxJQUFJMEgsV0FBVzFILFdBQVcsR0FBRztvREFDOUZ1QyxRQUFRUSxHQUFHLENBQUMsOEJBQXNEb0UsT0FBeEJDLEtBQUszRCxFQUFFLEVBQUMsa0JBQTBCLE9BQVYwRDtvREFDbEUsT0FBTzt3REFBRSxHQUFHQyxJQUFJO3dEQUFFRDtvREFBVTtnREFDOUI7Z0RBQ0E1RSxRQUFRUSxHQUFHLENBQUMsOEJBQXNDLE9BQVJxRSxLQUFLM0QsRUFBRSxFQUFDO2dEQUNsRCxPQUFPO29EQUFFLEdBQUcyRCxJQUFJO29EQUFFRCxXQUFXO2dEQUFNOzRDQUNyQzs7d0NBQ0E1RSxRQUFRUSxHQUFHLENBQUMsbUVBQW1FaUYsUUFBUWpDLEdBQUc7eUZBQUNlLENBQUFBLElBQU07b0RBQUVyRCxJQUFJcUQsRUFBRXJELEVBQUU7b0RBQUUwRCxXQUFXTCxFQUFFSyxTQUFTO2dEQUFDOzt3Q0FDcEksT0FBT2E7b0NBQ1Q7OzRCQUNGLE9BQU87Z0NBQ0wsNERBQTREO2dDQUM1RHpGLFFBQVFRLEdBQUcsQ0FBQztnQ0FDWnZCO2lGQUFpQm1FLENBQUFBO3dDQUNmLE1BQU1xQyxVQUFVckMsS0FBS0ksR0FBRztpR0FBQ3FCLENBQUFBO2dEQUN2QixnREFBZ0Q7Z0RBQ2hELE1BQU1NLGFBQWE1SCxLQUFLQSxJQUFJLENBQUNELGFBQWEsQ0FBQ2dILElBQUk7b0hBQzdDYyxDQUFBQSxPQUFRQSxLQUFLckMsY0FBYyxLQUFLOEIsS0FBSzNELEVBQUU7O2dEQUd6QyxJQUFJaUUsWUFBWTtvREFDZCxNQUFNUCxZQUFZTyxXQUFXUCxTQUFTLElBQUtPLFdBQVcxSCxXQUFXLElBQUkwSCxXQUFXMUgsV0FBVyxHQUFHO29EQUM5RnVDLFFBQVFRLEdBQUcsQ0FBQyw4QkFBc0RvRSxPQUF4QkMsS0FBSzNELEVBQUUsRUFBQyxrQkFBMEIsT0FBVjBEO29EQUNsRSxPQUFPO3dEQUFFLEdBQUdDLElBQUk7d0RBQUVEO29EQUFVO2dEQUM5QjtnREFDQTVFLFFBQVFRLEdBQUcsQ0FBQyw4QkFBc0MsT0FBUnFFLEtBQUszRCxFQUFFLEVBQUM7Z0RBQ2xELE9BQU87b0RBQUUsR0FBRzJELElBQUk7b0RBQUVELFdBQVc7Z0RBQU07NENBQ3JDOzt3Q0FDQTVFLFFBQVFRLEdBQUcsQ0FBQyw0Q0FBNENpRixRQUFRakMsR0FBRzt5RkFBQ2UsQ0FBQUEsSUFBTTtvREFBRXJELElBQUlxRCxFQUFFckQsRUFBRTtvREFBRTBELFdBQVdMLEVBQUVLLFNBQVM7Z0RBQUM7O3dDQUM3RyxPQUFPYTtvQ0FDVDs7NEJBQ0Y7d0JBQ0Y7d0JBRUEsT0FBT1Y7b0JBQ1Q7Z0JBQ0YsRUFBRSxPQUFPOUUsT0FBTztvQkFDZEQsUUFBUUMsS0FBSyxDQUFDLCtCQUErQkE7Z0JBQy9DO1lBQ0YsRUFBRSxPQUFPQSxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsNkJBQTZCQTtZQUM3QztRQUNGLDJGQUEyRjtRQUMzRix1REFBdUQ7UUFDdkQ7b0RBQUc7UUFBQ3RCO1FBQU1pQjtRQUFpQkc7S0FBZ0I7SUFFM0MscUVBQXFFO0lBQ3JFLE1BQU11Rix1QkFBdUJ4SSw2Q0FBTUEsQ0FBQztJQUVwQyx1Q0FBdUM7SUFDdkNILGdEQUFTQTtrQ0FBQztZQUNSLElBQUlnQyxNQUFNO2dCQUNScUIsUUFBUVEsR0FBRyxDQUFDO2dCQUNaOEUscUJBQXFCL0UsT0FBTyxHQUFHO1lBQ2pDO1FBQ0Y7aUNBQUc7UUFBQzVCLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUU7S0FBQyxHQUFHLDJFQUEyRTtJQUUzRix5RUFBeUU7SUFDekV2RSxnREFBU0E7a0NBQUM7WUFDUiw2REFBNkQ7WUFDN0QsSUFBSSxDQUFDZ0MsTUFBTTtnQkFDVHFCLFFBQVFRLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsbUNBQW1DO1lBQ25DUixRQUFRUSxHQUFHLENBQUMsbUNBQW1DN0IsS0FBS3dDLElBQUksSUFBSTtZQUU1RCxrQkFBa0I7WUFDbEIsTUFBTUksZUFBZTNCO1lBQ3JCLElBQUksQ0FBQzJCLGNBQWM7Z0JBQ2pCdkIsUUFBUVEsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxrQ0FBa0M7WUFDbEMsSUFBSWQsYUFBYVUsYUFBYUcsT0FBTyxFQUFFO2dCQUNyQ1AsUUFBUVEsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSw0Q0FBNEM7WUFDNUMsSUFBSThFLHFCQUFxQi9FLE9BQU8sRUFBRTtnQkFDaENQLFFBQVFRLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsc0NBQXNDO1lBQ3RDLE1BQU1rSjswREFBa0I7b0JBQ3RCLElBQUloSyxhQUFhVSxhQUFhRyxPQUFPLEVBQUU7d0JBQ3JDUCxRQUFRUSxHQUFHLENBQUM7d0JBQ1o7b0JBQ0Y7b0JBRUEseUJBQXlCO29CQUN6QkosYUFBYUcsT0FBTyxHQUFHO29CQUN2QlAsUUFBUVEsR0FBRyxDQUFDO29CQUVaLElBQUk7d0JBQ0YsaUNBQWlDO3dCQUNqQ1IsUUFBUVEsR0FBRyxDQUFDO3dCQUNaLE1BQU1tSixvQkFBb0IsTUFBTXBFLGtCQUFrQjt3QkFDbER2RixRQUFRUSxHQUFHLENBQUMseUJBQXlCbUosQ0FBQUEsOEJBQUFBLHdDQUFBQSxrQkFBbUIvSSxNQUFNLEtBQUksR0FBRzt3QkFFckUsMkNBQTJDO3dCQUMzQ1osUUFBUVEsR0FBRyxDQUFDO3dCQUNaLE1BQU0rSSxnQkFBZ0I7d0JBQ3RCdkosUUFBUVEsR0FBRyxDQUFDO3dCQUVaLHdCQUF3Qjt3QkFDeEI4RSxxQkFBcUIvRSxPQUFPLEdBQUc7d0JBQy9CUCxRQUFRUSxHQUFHLENBQUM7b0JBQ2QsRUFBRSxPQUFPUCxPQUFPO3dCQUNkRCxRQUFRQyxLQUFLLENBQUMsMkJBQTJCQTtvQkFDM0MsU0FBVTt3QkFDUkcsYUFBYUcsT0FBTyxHQUFHO29CQUN6QjtnQkFDRjs7WUFFQSxzRUFBc0U7WUFDdEVQLFFBQVFRLEdBQUcsQ0FBQztZQUNaLE1BQU1vSixvQkFBb0I3TDs0REFBUztvQkFDakNpQyxRQUFRUSxHQUFHLENBQUM7b0JBQ1prSjtnQkFDRjsyREFBRyxPQUFPLHNDQUFzQztZQUVoRCwrQkFBK0I7WUFDL0JFO1lBRUEsMEVBQTBFO1lBQzFFLHVFQUF1RTtZQUV2RTswQ0FBTztnQkFDTCxtQkFBbUI7Z0JBQ3JCOztRQUNGLDRHQUE0RztRQUM1Ryx1REFBdUQ7UUFDdkQ7aUNBQUc7UUFBQ2pMO1FBQU1pQjtRQUFpQjJGO1FBQW1CN0Y7S0FBVTtJQUV4RCx1REFBdUQ7SUFDdkQvQyxnREFBU0E7a0NBQUM7WUFDUixJQUFJdUMsc0JBQXNCNUIsY0FBY3NELE1BQU0sR0FBRyxHQUFHO2dCQUNsRCxNQUFNaUoscUJBQXFCdk0sY0FBY2dHLElBQUk7aUVBQUNpQixDQUFBQSxJQUFLQSxFQUFFckQsRUFBRSxLQUFLaEM7O2dCQUM1RCxJQUFJLENBQUMySyxvQkFBb0I7b0JBQ3ZCN0osUUFBUThKLElBQUksQ0FBQyxxRUFBcUU1SztvQkFDbEZDLHNCQUFzQjtnQkFDeEI7WUFDRjtRQUNGO2lDQUFHO1FBQUNEO1FBQW9CNUI7S0FBYztJQUV0Qyx5RUFBeUU7SUFDekVYLGdEQUFTQTtrQ0FBQztZQUNSLGdFQUFnRTtZQUNoRSxJQUFJLENBQUNnQyxNQUFNO1lBRVgsMkVBQTJFO1lBQzNFLE1BQU1vTCxxQkFBcUJDOzZEQUFZO29CQUNyQywyREFBMkQ7b0JBQzNELElBQUl0SyxhQUFhVSxhQUFhRyxPQUFPLEVBQUU7b0JBRXZDLE1BQU1nQixlQUFlM0I7b0JBRXJCLDhDQUE4QztvQkFDOUMsSUFBSSxDQUFDMkIsZ0JBQWdCeEMsZUFBZUYsUUFBUTt3QkFDMUNtQixRQUFRUSxHQUFHLENBQUM7d0JBQ1osSUFBSTs0QkFDRjNCLE9BQU9xQixVQUFVO3dCQUNuQixFQUFFLE9BQU9ELE9BQU87NEJBQ2RELFFBQVFDLEtBQUssQ0FBQywrQkFBK0JBO3dCQUMvQzt3QkFDQWpCLGVBQWU7b0JBQ2pCO29CQUVBLCtFQUErRTtvQkFDL0UsSUFBSXVDLGdCQUFnQixDQUFDeEMsZUFBZSxDQUFDRixRQUFRO3dCQUMzQyxNQUFNd0MsTUFBTUQsS0FBS0MsR0FBRzt3QkFDcEIsbUVBQW1FO3dCQUNuRSxJQUFJQSxNQUFNaEIsbUJBQW1CRSxPQUFPLEdBQUcsUUFBUTs0QkFDN0NQLFFBQVFRLEdBQUcsQ0FBQzs0QkFDWix3REFBd0Q7NEJBQ3hETCxxQkFBcUJJLE9BQU8sR0FBRzs0QkFDL0JGLG1CQUFtQkUsT0FBTyxHQUFHYzt3QkFDL0I7b0JBQ0Y7Z0JBQ0Y7NERBQUcsU0FBUyw2QkFBNkI7WUFFekM7MENBQU8sSUFBTTRJLGNBQWNGOztRQUM3Qix1REFBdUQ7UUFDdkQ7aUNBQUc7UUFBQ3BMO1FBQU1pQjtLQUFnQjtJQUUxQiw4QkFBOEI7SUFDOUIsTUFBTXNLLGtCQUFrQnROLGtEQUFXQTtxREFBQztZQUNsQyxxQ0FBcUM7WUFDckMsSUFBSSxDQUFDK0IsTUFBTTtZQUVYWTs2REFBZTZELENBQUFBO29CQUNiLE1BQU0rRyxXQUFXLENBQUMvRztvQkFDbEIsOENBQThDO29CQUM5QyxJQUFJK0csVUFBVTt3QkFDWjFLLGVBQWU7b0JBQ2pCO29CQUNBLE9BQU8wSztnQkFDVDs7UUFDRjtvREFBRztRQUFDeEw7S0FBSztJQUVULDZCQUE2QjtJQUM3QixNQUFNeUwsa0JBQWtCeE4sa0RBQVdBO3FEQUFDO1lBQ2xDLHFDQUFxQztZQUNyQyxJQUFJLENBQUMrQixNQUFNO1lBRVhjOzZEQUFlMkQsQ0FBQUE7b0JBQ2IsTUFBTStHLFdBQVcsQ0FBQy9HO29CQUNsQiw4Q0FBOEM7b0JBQzlDLElBQUkrRyxVQUFVO3dCQUNaNUssZUFBZTtvQkFDakI7b0JBQ0EsT0FBTzRLO2dCQUNUOztRQUNGO29EQUFHO1FBQUN4TDtLQUFLO0lBRVQsc0RBQXNEO0lBQ3REcUIsUUFBUVEsR0FBRyxDQUFDLGlEQUFpRCxPQUFPeUgsdUJBQXVCO0lBRTNGLG9DQUFvQztJQUNwQyxNQUFNb0Msd0JBQXdCek4sa0RBQVdBOzJEQUFDLE9BQU9tRyxnQkFBZ0J5QztZQUMvRCxJQUFJO2dCQUNGLElBQUksQ0FBQ3pDLGtCQUFrQixDQUFDeUMsZUFBZTtvQkFDckN4RixRQUFRQyxLQUFLLENBQUM7b0JBQ2QsT0FBTztnQkFDVDtnQkFFQSxNQUFNc0IsZUFBZTNCO2dCQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUNqQnZCLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPO2dCQUNUO2dCQUVBRCxRQUFRUSxHQUFHLENBQUMsNEJBQXdEdUMsT0FBNUJ5QyxlQUFjLG1CQUE2QixPQUFmekM7Z0JBRXBFLE1BQU1zRCxXQUFXLE1BQU1DLE1BQU0sR0FBaUN2RCxPQUE5QjlGLFNBQVEsd0JBQXFDLE9BQWY4RixnQkFBZSxrQkFBZ0I7b0JBQzNGZ0csUUFBUTtvQkFDUnhDLFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQkMsZUFBZSxVQUF1QixPQUFiakY7b0JBQzNCO29CQUNBeUgsTUFBTWxDLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRXZCO29CQUFjO2dCQUN2QztnQkFFQXhGLFFBQVFRLEdBQUcsQ0FBQyxvQkFBdUM2RixPQUFuQkEsU0FBU3pELE1BQU0sRUFBQyxLQUF1QixPQUFwQnlELFNBQVNJLFVBQVU7Z0JBRXRFLElBQUksQ0FBQ0osU0FBU0ssRUFBRSxFQUFFO29CQUNoQixJQUFJTCxTQUFTekQsTUFBTSxLQUFLLEtBQUs7d0JBQzNCNUMsUUFBUUMsS0FBSyxDQUFDO3dCQUNkRjt3QkFDQSxPQUFPO29CQUNUO29CQUVBLHdCQUF3QjtvQkFDeEIsTUFBTXVLLFlBQVksTUFBTWpFLFNBQVNrRSxJQUFJO29CQUNyQ3ZLLFFBQVFDLEtBQUssQ0FBQyxRQUF3QixPQUFoQm9HLFNBQVN6RCxNQUFNLEVBQUMsZ0NBQThCMEg7b0JBQ3BFLE1BQU0sSUFBSTNELE1BQU0sbUNBQW1ELE9BQWhCTixTQUFTekQsTUFBTTtnQkFDcEU7Z0JBRUEsTUFBTXJGLE9BQU8sTUFBTThJLFNBQVNPLElBQUk7Z0JBRWhDLElBQUlySixLQUFLc0osT0FBTyxFQUFFO29CQUNoQiwrQ0FBK0M7b0JBQy9DNUg7MkVBQWlCbUUsQ0FBQUE7NEJBQ2YsT0FBT0EsS0FBS0ksR0FBRzttRkFBQ3FCLENBQUFBO29DQUNkLElBQUlBLEtBQUszRCxFQUFFLEtBQUs2QixnQkFBZ0I7d0NBQzlCLHdDQUF3Qzt3Q0FDeEMsTUFBTXlILG9CQUFvQjNGLEtBQUthLFlBQVksQ0FBQ3BDLElBQUk7aUhBQUNxQyxDQUFBQSxJQUFLQSxFQUFFM0MsTUFBTSxLQUFLd0M7O3dDQUVuRSxJQUFJZ0YsbUJBQW1COzRDQUNyQixPQUFPM0Y7d0NBQ1Q7d0NBRUEsZ0NBQWdDO3dDQUNoQyxPQUFPOzRDQUNMLEdBQUdBLElBQUk7NENBQ1BhLGNBQWM7bURBQUliLEtBQUthLFlBQVk7Z0RBQUVuSSxLQUFLQSxJQUFJOzZDQUFDO3dDQUNqRDtvQ0FDRjtvQ0FDQSxPQUFPc0g7Z0NBQ1Q7O3dCQUNGOztvQkFFQSxrRkFBa0Y7b0JBQ2xGLElBQUloRyxVQUFVRSxhQUFhO3dCQUN6QixNQUFNMEwsZ0JBQWdCOzRCQUNwQjFIOzRCQUNBZ0IsU0FBUyxHQUEyQixPQUF4QnhHLEtBQUtBLElBQUksQ0FBQ29CLElBQUksQ0FBQzhCLFFBQVEsRUFBQzs0QkFDcENxRCxhQUFhO3dCQUNmO3dCQUVBakYsT0FBT2lKLElBQUksQ0FBQyxnQkFBZ0IyQztvQkFDOUI7b0JBRUEsT0FBT2xOLEtBQUtBLElBQUk7Z0JBQ2xCO2dCQUVBLE9BQU87WUFDVCxFQUFFLE9BQU8wQyxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsbUNBQW1DQTtnQkFDakQsT0FBTztZQUNUO1FBQ0Y7MERBQUc7UUFBQ3BCO1FBQVFFO1FBQWFhO1FBQWlCRztLQUFnQjtJQUUxRCwrQ0FBK0M7SUFDL0MsTUFBTTJLLGlDQUFpQzlOLGtEQUFXQTtvRUFBQyxPQUFPbUcsZ0JBQWdCMkM7WUFDeEUsSUFBSTtnQkFDRixJQUFJLENBQUMzQyxrQkFBa0IsQ0FBQzJDLGdCQUFnQixDQUFDQSxhQUFhOUUsTUFBTSxFQUFFO29CQUM1RFosUUFBUUMsS0FBSyxDQUFDO29CQUNkLE9BQU87Z0JBQ1Q7Z0JBRUFELFFBQVFRLEdBQUcsQ0FBQyxlQUE2RHVDLE9BQTlDMkMsYUFBYTlFLE1BQU0sRUFBQyw0QkFBeUMsT0FBZm1DO2dCQUV6RSxxQ0FBcUM7Z0JBQ3JDLE1BQU00SCxVQUFVLEVBQUU7Z0JBQ2xCLE1BQU1DLFNBQVMsRUFBRTtnQkFFakIsb0NBQW9DO2dCQUNwQyxLQUFLLE1BQU1DLGVBQWVuRixhQUFjO29CQUN0QyxJQUFJO3dCQUNGLE1BQU1PLFNBQVMsTUFBTW9FLHNCQUFzQnRILGdCQUFnQjhILFlBQVkzSixFQUFFO3dCQUN6RSxJQUFJK0UsUUFBUTs0QkFDVjBFLFFBQVE1SixJQUFJLENBQUNrRjt3QkFDZixPQUFPOzRCQUNMMkUsT0FBTzdKLElBQUksQ0FBQztnQ0FBRXBDLE1BQU1rTTtnQ0FBYTVLLE9BQU87NEJBQWtDO3dCQUM1RTtvQkFDRixFQUFFLE9BQU9BLE9BQU87d0JBQ2RELFFBQVFDLEtBQUssQ0FBQyxrQ0FBaUQsT0FBZjRLLFlBQVkzSixFQUFFLEVBQUMsTUFBSWpCO3dCQUNuRTJLLE9BQU83SixJQUFJLENBQUM7NEJBQUVwQyxNQUFNa007NEJBQWE1SyxPQUFPQSxNQUFNNEMsT0FBTyxJQUFJO3dCQUFvQjtvQkFDL0U7Z0JBQ0Y7Z0JBRUEsT0FBTztvQkFDTGdFLFNBQVM4RCxRQUFRL0osTUFBTSxHQUFHO29CQUMxQmtLLE9BQU9IO29CQUNQQyxRQUFRQTtvQkFDUkcsT0FBT3JGLGFBQWE5RSxNQUFNO29CQUMxQm9LLGNBQWNMLFFBQVEvSixNQUFNO29CQUM1QnFLLFlBQVlMLE9BQU9oSyxNQUFNO2dCQUMzQjtZQUNGLEVBQUUsT0FBT1gsT0FBTztnQkFDZEQsUUFBUUMsS0FBSyxDQUFDLDhDQUE4Q0E7Z0JBQzVELE9BQU87b0JBQ0w0RyxTQUFTO29CQUNUaUUsT0FBTyxFQUFFO29CQUNURixRQUFRO3dCQUFDOzRCQUFFM0ssT0FBT0EsTUFBTTRDLE9BQU8sSUFBSTt3QkFBb0I7cUJBQUU7b0JBQ3pEa0ksT0FBT3JGLGFBQWE5RSxNQUFNO29CQUMxQm9LLGNBQWM7b0JBQ2RDLFlBQVl2RixhQUFhOUUsTUFBTTtnQkFDakM7WUFDRjtRQUNGO21FQUFHO1FBQUN5SjtLQUFzQjtJQUUxQixzREFBc0Q7SUFDdEQsTUFBTWEsNkJBQTZCdE8sa0RBQVdBO2dFQUFDLE9BQU9tRyxnQkFBZ0J5QztZQUNwRXhGLFFBQVFRLEdBQUcsQ0FBQyx1Q0FBdUM7Z0JBQUV1QztnQkFBZ0J5QztnQkFBZXhDLE1BQU0sRUFBRXJFLGlCQUFBQSwyQkFBQUEsS0FBTXVDLEVBQUU7WUFBQztZQUVyRyxJQUFJO2dCQUNGLElBQUksQ0FBQzZCLGtCQUFrQixDQUFDeUMsZUFBZTtvQkFDckN4RixRQUFRQyxLQUFLLENBQUM7b0JBQ2QsT0FBTztnQkFDVDtnQkFFQSxNQUFNc0IsZUFBZTNCO2dCQUNyQixJQUFJLENBQUMyQixjQUFjO29CQUNqQnZCLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPO2dCQUNUO2dCQUVBLE1BQU1vRyxXQUFXLE1BQU1DLE1BQU0sR0FBaUN2RCxPQUE5QjlGLFNBQVEsd0JBQXFEdUksT0FBL0J6QyxnQkFBZSxrQkFBOEIsT0FBZHlDLGdCQUFpQjtvQkFDNUd1RCxRQUFRO29CQUNSeEMsU0FBUzt3QkFDUEMsZUFBZSxVQUF1QixPQUFiakY7b0JBQzNCO2dCQUNGO2dCQUVBLElBQUksQ0FBQzhFLFNBQVNLLEVBQUUsRUFBRTtvQkFDaEIsSUFBSUwsU0FBU3pELE1BQU0sS0FBSyxLQUFLO3dCQUMzQjVDLFFBQVFDLEtBQUssQ0FBQzt3QkFDZEY7d0JBQ0EsT0FBTztvQkFDVDtvQkFDQSxNQUFNLElBQUk0RyxNQUFNLGlDQUFpRCxPQUFoQk4sU0FBU3pELE1BQU07Z0JBQ2xFO2dCQUVBLE1BQU1yRixPQUFPLE1BQU04SSxTQUFTTyxJQUFJO2dCQUVoQyxJQUFJckosS0FBS3NKLE9BQU8sRUFBRTtvQkFDaEIscURBQXFEO29CQUNyRCxJQUFJckIsbUJBQWtCN0csaUJBQUFBLDJCQUFBQSxLQUFNdUMsRUFBRSxHQUFFO3dCQUM5QmxCLFFBQVFRLEdBQUcsQ0FBQyxzQkFBcUMsT0FBZnVDLGdCQUFlO3dCQUVqRCx5QkFBeUI7d0JBQ3pCOUQ7b0ZBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTTs0RkFBQ2tCLENBQUFBLE9BQVFBLEtBQUszRCxFQUFFLEtBQUs2Qjs7O3dCQUV6RCxtQkFBbUI7d0JBQ25CM0Q7b0ZBQVlnRSxDQUFBQTtnQ0FDVixNQUFNcUMsVUFBVTtvQ0FBRSxHQUFHckMsSUFBSTtnQ0FBQztnQ0FDMUIsT0FBT3FDLE9BQU8sQ0FBQzFDLGVBQWU7Z0NBQzlCLE9BQU8wQzs0QkFDVDs7d0JBRUEsZUFBZTt3QkFDZnBJLGFBQWFDLGFBQWEsQ0FBQ0MsSUFBSSxHQUFHO3dCQUNsQ0YsYUFBYUMsYUFBYSxDQUFDRSxTQUFTLEdBQUc7d0JBQ3ZDLElBQUlILGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWUsRUFBRTs0QkFDekMsT0FBTzFGLGFBQWFLLFFBQVEsQ0FBQ3FGLGVBQWU7d0JBQzlDO3dCQUVBLGtDQUFrQzt3QkFDbEMsSUFBSTdELHVCQUF1QjZELGdCQUFnQjs0QkFDekM1RCxzQkFBc0I7d0JBQ3hCO3dCQUVBLGdEQUFnRDt3QkFDaERaO29GQUFXO2dDQUNUZ0gsa0JBQWtCOzRCQUNwQjttRkFBRzt3QkFFSHZGLFFBQVFRLEdBQUcsQ0FBQyxZQUEyQixPQUFmdUMsZ0JBQWU7b0JBQ3pDLE9BQU87NEJBZXdCeEYsaUJBQUFBO3dCQWQ3QixnREFBZ0Q7d0JBQ2hEMEI7b0ZBQWlCbUUsQ0FBQUE7Z0NBQ2YsT0FBT0EsS0FBS0ksR0FBRzs0RkFBQ3FCLENBQUFBO3dDQUNkLElBQUlBLEtBQUszRCxFQUFFLEtBQUs2QixnQkFBZ0I7NENBQzlCLE9BQU87Z0RBQ0wsR0FBRzhCLElBQUk7Z0RBQ1BhLGNBQWNiLEtBQUthLFlBQVksQ0FBQy9CLE1BQU07NEdBQUNnQyxDQUFBQSxJQUFLQSxFQUFFM0MsTUFBTSxLQUFLd0M7OzRDQUMzRDt3Q0FDRjt3Q0FDQSxPQUFPWDtvQ0FDVDs7NEJBQ0Y7O3dCQUVBLHdFQUF3RTt3QkFDeEUsSUFBSWhHLFVBQVVFLGlCQUFleEIsYUFBQUEsS0FBS0EsSUFBSSxjQUFUQSxrQ0FBQUEsa0JBQUFBLFdBQVdvQixJQUFJLGNBQWZwQixzQ0FBQUEsZ0JBQWlCa0QsUUFBUSxHQUFFOzRCQUN0RCxNQUFNZ0ssZ0JBQWdCO2dDQUNwQjFIO2dDQUNBZ0IsU0FBUyxHQUEyQixPQUF4QnhHLEtBQUtBLElBQUksQ0FBQ29CLElBQUksQ0FBQzhCLFFBQVEsRUFBQztnQ0FDcENxRCxhQUFhOzRCQUNmOzRCQUVBakYsT0FBT2lKLElBQUksQ0FBQyxnQkFBZ0IyQzt3QkFDOUI7b0JBQ0Y7b0JBRUEsT0FBT2xOLEtBQUtBLElBQUk7Z0JBQ2xCO2dCQUVBLE9BQU87WUFDVCxFQUFFLE9BQU8wQyxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsaUNBQWlDQTtnQkFDL0MsT0FBTztZQUNUO1FBQ0Y7K0RBQUc7UUFBQ3BCO1FBQVFFO1FBQWFKO1FBQU1PO1FBQW9CVTtRQUFpQkc7UUFBaUJ3SjtLQUFnQjtJQUVyRyw4QkFBOEI7SUFDOUIsTUFBTTRCLHFCQUFxQnZPLGtEQUFXQTt3REFBQyxPQUFPbUcsZ0JBQWdCcUk7WUFDNUQsSUFBSTtnQkFDRixJQUFJLENBQUN6TSxNQUFNO2dCQUVYLE1BQU00QyxlQUFlM0I7Z0JBQ3JCLElBQUksQ0FBQzJCLGNBQWM7Z0JBRW5CLE1BQU04RSxXQUFXLE1BQU1DLE1BQU0sR0FBVyxPQUFSckosU0FBUSw2QkFBMkI7b0JBQ2pFOEwsUUFBUTtvQkFDUnhDLFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQkMsZUFBZSxVQUF1QixPQUFiakY7b0JBQzNCO29CQUNBeUgsTUFBTWxDLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRWhFO3dCQUFnQnFJO29CQUFVO2dCQUNuRDtnQkFFQSxJQUFJL0UsU0FBU0ssRUFBRSxFQUFFO29CQUNmLHVDQUF1QztvQkFDdkN6SDt3RUFBaUJtRSxDQUFBQSxPQUFRQSxLQUFLSSxHQUFHO2dGQUFDcUIsQ0FBQUEsT0FDaENBLEtBQUszRCxFQUFFLEtBQUs2QixpQkFBaUI7d0NBQUUsR0FBRzhCLElBQUk7d0NBQUVELFdBQVc7b0NBQU0sSUFBSUM7OztvQkFHL0QseUZBQXlGO29CQUN6RnhGO3dFQUFlK0QsQ0FBQUE7NEJBQ2IsTUFBTXlCLE9BQU92SCxjQUFjZ0gsSUFBSTtxRkFBQ0MsQ0FBQUEsSUFBS0EsRUFBRXJELEVBQUUsS0FBSzZCOzs0QkFDOUMsTUFBTXNJLFlBQVl4RyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1ELFNBQVMsS0FBSUMsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNcEgsV0FBVyxJQUFHOzRCQUN6RCxPQUFPNE4sWUFBWUMsS0FBS0MsR0FBRyxDQUFDLEdBQUduSSxPQUFPLEtBQUtBO3dCQUM3Qzs7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9uRCxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsbUNBQW1DQTtZQUNuRDtRQUNGO3VEQUFHO1FBQUN0QjtRQUFNaUI7UUFBaUJ0QztLQUFjO0lBRXpDLDBDQUEwQztJQUMxQyxNQUFNa08sbUJBQW1CNU8sa0RBQVdBO3NEQUFDO1lBQ25DLElBQUk7Z0JBQ0YsSUFBSSxDQUFDK0IsTUFBTTtnQkFFWCxNQUFNNEMsZUFBZTNCO2dCQUNyQixJQUFJLENBQUMyQixjQUFjO2dCQUVuQixNQUFNOEUsV0FBVyxNQUFNQyxNQUFNLEdBQVcsT0FBUnJKLFNBQVEsZ0NBQThCO29CQUNwRThMLFFBQVE7b0JBQ1J4QyxTQUFTO3dCQUNQLGdCQUFnQjt3QkFDaEJDLGVBQWUsVUFBdUIsT0FBYmpGO29CQUMzQjtnQkFDRjtnQkFFQSxJQUFJOEUsU0FBU0ssRUFBRSxFQUFFO29CQUNmckgsZUFBZTtvQkFDZko7c0VBQWlCbUUsQ0FBQUEsT0FBUUEsS0FBS0ksR0FBRzs4RUFBQ3FCLENBQUFBLE9BQVM7d0NBQUUsR0FBR0EsSUFBSTt3Q0FBRXBILGFBQWE7b0NBQUU7OztnQkFDdkU7WUFDRixFQUFFLE9BQU93QyxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsaUNBQWlDQTtZQUNqRDtRQUNGO3FEQUFHO1FBQUN0QjtRQUFNaUI7S0FBZ0I7SUFFMUIsbUJBQW1CO0lBQ25CLE1BQU02TCxpQkFBaUI3TyxrREFBV0E7b0RBQUMsT0FBTzhPLFlBQVkzSTtZQUNwRCxJQUFJO2dCQUNGLElBQUksQ0FBQzJJLGNBQWNBLFdBQVc5SyxNQUFNLEtBQUssR0FBRztvQkFDMUNaLFFBQVFDLEtBQUssQ0FBQztvQkFDZCxPQUFPO2dCQUNUO2dCQUVBLE1BQU1zQixlQUFlM0I7Z0JBQ3JCLElBQUksQ0FBQzJCLGNBQWM7b0JBQ2pCdkIsUUFBUUMsS0FBSyxDQUFDO29CQUNkLE9BQU87Z0JBQ1Q7Z0JBRUEsTUFBTTBLLFVBQVUsRUFBRTtnQkFDbEIsTUFBTUMsU0FBUyxFQUFFO2dCQUVqQiwrQkFBK0I7Z0JBQy9CLEtBQUssTUFBTVEsYUFBYU0sV0FBWTtvQkFDbEMsSUFBSTt3QkFDRixNQUFNckYsV0FBVyxNQUFNQyxNQUFNLEdBQTRCOEUsT0FBekJuTyxTQUFRLG1CQUEyQixPQUFWbU8sWUFBYTs0QkFDcEVyQyxRQUFROzRCQUNSeEMsU0FBUztnQ0FDUEMsZUFBZSxVQUF1QixPQUFiakY7NEJBQzNCO3dCQUNGO3dCQUVBLElBQUksQ0FBQzhFLFNBQVNLLEVBQUUsRUFBRTs0QkFDaEIsSUFBSUwsU0FBU3pELE1BQU0sS0FBSyxLQUFLO2dDQUMzQjVDLFFBQVFDLEtBQUssQ0FBQztnQ0FDZEY7Z0NBQ0EsT0FBTzs0QkFDVDs0QkFDQSxNQUFNLElBQUk0RyxNQUFNLDRCQUE0QyxPQUFoQk4sU0FBU3pELE1BQU07d0JBQzdEO3dCQUVBLE1BQU1yRixPQUFPLE1BQU04SSxTQUFTTyxJQUFJO3dCQUVoQyxJQUFJckosS0FBS3NKLE9BQU8sRUFBRTs0QkFDaEI4RCxRQUFRNUosSUFBSSxDQUFDeEQsS0FBS0EsSUFBSTs0QkFFdEIsbUNBQW1DOzRCQUNuQzZCOzRFQUFZZ0UsQ0FBQUE7b0NBQ1YsTUFBTWEsa0JBQWtCO3dDQUFFLEdBQUdiLElBQUk7b0NBQUM7b0NBRWxDLCtCQUErQjtvQ0FDL0J1SSxPQUFPQyxJQUFJLENBQUMzSCxpQkFBaUJqRCxPQUFPO29GQUFDNkssQ0FBQUE7NENBQ25DLGtEQUFrRDs0Q0FDbEQ1SCxlQUFlLENBQUM0SCxPQUFPLEdBQUc1SCxlQUFlLENBQUM0SCxPQUFPLENBQUNySSxHQUFHOzRGQUFDdUUsQ0FBQUE7b0RBQ3BELElBQUlBLElBQUk3RyxFQUFFLEtBQUtrSyxXQUFXO3dEQUN4QixPQUFPOzREQUNMLEdBQUdyRCxHQUFHOzREQUNOLEdBQUd4SyxLQUFLQSxJQUFJOzREQUNadU8sV0FBVzt3REFDYjtvREFDRjtvREFDQSxPQUFPL0Q7Z0RBQ1Q7O3dDQUNGOztvQ0FFQSxPQUFPOUQ7Z0NBQ1Q7O3dCQUNGLE9BQU87NEJBQ0wyRyxPQUFPN0osSUFBSSxDQUFDO2dDQUFFcUs7Z0NBQVduTCxPQUFPOzRCQUEyQjt3QkFDN0Q7b0JBQ0YsRUFBRSxPQUFPQSxPQUFPO3dCQUNkRCxRQUFRQyxLQUFLLENBQUMsMkJBQXFDLE9BQVZtTCxXQUFVLE1BQUluTDt3QkFDdkQySyxPQUFPN0osSUFBSSxDQUFDOzRCQUFFcUs7NEJBQVduTCxPQUFPQSxNQUFNNEMsT0FBTyxJQUFJO3dCQUFvQjtvQkFDdkU7Z0JBQ0Y7Z0JBRUEseURBQXlEO2dCQUN6RCxJQUFJRSxrQkFBa0I0SCxRQUFRL0osTUFBTSxHQUFHLEdBQUc7b0JBQ3hDWixRQUFRUSxHQUFHLENBQUMsZ0RBQWdEdUM7b0JBQzVELElBQUkxRixhQUFhSyxRQUFRLENBQUNxRixlQUFlLEVBQUU7d0JBQ3pDLE9BQU8xRixhQUFhSyxRQUFRLENBQUNxRixlQUFlO29CQUM5QztvQkFFQSw0RkFBNEY7b0JBQzVGLElBQUk7d0JBQ0YvQyxRQUFRUSxHQUFHLENBQUM7d0JBQ1osTUFBTTRHLGFBQWFyRTt3QkFFbkIsZ0ZBQWdGO3dCQUNoRixNQUFNd0Msa0JBQWtCO29CQUMxQixFQUFFLE9BQU93RyxhQUFhO3dCQUNwQi9MLFFBQVFDLEtBQUssQ0FBQywrQ0FBK0M4TDtvQkFDL0Q7Z0JBQ0Y7Z0JBRUEsT0FBTztvQkFDTGxGLFNBQVM4RCxRQUFRL0osTUFBTSxHQUFHO29CQUMxQm9MLFNBQVNyQjtvQkFDVEMsUUFBUUE7b0JBQ1JHLE9BQU9XLFdBQVc5SyxNQUFNO29CQUN4Qm9LLGNBQWNMLFFBQVEvSixNQUFNO29CQUM1QnFLLFlBQVlMLE9BQU9oSyxNQUFNO2dCQUMzQjtZQUNGLEVBQUUsT0FBT1gsT0FBTztnQkFDZEQsUUFBUUMsS0FBSyxDQUFDLDZCQUE2QkE7Z0JBQzNDLE9BQU87b0JBQ0w0RyxTQUFTO29CQUNUbUYsU0FBUyxFQUFFO29CQUNYcEIsUUFBUTt3QkFBQzs0QkFBRTNLLE9BQU9BLE1BQU00QyxPQUFPLElBQUk7d0JBQW9CO3FCQUFFO29CQUN6RGtJLE9BQU9XLFdBQVc5SyxNQUFNO29CQUN4Qm9LLGNBQWM7b0JBQ2RDLFlBQVlTLFdBQVc5SyxNQUFNO2dCQUMvQjtZQUNGO1FBQ0Y7bURBQUc7UUFBQ2hCO1FBQWlCRztRQUFpQnFIO1FBQWM3QjtLQUFrQjtJQUV0RSwyRUFBMkU7SUFDM0UsTUFBTTBHLGVBQWVwUCw4Q0FBT0E7OENBQUMsSUFBTztnQkFDbENTO2dCQUNBSTtnQkFDQUQ7Z0JBQ0F5QjtnQkFDQUk7Z0JBQ0FFO2dCQUNBVDtnQkFDQVc7Z0JBQ0FQO2dCQUNBaUk7Z0JBQ0FFO2dCQUNBVztnQkFDQWtCO2dCQUNBZTtnQkFDQUU7Z0JBQ0E3RTtnQkFDQWdFO2dCQUNBNEI7Z0JBQ0FLO2dCQUNBbkI7Z0JBQ0FLO2dCQUNBUTtnQkFDQU87WUFDRjs2Q0FBSTtRQUNGbk87UUFDQUk7UUFDQUQ7UUFDQXlCO1FBQ0FJO1FBQ0FFO1FBQ0FUO1FBQ0FXO1FBQ0FQO1FBQ0FpSTtRQUNBRTtRQUNBVztRQUNBa0I7UUFDQWU7UUFDQUU7UUFDQTdFO1FBQ0FnRTtRQUNBNEI7UUFDQUs7UUFDQW5CO1FBQ0FLO1FBQ0FRO1FBQ0FPO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ2pOLFlBQVkwTixRQUFRO1FBQUNDLE9BQU9GO2tCQUMxQnZOOzs7Ozs7QUFHUCxFQUFFO0dBMzREV0Q7O1FBQ2MxQixpREFBT0E7OztLQURyQjBCO0FBNjRETixNQUFNMk4sVUFBVTs7SUFDckIsTUFBTUMsVUFBVTVQLGlEQUFVQSxDQUFDK0I7SUFFM0IsSUFBSSxDQUFDNk4sU0FBUztRQUNaLE1BQU0sSUFBSTFGLE1BQU07SUFDbEI7SUFFQSxPQUFPMEY7QUFDVCxFQUFFO0lBUldEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFByb2dyYW1hY2FvXFxISUdIIFRJREUgU1lTVEVNU1xcaGlnaC10aWRlLXN5c3RlbXMtZnJvbnRlbmRcXHNyY1xcY29udGV4dHNcXENoYXRDb250ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlTWVtbywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnLi9BdXRoQ29udGV4dCc7XHJcbmltcG9ydCBpbyBmcm9tICdzb2NrZXQuaW8tY2xpZW50JztcclxuXHJcbmNvbnN0IEFQSV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjUwMDAnO1xyXG5cclxuLy8gQ2FjaGUgcGFyYSBldml0YXIgcmVxdWlzacOnw7VlcyBkdXBsaWNhZGFzXHJcbmNvbnN0IHJlcXVlc3RDYWNoZSA9IHtcclxuICBjb252ZXJzYXRpb25zOiB7IGRhdGE6IG51bGwsIHRpbWVzdGFtcDogMCB9LFxyXG4gIHVucmVhZENvdW50OiB7IGRhdGE6IG51bGwsIHRpbWVzdGFtcDogMCB9LFxyXG4gIG1lc3NhZ2VzOiB7fSwgLy8gQ2FjaGUgcGFyYSBtZW5zYWdlbnMgcG9yIGNvbnZlcnNhXHJcbiAgdG9rZW5DaGVjazogeyB0aW1lc3RhbXA6IDAsIHZhbGlkOiBmYWxzZSB9IC8vIENhY2hlIHBhcmEgdmVyaWZpY2HDp8OjbyBkZSB0b2tlblxyXG59O1xyXG5cclxuLy8gVGVtcG8gZGUgZXhwaXJhw6fDo28gZG8gY2FjaGUgZW0gbWlsaXNzZWd1bmRvc1xyXG5jb25zdCBDQUNIRV9FWFBJUkFUSU9OID0gMzAwMDA7IC8vIDMwIHNlZ3VuZG9zIHBhcmEgZGFkb3Mgbm9ybWFpc1xyXG5jb25zdCBUT0tFTl9DSEVDS19FWFBJUkFUSU9OID0gNjAwMDA7IC8vIDEgbWludXRvIHBhcmEgdmVyaWZpY2HDp8OjbyBkZSB0b2tlblxyXG5cclxuLy8gRnVuw6fDo28gZGUgZGVib3VuY2UgcGFyYSBsaW1pdGFyIGEgZnJlcXXDqm5jaWEgZGUgY2hhbWFkYXNcclxuY29uc3QgZGVib3VuY2UgPSAoZnVuYywgd2FpdCkgPT4ge1xyXG4gIGxldCB0aW1lb3V0O1xyXG4gIHJldHVybiBmdW5jdGlvbiBleGVjdXRlZEZ1bmN0aW9uKC4uLmFyZ3MpIHtcclxuICAgIGNvbnN0IGxhdGVyID0gKCkgPT4ge1xyXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dCk7XHJcbiAgICAgIGZ1bmMoLi4uYXJncyk7XHJcbiAgICB9O1xyXG4gICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpO1xyXG4gICAgdGltZW91dCA9IHNldFRpbWVvdXQobGF0ZXIsIHdhaXQpO1xyXG4gIH07XHJcbn07XHJcblxyXG5jb25zdCBDaGF0Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQobnVsbCk7XHJcblxyXG5leHBvcnQgY29uc3QgQ2hhdFByb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IHsgdXNlciwgbG9nb3V0IH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgW3NvY2tldCwgc2V0U29ja2V0XSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFtpc0Nvbm5lY3RlZCwgc2V0SXNDb25uZWN0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjb252ZXJzYXRpb25zLCBzZXRDb252ZXJzYXRpb25zXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbYWN0aXZlQ29udmVyc2F0aW9uLCBzZXRBY3RpdmVDb252ZXJzYXRpb25dID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW21lc3NhZ2VzLCBzZXRNZXNzYWdlc10gPSB1c2VTdGF0ZSh7fSk7XHJcbiAgY29uc3QgW3VucmVhZENvdW50LCBzZXRVbnJlYWRDb3VudF0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbaXNQYW5lbE9wZW4sIHNldElzUGFuZWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNNb2RhbE9wZW4sIHNldElzTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyBPYnRlciBvIHRva2VuIGF0dWFsIGRvIGxvY2FsU3RvcmFnZSAtIG1lbW9pemFkbyBwYXJhIGV2aXRhciBjaGFtYWRhcyBkZXNuZWNlc3PDoXJpYXNcclxuICBjb25zdCBnZXRDdXJyZW50VG9rZW4gPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgcmV0dXJuIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBGdW7Dp8OjbyBwYXJhIGxpZGFyIGNvbSBlcnJvcyBkZSBhdXRlbnRpY2HDp8Ojb1xyXG4gIGNvbnN0IGhhbmRsZUF1dGhFcnJvciA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gZGUgYXV0ZW50aWNhw6fDo28gZGV0ZWN0YWRvLiBSZWRpcmVjaW9uYW5kbyBwYXJhIGxvZ2luLi4uJyk7XHJcbiAgICAvLyBMaW1wYXIgZGFkb3MgbG9jYWlzXHJcbiAgICBzZXRDb252ZXJzYXRpb25zKFtdKTtcclxuICAgIHNldE1lc3NhZ2VzKHt9KTtcclxuICAgIHNldEFjdGl2ZUNvbnZlcnNhdGlvbihudWxsKTtcclxuICAgIHNldFVucmVhZENvdW50KDApO1xyXG4gICAgc2V0SXNQYW5lbE9wZW4oZmFsc2UpO1xyXG4gICAgc2V0SXNNb2RhbE9wZW4oZmFsc2UpO1xyXG5cclxuICAgIC8vIERlc2NvbmVjdGFyIHNvY2tldCBzZSBleGlzdGlyXHJcbiAgICBpZiAoc29ja2V0KSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgc29ja2V0LmRpc2Nvbm5lY3QoKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGRlc2NvbmVjdGFyIHNvY2tldDonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIExpbXBhciBjYWNoZVxyXG4gICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YSA9IG51bGw7XHJcbiAgICByZXF1ZXN0Q2FjaGUudW5yZWFkQ291bnQuZGF0YSA9IG51bGw7XHJcbiAgICByZXF1ZXN0Q2FjaGUubWVzc2FnZXMgPSB7fTsgLy8gTGltcGFyIGNhY2hlIGRlIG1lbnNhZ2Vuc1xyXG5cclxuICAgIC8vIFJlZGlyZWNpb25hciBwYXJhIGxvZ2luXHJcbiAgICBpZiAobG9nb3V0KSB7XHJcbiAgICAgIGxvZ291dCgpO1xyXG4gICAgfVxyXG4gIH0sIFtzb2NrZXQsIGxvZ291dF0pO1xyXG5cclxuICAvLyBVc2FyIHJlZmVyw6puY2lhcyBwYXJhIGNvbnRyb2xhciBlc3RhZG9zIHF1ZSBuw6NvIGRldmVtIGNhdXNhciByZS1yZW5kZXJpemHDp8O1ZXNcclxuICBjb25zdCBzb2NrZXRJbml0aWFsaXplZFJlZiA9IHVzZVJlZihmYWxzZSk7XHJcbiAgY29uc3QgaXNMb2FkaW5nUmVmID0gdXNlUmVmKGZhbHNlKTtcclxuICBjb25zdCBsYXN0SW5pdEF0dGVtcHRSZWYgPSB1c2VSZWYoMCk7XHJcbiAgY29uc3QgcmVjb25uZWN0QXR0ZW1wdHNSZWYgPSB1c2VSZWYoMCk7XHJcblxyXG4gIC8vIFJlZGVmaW5pciBhIHJlZmVyw6puY2lhIHF1YW5kbyBvIHVzdcOhcmlvIG11ZGFyXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFNlIG8gdXN1w6FyaW8gZm9yIG51bGwgKGxvZ291dCksIGRlc2NvbmVjdGFyIG8gc29ja2V0XHJcbiAgICBpZiAoIXVzZXIgJiYgc29ja2V0KSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgc29ja2V0LmRpc2Nvbm5lY3QoKTtcclxuICAgICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XHJcbiAgICAgICAgc2V0U29ja2V0KG51bGwpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gZGVzY29uZWN0YXIgc29ja2V0IGFww7NzIGxvZ291dDonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBSZXNldGFyIGVzdGFkbyBxdWFuZG8gbyB1c3XDoXJpbyBtdWRhXHJcbiAgICBzb2NrZXRJbml0aWFsaXplZFJlZi5jdXJyZW50ID0gZmFsc2U7XHJcbiAgICBsYXN0SW5pdEF0dGVtcHRSZWYuY3VycmVudCA9IDA7XHJcbiAgICByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50ID0gMDtcclxuICB9LCBbdXNlciwgc29ja2V0XSk7XHJcblxyXG4gIC8vIExpbXBhciBlc3RhZG8gZG8gY2hhdCBxdWFuZG8gbyB1c3XDoXJpbyBtdWRhXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFNlIG8gdXN1w6FyaW8gbXVkb3UgKG5vdm8gbG9naW4pLCBsaW1wYXIgdG9kbyBvIGVzdGFkbyBkbyBjaGF0XHJcbiAgICBpZiAodXNlcikge1xyXG4gICAgICBjb25zb2xlLmxvZygnVXN1w6FyaW8gbG9nYWRvLCBsaW1wYW5kbyBlc3RhZG8gZG8gY2hhdCBwYXJhIG5vdm8gdXN1w6FyaW86JywgdXNlci5mdWxsTmFtZSk7XHJcbiAgICAgIC8vIExpbXBhciBjb252ZXJzYXMgZSBtZW5zYWdlbnMgcGFyYSBvIG5vdm8gdXN1w6FyaW9cclxuICAgICAgc2V0Q29udmVyc2F0aW9ucyhbXSk7XHJcbiAgICAgIHNldE1lc3NhZ2VzKHt9KTtcclxuICAgICAgc2V0QWN0aXZlQ29udmVyc2F0aW9uKG51bGwpO1xyXG4gICAgICBzZXRVbnJlYWRDb3VudCgwKTtcclxuICAgICAgc2V0SXNQYW5lbE9wZW4oZmFsc2UpO1xyXG4gICAgICBzZXRJc01vZGFsT3BlbihmYWxzZSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBMaW1wYXIgY2FjaGVcclxuICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YSA9IG51bGw7XHJcbiAgICAgIHJlcXVlc3RDYWNoZS51bnJlYWRDb3VudC5kYXRhID0gbnVsbDtcclxuICAgICAgcmVxdWVzdENhY2hlLm1lc3NhZ2VzID0ge307XHJcbiAgICAgIHJlcXVlc3RDYWNoZS50b2tlbkNoZWNrLnRpbWVzdGFtcCA9IDA7XHJcbiAgICAgIHJlcXVlc3RDYWNoZS50b2tlbkNoZWNrLnZhbGlkID0gZmFsc2U7XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmxvZygnQ2FjaGUgbGltcG8gcGFyYSBub3ZvIHVzdcOhcmlvJyk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBMaW1wYXIgZGFkb3MgcmVzaWR1YWlzIGRvIGxvY2FsU3RvcmFnZSByZWxhY2lvbmFkb3MgYW8gY2hhdFxyXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAvLyBMaW1wYXIgcXVhbHF1ZXIgY2FjaGUgZXNwZWPDrWZpY28gZG8gY2hhdCBxdWUgcG9zc2EgZXN0YXIgbm8gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgY29uc3Qga2V5c1RvUmVtb3ZlID0gW107XHJcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsb2NhbFN0b3JhZ2UubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICAgIGNvbnN0IGtleSA9IGxvY2FsU3RvcmFnZS5rZXkoaSk7XHJcbiAgICAgICAgICBpZiAoa2V5ICYmIChrZXkuc3RhcnRzV2l0aCgnY2hhdF8nKSB8fCBrZXkuc3RhcnRzV2l0aCgnY29udmVyc2F0aW9uXycpIHx8IGtleS5zdGFydHNXaXRoKCdtZXNzYWdlXycpKSkge1xyXG4gICAgICAgICAgICBrZXlzVG9SZW1vdmUucHVzaChrZXkpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBrZXlzVG9SZW1vdmUuZm9yRWFjaChrZXkgPT4gbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0RhZG9zIHJlc2lkdWFpcyBkbyBjaGF0IHJlbW92aWRvcyBkbyBsb2NhbFN0b3JhZ2UnKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFt1c2VyPy5pZF0pOyAvLyBFeGVjdXRhciBxdWFuZG8gbyBJRCBkbyB1c3XDoXJpbyBtdWRhclxyXG5cclxuICAvLyBJbmljaWFsaXphciBjb25leMOjbyBXZWJTb2NrZXRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gU2UgbsOjbyBow6EgdXN1w6FyaW8sIG7Do28gaW5pY2lhbGl6YXJcclxuICAgIGlmICghdXNlcikge1xyXG4gICAgICBjb25zb2xlLmxvZygnVXN1w6FyaW8gbsOjbyBsb2dhZG8sIG7Do28gaW5pY2lhbGl6YW5kbyBXZWJTb2NrZXQnKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENsaWVudGVzIGFnb3JhIHBvZGVtIHVzYXIgbyBjaGF0XHJcbiAgICBjb25zb2xlLmxvZygnSW5pY2lhbGl6YW5kbyBjaGF0IHBhcmEgdXN1w6FyaW86JywgdXNlci5yb2xlIHx8ICdVU0VSJyk7XHJcblxyXG4gICAgLy8gU2UgasOhIGV4aXN0ZSB1bSBzb2NrZXQgY29uZWN0YWRvLCBuw6NvIGZhemVyIG5hZGFcclxuICAgIGlmIChzb2NrZXQgJiYgaXNDb25uZWN0ZWQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBqw6EgZXN0w6EgY29uZWN0YWRvLCBuw6NvIMOpIG5lY2Vzc8OhcmlvIHJlaW5pY2lhbGl6YXInKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFNlIGrDoSBlc3TDoSBpbmljaWFsaXphZG8gZSBhIMO6bHRpbWEgdGVudGF0aXZhIGZvaSByZWNlbnRlLCBuw6NvIHRlbnRhciBub3ZhbWVudGVcclxuICAgIGlmIChzb2NrZXRJbml0aWFsaXplZFJlZi5jdXJyZW50ICYmIERhdGUubm93KCkgLSBsYXN0SW5pdEF0dGVtcHRSZWYuY3VycmVudCA8IDYwMDAwKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdTb2NrZXQgasOhIGluaWNpYWxpemFkbyByZWNlbnRlbWVudGUsIGFndWFyZGFuZG8uLi4nKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE1hcmNhciBjb21vIGluaWNpYWxpemFkbyBpbWVkaWF0YW1lbnRlIHBhcmEgZXZpdGFyIG3Dumx0aXBsYXMgdGVudGF0aXZhc1xyXG4gICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IHRydWU7XHJcbiAgICBsYXN0SW5pdEF0dGVtcHRSZWYuY3VycmVudCA9IERhdGUubm93KCk7XHJcblxyXG4gICAgLy8gRnVuw6fDo28gYXNzw61uY3JvbmEgcGFyYSBpbmljaWFsaXphciBvIFdlYlNvY2tldFxyXG4gICAgY29uc3QgaW5pdGlhbGl6ZVdlYlNvY2tldCA9IGFzeW5jICgpID0+IHtcclxuICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gdXN1w6FyaW8gZXN0w6EgbG9nYWRvIGFudGVzIGRlIGluaWNpYWxpemFyIG8gV2ViU29ja2V0XHJcbiAgICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG5cclxuICAgICAgLy8gQ2xpZW50ZXMgYWdvcmEgcG9kZW0gdXNhciBvIGNoYXRcclxuICAgICAgY29uc29sZS5sb2coJ0luaWNpYWxpemFuZG8gV2ViU29ja2V0IHBhcmE6JywgdXNlci5yb2xlIHx8ICdVU0VSJyk7XHJcblxyXG4gICAgICBjb25zdCBjdXJyZW50VG9rZW4gPSBnZXRDdXJyZW50VG9rZW4oKTtcclxuICAgICAgaWYgKCFjdXJyZW50VG9rZW4pIHJldHVybjtcclxuXHJcbiAgICAgIC8vIEV2aXRhciByZWNvbmV4w7VlcyBkZXNuZWNlc3PDoXJpYXMgLSB2ZXJpZmljYcOnw6NvIGFkaWNpb25hbFxyXG4gICAgICBpZiAoc29ja2V0KSB7XHJcbiAgICAgICAgaWYgKGlzQ29ubmVjdGVkKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IGrDoSBjb25lY3RhZG8sIGlnbm9yYW5kbyBpbmljaWFsaXphw6fDo28nKTtcclxuICAgICAgICAgIHJldHVybiBzb2NrZXQ7IC8vIFJldG9ybmFyIG8gc29ja2V0IGV4aXN0ZW50ZVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyBTZSBvIHNvY2tldCBleGlzdGUgbWFzIG7Do28gZXN0w6EgY29uZWN0YWRvLCB0ZW50YXIgcmVjb25lY3RhciBlbSB2ZXogZGUgY3JpYXIgdW0gbm92b1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1NvY2tldCBleGlzdGUgbWFzIG7Do28gZXN0w6EgY29uZWN0YWRvLCB0ZW50YW5kbyByZWNvbmVjdGFyJyk7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAvLyBUZW50YXIgcmVjb25lY3RhciBvIHNvY2tldCBleGlzdGVudGUgZW0gdmV6IGRlIGNyaWFyIHVtIG5vdm9cclxuICAgICAgICAgICAgaWYgKCFzb2NrZXQuY29ubmVjdGVkICYmIHNvY2tldC5jb25uZWN0KSB7XHJcbiAgICAgICAgICAgICAgc29ja2V0LmNvbm5lY3QoKTtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVGVudGF0aXZhIGRlIHJlY29uZXjDo28gaW5pY2lhZGEnKTtcclxuICAgICAgICAgICAgICByZXR1cm4gc29ja2V0O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIHJlY29uZWN0YXIgc29ja2V0IGV4aXN0ZW50ZTonLCBlcnJvcik7XHJcbiAgICAgICAgICAgIC8vIFPDsyBkZXNjb25lY3RhciBzZSBhIHJlY29uZXjDo28gZmFsaGFyXHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgc29ja2V0LmRpc2Nvbm5lY3QoKTtcclxuICAgICAgICAgICAgfSBjYXRjaCAoZGlzY29ubmVjdEVycm9yKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBkZXNjb25lY3RhciBzb2NrZXQgYXDDs3MgZmFsaGEgbmEgcmVjb25leMOjbzonLCBkaXNjb25uZWN0RXJyb3IpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBNYXJjYXIgY29tbyBpbmljaWFsaXphZG8gcGFyYSBldml0YXIgbcO6bHRpcGxhcyBpbmljaWFsaXphw6fDtWVzXHJcbiAgICAgIHNvY2tldEluaXRpYWxpemVkUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG5cclxuICAgICAgLy8gTGltaXRhciBhIGZyZXF1w6puY2lhIGRlIHJlY29uZXjDtWVzXHJcbiAgICAgIGNvbnN0IG1heFJlY29ubmVjdEF0dGVtcHRzID0gNTtcclxuICAgICAgY29uc3QgcmVjb25uZWN0RGVsYXkgPSAzMDAwOyAvLyAzIHNlZ3VuZG9zXHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnSW5pY2lhbGl6YW5kbyBXZWJTb2NrZXQuLi4nKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gdG9rZW4gw6kgdsOhbGlkbyBhbnRlcyBkZSBpbmljaWFsaXphciBvIFdlYlNvY2tldFxyXG4gICAgICAgIC8vIFVzYXIgdW1hIHZlcmlmaWNhw6fDo28gbWFpcyBzaW1wbGVzIHBhcmEgZXZpdGFyIG3Dumx0aXBsYXMgcmVxdWlzacOnw7Vlc1xyXG4gICAgICAgIC8vIEFzc3VtaXIgcXVlIG8gdG9rZW4gw6kgdsOhbGlkbyBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2Fkb1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiB2w6FsaWRvLCBpbmljaWFsaXphbmRvIFdlYlNvY2tldCBwYXJhIG8gdXN1w6FyaW86JywgdXNlci5mdWxsTmFtZSk7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdJbmljaWFsaXphbmRvIFdlYlNvY2tldCBjb20gYXV0ZW50aWNhw6fDo28nKTtcclxuXHJcbiAgICAgICAgY29uc3Qgc29ja2V0SW5zdGFuY2UgPSBpbyhBUElfVVJMLCB7XHJcbiAgICAgICAgICBwYXRoOiAnL3NvY2tldC5pbycsXHJcbiAgICAgICAgICBhdXRoOiB7IHRva2VuOiBjdXJyZW50VG9rZW4gfSxcclxuICAgICAgICAgIHRyYW5zcG9ydHM6IFsnd2Vic29ja2V0JywgJ3BvbGxpbmcnXSxcclxuICAgICAgICAgIHJlY29ubmVjdGlvbkF0dGVtcHRzOiBtYXhSZWNvbm5lY3RBdHRlbXB0cyxcclxuICAgICAgICAgIHJlY29ubmVjdGlvbkRlbGF5OiByZWNvbm5lY3REZWxheSxcclxuICAgICAgICAgIHRpbWVvdXQ6IDEwMDAwLCAvLyAxMCBzZWd1bmRvc1xyXG4gICAgICAgICAgYXV0b0Nvbm5lY3Q6IHRydWUsXHJcbiAgICAgICAgICByZWNvbm5lY3Rpb246IHRydWVcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2Nvbm5lY3QnLCAoKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IGNvbm5lY3RlZCcpO1xyXG4gICAgICAgICAgc2V0SXNDb25uZWN0ZWQodHJ1ZSk7XHJcbiAgICAgICAgICByZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50ID0gMDtcclxuXHJcbiAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGVyc29uYWxpemFkbyBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIGNvbmV4w6NvXHJcbiAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2NoYXQ6d2Vic29ja2V0OnVwZGF0ZScsIHtcclxuICAgICAgICAgICAgZGV0YWlsOiB7IHR5cGU6ICdjb25uZWN0aW9uJywgc3RhdHVzOiAnY29ubmVjdGVkJywgdGltZXN0YW1wOiBEYXRlLm5vdygpIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2Rpc2Nvbm5lY3QnLCAoKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IGRpc2Nvbm5lY3RlZCcpO1xyXG4gICAgICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xyXG5cclxuICAgICAgICAgIC8vIERpc3BhcmFyIGV2ZW50byBwZXJzb25hbGl6YWRvIHBhcmEgbm90aWZpY2FyIGNvbXBvbmVudGVzIHNvYnJlIGEgZGVzY29uZXjDo29cclxuICAgICAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudCgnY2hhdDp3ZWJzb2NrZXQ6dXBkYXRlJywge1xyXG4gICAgICAgICAgICBkZXRhaWw6IHsgdHlwZTogJ2Nvbm5lY3Rpb24nLCBzdGF0dXM6ICdkaXNjb25uZWN0ZWQnLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfVxyXG4gICAgICAgICAgfSkpO1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vbignY29ubmVjdF9lcnJvcicsIChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignV2ViU29ja2V0IGNvbm5lY3Rpb24gZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICAgICAgcmVjb25uZWN0QXR0ZW1wdHNSZWYuY3VycmVudCsrO1xyXG5cclxuICAgICAgICAgIC8vIFNlIG8gZXJybyBmb3IgZGUgYXV0ZW50aWNhw6fDo28sIG7Do28gdGVudGFyIHJlY29uZWN0YXJcclxuICAgICAgICAgIGlmIChlcnJvci5tZXNzYWdlICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ0F1dGhlbnRpY2F0aW9uIGVycm9yJykpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJybyBkZSBhdXRlbnRpY2HDp8OjbyBubyBXZWJTb2NrZXQsIG7Do28gdGVudGFuZG8gcmVjb25lY3RhcicpO1xyXG4gICAgICAgICAgICBzb2NrZXRJbnN0YW5jZS5kaXNjb25uZWN0KCk7XHJcbiAgICAgICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcclxuICAgICAgICAgICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IGZhbHNlOyAvLyBQZXJtaXRpciBub3ZhIHRlbnRhdGl2YSBubyBmdXR1cm9cclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGlmIChyZWNvbm5lY3RBdHRlbXB0c1JlZi5jdXJyZW50ID49IG1heFJlY29ubmVjdEF0dGVtcHRzKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ03DoXhpbW8gZGUgdGVudGF0aXZhcyBkZSByZWNvbmV4w6NvIGF0aW5naWRvJyk7XHJcbiAgICAgICAgICAgIHNvY2tldEluc3RhbmNlLmRpc2Nvbm5lY3QoKTtcclxuICAgICAgICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xyXG4gICAgICAgICAgICBzb2NrZXRJbml0aWFsaXplZFJlZi5jdXJyZW50ID0gZmFsc2U7IC8vIFBlcm1pdGlyIG5vdmEgdGVudGF0aXZhIG5vIGZ1dHVyb1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBFdmVudG8gcGFyYSByZWNlYmVyIG5vdmFzIG1lbnNhZ2Vuc1xyXG4gICAgICAgIHNvY2tldEluc3RhbmNlLm9uKCdtZXNzYWdlOm5ldycsIChtZXNzYWdlKSA9PiB7XHJcbiAgICAgICAgICBpZiAoIW1lc3NhZ2UgfHwgIW1lc3NhZ2UuY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignTWVuc2FnZW0gaW52w6FsaWRhIHJlY2ViaWRhOicsIG1lc3NhZ2UpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLy8gVXNhciB1bWEgZnVuw6fDo28gYW7DtG5pbWEgcGFyYSBldml0YXIgZGVwZW5kw6puY2lhcyBjaXJjdWxhcmVzXHJcbiAgICAgICAgICBjb25zdCB1c2VySWQgPSB1c2VyPy5pZDtcclxuXHJcbiAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgYSBtZW5zYWdlbSB0ZW0gdW0gdGVtcElkIGFzc29jaWFkbyAocGFyYSBzdWJzdGl0dWlyIG1lbnNhZ2VtIHRlbXBvcsOhcmlhKVxyXG4gICAgICAgICAgLy8gTyBiYWNrZW5kIHBvZGUgbsOjbyByZXRvcm5hciBvIHRlbXBJZCwgZW50w6NvIHByZWNpc2Ftb3MgdmVyaWZpY2FyIHNlIMOpIHVtYSBtZW5zYWdlbSBkbyB1c3XDoXJpbyBhdHVhbFxyXG4gICAgICAgICAgY29uc3QgdGVtcElkID0gbWVzc2FnZS50ZW1wSWQ7XHJcbiAgICAgICAgICBjb25zdCBpc0N1cnJlbnRVc2VyTWVzc2FnZSA9IG1lc3NhZ2Uuc2VuZGVySWQgPT09IHVzZXJJZDtcclxuXHJcblxyXG4gICAgICAgICAgLy8gQXR1YWxpemFyIG1lbnNhZ2VucyBkYSBjb252ZXJzYVxyXG4gICAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbk1lc3NhZ2VzID0gcHJldlttZXNzYWdlLmNvbnZlcnNhdGlvbklkXSB8fCBbXTtcclxuXHJcbiAgICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIG1lbnNhZ2VtIGrDoSBleGlzdGUgcGFyYSBldml0YXIgZHVwbGljYcOnw6NvXHJcbiAgICAgICAgICAgIGlmIChjb252ZXJzYXRpb25NZXNzYWdlcy5zb21lKG0gPT4gbS5pZCA9PT0gbWVzc2FnZS5pZCkpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gcHJldjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gU2UgdGl2ZXIgdW0gdGVtcElkLCBzdWJzdGl0dWlyIGEgbWVuc2FnZW0gdGVtcG9yw6FyaWEgcGVsYSBtZW5zYWdlbSByZWFsXHJcbiAgICAgICAgICAgIGlmICh0ZW1wSWQgJiYgY29udmVyc2F0aW9uTWVzc2FnZXMuc29tZShtID0+IG0uaWQgPT09IHRlbXBJZCkpIHtcclxuICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgIFttZXNzYWdlLmNvbnZlcnNhdGlvbklkXTogY29udmVyc2F0aW9uTWVzc2FnZXMubWFwKG0gPT5cclxuICAgICAgICAgICAgICAgICAgbS5pZCA9PT0gdGVtcElkID8gbWVzc2FnZSA6IG1cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBTZSBmb3IgdW1hIG1lbnNhZ2VtIGRvIHVzdcOhcmlvIGF0dWFsLCB2ZXJpZmljYXIgc2UgaMOhIG1lbnNhZ2VucyB0ZW1wb3LDoXJpYXMgcmVjZW50ZXNcclxuICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50VXNlck1lc3NhZ2UgPSBtZXNzYWdlLnNlbmRlcklkID09PSB1c2VySWQgfHwgbWVzc2FnZS5zZW5kZXJDbGllbnRJZCA9PT0gdXNlcklkO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgaWYgKGlzQ3VycmVudFVzZXJNZXNzYWdlICYmICF0ZW1wSWQpIHtcclxuICAgICAgICAgICAgICAvLyBQcm9jdXJhciBwb3IgbWVuc2FnZW5zIHRlbXBvcsOhcmlhcyByZWNlbnRlcyAobm9zIMO6bHRpbW9zIDEwIHNlZ3VuZG9zKVxyXG4gICAgICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XHJcbiAgICAgICAgICAgICAgY29uc3QgdGVtcE1lc3NhZ2VzID0gY29udmVyc2F0aW9uTWVzc2FnZXMuZmlsdGVyKG0gPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKCFtLmlzVGVtcCkgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgaWYgKCEobS5zZW5kZXJJZCA9PT0gdXNlcklkIHx8IG0uc2VuZGVyQ2xpZW50SWQgPT09IHVzZXJJZCkpIHJldHVybiBmYWxzZTtcclxuICAgICAgICAgICAgICAgIGlmICgobm93IC0gbmV3IERhdGUobS5jcmVhdGVkQXQpKSA+PSAxMDAwMCkgcmV0dXJuIGZhbHNlOyAvLyAxMCBzZWd1bmRvc1xyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAvLyBQYXJhIG1lbnNhZ2VucyBjb20gYW5leG9zLCB2ZXJpZmljYXIgc2UgYW1iYXMgc8OjbyBBVFRBQ0hNRU5UXHJcbiAgICAgICAgICAgICAgICBpZiAobWVzc2FnZS5jb250ZW50VHlwZSA9PT0gJ0FUVEFDSE1FTlQnICYmIG0uY29udGVudFR5cGUgPT09ICdBVFRBQ0hNRU5UJykge1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgLy8gUGFyYSBtZW5zYWdlbnMgZGUgdGV4dG8sIHZlcmlmaWNhciBzZSBvIGNvbnRlw7pkbyDDqSBpZ3VhbFxyXG4gICAgICAgICAgICAgICAgaWYgKG1lc3NhZ2UuY29udGVudFR5cGUgPT09ICdURVhUJyAmJiBtLmNvbnRlbnRUeXBlID09PSAnVEVYVCcgJiYgbS5jb250ZW50ID09PSBtZXNzYWdlLmNvbnRlbnQpIHtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgaWYgKHRlbXBNZXNzYWdlcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBTdWJzdGl0dWlyIGEgcHJpbWVpcmEgbWVuc2FnZW0gdGVtcG9yw6FyaWEgZW5jb250cmFkYVxyXG4gICAgICAgICAgICAgICAgY29uc3QgdGVtcE1lc3NhZ2UgPSB0ZW1wTWVzc2FnZXNbMF07XHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgW21lc3NhZ2UuY29udmVyc2F0aW9uSWRdOiBjb252ZXJzYXRpb25NZXNzYWdlcy5tYXAobSA9PlxyXG4gICAgICAgICAgICAgICAgICAgIG0uaWQgPT09IHRlbXBNZXNzYWdlLmlkID8gbWVzc2FnZSA6IG1cclxuICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIENhc28gY29udHLDoXJpbywgYWRpY2lvbmFyIGEgbm92YSBtZW5zYWdlbVxyXG4gICAgICAgICAgICAvLyBNYW50ZXIgYSBvcmRlbSBjcm9ub2zDs2dpY2EgKG1haXMgYW50aWdhcyBwcmltZWlybylcclxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZE1lc3NhZ2VzID0gWy4uLmNvbnZlcnNhdGlvbk1lc3NhZ2VzLCBtZXNzYWdlXS5zb3J0KChhLCBiKSA9PlxyXG4gICAgICAgICAgICAgIG5ldyBEYXRlKGEuY3JlYXRlZEF0KSAtIG5ldyBEYXRlKGIuY3JlYXRlZEF0KVxyXG4gICAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgIFttZXNzYWdlLmNvbnZlcnNhdGlvbklkXTogdXBkYXRlZE1lc3NhZ2VzXHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgbGlzdGEgZGUgY29udmVyc2FzIChtb3ZlciBwYXJhIG8gdG9wbylcclxuICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIGNvbnZlcnNhIGV4aXN0ZVxyXG4gICAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb24gPSBwcmV2LmZpbmQoYyA9PiBjLmlkID09PSBtZXNzYWdlLmNvbnZlcnNhdGlvbklkKTtcclxuICAgICAgICAgICAgaWYgKCFjb252ZXJzYXRpb24pIHJldHVybiBwcmV2O1xyXG5cclxuICAgICAgICAgICAgLy8gU2UgYSBjb252ZXJzYSBqw6EgdGl2ZXIgdW1hIG1lbnNhZ2VtIHRlbXBvcsOhcmlhIGNvbSBvIG1lc21vIHRlbXBJZCwgbsOjbyBtb3ZlciBwYXJhIG8gdG9wb1xyXG4gICAgICAgICAgICBpZiAodGVtcElkICYmIGNvbnZlcnNhdGlvbi5sYXN0TWVzc2FnZSAmJiBjb252ZXJzYXRpb24ubGFzdE1lc3NhZ2UuaWQgPT09IHRlbXBJZCkge1xyXG4gICAgICAgICAgICAgIC8vIEFwZW5hcyBhdHVhbGl6YXIgYSDDumx0aW1hIG1lbnNhZ2VtIHNlbSByZW9yZGVuYXJcclxuICAgICAgICAgICAgICByZXR1cm4gcHJldi5tYXAoYyA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAoYy5pZCA9PT0gbWVzc2FnZS5jb252ZXJzYXRpb25JZCkge1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm4geyAuLi5jLCBsYXN0TWVzc2FnZTogbWVzc2FnZSB9O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGM7XHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIFNlIGZvciB1bWEgbWVuc2FnZW0gZG8gdXN1w6FyaW8gYXR1YWwgZSBuw6NvIHRpdmVyIHRlbXBJZCwgdmVyaWZpY2FyIHNlIGjDoSB1bWEgbWVuc2FnZW0gdGVtcG9yw6FyaWEgcmVjZW50ZVxyXG4gICAgICAgICAgICBjb25zdCBpc0N1cnJlbnRVc2VyTWVzc2FnZSA9IG1lc3NhZ2Uuc2VuZGVySWQgPT09IHVzZXJJZCB8fCBtZXNzYWdlLnNlbmRlckNsaWVudElkID09PSB1c2VySWQ7XHJcbiAgICAgICAgICAgIGlmIChpc0N1cnJlbnRVc2VyTWVzc2FnZSAmJiAhdGVtcElkICYmIGNvbnZlcnNhdGlvbi5sYXN0TWVzc2FnZSAmJiBjb252ZXJzYXRpb24ubGFzdE1lc3NhZ2UuaXNUZW1wKSB7XHJcbiAgICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgw7psdGltYSBtZW5zYWdlbSDDqSB0ZW1wb3LDoXJpYSBlIHRlbSBvIG1lc21vIGNvbnRlw7pkb1xyXG4gICAgICAgICAgICAgIGlmIChjb252ZXJzYXRpb24ubGFzdE1lc3NhZ2UuY29udGVudCA9PT0gbWVzc2FnZS5jb250ZW50ICYmXHJcbiAgICAgICAgICAgICAgICAgIChjb252ZXJzYXRpb24ubGFzdE1lc3NhZ2Uuc2VuZGVySWQgPT09IHVzZXJJZCB8fCBjb252ZXJzYXRpb24ubGFzdE1lc3NhZ2Uuc2VuZGVyQ2xpZW50SWQgPT09IHVzZXJJZCkpIHtcclxuICAgICAgICAgICAgICAgIC8vIEFwZW5hcyBhdHVhbGl6YXIgYSDDumx0aW1hIG1lbnNhZ2VtIHNlbSByZW9yZGVuYXJcclxuICAgICAgICAgICAgICAgIHJldHVybiBwcmV2Lm1hcChjID0+IHtcclxuICAgICAgICAgICAgICAgICAgaWYgKGMuaWQgPT09IG1lc3NhZ2UuY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4geyAuLi5jLCBsYXN0TWVzc2FnZTogbWVzc2FnZSB9O1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiBjO1xyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBDYXNvIGNvbnRyw6FyaW8sIG1vdmVyIHBhcmEgbyB0b3BvXHJcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRDb252ZXJzYXRpb25zID0gcHJldi5maWx0ZXIoYyA9PiBjLmlkICE9PSBtZXNzYWdlLmNvbnZlcnNhdGlvbklkKTtcclxuICAgICAgICAgICAgcmV0dXJuIFtcclxuICAgICAgICAgICAgICB7IC4uLmNvbnZlcnNhdGlvbiwgbGFzdE1lc3NhZ2U6IG1lc3NhZ2UgfSxcclxuICAgICAgICAgICAgICAuLi51cGRhdGVkQ29udmVyc2F0aW9uc1xyXG4gICAgICAgICAgICBdO1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgLy8gTWFyY2FyIGNvbnZlcnNhIGNvbW8gdGVuZG8gbWVuc2FnZW5zIG7Do28gbGlkYXMgc2UgYSBtZW5zYWdlbSBuw6NvIGZvciBkbyB1c3XDoXJpbyBhdHVhbFxyXG4gICAgICAgICAgY29uc3QgaXNGcm9tQ3VycmVudFVzZXIgPSBtZXNzYWdlLnNlbmRlcklkID09PSB1c2VySWQgfHwgbWVzc2FnZS5zZW5kZXJDbGllbnRJZCA9PT0gdXNlcklkO1xyXG4gICAgICAgICAgaWYgKCFpc0Zyb21DdXJyZW50VXNlcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnW1dlYlNvY2tldF0gTm92YSBtZW5zYWdlbSBkZSBvdXRybyB1c3XDoXJpbywgbWFyY2FuZG8gY29udmVyc2EgY29tbyBuw6NvIGxpZGEnKTtcclxuXHJcbiAgICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIGNvbnZlcnNhIGrDoSBlc3RhdmEgbWFyY2FkYSBjb21vIG7Do28gbGlkYVxyXG4gICAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb24gPSBjb252ZXJzYXRpb25zLmZpbmQoYyA9PiBjLmlkID09PSBtZXNzYWdlLmNvbnZlcnNhdGlvbklkKTtcclxuICAgICAgICAgICAgY29uc3Qgd2FzQWxyZWFkeVVucmVhZCA9IGNvbnZlcnNhdGlvbj8uaGFzVW5yZWFkIHx8IGZhbHNlO1xyXG5cclxuICAgICAgICAgICAgLy8gTWFyY2FyIGNvbnZlcnNhIGNvbW8gdGVuZG8gbWVuc2FnZW5zIG7Do28gbGlkYXNcclxuICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgICAgICAgIGlmIChjb252LmlkID09PSBtZXNzYWdlLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgW1dlYlNvY2tldF0gQ29udmVyc2EgJHtjb252LmlkfSBtYXJjYWRhIGNvbW8gbsOjbyBsaWRhYCk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4geyAuLi5jb252LCBoYXNVbnJlYWQ6IHRydWUgfTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgcmV0dXJuIGNvbnY7XHJcbiAgICAgICAgICAgIH0pKTtcclxuXHJcbiAgICAgICAgICAgIC8vIEluY3JlbWVudGFyIGNvbnRhZG9yIHRvdGFsIGFwZW5hcyBzZSBhIGNvbnZlcnNhIG7Do28gZXN0YXZhIG1hcmNhZGEgY29tbyBuw6NvIGxpZGFcclxuICAgICAgICAgICAgaWYgKCF3YXNBbHJlYWR5VW5yZWFkKSB7XHJcbiAgICAgICAgICAgICAgc2V0VW5yZWFkQ291bnQocHJldiA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdDb3VudCA9IHByZXYgKyAxO1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1tXZWJTb2NrZXRdIENvbnRhZG9yIGRlIGNvbnZlcnNhcyBuw6NvIGxpZGFzIGF0dWFsaXphZG8gZGUnLCBwcmV2LCAncGFyYScsIG5ld0NvdW50KTtcclxuICAgICAgICAgICAgICAgIHJldHVybiBuZXdDb3VudDtcclxuICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1tXZWJTb2NrZXRdIE1lbnNhZ2VtIGRvIHByw7NwcmlvIHVzdcOhcmlvLCBuw6NvIGFsdGVyYW5kbyBzdGF0dXMgZGUgbsOjbyBsaWRhJyk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLy8gRGlzcGFyYXIgZXZlbnRvIHBlcnNvbmFsaXphZG8gcGFyYSBub3RpZmljYXIgY29tcG9uZW50ZXMgc29icmUgYSBub3ZhIG1lbnNhZ2VtXHJcbiAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2NoYXQ6d2Vic29ja2V0OnVwZGF0ZScsIHtcclxuICAgICAgICAgICAgZGV0YWlsOiB7IHR5cGU6ICdtZXNzYWdlJywgY29udmVyc2F0aW9uSWQ6IG1lc3NhZ2UuY29udmVyc2F0aW9uSWQsIHRpbWVzdGFtcDogRGF0ZS5ub3coKSB9XHJcbiAgICAgICAgICB9KSk7XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vIEV2ZW50byBwYXJhIGF0dWFsaXphw6fDo28gZGUgY29udGFnZW0gZGUgbWVuc2FnZW5zIG7Do28gbGlkYXNcclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vbigndW5yZWFkOnVwZGF0ZScsIChkYXRhKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnW1dlYlNvY2tldF0gQXR1YWxpemHDp8OjbyBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhcyByZWNlYmlkYTonLCBkYXRhKTtcclxuXHJcbiAgICAgICAgICAvLyBVc2FyIG8gbm92byBjYW1wbyB0b3RhbFVucmVhZENvbnZlcnNhdGlvbnMgb3UgZmFsbGJhY2sgcGFyYSB0b3RhbFVucmVhZFxyXG4gICAgICAgICAgY29uc3QgdG90YWxVbnJlYWRDb252ZXJzYXRpb25zID0gZGF0YS50b3RhbFVucmVhZENvbnZlcnNhdGlvbnMgfHwgZGF0YS50b3RhbFVucmVhZCB8fCAwO1xyXG5cclxuICAgICAgICAgIGlmICh0eXBlb2YgdG90YWxVbnJlYWRDb252ZXJzYXRpb25zID09PSAnbnVtYmVyJykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgW1dlYlNvY2tldF0gQXR1YWxpemFuZG8gY29udGFkb3IgdG90YWwgZGUgY29udmVyc2FzIG7Do28gbGlkYXMgcGFyYTogJHt0b3RhbFVucmVhZENvbnZlcnNhdGlvbnN9YCk7XHJcbiAgICAgICAgICAgIHNldFVucmVhZENvdW50KHRvdGFsVW5yZWFkQ29udmVyc2F0aW9ucyk7XHJcblxyXG4gICAgICAgICAgICAvLyBBdHVhbGl6YXIgYXMgY29udmVyc2FzIGNvbSBzdGF0dXMgZGUgbsOjbyBsaWRhc1xyXG4gICAgICAgICAgICBpZiAoZGF0YS5jb252ZXJzYXRpb25zICYmIEFycmF5LmlzQXJyYXkoZGF0YS5jb252ZXJzYXRpb25zKSkge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbV2ViU29ja2V0XSBBdHVhbGl6YW5kbyAke2RhdGEuY29udmVyc2F0aW9ucy5sZW5ndGh9IGNvbnZlcnNhcyBjb20gc3RhdHVzIGRlIG7Do28gbGlkYXNgKTtcclxuICAgICAgICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4ge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAvLyBQcm9jdXJhciBlc3RhIGNvbnZlcnNhIG5vcyBkYWRvcyBkZSBuw6NvIGxpZGFzXHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHVucmVhZEluZm8gPSBkYXRhLmNvbnZlcnNhdGlvbnMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICBpdGVtID0+IGl0ZW0uY29udmVyc2F0aW9uSWQgPT09IGNvbnYuaWRcclxuICAgICAgICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgICAgICAgIGlmICh1bnJlYWRJbmZvKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaGFzVW5yZWFkID0gdW5yZWFkSW5mby5oYXNVbnJlYWQgfHwgKHVucmVhZEluZm8udW5yZWFkQ291bnQgJiYgdW5yZWFkSW5mby51bnJlYWRDb3VudCA+IDApO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbV2ViU29ja2V0XSBDb252ZXJzYSAke2NvbnYuaWR9IHRlbSBtZW5zYWdlbnMgbsOjbyBsaWRhczogJHtoYXNVbnJlYWR9YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgaGFzVW5yZWFkIH07XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgLy8gUmVzZXRhciBzdGF0dXMgc2UgbsOjbyBlc3RpdmVyIG5hIGxpc3RhIGRlIG7Do28gbGlkYXNcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgaGFzVW5yZWFkOiBmYWxzZSB9O1xyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGVyc29uYWxpemFkbyBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIGF0dWFsaXphw6fDo28gZGUgbsOjbyBsaWRhc1xyXG4gICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgIGRldGFpbDogeyB0eXBlOiAndW5yZWFkJywgdGltZXN0YW1wOiBEYXRlLm5vdygpIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gRXZlbnRvIHBhcmEgYXR1YWxpemHDp8OjbyBkZSBsaXN0YSBkZSBjb252ZXJzYXNcclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vbignY29udmVyc2F0aW9uczp1cGRhdGUnLCAoZGF0YSkgPT4ge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0F0dWFsaXphw6fDo28gZGUgbGlzdGEgZGUgY29udmVyc2FzIHJlY2ViaWRhIHZpYSBXZWJTb2NrZXQ6JywgZGF0YSk7XHJcbiAgICAgICAgICAvLyBWZXJpZmljYXIgc2Ugb3MgZGFkb3MgZXN0w6NvIG5vIGZvcm1hdG8gZXNwZXJhZG9cclxuICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgIC8vIFBvZGUgdmlyIGNvbW8gYXJyYXkgZGlyZXRvIG91IGNvbW8gb2JqZXRvIGNvbSBwcm9wcmllZGFkZSBjb252ZXJzYXRpb25zXHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbnNBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogZGF0YS5jb252ZXJzYXRpb25zIHx8IFtdO1xyXG5cclxuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoY29udmVyc2F0aW9uc0FycmF5KSAmJiBjb252ZXJzYXRpb25zQXJyYXkubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBdHVhbGl6YW5kbyAke2NvbnZlcnNhdGlvbnNBcnJheS5sZW5ndGh9IGNvbnZlcnNhcyB2aWEgV2ViU29ja2V0YCk7XHJcbiAgICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhjb252ZXJzYXRpb25zQXJyYXkpO1xyXG5cclxuICAgICAgICAgICAgICAvLyBBdHVhbGl6YXIgYSBmbGFnIHBhcmEgaW5kaWNhciBxdWUgb3MgZGFkb3MgZm9yYW0gY2FycmVnYWRvc1xyXG4gICAgICAgICAgICAgIGluaXRpYWxEYXRhTG9hZGVkUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG5cclxuICAgICAgICAgICAgICAvLyBBdHVhbGl6YXIgbyBjYWNoZVxyXG4gICAgICAgICAgICAgIHJlcXVlc3RDYWNoZS5jb252ZXJzYXRpb25zLmRhdGEgPSBjb252ZXJzYXRpb25zQXJyYXk7XHJcbiAgICAgICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMudGltZXN0YW1wID0gRGF0ZS5ub3coKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gRGlzcGFyYXIgZXZlbnRvIHBlcnNvbmFsaXphZG8gcGFyYSBub3RpZmljYXIgY29tcG9uZW50ZXMgc29icmUgYSBhdHVhbGl6YcOnw6NvXHJcbiAgICAgICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgICAgICBkZXRhaWw6IHsgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfVxyXG4gICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGb3JtYXRvIGludsOhbGlkbyBvdSBhcnJheSB2YXppbyBkZSBjb252ZXJzYXMgcmVjZWJpZG8gdmlhIFdlYlNvY2tldDonLCBkYXRhKTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gU2UgcmVjZWJlbW9zIHVtIGFycmF5IHZhemlvLCBmb3LDp2FyIGNhcnJlZ2FtZW50byBkYXMgY29udmVyc2FzXHJcbiAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoY29udmVyc2F0aW9uc0FycmF5KSAmJiBjb252ZXJzYXRpb25zQXJyYXkubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQXJyYXkgdmF6aW8gcmVjZWJpZG8sIGZvcsOnYW5kbyBjYXJyZWdhbWVudG8gZGUgY29udmVyc2FzLi4uJyk7XHJcbiAgICAgICAgICAgICAgICBsb2FkQ29udmVyc2F0aW9ucyh0cnVlKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gRXZlbnRvIHBhcmEgbm90aWZpY2FyIHF1ZSB1bWEgbm92YSBjb252ZXJzYSBmb2kgY3JpYWRhXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2NvbnZlcnNhdGlvbjpjcmVhdGVkJywgKGRhdGEpID0+IHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdOb3ZhIGNvbnZlcnNhIGNyaWFkYSByZWNlYmlkYSB2aWEgV2ViU29ja2V0OicsIGRhdGEpO1xyXG4gICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5pZCkge1xyXG4gICAgICAgICAgICAvLyBBZGljaW9uYXIgYSBub3ZhIGNvbnZlcnNhIGFvIGluw61jaW8gZGEgbGlzdGFcclxuICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgYSBjb252ZXJzYSBqw6EgZXhpc3RlXHJcbiAgICAgICAgICAgICAgaWYgKHByZXYuc29tZShjID0+IGMuaWQgPT09IGRhdGEuaWQpKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gcHJldjtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgcmV0dXJuIFtkYXRhLCAuLi5wcmV2XTtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vIEV2ZW50byBwYXJhIHF1YW5kbyB1bSBwYXJ0aWNpcGFudGUgw6kgcmVtb3ZpZG9cclxuICAgICAgICBzb2NrZXRJbnN0YW5jZS5vbigncGFydGljaXBhbnQ6cmVtb3ZlZCcsIChkYXRhKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnUGFydGljaXBhbnRlIHJlbW92aWRvIHJlY2ViaWRvIHZpYSBXZWJTb2NrZXQ6JywgZGF0YSk7XHJcbiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvbnZlcnNhdGlvbklkICYmIGRhdGEucGFydGljaXBhbnRJZCkge1xyXG4gICAgICAgICAgICAvLyBTZSBvIHVzdcOhcmlvIGF0dWFsIGZvaSByZW1vdmlkbywgcmVtb3ZlciBhIGNvbnZlcnNhIGRhIGxpc3RhXHJcbiAgICAgICAgICAgIGlmIChkYXRhLnBhcnRpY2lwYW50SWQgPT09IHVzZXI/LmlkKSB7XHJcbiAgICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHByZXYuZmlsdGVyKGMgPT4gYy5pZCAhPT0gZGF0YS5jb252ZXJzYXRpb25JZCkpO1xyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIC8vIFNlIGVyYSBhIGNvbnZlcnNhIGF0aXZhLCBsaW1wYXJcclxuICAgICAgICAgICAgICBpZiAoYWN0aXZlQ29udmVyc2F0aW9uID09PSBkYXRhLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24obnVsbCk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIC8vIExpbXBhciBtZW5zYWdlbnMgZGEgY29udmVyc2FcclxuICAgICAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7IC4uLnByZXYgfTtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSB1cGRhdGVkW2RhdGEuY29udmVyc2F0aW9uSWRdO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHVwZGF0ZWQ7XHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgLy8gQXR1YWxpemFyIGEgbGlzdGEgZGUgcGFydGljaXBhbnRlcyBkYSBjb252ZXJzYVxyXG4gICAgICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiBwcmV2Lm1hcChjb252ID0+IHtcclxuICAgICAgICAgICAgICAgIGlmIChjb252LmlkID09PSBkYXRhLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uY29udixcclxuICAgICAgICAgICAgICAgICAgICBwYXJ0aWNpcGFudHM6IGNvbnYucGFydGljaXBhbnRzLmZpbHRlcihwID0+IFxyXG4gICAgICAgICAgICAgICAgICAgICAgKHAudXNlcklkICE9PSBkYXRhLnBhcnRpY2lwYW50SWQgJiYgcC5jbGllbnRJZCAhPT0gZGF0YS5wYXJ0aWNpcGFudElkKVxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHJldHVybiBjb252O1xyXG4gICAgICAgICAgICAgIH0pKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBFdmVudG8gcGFyYSBxdWFuZG8gbyB1c3XDoXJpbyBzYWkgZGUgdW1hIGNvbnZlcnNhXHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2NvbnZlcnNhdGlvbjpsZWZ0JywgKGRhdGEpID0+IHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVc3XDoXJpbyBzYWl1IGRhIGNvbnZlcnNhIHZpYSBXZWJTb2NrZXQ6JywgZGF0YSk7XHJcbiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvbnZlcnNhdGlvbklkICYmIGRhdGEudXNlcklkID09PSB1c2VyPy5pZCkge1xyXG4gICAgICAgICAgICAvLyBSZW1vdmVyIGEgY29udmVyc2EgZGEgbGlzdGFcclxuICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHByZXYuZmlsdGVyKGMgPT4gYy5pZCAhPT0gZGF0YS5jb252ZXJzYXRpb25JZCkpO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgLy8gU2UgZXJhIGEgY29udmVyc2EgYXRpdmEsIGxpbXBhclxyXG4gICAgICAgICAgICBpZiAoYWN0aXZlQ29udmVyc2F0aW9uID09PSBkYXRhLmNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgICAgc2V0QWN0aXZlQ29udmVyc2F0aW9uKG51bGwpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAvLyBMaW1wYXIgbWVuc2FnZW5zIGRhIGNvbnZlcnNhXHJcbiAgICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7IC4uLnByZXYgfTtcclxuICAgICAgICAgICAgICBkZWxldGUgdXBkYXRlZFtkYXRhLmNvbnZlcnNhdGlvbklkXTtcclxuICAgICAgICAgICAgICByZXR1cm4gdXBkYXRlZDtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGFyYSBhdHVhbGl6YXIgYSBpbnRlcmZhY2VcclxuICAgICAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdjaGF0OndlYnNvY2tldDp1cGRhdGUnLCB7XHJcbiAgICAgICAgICAgICAgZGV0YWlsOiB7IFxyXG4gICAgICAgICAgICAgICAgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLCBcclxuICAgICAgICAgICAgICAgIGFjdGlvbjogJ2xlZnQnLFxyXG4gICAgICAgICAgICAgICAgY29udmVyc2F0aW9uSWQ6IGRhdGEuY29udmVyc2F0aW9uSWQsXHJcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCkgXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIHNldFNvY2tldChzb2NrZXRJbnN0YW5jZSk7XHJcblxyXG4gICAgICAgIHJldHVybiBzb2NrZXRJbnN0YW5jZTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGluaWNpYWxpemFyIFdlYlNvY2tldDonLCBlcnJvcik7XHJcbiAgICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpO1xyXG4gICAgICAgIHNvY2tldEluaXRpYWxpemVkUmVmLmN1cnJlbnQgPSBmYWxzZTsgLy8gUGVybWl0aXIgbm92YSB0ZW50YXRpdmEgbm8gZnV0dXJvXHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgLy8gVXNhciB1bWEgdmFyacOhdmVsIHBhcmEgY29udHJvbGFyIHNlIGrDoSBlc3RhbW9zIHRlbnRhbmRvIGluaWNpYWxpemFyXHJcbiAgICBsZXQgaW5pdGlhbGl6YXRpb25JblByb2dyZXNzID0gZmFsc2U7XHJcblxyXG4gICAgLy8gRnVuw6fDo28gcGFyYSBpbmljaWFsaXphciBjb20gc2VndXJhbsOnYVxyXG4gICAgY29uc3Qgc2FmZUluaXRpYWxpemUgPSAoKSA9PiB7XHJcbiAgICAgIGlmIChpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnSsOhIGV4aXN0ZSB1bWEgaW5pY2lhbGl6YcOnw6NvIGVtIGFuZGFtZW50bywgaWdub3JhbmRvJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MgPSB0cnVlO1xyXG5cclxuICAgICAgLy8gVXNhciB1bSB0aW1lb3V0IHBhcmEgZ2FyYW50aXIgcXVlIG7Do28gdGVudGFtb3MgaW5pY2lhbGl6YXIgbXVpdG8gZnJlcXVlbnRlbWVudGVcclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgaW5pdGlhbGl6ZVdlYlNvY2tldCgpXHJcbiAgICAgICAgICAudGhlbihyZXN1bHQgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnSW5pY2lhbGl6YcOnw6NvIGRvIFdlYlNvY2tldCBjb25jbHXDrWRhOicsIHJlc3VsdCA/ICdzdWNlc3NvJyA6ICdmYWxoYScpO1xyXG4gICAgICAgICAgfSlcclxuICAgICAgICAgIC5jYXRjaChlcnJvciA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gaW5pY2lhbGl6YXIgV2ViU29ja2V0OicsIGVycm9yKTtcclxuICAgICAgICAgICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IGZhbHNlOyAvLyBQZXJtaXRpciBub3ZhIHRlbnRhdGl2YSBubyBmdXR1cm9cclxuICAgICAgICAgIH0pXHJcbiAgICAgICAgICAuZmluYWxseSgoKSA9PiB7XHJcbiAgICAgICAgICAgIGluaXRpYWxpemF0aW9uSW5Qcm9ncmVzcyA9IGZhbHNlO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgIH0sIDIwMDApOyAvLyBFc3BlcmFyIDIgc2VndW5kb3MgYW50ZXMgZGUgaW5pY2lhbGl6YXJcclxuICAgIH07XHJcblxyXG4gICAgLy8gQ2hhbWFyIGEgZnVuw6fDo28gZGUgaW5pY2lhbGl6YcOnw6NvXHJcbiAgICBzYWZlSW5pdGlhbGl6ZSgpO1xyXG5cclxuICAgIC8vIENsZWFudXAgZnVuY3Rpb24gLSBzw7MgZGVzY29uZWN0YXIgcXVhbmRvIG8gY29tcG9uZW50ZSBmb3IgZGVzbW9udGFkbyBjb21wbGV0YW1lbnRlXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAvLyBWZXJpZmljYXIgc2UgZXN0YW1vcyByZWFsbWVudGUgZGVzbW9udGFuZG8gbyBjb21wb25lbnRlICh1c3XDoXJpbyBmZXogbG9nb3V0KVxyXG4gICAgICBpZiAoIXVzZXIpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnVXN1w6FyaW8gZmV6IGxvZ291dCwgZGVzY29uZWN0YW5kbyBzb2NrZXQnKTtcclxuICAgICAgICBpZiAoc29ja2V0KSB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBzb2NrZXQuZGlzY29ubmVjdCgpO1xyXG4gICAgICAgICAgICBzb2NrZXRJbml0aWFsaXplZFJlZi5jdXJyZW50ID0gZmFsc2U7IC8vIFBlcm1pdGlyIG5vdmEgdGVudGF0aXZhIGFww7NzIGNsZWFudXBcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gZGVzY29uZWN0YXIgc29ja2V0OicsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NvbXBvbmVudGUgc2VuZG8gcmVtb250YWRvLCBtYW50ZW5kbyBzb2NrZXQgY29uZWN0YWRvJyk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgLy8gUmVtb3ZlbmRvIHNvY2tldCBlIGlzQ29ubmVjdGVkIGRhcyBkZXBlbmTDqm5jaWFzIHBhcmEgZXZpdGFyIGxvb3BzXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBDYXJyZWdhciBjb252ZXJzYXMgZG8gdXN1w6FyaW8gY29tIGNhY2hlXHJcbiAgY29uc3QgbG9hZENvbnZlcnNhdGlvbnMgPSB1c2VDYWxsYmFjayhhc3luYyAoZm9yY2VSZWZyZXNoID0gdHJ1ZSkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ2xvYWRDb252ZXJzYXRpb25zIGNoYW1hZG8gY29tIGZvcmNlUmVmcmVzaCA9JywgZm9yY2VSZWZyZXNoKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2Fkb1xyXG4gICAgICBpZiAoIXVzZXIpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdVc3XDoXJpbyBuw6NvIGxvZ2FkbyBhbyBjYXJyZWdhciBjb252ZXJzYXMnKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENsaWVudGVzIGFnb3JhIHBvZGVtIGNhcnJlZ2FyIGNvbnZlcnNhc1xyXG4gICAgICBjb25zb2xlLmxvZygnQ2FycmVnYW5kbyBjb252ZXJzYXMgcGFyYTonLCB1c2VyLnJvbGUgfHwgJ1VTRVInKTtcclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBqw6EgZXN0w6EgY2FycmVnYW5kb1xyXG4gICAgICBpZiAoaXNMb2FkaW5nIHx8IGlzTG9hZGluZ1JlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0rDoSBlc3TDoSBjYXJyZWdhbmRvIGNvbnZlcnNhcywgcmV0b3JuYW5kbyBlc3RhZG8gYXR1YWwnKTtcclxuICAgICAgICByZXR1cm4gY29udmVyc2F0aW9ucztcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVmVyaWZpY2FyIHNlIGrDoSB0ZW1vcyBjb252ZXJzYXMgY2FycmVnYWRhcyBlIG7Do28gw6kgdW1hIGF0dWFsaXphw6fDo28gZm9yw6dhZGFcclxuICAgICAgaWYgKCFmb3JjZVJlZnJlc2ggJiYgY29udmVyc2F0aW9ucy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0rDoSB0ZW1vcyBjb252ZXJzYXMgY2FycmVnYWRhcyBlIG7Do28gw6kgdW1hIGF0dWFsaXphw6fDo28gZm9yw6dhZGEsIHJldG9ybmFuZG8gZXN0YWRvIGF0dWFsJyk7XHJcbiAgICAgICAgcmV0dXJuIGNvbnZlcnNhdGlvbnM7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIGNhcnJlZ2FuZG8gcGFyYSBldml0YXIgY2hhbWFkYXMgc2ltdWx0w6JuZWFzXHJcbiAgICAgIGlzTG9hZGluZ1JlZi5jdXJyZW50ID0gdHJ1ZTtcclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBjYWNoZSBhcGVuYXMgc2UgbsOjbyBmb3IgcmVmcmVzaCBmb3LDp2Fkb1xyXG4gICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xyXG4gICAgICBpZiAoIWZvcmNlUmVmcmVzaCAmJlxyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YSAmJlxyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YS5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICBub3cgLSByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy50aW1lc3RhbXAgPCBDQUNIRV9FWFBJUkFUSU9OKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VzYW5kbyBjYWNoZSBwYXJhIGNvbnZlcnNhcycpO1xyXG4gICAgICAgIHJldHVybiByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy5kYXRhO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmxvZygnQnVzY2FuZG8gY29udmVyc2FzIGF0dWFsaXphZGFzIGRhIEFQSScpO1xyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiBhbyBjYXJyZWdhciBjb252ZXJzYXM6JywgY3VycmVudFRva2VuID8gJ0Rpc3BvbsOtdmVsJyA6ICdOw6NvIGRpc3BvbsOtdmVsJyk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbCBhbyBjYXJyZWdhciBjb252ZXJzYXMnKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0J1c2NhbmRvIGNvbnZlcnNhcyBkYSBBUEkuLi4nKTtcclxuICAgICAgICBjb25zb2xlLmxvZygnRmF6ZW5kbyByZXF1aXNpw6fDo28gcGFyYTonLCBgJHtBUElfVVJMfS9jaGF0L2NvbnZlcnNhdGlvbnM/aW5jbHVkZU1lc3NhZ2VzPXRydWVgKTtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvY29udmVyc2F0aW9ucz9pbmNsdWRlTWVzc2FnZXM9dHJ1ZWAsIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBjb25zb2xlLmxvZygnU3RhdHVzIGRhIHJlc3Bvc3RhOicsIHJlc3BvbnNlLnN0YXR1cywgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XHJcblxyXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdUb2tlbiBleHBpcmFkbyBvdSBpbnbDoWxpZG8gYW8gY2FycmVnYXIgY29udmVyc2FzJyk7XHJcbiAgICAgICAgICAgIGhhbmRsZUF1dGhFcnJvcigpO1xyXG4gICAgICAgICAgICByZXR1cm4gW107XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gY2FycmVnYXIgY29udmVyc2FzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1Jlc3Bvc3RhIGRhIEFQSSBkZSBjb252ZXJzYXM6JywgZGF0YSk7XHJcblxyXG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcclxuICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBow6EgZGFkb3MgdsOhbGlkb3NcclxuICAgICAgICAgIC8vIEEgQVBJIHJldG9ybmEgeyBzdWNjZXNzOiB0cnVlLCBkYXRhOiB7IGNvbnZlcnNhdGlvbnM6IFsuLi5dLCB0b3RhbCwgbGltaXQsIG9mZnNldCB9IH1cclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdFc3RydXR1cmEgY29tcGxldGEgZGEgcmVzcG9zdGEgZGEgQVBJOicsIEpTT04uc3RyaW5naWZ5KGRhdGEpKTtcclxuXHJcbiAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25zQXJyYXkgPSBkYXRhLmRhdGE/LmNvbnZlcnNhdGlvbnMgfHwgW107XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQXJyYXkgZGUgY29udmVyc2FzIGV4dHJhw61kbzonLCBjb252ZXJzYXRpb25zQXJyYXkpO1xyXG5cclxuICAgICAgICAgIGlmICghQXJyYXkuaXNBcnJheShjb252ZXJzYXRpb25zQXJyYXkpKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3Bvc3RhIGRhIEFQSSBuw6NvIGNvbnTDqW0gdW0gYXJyYXkgZGUgY29udmVyc2FzOicsIGRhdGEpO1xyXG4gICAgICAgICAgICByZXR1cm4gW107XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgY29uc29sZS5sb2coYFJlY2ViaWRhcyAke2NvbnZlcnNhdGlvbnNBcnJheS5sZW5ndGh9IGNvbnZlcnNhcyBkYSBBUElgKTtcclxuXHJcbiAgICAgICAgICAvLyBQcm9jZXNzYXIgYXMgw7psdGltYXMgbWVuc2FnZW5zIGRhcyBjb252ZXJzYXNcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdQcm9jZXNzYW5kbyDDumx0aW1hcyBtZW5zYWdlbnMgZGFzIGNvbnZlcnNhcy4uLicpO1xyXG4gICAgICAgICAgY29uc3QgcHJvY2Vzc2VkQ29udmVyc2F0aW9ucyA9IGNvbnZlcnNhdGlvbnNBcnJheS5tYXAoY29udmVyc2F0aW9uID0+IHtcclxuICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgY29udmVyc2EgdGVtIG1lbnNhZ2Vuc1xyXG4gICAgICAgICAgICBpZiAoY29udmVyc2F0aW9uLm1lc3NhZ2VzICYmIGNvbnZlcnNhdGlvbi5tZXNzYWdlcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYENvbnZlcnNhICR7Y29udmVyc2F0aW9uLmlkfSB0ZW0gJHtjb252ZXJzYXRpb24ubWVzc2FnZXMubGVuZ3RofSBtZW5zYWdlbnNgKTtcclxuICAgICAgICAgICAgICAvLyBFeHRyYWlyIGEgw7psdGltYSBtZW5zYWdlbVxyXG4gICAgICAgICAgICAgIGNvbnN0IGxhc3RNZXNzYWdlID0gY29udmVyc2F0aW9uLm1lc3NhZ2VzWzBdOyAvLyBBIHByaW1laXJhIG1lbnNhZ2VtIMOpIGEgbWFpcyByZWNlbnRlIChvcmRlbmFkYSBwb3IgY3JlYXRlZElkIGRlc2MpXHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1VsdGltYSBtZW5zYWdlbTonLCBsYXN0TWVzc2FnZSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIElNUE9SVEFOVEU6IFRhbWLDqW0gc2FsdmFyIHRvZGFzIGFzIG1lbnNhZ2VucyBkZXN0YSBjb252ZXJzYSBubyBlc3RhZG9cclxuICAgICAgICAgICAgICBjb25zdCBzb3J0ZWRNZXNzYWdlcyA9IFsuLi5jb252ZXJzYXRpb24ubWVzc2FnZXNdLnNvcnQoKGEsIGIpID0+XHJcbiAgICAgICAgICAgICAgICBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkgLSBuZXcgRGF0ZShiLmNyZWF0ZWRBdClcclxuICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBERUJVRzogU2FsdmFuZG8gJHtzb3J0ZWRNZXNzYWdlcy5sZW5ndGh9IG1lbnNhZ2VucyBkYSBjb252ZXJzYSAke2NvbnZlcnNhdGlvbi5pZH1gLCBzb3J0ZWRNZXNzYWdlcyk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIEF0dWFsaXphciBlc3RhZG8gZGFzIG1lbnNhZ2Vuc1xyXG4gICAgICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICBbY29udmVyc2F0aW9uLmlkXTogc29ydGVkTWVzc2FnZXNcclxuICAgICAgICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIFJlbW92ZXIgbyBhcnJheSBkZSBtZW5zYWdlbnMgcGFyYSBldml0YXIgZHVwbGljYcOnw6NvXHJcbiAgICAgICAgICAgICAgY29uc3QgeyBtZXNzYWdlcywgLi4uY29udmVyc2F0aW9uV2l0aG91dE1lc3NhZ2VzIH0gPSBjb252ZXJzYXRpb247XHJcblxyXG4gICAgICAgICAgICAgIC8vIEFkaWNpb25hciBhIMO6bHRpbWEgbWVuc2FnZW0gY29tbyBwcm9wcmllZGFkZSBsYXN0TWVzc2FnZVxyXG4gICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAuLi5jb252ZXJzYXRpb25XaXRob3V0TWVzc2FnZXMsXHJcbiAgICAgICAgICAgICAgICBsYXN0TWVzc2FnZVxyXG4gICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYENvbnZlcnNhICR7Y29udmVyc2F0aW9uLmlkfSBuw6NvIHRlbSBtZW5zYWdlbnNgKTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnNhdGlvbjtcclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIC8vIExvZyBwYXJhIGRlYnVnIGRvcyBkYWRvcyBkYXMgY29udmVyc2FzXHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQ29udmVyc2FzIHByb2Nlc3NhZGFzOicsIHByb2Nlc3NlZENvbnZlcnNhdGlvbnMubWFwKGMgPT4gKHtcclxuICAgICAgICAgICAgaWQ6IGMuaWQsXHJcbiAgICAgICAgICAgIHBhcnRpY2lwYW50czogYy5wYXJ0aWNpcGFudHM/Lm1hcChwID0+ICh7XHJcbiAgICAgICAgICAgICAgdXNlcklkOiBwLnVzZXJJZCxcclxuICAgICAgICAgICAgICBjbGllbnRJZDogcC5jbGllbnRJZCxcclxuICAgICAgICAgICAgICB1c2VyOiBwLnVzZXIsXHJcbiAgICAgICAgICAgICAgY2xpZW50OiBwLmNsaWVudFxyXG4gICAgICAgICAgICB9KSlcclxuICAgICAgICAgIH0pKSk7XHJcblxyXG4gICAgICAgICAgLy8gQXR1YWxpemFyIGNhY2hlIGNvbSBhcyBjb252ZXJzYXMgcHJvY2Vzc2FkYXNcclxuICAgICAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XHJcbiAgICAgICAgICByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy5kYXRhID0gcHJvY2Vzc2VkQ29udmVyc2F0aW9ucztcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS5jb252ZXJzYXRpb25zLnRpbWVzdGFtcCA9IG5vdztcclxuXHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgZXN0YWRvIC0gZ2FyYW50aXIgcXVlIGVzdGFtb3MgYXR1YWxpemFuZG8gbyBlc3RhZG8gbWVzbW8gc2UgbyBhcnJheSBlc3RpdmVyIHZhemlvXHJcbiAgICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByb2Nlc3NlZENvbnZlcnNhdGlvbnMpO1xyXG5cclxuICAgICAgICAgIC8vIERpc3BhcmFyIGV2ZW50byBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIGF0dWFsaXphw6fDo29cclxuICAgICAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudCgnY2hhdDp3ZWJzb2NrZXQ6dXBkYXRlJywge1xyXG4gICAgICAgICAgICBkZXRhaWw6IHsgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfVxyXG4gICAgICAgICAgfSkpO1xyXG5cclxuICAgICAgICAgIHJldHVybiBjb252ZXJzYXRpb25zQXJyYXk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3Bvc3RhIGRhIEFQSSBuw6NvIGZvaSBiZW0tc3VjZWRpZGE6JywgZGF0YSk7XHJcbiAgICAgICAgICByZXR1cm4gW107XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgY29udmVyc2F0aW9uczonLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgaXNMb2FkaW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gbG9hZENvbnZlcnNhdGlvbnM6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gW107XHJcbiAgICB9XHJcbiAgLy8gUmVtb3ZlbmRvIGNvbnZlcnNhdGlvbnMgZGFzIGRlcGVuZMOqbmNpYXMgcGFyYSBldml0YXIgbG9vcHMgaW5maW5pdG9zXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvciwgaXNMb2FkaW5nXSk7XHJcblxyXG4gIC8vIENhcnJlZ2FyIG1lbnNhZ2VucyBkZSB1bWEgY29udmVyc2EgY29tIGNhY2hlIGFwcmltb3JhZG9cclxuICBjb25zdCBsb2FkTWVzc2FnZXMgPSB1c2VDYWxsYmFjayhhc3luYyAoY29udmVyc2F0aW9uSWQpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2Fkb1xyXG4gICAgICBpZiAoIXVzZXIpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdVc3XDoXJpbyBuw6NvIGxvZ2FkbyBhbyBjYXJyZWdhciBtZW5zYWdlbnMnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBjYWNoZSBkZSBtZW5zYWdlbnNcclxuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0gJiZcclxuICAgICAgICAgIG5vdyAtIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0udGltZXN0YW1wIDwgQ0FDSEVfRVhQSVJBVElPTikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2FuZG8gY2FjaGUgcGFyYSBtZW5zYWdlbnMgZGEgY29udmVyc2E6JywgY29udmVyc2F0aW9uSWQpO1xyXG4gICAgICAgIHJldHVybiByZXF1ZXN0Q2FjaGUubWVzc2FnZXNbY29udmVyc2F0aW9uSWRdLmRhdGE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBqw6EgdGVtIG1lbnNhZ2VucyBjb21wbGV0YXMgY2FycmVnYWRhcyBubyBjYWNoZVxyXG4gICAgICAvLyBOw6NvIHVzYXIgbyBlc3RhZG8gbWVzc2FnZXMgYXF1aSBwb2lzIHBvZGUgY29udGVyIGFwZW5hcyBhIMO6bHRpbWEgbWVuc2FnZW0gZG8gbG9hZENvbnZlcnNhdGlvbnNcclxuXHJcbiAgICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgICBpZiAoIWN1cnJlbnRUb2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIG7Do28gZGlzcG9uw612ZWwgYW8gY2FycmVnYXIgbWVuc2FnZW5zJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBFdml0YXIgbcO6bHRpcGxhcyByZXF1aXNpw6fDtWVzIHNpbXVsdMOibmVhcyBwYXJhIGEgbWVzbWEgY29udmVyc2FcclxuICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0/LmxvYWRpbmcpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnSsOhIGV4aXN0ZSB1bWEgcmVxdWlzacOnw6NvIGVtIGFuZGFtZW50byBwYXJhIGVzdGEgY29udmVyc2EnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIGNhcnJlZ2FuZG9cclxuICAgICAgcmVxdWVzdENhY2hlLm1lc3NhZ2VzW2NvbnZlcnNhdGlvbklkXSA9IHsgbG9hZGluZzogdHJ1ZSB9O1xyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQnVzY2FuZG8gbWVuc2FnZW5zIGRhIEFQSSBwYXJhIGNvbnZlcnNhOicsIGNvbnZlcnNhdGlvbklkKTtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvY29udmVyc2F0aW9ucy8ke2NvbnZlcnNhdGlvbklkfS9tZXNzYWdlc2AsIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIGV4cGlyYWRvIG91IGludsOhbGlkbyBhbyBjYXJyZWdhciBtZW5zYWdlbnMnKTtcclxuICAgICAgICAgICAgaGFuZGxlQXV0aEVycm9yKCk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRXJybyBhbyBjYXJyZWdhciBtZW5zYWdlbnM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgICAgLy8gRGVidWcgcGFyYSB2ZXJpZmljYXIgbWVuc2FnZW5zIGNhcnJlZ2FkYXNcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdERUJVRzogTWVuc2FnZW5zIGNhcnJlZ2FkYXMgcGFyYSBjb252ZXJzYScsIGNvbnZlcnNhdGlvbklkLCBkYXRhLmRhdGEpO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgY2FjaGVcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0gPSB7XHJcbiAgICAgICAgICAgIGRhdGE6IGRhdGEuZGF0YSxcclxuICAgICAgICAgICAgdGltZXN0YW1wOiBub3csXHJcbiAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlXHJcbiAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgIC8vIEF0dWFsaXphciBlc3RhZG8gZGFzIG1lbnNhZ2Vuc1xyXG4gICAgICAgICAgLy8gR2FyYW50aXIgcXVlIGFzIG1lbnNhZ2VucyBlc3RlamFtIG5hIG9yZGVtIGNvcnJldGEgKG1haXMgYW50aWdhcyBwcmltZWlybylcclxuICAgICAgICAgIC8vIHBhcmEgcXVlIGEgb3JkZW5hw6fDo28gbm8gY29tcG9uZW50ZSBmdW5jaW9uZSBjb3JyZXRhbWVudGVcclxuICAgICAgICAgIGNvbnN0IHNvcnRlZE1lc3NhZ2VzID0gWy4uLmRhdGEuZGF0YV0uc29ydCgoYSwgYikgPT5cclxuICAgICAgICAgICAgbmV3IERhdGUoYS5jcmVhdGVkQXQpIC0gbmV3IERhdGUoYi5jcmVhdGVkQXQpXHJcbiAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdERUJVRzogTWVuc2FnZW5zIG9yZGVuYWRhcycsIHNvcnRlZE1lc3NhZ2VzKTtcclxuXHJcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgIFtjb252ZXJzYXRpb25JZF06IHNvcnRlZE1lc3NhZ2VzXHJcbiAgICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgICAgLy8gQXR1YWxpemFyIGEgw7psdGltYSBtZW5zYWdlbSBkYSBjb252ZXJzYVxyXG4gICAgICAgICAgaWYgKGRhdGEuZGF0YSAmJiBkYXRhLmRhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjb25zdCBsYXN0TWVzc2FnZSA9IGRhdGEuZGF0YVtkYXRhLmRhdGEubGVuZ3RoIC0gMV07XHJcbiAgICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKGNvbnYuaWQgPT09IGNvbnZlcnNhdGlvbklkICYmICghY29udi5sYXN0TWVzc2FnZSB8fCBuZXcgRGF0ZShsYXN0TWVzc2FnZS5jcmVhdGVkQXQpID4gbmV3IERhdGUoY29udi5sYXN0TWVzc2FnZS5jcmVhdGVkQXQpKSkge1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm4geyAuLi5jb252LCBsYXN0TWVzc2FnZSB9O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGNvbnY7XHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHJldHVybiBkYXRhLmRhdGE7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbWVzc2FnZXM6JywgZXJyb3IpO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIC8vIFJlbW92ZXIgZmxhZyBkZSBjYXJyZWdhbWVudG8gZW0gY2FzbyBkZSBlcnJvXHJcbiAgICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0/LmxvYWRpbmcpIHtcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0ubG9hZGluZyA9IGZhbHNlO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gbG9hZE1lc3NhZ2VzOicsIGVycm9yKTtcclxuICAgIH1cclxuICAvLyBSZW1vdmVuZG8gbWVzc2FnZXMgZGFzIGRlcGVuZMOqbmNpYXMgcGFyYSBldml0YXIgbG9vcHMgaW5maW5pdG9zXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBFbnZpYXIgbWVuc2FnZW1cclxuICBjb25zdCBzZW5kTWVzc2FnZSA9IHVzZUNhbGxiYWNrKChjb252ZXJzYXRpb25JZCwgY29udGVudCwgY29udGVudFR5cGUgPSAnVEVYVCcsIG1ldGFkYXRhID0gbnVsbCkgPT4ge1xyXG4gICAgaWYgKCFzb2NrZXQgfHwgIWlzQ29ubmVjdGVkKSByZXR1cm47XHJcblxyXG4gICAgLy8gR2VyYXIgdW0gSUQgdGVtcG9yw6FyaW8gcGFyYSBhIG1lbnNhZ2VtXHJcbiAgICBjb25zdCB0ZW1wSWQgPSBgdGVtcC0ke0RhdGUubm93KCl9YDtcclxuXHJcbiAgICAvLyBDcmlhciB1bWEgbWVuc2FnZW0gdGVtcG9yw6FyaWEgcGFyYSBleGliaXIgaW1lZGlhdGFtZW50ZVxyXG4gICAgY29uc3QgdGVtcE1lc3NhZ2UgPSB7XHJcbiAgICAgIGlkOiB0ZW1wSWQsXHJcbiAgICAgIGNvbnZlcnNhdGlvbklkLFxyXG4gICAgICBjb250ZW50LFxyXG4gICAgICBjb250ZW50VHlwZSxcclxuICAgICAgbWV0YWRhdGEsXHJcbiAgICAgIHNlbmRlcklkOiB1c2VyPy5pc0NsaWVudCB8fCB1c2VyPy5yb2xlID09PSAnQ0xJRU5UJyA/IG51bGwgOiB1c2VyPy5pZCxcclxuICAgICAgc2VuZGVyQ2xpZW50SWQ6IHVzZXI/LmlzQ2xpZW50IHx8IHVzZXI/LnJvbGUgPT09ICdDTElFTlQnID8gdXNlcj8uaWQgOiBudWxsLFxyXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgc2VuZGVyOiB1c2VyPy5pc0NsaWVudCB8fCB1c2VyPy5yb2xlID09PSAnQ0xJRU5UJyA/IG51bGwgOiB7IGlkOiB1c2VyPy5pZCwgZnVsbE5hbWU6IHVzZXI/LmZ1bGxOYW1lIH0sXHJcbiAgICAgIHNlbmRlckNsaWVudDogdXNlcj8uaXNDbGllbnQgfHwgdXNlcj8ucm9sZSA9PT0gJ0NMSUVOVCcgPyB7IGlkOiB1c2VyPy5pZCwgZnVsbE5hbWU6IHVzZXI/LmZ1bGxOYW1lIHx8IHVzZXI/LmxvZ2luLCBsb2dpbjogdXNlcj8ubG9naW4gfSA6IG51bGwsXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIHRlbXBvcsOhcmlhIHBhcmEgZXZpdGFyIGR1cGxpY2HDp8Ojb1xyXG4gICAgICBpc1RlbXA6IHRydWVcclxuICAgIH07XHJcblxyXG4gICAgLy8gQXR1YWxpemFyIG1lbnNhZ2VucyBsb2NhbG1lbnRlIGFudGVzIGRlIGVudmlhclxyXG4gICAgc2V0TWVzc2FnZXMocHJldiA9PiAoe1xyXG4gICAgICAuLi5wcmV2LFxyXG4gICAgICBbY29udmVyc2F0aW9uSWRdOiBbLi4uKHByZXZbY29udmVyc2F0aW9uSWRdIHx8IFtdKSwgdGVtcE1lc3NhZ2VdXHJcbiAgICB9KSk7XHJcblxyXG4gICAgLy8gQXR1YWxpemFyIGEgY29udmVyc2EgY29tIGEgw7psdGltYSBtZW5zYWdlbVxyXG4gICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgcmV0dXJuIHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgIGlmIChjb252LmlkID09PSBjb252ZXJzYXRpb25JZCkge1xyXG4gICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgbGFzdE1lc3NhZ2U6IHRlbXBNZXNzYWdlIH07XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBjb252O1xyXG4gICAgICB9KTtcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIEVudmlhciBhIG1lbnNhZ2VtIHZpYSBXZWJTb2NrZXRcclxuICAgIGNvbnN0IG1lc3NhZ2VEYXRhID0ge1xyXG4gICAgICBjb252ZXJzYXRpb25JZCxcclxuICAgICAgY29udGVudCxcclxuICAgICAgY29udGVudFR5cGUsXHJcbiAgICAgIG1ldGFkYXRhLFxyXG4gICAgICB0ZW1wSWQgLy8gSW5jbHVpciBvIElEIHRlbXBvcsOhcmlvIHBhcmEgcG9kZXIgc3Vic3RpdHVpciBkZXBvaXNcclxuICAgIH07XHJcblxyXG5cclxuICAgIHNvY2tldC5lbWl0KCdtZXNzYWdlOnNlbmQnLCBtZXNzYWdlRGF0YSwgKHJlc3BvbnNlKSA9PiB7XHJcbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzKSB7XHJcbiAgICAgICAgLy8gQSBtZW5zYWdlbSByZWFsIHNlcsOhIGFkaWNpb25hZGEgcGVsbyBldmVudG8gbWVzc2FnZTpuZXcgZG8gV2ViU29ja2V0XHJcbiAgICAgICAgLy8gTsOjbyBwcmVjaXNhbW9zIGZhemVyIG5hZGEgYXF1aSwgcG9pcyBvIFdlYlNvY2tldCB2YWkgYXR1YWxpemFyIGEgbWVuc2FnZW1cclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIG1lc3NhZ2U6JywgcmVzcG9uc2UuZXJyb3IpO1xyXG4gICAgICAgIC8vIEVtIGNhc28gZGUgZXJybywgcG9kZW1vcyBtYXJjYXIgYSBtZW5zYWdlbSBjb21vIGZhbGhhXHJcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiB7XHJcbiAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25NZXNzYWdlcyA9IHByZXZbY29udmVyc2F0aW9uSWRdIHx8IFtdO1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgW2NvbnZlcnNhdGlvbklkXTogY29udmVyc2F0aW9uTWVzc2FnZXMubWFwKG1zZyA9PlxyXG4gICAgICAgICAgICAgIG1zZy5pZCA9PT0gdGVtcElkID8geyAuLi5tc2csIGZhaWxlZDogdHJ1ZSB9IDogbXNnXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gIH0sIFtzb2NrZXQsIGlzQ29ubmVjdGVkLCB1c2VyXSk7XHJcblxyXG4gIC8vIENyaWFyIG5vdmEgY29udmVyc2FcclxuICBjb25zdCBjcmVhdGVDb252ZXJzYXRpb24gPSB1c2VDYWxsYmFjayhhc3luYyAocGFydGljaXBhbnRJZHMsIHRpdGxlID0gbnVsbCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ0NyaWFuZG8gY29udmVyc2EgY29tIHBhcnRpY2lwYW50ZXM6JywgcGFydGljaXBhbnRJZHMpO1xyXG4gICAgICBjb25zb2xlLmxvZygnVGlwbyBkZSBjb252ZXJzYTonLCBwYXJ0aWNpcGFudElkcy5sZW5ndGggPiAxID8gJ0dSVVBPJyA6ICdJTkRJVklEVUFMJyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdUw610dWxvIGRhIGNvbnZlcnNhOicsIHRpdGxlKTtcclxuICAgICAgXHJcbiAgICAgIC8vIOKchSBWQUxJREHDh8ODTzogU3lzdGVtIGFkbWluIG7Do28gcG9kZSBjcmlhciBncnVwb3MgY29tIHVzdcOhcmlvcyBkZSBlbXByZXNhcyBkaWZlcmVudGVzXHJcbiAgICAgIGlmICh1c2VyPy5yb2xlID09PSAnU1lTVEVNX0FETUlOJyAmJiBwYXJ0aWNpcGFudElkcy5sZW5ndGggPiAxKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1ZhbGlkYW5kbyBlbXByZXNhcyBwYXJhIHN5c3RlbSBhZG1pbi4uLicpO1xyXG4gICAgICAgIC8vIEJ1c2NhciBpbmZvcm1hw6fDtWVzIGRvcyBwYXJ0aWNpcGFudGVzIHBhcmEgdmFsaWRhciBlbXByZXNhc1xyXG4gICAgICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgICAgIGlmIChjdXJyZW50VG9rZW4pIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHBhcnRpY2lwYW50UHJvbWlzZXMgPSBwYXJ0aWNpcGFudElkcy5tYXAoYXN5bmMgKGlkKSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS91c2Vycy8ke2lkfWAsIHtcclxuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHVzZXJEYXRhLnN1Y2Nlc3MgPyB1c2VyRGF0YS5kYXRhIDogdXNlckRhdGE7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIGNvbnN0IHBhcnRpY2lwYW50cyA9IGF3YWl0IFByb21pc2UuYWxsKHBhcnRpY2lwYW50UHJvbWlzZXMpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUGFydGljaXBhbnRlcyBjYXJyZWdhZG9zOicsIHBhcnRpY2lwYW50cyk7XHJcbiAgICAgICAgICAgIGNvbnN0IHZhbGlkUGFydGljaXBhbnRzID0gcGFydGljaXBhbnRzLmZpbHRlcihwID0+IHAgJiYgcC5jb21wYW55SWQpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUGFydGljaXBhbnRlcyB2w6FsaWRvczonLCB2YWxpZFBhcnRpY2lwYW50cyk7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICBpZiAodmFsaWRQYXJ0aWNpcGFudHMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgICAgIGNvbnN0IGNvbXBhbmllcyA9IFsuLi5uZXcgU2V0KHZhbGlkUGFydGljaXBhbnRzLm1hcChwID0+IHAuY29tcGFueUlkKSldO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdFbXByZXNhcyBlbmNvbnRyYWRhczonLCBjb21wYW5pZXMpO1xyXG4gICAgICAgICAgICAgIGlmIChjb21wYW5pZXMubGVuZ3RoID4gMSkge1xyXG4gICAgICAgICAgICAgICAgYWxlcnQoJ07Do28gw6kgcG9zc8OtdmVsIGNyaWFyIGdydXBvcyBjb20gdXN1w6FyaW9zIGRlIGVtcHJlc2FzIGRpZmVyZW50ZXMuJyk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gY2F0Y2ggKHZhbGlkYXRpb25FcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIG5hIHZhbGlkYcOnw6NvIGRlIGVtcHJlc2FzOicsIHZhbGlkYXRpb25FcnJvcik7XHJcbiAgICAgICAgICAgIGFsZXJ0KCdFcnJvIG5hIHZhbGlkYcOnw6NvOiAnICsgdmFsaWRhdGlvbkVycm9yLm1lc3NhZ2UpO1xyXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBhbGd1bSBwYXJ0aWNpcGFudGUgw6kgY2xpZW50ZVxyXG4gICAgICBjb25zdCBoYXNDbGllbnRQYXJ0aWNpcGFudHMgPSBwYXJ0aWNpcGFudElkcy5zb21lKGlkID0+IHtcclxuICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBJRCBjb3JyZXNwb25kZSBhIHVtIGNsaWVudGUgKGFzc3VtaW5kbyBxdWUgY2xpZW50ZXMgdMOqbSBpc0NsaWVudDogdHJ1ZSlcclxuICAgICAgICByZXR1cm4gdHlwZW9mIGlkID09PSAnb2JqZWN0JyAmJiBpZC5pc0NsaWVudDtcclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmxvZygnVGVtIHBhcnRpY2lwYW50ZXMgY2xpZW50ZXM6JywgaGFzQ2xpZW50UGFydGljaXBhbnRzKTtcclxuXHJcbiAgICAgIC8vIE9idGVyIG8gdG9rZW4gbWFpcyByZWNlbnRlIGRvIGxvY2FsU3RvcmFnZVxyXG4gICAgICBjb25zdCBjdXJyZW50VG9rZW4gPSBnZXRDdXJyZW50VG9rZW4oKTtcclxuICAgICAgY29uc29sZS5sb2coJ1Rva2VuIGRpc3BvbsOtdmVsOicsIGN1cnJlbnRUb2tlbiA/ICdTaW0nIDogJ07Do28nKTtcclxuXHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbC4gVXN1w6FyaW8gcHJlY2lzYSBmYXplciBsb2dpbiBub3ZhbWVudGUuJyk7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX1VSTH0vY2hhdC9jb252ZXJzYXRpb25zYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgIHR5cGU6IHBhcnRpY2lwYW50SWRzLmxlbmd0aCA+IDEgPyAnR1JPVVAnIDogJ0lORElWSURVQUwnLFxyXG4gICAgICAgICAgdGl0bGUsXHJcbiAgICAgICAgICBwYXJ0aWNpcGFudElkczogcGFydGljaXBhbnRJZHMubWFwKGlkID0+IHR5cGVvZiBpZCA9PT0gJ29iamVjdCcgPyBpZC5pZCA6IGlkKSxcclxuICAgICAgICAgIGluY2x1ZGVDbGllbnRzOiB0cnVlIC8vIFBlcm1pdGlyIGluY2x1aXIgY2xpZW50ZXMgbmFzIGNvbnZlcnNhc1xyXG4gICAgICAgIH0pXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ1Jlc3Bvc3RhIGRhIEFQSTonLCByZXNwb25zZS5zdGF0dXMsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBjb25zb2xlLmxvZygnRGFkb3MgZGEgcmVzcG9zdGE6JywgZGF0YSk7XHJcblxyXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XHJcbiAgICAgICAgLy8gRXh0cmFpciBvcyBkYWRvcyBkYSBjb252ZXJzYSBjcmlhZGFcclxuICAgICAgICBjb25zdCBjb252ZXJzYXRpb25EYXRhID0gZGF0YS5kYXRhO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDb252ZXJzYSBjcmlhZGEgY29tIHN1Y2Vzc286JywgY29udmVyc2F0aW9uRGF0YSk7XHJcblxyXG4gICAgICAgIC8vIEFkaWNpb25hciBhIG5vdmEgY29udmVyc2Egw6AgbGlzdGFcclxuICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4ge1xyXG4gICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgY29udmVyc2EgasOhIGV4aXN0ZSBuYSBsaXN0YSBwYXJhIGV2aXRhciBkdXBsaWNhw6fDo29cclxuICAgICAgICAgIGlmIChwcmV2LnNvbWUoYyA9PiBjLmlkID09PSBjb252ZXJzYXRpb25EYXRhLmlkKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gcHJldjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybiBbY29udmVyc2F0aW9uRGF0YSwgLi4ucHJldl07XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vIERpc3BhcmFyIGV2ZW50byBwYXJhIG5vdGlmaWNhciBjb21wb25lbnRlcyBzb2JyZSBhIG5vdmEgY29udmVyc2FcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudCgnY2hhdDp3ZWJzb2NrZXQ6dXBkYXRlJywge1xyXG4gICAgICAgICAgICBkZXRhaWw6IHtcclxuICAgICAgICAgICAgICB0eXBlOiAnY29udmVyc2F0aW9ucycsXHJcbiAgICAgICAgICAgICAgY29udmVyc2F0aW9uSWQ6IGNvbnZlcnNhdGlvbkRhdGEuaWQsXHJcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9LCAzMDApO1xyXG5cclxuICAgICAgICByZXR1cm4gY29udmVyc2F0aW9uRGF0YTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNyaWFyIGNvbnZlcnNhOicsIGRhdGEuZXJyb3IgfHwgJ0Vycm8gZGVzY29uaGVjaWRvJyk7XHJcbiAgICAgICAgXHJcblxyXG5cclxuICAgICAgICAvLyBTZSBvIGVycm8gZm9yIGRlIGF1dGVudGljYcOnw6NvLCByZWRpcmVjaW9uYXIgcGFyYSBsb2dpblxyXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gZXhwaXJhZG8gb3UgaW52w6FsaWRvLiBSZWRpcmVjaW9uYW5kbyBwYXJhIGxvZ2luLi4uJyk7XHJcbiAgICAgICAgICBoYW5kbGVBdXRoRXJyb3IoKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgY29udmVyc2F0aW9uOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgfSwgW2hhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBDcmlhciBvdSBvYnRlciBjb252ZXJzYSBjb20gdW0gdXN1w6FyaW8gZXNwZWPDrWZpY29cclxuICBjb25zdCBjcmVhdGVPckdldENvbnZlcnNhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jIChvdGhlclVzZXIpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdjcmVhdGVPckdldENvbnZlcnNhdGlvbiBjaGFtYWRvIGNvbSB1c3XDoXJpbzonLCBvdGhlclVzZXIpO1xyXG5cclxuICAgICAgaWYgKCFvdGhlclVzZXIgfHwgIW90aGVyVXNlci5pZCkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VzdcOhcmlvIGludsOhbGlkbzonLCBvdGhlclVzZXIpO1xyXG4gICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBQcmltZWlybywgdmVyaWZpY2FyIHNlIGrDoSBleGlzdGUgdW1hIGNvbnZlcnNhIGluZGl2aWR1YWwgY29tIGVzdGUgdXN1w6FyaW9cclxuICAgICAgY29uc3QgZXhpc3RpbmdDb252ZXJzYXRpb24gPSBjb252ZXJzYXRpb25zLmZpbmQoY29udiA9PiB7XHJcbiAgICAgICAgaWYgKGNvbnYudHlwZSAhPT0gJ0lORElWSURVQUwnKSByZXR1cm4gZmFsc2U7XHJcblxyXG4gICAgICAgIHJldHVybiBjb252LnBhcnRpY2lwYW50cz8uc29tZShwID0+IHAudXNlcklkID09PSBvdGhlclVzZXIuaWQpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmIChleGlzdGluZ0NvbnZlcnNhdGlvbikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDb252ZXJzYSBleGlzdGVudGUgZW5jb250cmFkYTonLCBleGlzdGluZ0NvbnZlcnNhdGlvbik7XHJcbiAgICAgICAgc2V0QWN0aXZlQ29udmVyc2F0aW9uKGV4aXN0aW5nQ29udmVyc2F0aW9uLmlkKTtcclxuICAgICAgICByZXR1cm4gZXhpc3RpbmdDb252ZXJzYXRpb247XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdDcmlhbmRvIG5vdmEgY29udmVyc2EgY29tIHVzdcOhcmlvOicsIG90aGVyVXNlci5mdWxsTmFtZSk7XHJcbiAgICAgIC8vIFNlIG7Do28gZXhpc3RpciwgY3JpYXIgdW1hIG5vdmEgY29udmVyc2FcclxuICAgICAgY29uc3QgbmV3Q29udmVyc2F0aW9uID0gYXdhaXQgY3JlYXRlQ29udmVyc2F0aW9uKFtvdGhlclVzZXIuaWRdKTtcclxuXHJcbiAgICAgIGlmIChuZXdDb252ZXJzYXRpb24pIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnTm92YSBjb252ZXJzYSBjcmlhZGEgY29tIHN1Y2Vzc286JywgbmV3Q29udmVyc2F0aW9uKTtcclxuICAgICAgICAvLyBHYXJhbnRpciBxdWUgYSBjb252ZXJzYSBzZWphIGFkaWNpb25hZGEgw6AgbGlzdGEgYW50ZXMgZGUgZGVmaW5pciBjb21vIGF0aXZhXHJcbiAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIGNvbnZlcnNhIGrDoSBleGlzdGUgbmEgbGlzdGEgcGFyYSBldml0YXIgZHVwbGljYcOnw6NvXHJcbiAgICAgICAgICBpZiAocHJldi5zb21lKGMgPT4gYy5pZCA9PT0gbmV3Q29udmVyc2F0aW9uLmlkKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gcHJldjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybiBbbmV3Q29udmVyc2F0aW9uLCAuLi5wcmV2XTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gRGVmaW5pciBhIGNvbnZlcnNhIGNvbW8gYXRpdmEgYXDDs3MgdW0gcGVxdWVubyBkZWxheSBwYXJhIGdhcmFudGlyIHF1ZSBhIGxpc3RhIGZvaSBhdHVhbGl6YWRhXHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24obmV3Q29udmVyc2F0aW9uLmlkKTtcclxuXHJcbiAgICAgICAgICAvLyBEaXNwYXJhciBldmVudG8gcGFyYSBub3RpZmljYXIgY29tcG9uZW50ZXMgc29icmUgYSBub3ZhIGNvbnZlcnNhXHJcbiAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2NoYXQ6d2Vic29ja2V0OnVwZGF0ZScsIHtcclxuICAgICAgICAgICAgZGV0YWlsOiB7XHJcbiAgICAgICAgICAgICAgdHlwZTogJ2NvbnZlcnNhdGlvbnMnLFxyXG4gICAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkOiBuZXdDb252ZXJzYXRpb24uaWQsXHJcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9LCAzMDApO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhbGhhIGFvIGNyaWFyIG5vdmEgY29udmVyc2EsIHJldG9ybm8gbnVsbyBvdSBpbmRlZmluaWRvJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBuZXdDb252ZXJzYXRpb247XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBvciBnZXR0aW5nIGNvbnZlcnNhdGlvbjonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG4gIC8vIFJlbW92ZW5kbyBjb252ZXJzYXRpb25zIGRhcyBkZXBlbmTDqm5jaWFzIHBhcmEgZXZpdGFyIGxvb3BzIGluZmluaXRvc1xyXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcclxuICB9LCBbY3JlYXRlQ29udmVyc2F0aW9uLCBzZXRBY3RpdmVDb252ZXJzYXRpb24sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuXHJcblxyXG4gIC8vIENhcnJlZ2FyIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzIGNvbSBjYWNoZVxyXG4gIGNvbnN0IGxvYWRVbnJlYWRDb3VudCA9IHVzZUNhbGxiYWNrKGFzeW5jIChmb3JjZVJlZnJlc2ggPSBmYWxzZSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gdXN1w6FyaW8gZXN0w6EgbG9nYWRvXHJcbiAgICAgIGlmICghdXNlcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VzdcOhcmlvIG7Do28gbG9nYWRvIGFvIGNhcnJlZ2FyIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBWZXJpZmljYXIgY2FjaGVcclxuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICAgICAgaWYgKCFmb3JjZVJlZnJlc2ggJiZcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS51bnJlYWRDb3VudC5kYXRhICE9PSBudWxsICYmXHJcbiAgICAgICAgICBub3cgLSByZXF1ZXN0Q2FjaGUudW5yZWFkQ291bnQudGltZXN0YW1wIDwgQ0FDSEVfRVhQSVJBVElPTikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2FuZG8gY2FjaGUgcGFyYSBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhcycpO1xyXG4gICAgICAgIHJldHVybiByZXF1ZXN0Q2FjaGUudW5yZWFkQ291bnQuZGF0YTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbCBhbyBjYXJyZWdhciBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhcycpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQnVzY2FuZG8gY29udGFnZW0gZGUgbWVuc2FnZW5zIG7Do28gbGlkYXMgZGEgQVBJLi4uJyk7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L21lc3NhZ2VzL3VucmVhZGAsIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIGV4cGlyYWRvIG91IGludsOhbGlkbyBhbyBjYXJyZWdhciBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhcycpO1xyXG4gICAgICAgICAgICBoYW5kbGVBdXRoRXJyb3IoKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGNhcnJlZ2FyIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1Jlc3Bvc3RhIGRhIEFQSSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhczonLCBkYXRhKTtcclxuXHJcbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ0RhZG9zIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzIHJlY2ViaWRvczonLCBkYXRhLmRhdGEpO1xyXG5cclxuICAgICAgICAgIC8vIFVzYXIgbyBub3ZvIGNhbXBvIHRvdGFsVW5yZWFkQ29udmVyc2F0aW9ucyBvdSBmYWxsYmFjayBwYXJhIHRvdGFsVW5yZWFkXHJcbiAgICAgICAgICBjb25zdCB0b3RhbFVucmVhZENvbnZlcnNhdGlvbnMgPSBkYXRhLmRhdGEudG90YWxVbnJlYWRDb252ZXJzYXRpb25zIHx8IGRhdGEuZGF0YS50b3RhbFVucmVhZCB8fCAwO1xyXG5cclxuICAgICAgICAgIC8vIEF0dWFsaXphciBjYWNoZSBjb20gbyB0b3RhbFVucmVhZENvbnZlcnNhdGlvbnNcclxuICAgICAgICAgIHJlcXVlc3RDYWNoZS51bnJlYWRDb3VudC5kYXRhID0gdG90YWxVbnJlYWRDb252ZXJzYXRpb25zO1xyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLnVucmVhZENvdW50LnRpbWVzdGFtcCA9IG5vdztcclxuXHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgZXN0YWRvXHJcbiAgICAgICAgICBzZXRVbnJlYWRDb3VudCh0b3RhbFVucmVhZENvbnZlcnNhdGlvbnMpO1xyXG5cclxuICAgICAgICAgIC8vIEF0dWFsaXphciBhcyBjb252ZXJzYXMgY29tIHN0YXR1cyBkZSBuw6NvIGxpZGFzXHJcbiAgICAgICAgICBpZiAoZGF0YS5kYXRhLmNvbnZlcnNhdGlvbnMgJiYgQXJyYXkuaXNBcnJheShkYXRhLmRhdGEuY29udmVyc2F0aW9ucykpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1tsb2FkVW5yZWFkQ291bnRdIERhZG9zIGRlIGNvbnZlcnNhcyBuw6NvIGxpZGFzIHJlY2ViaWRvczonLCBkYXRhLmRhdGEuY29udmVyc2F0aW9ucyk7XHJcblxyXG4gICAgICAgICAgICAvLyBQcmltZWlybywgdmVyaWZpY2FyIHNlIGrDoSB0ZW1vcyBhcyBjb252ZXJzYXMgY2FycmVnYWRhc1xyXG4gICAgICAgICAgICBjb25zdCBjb252ZXJzYXRpb25JZHMgPSBkYXRhLmRhdGEuY29udmVyc2F0aW9ucy5tYXAoYyA9PiBjLmNvbnZlcnNhdGlvbklkKTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1tsb2FkVW5yZWFkQ291bnRdIElEcyBkZSBjb252ZXJzYXMgbsOjbyBsaWRhczonLCBjb252ZXJzYXRpb25JZHMpO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnW2xvYWRVbnJlYWRDb3VudF0gQ29udmVyc2FzIGF0dWFsbWVudGUgY2FycmVnYWRhczonLCBjb252ZXJzYXRpb25zLmxlbmd0aCwgY29udmVyc2F0aW9ucy5tYXAoYyA9PiBjLmlkKSk7XHJcblxyXG4gICAgICAgICAgICAvLyBTZSBuw6NvIHRlbW9zIGNvbnZlcnNhcyBjYXJyZWdhZGFzIG91IHNlIHRlbW9zIG1lbm9zIGNvbnZlcnNhcyBkbyBxdWUgYXMgbsOjbyBsaWRhcyxcclxuICAgICAgICAgICAgLy8gZm9yw6dhciB1bWEgYXR1YWxpemHDp8OjbyBkYXMgY29udmVyc2FzXHJcbiAgICAgICAgICAgIGlmIChjb252ZXJzYXRpb25zLmxlbmd0aCA9PT0gMCB8fFxyXG4gICAgICAgICAgICAgICAgIWNvbnZlcnNhdGlvbklkcy5ldmVyeShpZCA9PiBjb252ZXJzYXRpb25zLnNvbWUoYyA9PiBjLmlkID09PSBpZCkpKSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1tsb2FkVW5yZWFkQ291bnRdIEZvcsOnYW5kbyBhdHVhbGl6YcOnw6NvIGRhcyBjb252ZXJzYXMgcG9ycXVlIGjDoSBtZW5zYWdlbnMgbsOjbyBsaWRhcyBlbSBjb252ZXJzYXMgbsOjbyBjYXJyZWdhZGFzJyk7XHJcbiAgICAgICAgICAgICAgYXdhaXQgbG9hZENvbnZlcnNhdGlvbnModHJ1ZSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIEFww7NzIGNhcnJlZ2FyIGFzIGNvbnZlcnNhcywgcHJlY2lzYW1vcyBhdHVhbGl6YXIgY29tIGFzIGluZm9ybWHDp8O1ZXMgZGUgbsOjbyBsaWRhc1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbbG9hZFVucmVhZENvdW50XSBBdHVhbGl6YW5kbyBjb252ZXJzYXMgcmVjw6ltLWNhcnJlZ2FkYXMgY29tIHN0YXR1cyBkZSBuw6NvIGxpZGFzLi4uJyk7XHJcbiAgICAgICAgICAgICAgc2V0Q29udmVyc2F0aW9ucyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWQgPSBwcmV2Lm1hcChjb252ID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgdW5yZWFkSW5mbyA9IGRhdGEuZGF0YS5jb252ZXJzYXRpb25zLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgaXRlbSA9PiBpdGVtLmNvbnZlcnNhdGlvbklkID09PSBjb252LmlkXHJcbiAgICAgICAgICAgICAgICAgICk7XHJcblxyXG4gICAgICAgICAgICAgICAgICBpZiAodW5yZWFkSW5mbykge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGhhc1VucmVhZCA9IHVucmVhZEluZm8uaGFzVW5yZWFkIHx8ICh1bnJlYWRJbmZvLnVucmVhZENvdW50ICYmIHVucmVhZEluZm8udW5yZWFkQ291bnQgPiAwKTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgW2xvYWRVbnJlYWRDb3VudF0gQ29udmVyc2EgJHtjb252LmlkfTogaGFzVW5yZWFkID0gJHtoYXNVbnJlYWR9YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgaGFzVW5yZWFkIH07XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYFtsb2FkVW5yZWFkQ291bnRdIENvbnZlcnNhICR7Y29udi5pZH06IGhhc1VucmVhZCA9IGZhbHNlIChuw6NvIGVuY29udHJhZGEgZW0gbsOjbyBsaWRhcylgKTtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgaGFzVW5yZWFkOiBmYWxzZSB9O1xyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW2xvYWRVbnJlYWRDb3VudF0gQ29udmVyc2FzIGF0dWFsaXphZGFzIGFww7NzIGxvYWRDb252ZXJzYXRpb25zOicsIHVwZGF0ZWQubWFwKGMgPT4gKHsgaWQ6IGMuaWQsIGhhc1VucmVhZDogYy5oYXNVbnJlYWQgfSkpKTtcclxuICAgICAgICAgICAgICAgIHJldHVybiB1cGRhdGVkO1xyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIC8vIEF0dWFsaXphciBhcyBjb252ZXJzYXMgZXhpc3RlbnRlcyBjb20gc3RhdHVzIGRlIG7Do28gbGlkYXNcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW2xvYWRVbnJlYWRDb3VudF0gQXR1YWxpemFuZG8gY29udmVyc2FzIGNvbSBzdGF0dXMgZGUgbsOjbyBsaWRhcy4uLicpO1xyXG4gICAgICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkID0gcHJldi5tYXAoY29udiA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIC8vIFByb2N1cmFyIGVzdGEgY29udmVyc2Egbm9zIGRhZG9zIGRlIG7Do28gbGlkYXNcclxuICAgICAgICAgICAgICAgICAgY29uc3QgdW5yZWFkSW5mbyA9IGRhdGEuZGF0YS5jb252ZXJzYXRpb25zLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgaXRlbSA9PiBpdGVtLmNvbnZlcnNhdGlvbklkID09PSBjb252LmlkXHJcbiAgICAgICAgICAgICAgICAgICk7XHJcblxyXG4gICAgICAgICAgICAgICAgICBpZiAodW5yZWFkSW5mbykge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGhhc1VucmVhZCA9IHVucmVhZEluZm8uaGFzVW5yZWFkIHx8ICh1bnJlYWRJbmZvLnVucmVhZENvdW50ICYmIHVucmVhZEluZm8udW5yZWFkQ291bnQgPiAwKTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgW2xvYWRVbnJlYWRDb3VudF0gQ29udmVyc2EgJHtjb252LmlkfTogaGFzVW5yZWFkID0gJHtoYXNVbnJlYWR9YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgaGFzVW5yZWFkIH07XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYFtsb2FkVW5yZWFkQ291bnRdIENvbnZlcnNhICR7Y29udi5pZH06IGhhc1VucmVhZCA9IGZhbHNlIChuw6NvIGVuY29udHJhZGEgZW0gbsOjbyBsaWRhcylgKTtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uY29udiwgaGFzVW5yZWFkOiBmYWxzZSB9O1xyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW2xvYWRVbnJlYWRDb3VudF0gQ29udmVyc2FzIGF0dWFsaXphZGFzOicsIHVwZGF0ZWQubWFwKGMgPT4gKHsgaWQ6IGMuaWQsIGhhc1VucmVhZDogYy5oYXNVbnJlYWQgfSkpKTtcclxuICAgICAgICAgICAgICAgIHJldHVybiB1cGRhdGVkO1xyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHRvdGFsVW5yZWFkQ29udmVyc2F0aW9ucztcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB1bnJlYWQgY291bnQ6JywgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBsb2FkVW5yZWFkQ291bnQ6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIC8vIFJlbW92ZW5kbyBjb252ZXJzYXRpb25zIGUgbG9hZENvbnZlcnNhdGlvbnMgZGFzIGRlcGVuZMOqbmNpYXMgcGFyYSBldml0YXIgbG9vcHMgaW5maW5pdG9zXHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGhhbmRsZUF1dGhFcnJvcl0pO1xyXG5cclxuICAvLyBSZWZlcsOqbmNpYSBwYXJhIGNvbnRyb2xhciBzZSBvcyBkYWRvcyBpbmljaWFpcyBqw6EgZm9yYW0gY2FycmVnYWRvc1xyXG4gIGNvbnN0IGluaXRpYWxEYXRhTG9hZGVkUmVmID0gdXNlUmVmKGZhbHNlKTtcclxuXHJcbiAgLy8gUmVzZXRhciBhIGZsYWcgcXVhbmRvIG8gdXN1w6FyaW8gbXVkYVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAodXNlcikge1xyXG4gICAgICBjb25zb2xlLmxvZygnVXN1w6FyaW8gYWx0ZXJhZG8sIHJlc2V0YW5kbyBmbGFnIGRlIGRhZG9zIGNhcnJlZ2Fkb3MnKTtcclxuICAgICAgaW5pdGlhbERhdGFMb2FkZWRSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgfVxyXG4gIH0sIFt1c2VyPy5pZF0pOyAvLyBEZXBlbmRlbmRvIGFwZW5hcyBkbyBJRCBkbyB1c3XDoXJpbyBwYXJhIGV2aXRhciByZS1yZW5kZXJzIGRlc25lY2Vzc8Ohcmlvc1xyXG5cclxuICAvLyBFZmVpdG8gcGFyYSBjYXJyZWdhciBkYWRvcyBpbmljaWFpcyBlIGNvbmZpZ3VyYXIgYXR1YWxpemHDp8OjbyBwZXJpw7NkaWNhXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFZlcmlmaWNhciBzZSBvIHVzdcOhcmlvIGVzdMOhIGxvZ2FkbyBhbnRlcyBkZSBjYXJyZWdhciBkYWRvc1xyXG4gICAgaWYgKCF1c2VyKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdVc3XDoXJpbyBuw6NvIGxvZ2FkbywgbsOjbyBjYXJyZWdhbmRvIGRhZG9zIGluaWNpYWlzJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGllbnRlcyBhZ29yYSBwb2RlbSB1c2FyIG8gY2hhdFxyXG4gICAgY29uc29sZS5sb2coJ0NhcnJlZ2FuZG8gZGFkb3MgaW5pY2lhaXMgcGFyYTonLCB1c2VyLnJvbGUgfHwgJ1VTRVInKTtcclxuXHJcbiAgICAvLyBWZXJpZmljYXIgdG9rZW5cclxuICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgaWYgKCFjdXJyZW50VG9rZW4pIHtcclxuICAgICAgY29uc29sZS5sb2coJ1Rva2VuIG7Do28gZGlzcG9uw612ZWwsIG7Do28gY2FycmVnYW5kbyBkYWRvcyBpbmljaWFpcycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmVyaWZpY2FyIHNlIGrDoSBlc3TDoSBjYXJyZWdhbmRvXHJcbiAgICBpZiAoaXNMb2FkaW5nIHx8IGlzTG9hZGluZ1JlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdKw6EgZXN0w6EgY2FycmVnYW5kbyBkYWRvcywgYWd1YXJkYW5kby4uLicpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmVyaWZpY2FyIHNlIG9zIGRhZG9zIGrDoSBmb3JhbSBjYXJyZWdhZG9zXHJcbiAgICBpZiAoaW5pdGlhbERhdGFMb2FkZWRSZWYuY3VycmVudCkge1xyXG4gICAgICBjb25zb2xlLmxvZygnRGFkb3MgasOhIGZvcmFtIGNhcnJlZ2Fkb3MgYW50ZXJpb3JtZW50ZSwgaWdub3JhbmRvJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIGRhZG9zIGluaWNpYWlzXHJcbiAgICBjb25zdCBsb2FkSW5pdGlhbERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmIChpc0xvYWRpbmcgfHwgaXNMb2FkaW5nUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnSsOhIGVzdMOhIGNhcnJlZ2FuZG8gZGFkb3MsIGNhbmNlbGFuZG8gY2FycmVnYW1lbnRvIGluaWNpYWwnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE1hcmNhciBjb21vIGNhcnJlZ2FuZG9cclxuICAgICAgaXNMb2FkaW5nUmVmLmN1cnJlbnQgPSB0cnVlO1xyXG4gICAgICBjb25zb2xlLmxvZygnSW5pY2lhbmRvIGNhcnJlZ2FtZW50byBkZSBkYWRvcyBkbyBjaGF0Li4uJyk7XHJcblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIC8vIFByaW1laXJvIGNhcnJlZ2FyIGFzIGNvbnZlcnNhc1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDYXJyZWdhbmRvIGNvbnZlcnNhcy4uLicpO1xyXG4gICAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbnNEYXRhID0gYXdhaXQgbG9hZENvbnZlcnNhdGlvbnModHJ1ZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NvbnZlcnNhcyBjYXJyZWdhZGFzOicsIGNvbnZlcnNhdGlvbnNEYXRhPy5sZW5ndGggfHwgMCwgJ2NvbnZlcnNhcycpO1xyXG5cclxuICAgICAgICAvLyBDYXJyZWdhciBjb250YWdlbSBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhc1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDYXJyZWdhbmRvIGNvbnRhZ2VtIGRlIG1lbnNhZ2VucyBuw6NvIGxpZGFzLi4uJyk7XHJcbiAgICAgICAgYXdhaXQgbG9hZFVucmVhZENvdW50KHRydWUpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDb250YWdlbSBkZSBuw6NvIGxpZGFzIGNhcnJlZ2FkYScpO1xyXG5cclxuICAgICAgICAvLyBNYXJjYXIgY29tbyBjYXJyZWdhZG9cclxuICAgICAgICBpbml0aWFsRGF0YUxvYWRlZFJlZi5jdXJyZW50ID0gdHJ1ZTtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ2FycmVnYW1lbnRvIGRlIGRhZG9zIGNvbmNsdcOtZG8gY29tIHN1Y2Vzc28nKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIGRhZG9zOicsIGVycm9yKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBpc0xvYWRpbmdSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIENhcnJlZ2FyIGRhZG9zIGluaWNpYWlzIGNvbSBkZWJvdW5jZSBwYXJhIGV2aXRhciBtw7psdGlwbGFzIGNoYW1hZGFzXHJcbiAgICBjb25zb2xlLmxvZygnQWdlbmRhbmRvIGNhcnJlZ2FtZW50byBkZSBkYWRvcyBpbmljaWFpcy4uLicpO1xyXG4gICAgY29uc3QgZGVib3VuY2VkTG9hZERhdGEgPSBkZWJvdW5jZSgoKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdDYXJyZWdhbmRvIGRhZG9zIGluaWNpYWlzIChkZWJvdW5jZWQpLi4uJyk7XHJcbiAgICAgIGxvYWRJbml0aWFsRGF0YSgpO1xyXG4gICAgfSwgMTAwMCk7IC8vIEVzcGVyYXIgMSBzZWd1bmRvIGFudGVzIGRlIGNhcnJlZ2FyXHJcblxyXG4gICAgLy8gQ2hhbWFyIGEgZnVuw6fDo28gY29tIGRlYm91bmNlXHJcbiAgICBkZWJvdW5jZWRMb2FkRGF0YSgpO1xyXG5cclxuICAgIC8vIFJlbW92ZW1vcyBhIGF0dWFsaXphw6fDo28gcGVyacOzZGljYSB2aWEgSFRUUCBwYXJhIGV2aXRhciBmbG9vZCBubyBiYWNrZW5kXHJcbiAgICAvLyBBZ29yYSBkZXBlbmRlbW9zIGFwZW5hcyBkbyBXZWJTb2NrZXQgcGFyYSBhdHVhbGl6YcOnw7VlcyBlbSB0ZW1wbyByZWFsXHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgLy8gQ2xlYW51cCBmdW5jdGlvblxyXG4gICAgfTtcclxuICAvLyBSZW1vdmVuZG8gbG9hZFVucmVhZENvdW50IGUgaXNQYW5lbE9wZW4sIGlzTW9kYWxPcGVuIGRhcyBkZXBlbmTDqm5jaWFzIHBhcmEgZXZpdGFyIGNoYW1hZGFzIGRlc25lY2Vzc8Ohcmlhc1xyXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcclxuICB9LCBbdXNlciwgZ2V0Q3VycmVudFRva2VuLCBsb2FkQ29udmVyc2F0aW9ucywgaXNMb2FkaW5nXSk7XHJcblxyXG4gIC8vIEVmZWl0byBwYXJhIHZlcmlmaWNhciBzZSBhY3RpdmVDb252ZXJzYXRpb24gw6kgdsOhbGlkb1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoYWN0aXZlQ29udmVyc2F0aW9uICYmIGNvbnZlcnNhdGlvbnMubGVuZ3RoID4gMCkge1xyXG4gICAgICBjb25zdCBjb252ZXJzYXRpb25FeGlzdHMgPSBjb252ZXJzYXRpb25zLnNvbWUoYyA9PiBjLmlkID09PSBhY3RpdmVDb252ZXJzYXRpb24pO1xyXG4gICAgICBpZiAoIWNvbnZlcnNhdGlvbkV4aXN0cykge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignQ29udmVyc2EgYXRpdmEgbsOjbyBlbmNvbnRyYWRhIG5hIGxpc3RhIGRlIGNvbnZlcnNhcywgcmVzZXRhbmRvLi4uJywgYWN0aXZlQ29udmVyc2F0aW9uKTtcclxuICAgICAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24obnVsbCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbYWN0aXZlQ29udmVyc2F0aW9uLCBjb252ZXJzYXRpb25zXSk7XHJcblxyXG4gIC8vIEVmZWl0byBwYXJhIG1vbml0b3JhciBtdWRhbsOnYXMgbm8gdG9rZW4gZSByZWNvbmVjdGFyIHF1YW5kbyBuZWNlc3PDoXJpb1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBWZXJpZmljYXIgc2UgbyB1c3XDoXJpbyBlc3TDoSBsb2dhZG8gYW50ZXMgZGUgbW9uaXRvcmFyIG8gdG9rZW5cclxuICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG5cclxuICAgIC8vIFZlcmlmaWNhciBvIHRva2VuIGEgY2FkYSAyIG1pbnV0b3MgKGF1bWVudGFkbyBwYXJhIHJlZHV6aXIgYSBmcmVxdcOqbmNpYSlcclxuICAgIGNvbnN0IHRva2VuQ2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgLy8gRXZpdGFyIHZlcmlmaWNhw6fDtWVzIGRlc25lY2Vzc8OhcmlhcyBzZSBlc3RpdmVyIGNhcnJlZ2FuZG9cclxuICAgICAgaWYgKGlzTG9hZGluZyB8fCBpc0xvYWRpbmdSZWYuY3VycmVudCkgcmV0dXJuO1xyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcblxyXG4gICAgICAvLyBTZSBuw6NvIGjDoSB0b2tlbiBtYXMgaMOhIGNvbmV4w6NvLCBkZXNjb25lY3RhclxyXG4gICAgICBpZiAoIWN1cnJlbnRUb2tlbiAmJiBpc0Nvbm5lY3RlZCAmJiBzb2NrZXQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnVG9rZW4gbsOjbyBlbmNvbnRyYWRvLCBkZXNjb25lY3RhbmRvIFdlYlNvY2tldC4uLicpO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBzb2NrZXQuZGlzY29ubmVjdCgpO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGRlc2NvbmVjdGFyIHNvY2tldDonLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gU2UgaMOhIHRva2VuIG1hcyBuw6NvIGjDoSBjb25leMOjbywgdGVudGFyIHJlY29uZWN0YXIgKGNvbSB2ZXJpZmljYcOnw6NvIGRlIHRlbXBvKVxyXG4gICAgICBpZiAoY3VycmVudFRva2VuICYmICFpc0Nvbm5lY3RlZCAmJiAhc29ja2V0KSB7XHJcbiAgICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICAgICAgICAvLyBMaW1pdGFyIHRlbnRhdGl2YXMgZGUgcmVjb25leMOjbyAobm8gbcOheGltbyB1bWEgYSBjYWRhIDIgbWludXRvcylcclxuICAgICAgICBpZiAobm93IC0gbGFzdEluaXRBdHRlbXB0UmVmLmN1cnJlbnQgPiAxMjAwMDApIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiBlbmNvbnRyYWRvLCB0ZW50YW5kbyByZWNvbmVjdGFyIFdlYlNvY2tldC4uLicpO1xyXG4gICAgICAgICAgLy8gUGVybWl0aXIgbm92YSB0ZW50YXRpdmEgZGUgaW5pY2lhbGl6YcOnw6NvIGRvIFdlYlNvY2tldFxyXG4gICAgICAgICAgc29ja2V0SW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IGZhbHNlO1xyXG4gICAgICAgICAgbGFzdEluaXRBdHRlbXB0UmVmLmN1cnJlbnQgPSBub3c7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9LCAxMjAwMDApOyAvLyBWZXJpZmljYXIgYSBjYWRhIDIgbWludXRvc1xyXG5cclxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRva2VuQ2hlY2tJbnRlcnZhbCk7XHJcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW5dKTtcclxuXHJcbiAgLy8gQWJyaXIvZmVjaGFyIHBhaW5lbCBkZSBjaGF0XHJcbiAgY29uc3QgdG9nZ2xlQ2hhdFBhbmVsID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgLy8gVmVyaWZpY2FyIHNlIG8gdXN1w6FyaW8gZXN0w6EgbG9nYWRvXHJcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcclxuXHJcbiAgICBzZXRJc1BhbmVsT3BlbihwcmV2ID0+IHtcclxuICAgICAgY29uc3QgbmV3U3RhdGUgPSAhcHJldjtcclxuICAgICAgLy8gU2UgZXN0aXZlciBhYnJpbmRvIG8gcGFpbmVsLCBmZWNoYXIgbyBtb2RhbFxyXG4gICAgICBpZiAobmV3U3RhdGUpIHtcclxuICAgICAgICBzZXRJc01vZGFsT3BlbihmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIG5ld1N0YXRlO1xyXG4gICAgfSk7XHJcbiAgfSwgW3VzZXJdKTtcclxuXHJcbiAgLy8gQWJyaXIvZmVjaGFyIG1vZGFsIGRlIGNoYXRcclxuICBjb25zdCB0b2dnbGVDaGF0TW9kYWwgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICAvLyBWZXJpZmljYXIgc2UgbyB1c3XDoXJpbyBlc3TDoSBsb2dhZG9cclxuICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG5cclxuICAgIHNldElzTW9kYWxPcGVuKHByZXYgPT4ge1xyXG4gICAgICBjb25zdCBuZXdTdGF0ZSA9ICFwcmV2O1xyXG4gICAgICAvLyBTZSBlc3RpdmVyIGFicmluZG8gbyBtb2RhbCwgZmVjaGFyIG8gcGFpbmVsXHJcbiAgICAgIGlmIChuZXdTdGF0ZSkge1xyXG4gICAgICAgIHNldElzUGFuZWxPcGVuKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gbmV3U3RhdGU7XHJcbiAgICB9KTtcclxuICB9LCBbdXNlcl0pO1xyXG5cclxuICAvLyBWZXJpZmljYXIgc2UgY3JlYXRlQ29udmVyc2F0aW9uIMOpIHVtYSBmdW7Dp8OjbyB2w6FsaWRhXHJcbiAgY29uc29sZS5sb2coJ0NoYXRDb250ZXh0OiBjcmVhdGVDb252ZXJzYXRpb24gw6kgdW1hIGZ1bsOnw6NvPycsIHR5cGVvZiBjcmVhdGVDb252ZXJzYXRpb24gPT09ICdmdW5jdGlvbicpO1xyXG5cclxuICAvLyBBZGljaW9uYXIgcGFydGljaXBhbnRlIGEgdW0gZ3J1cG9cclxuICBjb25zdCBhZGRQYXJ0aWNpcGFudFRvR3JvdXAgPSB1c2VDYWxsYmFjayhhc3luYyAoY29udmVyc2F0aW9uSWQsIHBhcnRpY2lwYW50SWQpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghY29udmVyc2F0aW9uSWQgfHwgIXBhcnRpY2lwYW50SWQpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdJRCBkYSBjb252ZXJzYSBlIElEIGRvIHBhcnRpY2lwYW50ZSBzw6NvIG9icmlnYXTDs3Jpb3MnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbCBhbyBhZGljaW9uYXIgcGFydGljaXBhbnRlJyk7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGBBZGljaW9uYW5kbyBwYXJ0aWNpcGFudGUgJHtwYXJ0aWNpcGFudElkfSDDoCBjb252ZXJzYSAke2NvbnZlcnNhdGlvbklkfWApO1xyXG4gICAgICBcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L2NvbnZlcnNhdGlvbnMvJHtjb252ZXJzYXRpb25JZH0vcGFydGljaXBhbnRzYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgcGFydGljaXBhbnRJZCB9KVxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGNvbnNvbGUubG9nKGBSZXNwb3N0YSBkYSBBUEk6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdUb2tlbiBleHBpcmFkbyBvdSBpbnbDoWxpZG8gYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZScpO1xyXG4gICAgICAgICAgaGFuZGxlQXV0aEVycm9yKCk7XHJcbiAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gTG9nIGRldGFsaGFkbyBkbyBlcnJvXHJcbiAgICAgICAgY29uc3QgZXJyb3JUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm8gJHtyZXNwb25zZS5zdGF0dXN9IGFvIGFkaWNpb25hciBwYXJ0aWNpcGFudGU6YCwgZXJyb3JUZXh0KTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZTogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblxyXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XHJcbiAgICAgICAgLy8gQXR1YWxpemFyIGEgY29udmVyc2EgY29tIG8gbm92byBwYXJ0aWNpcGFudGVcclxuICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4ge1xyXG4gICAgICAgICAgcmV0dXJuIHByZXYubWFwKGNvbnYgPT4ge1xyXG4gICAgICAgICAgICBpZiAoY29udi5pZCA9PT0gY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBwYXJ0aWNpcGFudGUgasOhIGV4aXN0ZVxyXG4gICAgICAgICAgICAgIGNvbnN0IHBhcnRpY2lwYW50RXhpc3RzID0gY29udi5wYXJ0aWNpcGFudHMuc29tZShwID0+IHAudXNlcklkID09PSBwYXJ0aWNpcGFudElkKTtcclxuXHJcbiAgICAgICAgICAgICAgaWYgKHBhcnRpY2lwYW50RXhpc3RzKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gY29udjtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC8vIEFkaWNpb25hciBvIG5vdm8gcGFydGljaXBhbnRlXHJcbiAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgIC4uLmNvbnYsXHJcbiAgICAgICAgICAgICAgICBwYXJ0aWNpcGFudHM6IFsuLi5jb252LnBhcnRpY2lwYW50cywgZGF0YS5kYXRhXVxyXG4gICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIGNvbnY7XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gQ3JpYXIgdW1hIG1lbnNhZ2VtIGRvIHNpc3RlbWEgcGFyYSBub3RpZmljYXIgcXVlIHVtIHBhcnRpY2lwYW50ZSBmb2kgYWRpY2lvbmFkb1xyXG4gICAgICAgIGlmIChzb2NrZXQgJiYgaXNDb25uZWN0ZWQpIHtcclxuICAgICAgICAgIGNvbnN0IHN5c3RlbU1lc3NhZ2UgPSB7XHJcbiAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkLFxyXG4gICAgICAgICAgICBjb250ZW50OiBgJHtkYXRhLmRhdGEudXNlci5mdWxsTmFtZX0gZm9pIGFkaWNpb25hZG8gYW8gZ3J1cG9gLFxyXG4gICAgICAgICAgICBjb250ZW50VHlwZTogJ1NZU1RFTSdcclxuICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgc29ja2V0LmVtaXQoJ21lc3NhZ2U6c2VuZCcsIHN5c3RlbU1lc3NhZ2UpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIGRhdGEuZGF0YTtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGFkaWNpb25hciBwYXJ0aWNpcGFudGU6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuICB9LCBbc29ja2V0LCBpc0Nvbm5lY3RlZCwgZ2V0Q3VycmVudFRva2VuLCBoYW5kbGVBdXRoRXJyb3JdKTtcclxuXHJcbiAgLy8gQWRpY2lvbmFyIG3Dumx0aXBsb3MgcGFydGljaXBhbnRlcyBhIHVtIGdydXBvXHJcbiAgY29uc3QgYWRkTXVsdGlwbGVQYXJ0aWNpcGFudHNUb0dyb3VwID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbnZlcnNhdGlvbklkLCBwYXJ0aWNpcGFudHMpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghY29udmVyc2F0aW9uSWQgfHwgIXBhcnRpY2lwYW50cyB8fCAhcGFydGljaXBhbnRzLmxlbmd0aCkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0lEIGRhIGNvbnZlcnNhIGUgbGlzdGEgZGUgcGFydGljaXBhbnRlcyBzw6NvIG9icmlnYXTDs3Jpb3MnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc29sZS5sb2coYEFkaWNpb25hbmRvICR7cGFydGljaXBhbnRzLmxlbmd0aH0gcGFydGljaXBhbnRlcyBhbyBncnVwbyAke2NvbnZlcnNhdGlvbklkfWApO1xyXG5cclxuICAgICAgLy8gQXJyYXkgcGFyYSBhcm1hemVuYXIgb3MgcmVzdWx0YWRvc1xyXG4gICAgICBjb25zdCByZXN1bHRzID0gW107XHJcbiAgICAgIGNvbnN0IGVycm9ycyA9IFtdO1xyXG5cclxuICAgICAgLy8gQWRpY2lvbmFyIHBhcnRpY2lwYW50ZXMgdW0gcG9yIHVtXHJcbiAgICAgIGZvciAoY29uc3QgcGFydGljaXBhbnQgb2YgcGFydGljaXBhbnRzKSB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGFkZFBhcnRpY2lwYW50VG9Hcm91cChjb252ZXJzYXRpb25JZCwgcGFydGljaXBhbnQuaWQpO1xyXG4gICAgICAgICAgaWYgKHJlc3VsdCkge1xyXG4gICAgICAgICAgICByZXN1bHRzLnB1c2gocmVzdWx0KTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGVycm9ycy5wdXNoKHsgdXNlcjogcGFydGljaXBhbnQsIGVycm9yOiAnRmFsaGEgYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZScgfSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm8gYW8gYWRpY2lvbmFyIHBhcnRpY2lwYW50ZSAke3BhcnRpY2lwYW50LmlkfTpgLCBlcnJvcik7XHJcbiAgICAgICAgICBlcnJvcnMucHVzaCh7IHVzZXI6IHBhcnRpY2lwYW50LCBlcnJvcjogZXJyb3IubWVzc2FnZSB8fCAnRXJybyBkZXNjb25oZWNpZG8nIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiByZXN1bHRzLmxlbmd0aCA+IDAsXHJcbiAgICAgICAgYWRkZWQ6IHJlc3VsdHMsXHJcbiAgICAgICAgZXJyb3JzOiBlcnJvcnMsXHJcbiAgICAgICAgdG90YWw6IHBhcnRpY2lwYW50cy5sZW5ndGgsXHJcbiAgICAgICAgc3VjY2Vzc0NvdW50OiByZXN1bHRzLmxlbmd0aCxcclxuICAgICAgICBlcnJvckNvdW50OiBlcnJvcnMubGVuZ3RoXHJcbiAgICAgIH07XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGFkaWNpb25hciBtw7psdGlwbG9zIHBhcnRpY2lwYW50ZXM6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICAgIGFkZGVkOiBbXSxcclxuICAgICAgICBlcnJvcnM6IFt7IGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdFcnJvIGRlc2NvbmhlY2lkbycgfV0sXHJcbiAgICAgICAgdG90YWw6IHBhcnRpY2lwYW50cy5sZW5ndGgsXHJcbiAgICAgICAgc3VjY2Vzc0NvdW50OiAwLFxyXG4gICAgICAgIGVycm9yQ291bnQ6IHBhcnRpY2lwYW50cy5sZW5ndGhcclxuICAgICAgfTtcclxuICAgIH1cclxuICB9LCBbYWRkUGFydGljaXBhbnRUb0dyb3VwXSk7XHJcblxyXG4gIC8vIFJlbW92ZXIgcGFydGljaXBhbnRlIGRlIHVtIGdydXBvIChvdSBzYWlyIGRvIGdydXBvKVxyXG4gIGNvbnN0IHJlbW92ZVBhcnRpY2lwYW50RnJvbUdyb3VwID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbnZlcnNhdGlvbklkLCBwYXJ0aWNpcGFudElkKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygncmVtb3ZlUGFydGljaXBhbnRGcm9tR3JvdXAgY2hhbWFkbzonLCB7IGNvbnZlcnNhdGlvbklkLCBwYXJ0aWNpcGFudElkLCB1c2VySWQ6IHVzZXI/LmlkIH0pO1xyXG4gICAgXHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoIWNvbnZlcnNhdGlvbklkIHx8ICFwYXJ0aWNpcGFudElkKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignSUQgZGEgY29udmVyc2EgZSBJRCBkbyBwYXJ0aWNpcGFudGUgc8OjbyBvYnJpZ2F0w7NyaW9zJyk7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgICBpZiAoIWN1cnJlbnRUb2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIG7Do28gZGlzcG9uw612ZWwgYW8gcmVtb3ZlciBwYXJ0aWNpcGFudGUnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L2NvbnZlcnNhdGlvbnMvJHtjb252ZXJzYXRpb25JZH0vcGFydGljaXBhbnRzLyR7cGFydGljaXBhbnRJZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gZXhwaXJhZG8gb3UgaW52w6FsaWRvIGFvIHJlbW92ZXIgcGFydGljaXBhbnRlJyk7XHJcbiAgICAgICAgICBoYW5kbGVBdXRoRXJyb3IoKTtcclxuICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gcmVtb3ZlciBwYXJ0aWNpcGFudGU6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgIC8vIFNlbXByZSByZW1vdmVyIGRhIGxpc3RhIGxvY2FsIHF1YW5kbyBvIHVzdcOhcmlvIHNhaVxyXG4gICAgICAgIGlmIChwYXJ0aWNpcGFudElkID09PSB1c2VyPy5pZCkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coYFJlbW92ZW5kbyBjb252ZXJzYSAke2NvbnZlcnNhdGlvbklkfSBkYSBsaXN0YSBsb2NhbGApO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyBSZW1vdmVyIGRhIGxpc3RhIGxvY2FsXHJcbiAgICAgICAgICBzZXRDb252ZXJzYXRpb25zKHByZXYgPT4gcHJldi5maWx0ZXIoY29udiA9PiBjb252LmlkICE9PSBjb252ZXJzYXRpb25JZCkpO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAvLyBMaW1wYXIgbWVuc2FnZW5zXHJcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZCA9IHsgLi4ucHJldiB9O1xyXG4gICAgICAgICAgICBkZWxldGUgdXBkYXRlZFtjb252ZXJzYXRpb25JZF07XHJcbiAgICAgICAgICAgIHJldHVybiB1cGRhdGVkO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC8vIExpbXBhciBjYWNoZVxyXG4gICAgICAgICAgcmVxdWVzdENhY2hlLmNvbnZlcnNhdGlvbnMuZGF0YSA9IG51bGw7XHJcbiAgICAgICAgICByZXF1ZXN0Q2FjaGUuY29udmVyc2F0aW9ucy50aW1lc3RhbXAgPSAwO1xyXG4gICAgICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0pIHtcclxuICAgICAgICAgICAgZGVsZXRlIHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC8vIFNlIGVyYSBhIGNvbnZlcnNhIGF0aXZhLCBsaW1wYXJcclxuICAgICAgICAgIGlmIChhY3RpdmVDb252ZXJzYXRpb24gPT09IGNvbnZlcnNhdGlvbklkKSB7XHJcbiAgICAgICAgICAgIHNldEFjdGl2ZUNvbnZlcnNhdGlvbihudWxsKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gRm9yw6dhciByZWNhcnJlZ2FtZW50byBkYXMgY29udmVyc2FzIGFww7NzIHNhaXJcclxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBsb2FkQ29udmVyc2F0aW9ucyh0cnVlKTtcclxuICAgICAgICAgIH0sIDUwMCk7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIGNvbnNvbGUubG9nKGBDb252ZXJzYSAke2NvbnZlcnNhdGlvbklkfSByZW1vdmlkYSBkYSBsaXN0YSBsb2NhbGApO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAvLyBBdHVhbGl6YXIgYSBjb252ZXJzYSByZW1vdmVuZG8gbyBwYXJ0aWNpcGFudGVcclxuICAgICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiB7XHJcbiAgICAgICAgICAgIHJldHVybiBwcmV2Lm1hcChjb252ID0+IHtcclxuICAgICAgICAgICAgICBpZiAoY29udi5pZCA9PT0gY29udmVyc2F0aW9uSWQpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgIC4uLmNvbnYsXHJcbiAgICAgICAgICAgICAgICAgIHBhcnRpY2lwYW50czogY29udi5wYXJ0aWNpcGFudHMuZmlsdGVyKHAgPT4gcC51c2VySWQgIT09IHBhcnRpY2lwYW50SWQpXHJcbiAgICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICByZXR1cm4gY29udjtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAvLyBDcmlhciB1bWEgbWVuc2FnZW0gZG8gc2lzdGVtYSBwYXJhIG5vdGlmaWNhciBxdWUgdW0gcGFydGljaXBhbnRlIHNhaXVcclxuICAgICAgICAgIGlmIChzb2NrZXQgJiYgaXNDb25uZWN0ZWQgJiYgZGF0YS5kYXRhPy51c2VyPy5mdWxsTmFtZSkge1xyXG4gICAgICAgICAgICBjb25zdCBzeXN0ZW1NZXNzYWdlID0ge1xyXG4gICAgICAgICAgICAgIGNvbnZlcnNhdGlvbklkLFxyXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IGAke2RhdGEuZGF0YS51c2VyLmZ1bGxOYW1lfSBzYWl1IGRvIGdydXBvYCxcclxuICAgICAgICAgICAgICBjb250ZW50VHlwZTogJ1NZU1RFTSdcclxuICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgIHNvY2tldC5lbWl0KCdtZXNzYWdlOnNlbmQnLCBzeXN0ZW1NZXNzYWdlKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiBkYXRhLmRhdGE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyByZW1vdmVyIHBhcnRpY2lwYW50ZTonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG4gIH0sIFtzb2NrZXQsIGlzQ29ubmVjdGVkLCB1c2VyLCBhY3RpdmVDb252ZXJzYXRpb24sIGdldEN1cnJlbnRUb2tlbiwgaGFuZGxlQXV0aEVycm9yLCBsb2FkVW5yZWFkQ291bnRdKTtcclxuXHJcbiAgLy8gTWFyY2FyIG1lbnNhZ2VucyBjb21vIGxpZGFzXHJcbiAgY29uc3QgbWFya01lc3NhZ2VzQXNSZWFkID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbnZlcnNhdGlvbklkLCBtZXNzYWdlSWQpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghdXNlcikgcmV0dXJuO1xyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSByZXR1cm47XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9VUkx9L2NoYXQvbWVzc2FnZXMvbWFyay1yZWFkYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7Y3VycmVudFRva2VufWBcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgY29udmVyc2F0aW9uSWQsIG1lc3NhZ2VJZCB9KVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIC8vIE1hcmNhciBjb252ZXJzYSBjb21vIGxpZGEgbG9jYWxtZW50ZVxyXG4gICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiBwcmV2Lm1hcChjb252ID0+XHJcbiAgICAgICAgICBjb252LmlkID09PSBjb252ZXJzYXRpb25JZCA/IHsgLi4uY29udiwgaGFzVW5yZWFkOiBmYWxzZSB9IDogY29udlxyXG4gICAgICAgICkpO1xyXG5cclxuICAgICAgICAvLyBEZWNyZW1lbnRhciBjb250YWRvciBkZSBjb252ZXJzYXMgbsOjbyBsaWRhcyBzZSBhIGNvbnZlcnNhIGVzdGF2YSBtYXJjYWRhIGNvbW8gbsOjbyBsaWRhXHJcbiAgICAgICAgc2V0VW5yZWFkQ291bnQocHJldiA9PiB7XHJcbiAgICAgICAgICBjb25zdCBjb252ID0gY29udmVyc2F0aW9ucy5maW5kKGMgPT4gYy5pZCA9PT0gY29udmVyc2F0aW9uSWQpO1xyXG4gICAgICAgICAgY29uc3Qgd2FzVW5yZWFkID0gY29udj8uaGFzVW5yZWFkIHx8IGNvbnY/LnVucmVhZENvdW50ID4gMDtcclxuICAgICAgICAgIHJldHVybiB3YXNVbnJlYWQgPyBNYXRoLm1heCgwLCBwcmV2IC0gMSkgOiBwcmV2O1xyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBtYXJraW5nIG1lc3NhZ2VzIGFzIHJlYWQ6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW4sIGNvbnZlcnNhdGlvbnNdKTtcclxuXHJcbiAgLy8gUmVzZXRhciBjb250YWRvciBkZSBtZW5zYWdlbnMgbsOjbyBsaWRhc1xyXG4gIGNvbnN0IHJlc2V0VW5yZWFkQ291bnQgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoIXVzZXIpIHJldHVybjtcclxuICAgICAgXHJcbiAgICAgIGNvbnN0IGN1cnJlbnRUb2tlbiA9IGdldEN1cnJlbnRUb2tlbigpO1xyXG4gICAgICBpZiAoIWN1cnJlbnRUb2tlbikgcmV0dXJuO1xyXG4gICAgICBcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L21lc3NhZ2VzL3Jlc2V0LXVucmVhZGAsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke2N1cnJlbnRUb2tlbn1gXHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHNldFVucmVhZENvdW50KDApO1xyXG4gICAgICAgIHNldENvbnZlcnNhdGlvbnMocHJldiA9PiBwcmV2Lm1hcChjb252ID0+ICh7IC4uLmNvbnYsIHVucmVhZENvdW50OiAwIH0pKSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlc2V0dGluZyB1bnJlYWQgY291bnQ6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH0sIFt1c2VyLCBnZXRDdXJyZW50VG9rZW5dKTtcclxuXHJcbiAgLy8gQXBhZ2FyIG1lbnNhZ2Vuc1xyXG4gIGNvbnN0IGRlbGV0ZU1lc3NhZ2VzID0gdXNlQ2FsbGJhY2soYXN5bmMgKG1lc3NhZ2VJZHMsIGNvbnZlcnNhdGlvbklkKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoIW1lc3NhZ2VJZHMgfHwgbWVzc2FnZUlkcy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdJRHMgZGFzIG1lbnNhZ2VucyBzw6NvIG9icmlnYXTDs3Jpb3MnKTtcclxuICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgY3VycmVudFRva2VuID0gZ2V0Q3VycmVudFRva2VuKCk7XHJcbiAgICAgIGlmICghY3VycmVudFRva2VuKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gbsOjbyBkaXNwb27DrXZlbCBhbyBhcGFnYXIgbWVuc2FnZW5zJyk7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBbXTtcclxuICAgICAgY29uc3QgZXJyb3JzID0gW107XHJcblxyXG4gICAgICAvLyBBcGFnYXIgbWVuc2FnZW5zIHVtYSBwb3IgdW1hXHJcbiAgICAgIGZvciAoY29uc3QgbWVzc2FnZUlkIG9mIG1lc3NhZ2VJZHMpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfVVJMfS9jaGF0L21lc3NhZ2VzLyR7bWVzc2FnZUlkfWAsIHtcclxuICAgICAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHtjdXJyZW50VG9rZW59YFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIGV4cGlyYWRvIG91IGludsOhbGlkbyBhbyBhcGFnYXIgbWVuc2FnZW0nKTtcclxuICAgICAgICAgICAgICBoYW5kbGVBdXRoRXJyb3IoKTtcclxuICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gYXBhZ2FyIG1lbnNhZ2VtOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG5cclxuICAgICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcclxuICAgICAgICAgICAgcmVzdWx0cy5wdXNoKGRhdGEuZGF0YSk7XHJcblxyXG4gICAgICAgICAgICAvLyBBdHVhbGl6YXIgbyBlc3RhZG8gZGFzIG1lbnNhZ2Vuc1xyXG4gICAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcclxuICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkTWVzc2FnZXMgPSB7IC4uLnByZXYgfTtcclxuXHJcbiAgICAgICAgICAgICAgLy8gUGVyY29ycmVyIHRvZGFzIGFzIGNvbnZlcnNhc1xyXG4gICAgICAgICAgICAgIE9iamVjdC5rZXlzKHVwZGF0ZWRNZXNzYWdlcykuZm9yRWFjaChjb252SWQgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gQXR1YWxpemFyIGEgbWVuc2FnZW0gbmEgY29udmVyc2EgY29ycmVzcG9uZGVudGVcclxuICAgICAgICAgICAgICAgIHVwZGF0ZWRNZXNzYWdlc1tjb252SWRdID0gdXBkYXRlZE1lc3NhZ2VzW2NvbnZJZF0ubWFwKG1zZyA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChtc2cuaWQgPT09IG1lc3NhZ2VJZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAuLi5tc2csXHJcbiAgICAgICAgICAgICAgICAgICAgICAuLi5kYXRhLmRhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgICBpc0RlbGV0ZWQ6IHRydWVcclxuICAgICAgICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiBtc2c7XHJcbiAgICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgcmV0dXJuIHVwZGF0ZWRNZXNzYWdlcztcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBlcnJvcnMucHVzaCh7IG1lc3NhZ2VJZCwgZXJyb3I6ICdGYWxoYSBhbyBhcGFnYXIgbWVuc2FnZW0nIH0pO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvIGFvIGFwYWdhciBtZW5zYWdlbSAke21lc3NhZ2VJZH06YCwgZXJyb3IpO1xyXG4gICAgICAgICAgZXJyb3JzLnB1c2goeyBtZXNzYWdlSWQsIGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdFcnJvIGRlc2NvbmhlY2lkbycgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBMaW1wYXIgbyBjYWNoZSBkZSBtZW5zYWdlbnMgcGFyYSBmb3LDp2FyIHVtYSBub3ZhIGJ1c2NhXHJcbiAgICAgIGlmIChjb252ZXJzYXRpb25JZCAmJiByZXN1bHRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnTGltcGFuZG8gY2FjaGUgZGUgbWVuc2FnZW5zIHBhcmEgYSBjb252ZXJzYTonLCBjb252ZXJzYXRpb25JZCk7XHJcbiAgICAgICAgaWYgKHJlcXVlc3RDYWNoZS5tZXNzYWdlc1tjb252ZXJzYXRpb25JZF0pIHtcclxuICAgICAgICAgIGRlbGV0ZSByZXF1ZXN0Q2FjaGUubWVzc2FnZXNbY29udmVyc2F0aW9uSWRdO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gUmVjYXJyZWdhciBhcyBtZW5zYWdlbnMgZGEgY29udmVyc2EgcGFyYSBnYXJhbnRpciBxdWUgZXN0YW1vcyBzaW5jcm9uaXphZG9zIGNvbSBvIGJhY2tlbmRcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1JlY2FycmVnYW5kbyBtZW5zYWdlbnMgYXDDs3MgZXhjbHVzw6NvJyk7XHJcbiAgICAgICAgICBhd2FpdCBsb2FkTWVzc2FnZXMoY29udmVyc2F0aW9uSWQpO1xyXG5cclxuICAgICAgICAgIC8vIFRhbWLDqW0gcmVjYXJyZWdhciBhIGxpc3RhIGRlIGNvbnZlcnNhcyBwYXJhIGdhcmFudGlyIHF1ZSB0dWRvIGVzdMOhIGF0dWFsaXphZG9cclxuICAgICAgICAgIGF3YWl0IGxvYWRDb252ZXJzYXRpb25zKHRydWUpO1xyXG4gICAgICAgIH0gY2F0Y2ggKHJlbG9hZEVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIHJlY2FycmVnYXIgbWVuc2FnZW5zIGFww7NzIGV4Y2x1c8OjbzonLCByZWxvYWRFcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IHJlc3VsdHMubGVuZ3RoID4gMCxcclxuICAgICAgICBkZWxldGVkOiByZXN1bHRzLFxyXG4gICAgICAgIGVycm9yczogZXJyb3JzLFxyXG4gICAgICAgIHRvdGFsOiBtZXNzYWdlSWRzLmxlbmd0aCxcclxuICAgICAgICBzdWNjZXNzQ291bnQ6IHJlc3VsdHMubGVuZ3RoLFxyXG4gICAgICAgIGVycm9yQ291bnQ6IGVycm9ycy5sZW5ndGhcclxuICAgICAgfTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gYXBhZ2FyIG1lbnNhZ2VuczonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgICAgZGVsZXRlZDogW10sXHJcbiAgICAgICAgZXJyb3JzOiBbeyBlcnJvcjogZXJyb3IubWVzc2FnZSB8fCAnRXJybyBkZXNjb25oZWNpZG8nIH1dLFxyXG4gICAgICAgIHRvdGFsOiBtZXNzYWdlSWRzLmxlbmd0aCxcclxuICAgICAgICBzdWNjZXNzQ291bnQ6IDAsXHJcbiAgICAgICAgZXJyb3JDb3VudDogbWVzc2FnZUlkcy5sZW5ndGhcclxuICAgICAgfTtcclxuICAgIH1cclxuICB9LCBbZ2V0Q3VycmVudFRva2VuLCBoYW5kbGVBdXRoRXJyb3IsIGxvYWRNZXNzYWdlcywgbG9hZENvbnZlcnNhdGlvbnNdKTtcclxuXHJcbiAgLy8gTWVtb2l6YXIgbyB2YWxvciBkbyBjb250ZXh0byBwYXJhIGV2aXRhciByZS1yZW5kZXJpemHDp8O1ZXMgZGVzbmVjZXNzw6FyaWFzXHJcbiAgY29uc3QgY29udGV4dFZhbHVlID0gdXNlTWVtbygoKSA9PiAoe1xyXG4gICAgY29udmVyc2F0aW9ucyxcclxuICAgIG1lc3NhZ2VzLFxyXG4gICAgdW5yZWFkQ291bnQsXHJcbiAgICBhY3RpdmVDb252ZXJzYXRpb24sXHJcbiAgICBpc1BhbmVsT3BlbixcclxuICAgIGlzTW9kYWxPcGVuLFxyXG4gICAgaXNDb25uZWN0ZWQsXHJcbiAgICBpc0xvYWRpbmcsXHJcbiAgICBzZXRBY3RpdmVDb252ZXJzYXRpb24sXHJcbiAgICBsb2FkTWVzc2FnZXMsXHJcbiAgICBzZW5kTWVzc2FnZSxcclxuICAgIGNyZWF0ZUNvbnZlcnNhdGlvbixcclxuICAgIGNyZWF0ZU9yR2V0Q29udmVyc2F0aW9uLFxyXG4gICAgdG9nZ2xlQ2hhdFBhbmVsLFxyXG4gICAgdG9nZ2xlQ2hhdE1vZGFsLFxyXG4gICAgbG9hZENvbnZlcnNhdGlvbnMsXHJcbiAgICBsb2FkVW5yZWFkQ291bnQsXHJcbiAgICBtYXJrTWVzc2FnZXNBc1JlYWQsXHJcbiAgICByZXNldFVucmVhZENvdW50LFxyXG4gICAgYWRkUGFydGljaXBhbnRUb0dyb3VwLFxyXG4gICAgYWRkTXVsdGlwbGVQYXJ0aWNpcGFudHNUb0dyb3VwLFxyXG4gICAgcmVtb3ZlUGFydGljaXBhbnRGcm9tR3JvdXAsXHJcbiAgICBkZWxldGVNZXNzYWdlc1xyXG4gIH0pLCBbXHJcbiAgICBjb252ZXJzYXRpb25zLFxyXG4gICAgbWVzc2FnZXMsXHJcbiAgICB1bnJlYWRDb3VudCxcclxuICAgIGFjdGl2ZUNvbnZlcnNhdGlvbixcclxuICAgIGlzUGFuZWxPcGVuLFxyXG4gICAgaXNNb2RhbE9wZW4sXHJcbiAgICBpc0Nvbm5lY3RlZCxcclxuICAgIGlzTG9hZGluZyxcclxuICAgIHNldEFjdGl2ZUNvbnZlcnNhdGlvbixcclxuICAgIGxvYWRNZXNzYWdlcyxcclxuICAgIHNlbmRNZXNzYWdlLFxyXG4gICAgY3JlYXRlQ29udmVyc2F0aW9uLFxyXG4gICAgY3JlYXRlT3JHZXRDb252ZXJzYXRpb24sXHJcbiAgICB0b2dnbGVDaGF0UGFuZWwsXHJcbiAgICB0b2dnbGVDaGF0TW9kYWwsXHJcbiAgICBsb2FkQ29udmVyc2F0aW9ucyxcclxuICAgIGxvYWRVbnJlYWRDb3VudCxcclxuICAgIG1hcmtNZXNzYWdlc0FzUmVhZCxcclxuICAgIHJlc2V0VW5yZWFkQ291bnQsXHJcbiAgICBhZGRQYXJ0aWNpcGFudFRvR3JvdXAsXHJcbiAgICBhZGRNdWx0aXBsZVBhcnRpY2lwYW50c1RvR3JvdXAsXHJcbiAgICByZW1vdmVQYXJ0aWNpcGFudEZyb21Hcm91cCxcclxuICAgIGRlbGV0ZU1lc3NhZ2VzXHJcbiAgXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Q2hhdENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e2NvbnRleHRWYWx1ZX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvQ2hhdENvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VDaGF0ID0gKCkgPT4ge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KENoYXRDb250ZXh0KTtcclxuXHJcbiAgaWYgKCFjb250ZXh0KSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUNoYXQgZGV2ZSBzZXIgdXNhZG8gZGVudHJvIGRlIHVtIENoYXRQcm92aWRlcicpO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIGNvbnRleHQ7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZU1lbW8iLCJ1c2VSZWYiLCJ1c2VBdXRoIiwiaW8iLCJBUElfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJyZXF1ZXN0Q2FjaGUiLCJjb252ZXJzYXRpb25zIiwiZGF0YSIsInRpbWVzdGFtcCIsInVucmVhZENvdW50IiwibWVzc2FnZXMiLCJ0b2tlbkNoZWNrIiwidmFsaWQiLCJDQUNIRV9FWFBJUkFUSU9OIiwiVE9LRU5fQ0hFQ0tfRVhQSVJBVElPTiIsImRlYm91bmNlIiwiZnVuYyIsIndhaXQiLCJ0aW1lb3V0IiwiZXhlY3V0ZWRGdW5jdGlvbiIsImFyZ3MiLCJsYXRlciIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJDaGF0Q29udGV4dCIsIkNoYXRQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsImxvZ291dCIsInNvY2tldCIsInNldFNvY2tldCIsImlzQ29ubmVjdGVkIiwic2V0SXNDb25uZWN0ZWQiLCJzZXRDb252ZXJzYXRpb25zIiwiYWN0aXZlQ29udmVyc2F0aW9uIiwic2V0QWN0aXZlQ29udmVyc2F0aW9uIiwic2V0TWVzc2FnZXMiLCJzZXRVbnJlYWRDb3VudCIsImlzUGFuZWxPcGVuIiwic2V0SXNQYW5lbE9wZW4iLCJpc01vZGFsT3BlbiIsInNldElzTW9kYWxPcGVuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZ2V0Q3VycmVudFRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImhhbmRsZUF1dGhFcnJvciIsImNvbnNvbGUiLCJlcnJvciIsImRpc2Nvbm5lY3QiLCJzb2NrZXRJbml0aWFsaXplZFJlZiIsImlzTG9hZGluZ1JlZiIsImxhc3RJbml0QXR0ZW1wdFJlZiIsInJlY29ubmVjdEF0dGVtcHRzUmVmIiwiY3VycmVudCIsImxvZyIsImZ1bGxOYW1lIiwia2V5c1RvUmVtb3ZlIiwiaSIsImxlbmd0aCIsImtleSIsInN0YXJ0c1dpdGgiLCJwdXNoIiwiZm9yRWFjaCIsInJlbW92ZUl0ZW0iLCJpZCIsInJvbGUiLCJEYXRlIiwibm93IiwiaW5pdGlhbGl6ZVdlYlNvY2tldCIsImN1cnJlbnRUb2tlbiIsImNvbm5lY3RlZCIsImNvbm5lY3QiLCJkaXNjb25uZWN0RXJyb3IiLCJtYXhSZWNvbm5lY3RBdHRlbXB0cyIsInJlY29ubmVjdERlbGF5Iiwic29ja2V0SW5zdGFuY2UiLCJwYXRoIiwiYXV0aCIsInRva2VuIiwidHJhbnNwb3J0cyIsInJlY29ubmVjdGlvbkF0dGVtcHRzIiwicmVjb25uZWN0aW9uRGVsYXkiLCJhdXRvQ29ubmVjdCIsInJlY29ubmVjdGlvbiIsIm9uIiwid2luZG93IiwiZGlzcGF0Y2hFdmVudCIsIkN1c3RvbUV2ZW50IiwiZGV0YWlsIiwidHlwZSIsInN0YXR1cyIsIm1lc3NhZ2UiLCJpbmNsdWRlcyIsImNvbnZlcnNhdGlvbklkIiwidXNlcklkIiwidGVtcElkIiwiaXNDdXJyZW50VXNlck1lc3NhZ2UiLCJzZW5kZXJJZCIsInByZXYiLCJjb252ZXJzYXRpb25NZXNzYWdlcyIsInNvbWUiLCJtIiwibWFwIiwic2VuZGVyQ2xpZW50SWQiLCJ0ZW1wTWVzc2FnZXMiLCJmaWx0ZXIiLCJpc1RlbXAiLCJjcmVhdGVkQXQiLCJjb250ZW50VHlwZSIsImNvbnRlbnQiLCJ0ZW1wTWVzc2FnZSIsInVwZGF0ZWRNZXNzYWdlcyIsInNvcnQiLCJhIiwiYiIsImNvbnZlcnNhdGlvbiIsImZpbmQiLCJjIiwibGFzdE1lc3NhZ2UiLCJ1cGRhdGVkQ29udmVyc2F0aW9ucyIsImlzRnJvbUN1cnJlbnRVc2VyIiwid2FzQWxyZWFkeVVucmVhZCIsImhhc1VucmVhZCIsImNvbnYiLCJuZXdDb3VudCIsInRvdGFsVW5yZWFkQ29udmVyc2F0aW9ucyIsInRvdGFsVW5yZWFkIiwiQXJyYXkiLCJpc0FycmF5IiwidW5yZWFkSW5mbyIsIml0ZW0iLCJjb252ZXJzYXRpb25zQXJyYXkiLCJpbml0aWFsRGF0YUxvYWRlZFJlZiIsImxvYWRDb252ZXJzYXRpb25zIiwicGFydGljaXBhbnRJZCIsInVwZGF0ZWQiLCJwYXJ0aWNpcGFudHMiLCJwIiwiY2xpZW50SWQiLCJhY3Rpb24iLCJpbml0aWFsaXphdGlvbkluUHJvZ3Jlc3MiLCJzYWZlSW5pdGlhbGl6ZSIsInRoZW4iLCJyZXN1bHQiLCJjYXRjaCIsImZpbmFsbHkiLCJmb3JjZVJlZnJlc2giLCJyZXNwb25zZSIsImZldGNoIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJzdGF0dXNUZXh0Iiwib2siLCJFcnJvciIsImpzb24iLCJzdWNjZXNzIiwiSlNPTiIsInN0cmluZ2lmeSIsInByb2Nlc3NlZENvbnZlcnNhdGlvbnMiLCJzb3J0ZWRNZXNzYWdlcyIsImNvbnZlcnNhdGlvbldpdGhvdXRNZXNzYWdlcyIsImNsaWVudCIsImxvYWRNZXNzYWdlcyIsImxvYWRpbmciLCJzZW5kTWVzc2FnZSIsIm1ldGFkYXRhIiwiaXNDbGllbnQiLCJ0b0lTT1N0cmluZyIsInNlbmRlciIsInNlbmRlckNsaWVudCIsImxvZ2luIiwibWVzc2FnZURhdGEiLCJlbWl0IiwibXNnIiwiZmFpbGVkIiwiY3JlYXRlQ29udmVyc2F0aW9uIiwicGFydGljaXBhbnRJZHMiLCJ0aXRsZSIsInBhcnRpY2lwYW50UHJvbWlzZXMiLCJ1c2VyRGF0YSIsIlByb21pc2UiLCJhbGwiLCJ2YWxpZFBhcnRpY2lwYW50cyIsImNvbXBhbnlJZCIsImNvbXBhbmllcyIsIlNldCIsImFsZXJ0IiwidmFsaWRhdGlvbkVycm9yIiwiaGFzQ2xpZW50UGFydGljaXBhbnRzIiwibWV0aG9kIiwiYm9keSIsImluY2x1ZGVDbGllbnRzIiwiY29udmVyc2F0aW9uRGF0YSIsImNyZWF0ZU9yR2V0Q29udmVyc2F0aW9uIiwib3RoZXJVc2VyIiwiZXhpc3RpbmdDb252ZXJzYXRpb24iLCJuZXdDb252ZXJzYXRpb24iLCJsb2FkVW5yZWFkQ291bnQiLCJjb252ZXJzYXRpb25JZHMiLCJldmVyeSIsImxvYWRJbml0aWFsRGF0YSIsImNvbnZlcnNhdGlvbnNEYXRhIiwiZGVib3VuY2VkTG9hZERhdGEiLCJjb252ZXJzYXRpb25FeGlzdHMiLCJ3YXJuIiwidG9rZW5DaGVja0ludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwidG9nZ2xlQ2hhdFBhbmVsIiwibmV3U3RhdGUiLCJ0b2dnbGVDaGF0TW9kYWwiLCJhZGRQYXJ0aWNpcGFudFRvR3JvdXAiLCJlcnJvclRleHQiLCJ0ZXh0IiwicGFydGljaXBhbnRFeGlzdHMiLCJzeXN0ZW1NZXNzYWdlIiwiYWRkTXVsdGlwbGVQYXJ0aWNpcGFudHNUb0dyb3VwIiwicmVzdWx0cyIsImVycm9ycyIsInBhcnRpY2lwYW50IiwiYWRkZWQiLCJ0b3RhbCIsInN1Y2Nlc3NDb3VudCIsImVycm9yQ291bnQiLCJyZW1vdmVQYXJ0aWNpcGFudEZyb21Hcm91cCIsIm1hcmtNZXNzYWdlc0FzUmVhZCIsIm1lc3NhZ2VJZCIsIndhc1VucmVhZCIsIk1hdGgiLCJtYXgiLCJyZXNldFVucmVhZENvdW50IiwiZGVsZXRlTWVzc2FnZXMiLCJtZXNzYWdlSWRzIiwiT2JqZWN0Iiwia2V5cyIsImNvbnZJZCIsImlzRGVsZXRlZCIsInJlbG9hZEVycm9yIiwiZGVsZXRlZCIsImNvbnRleHRWYWx1ZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VDaGF0IiwiY29udGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.js\n"));

/***/ })

});