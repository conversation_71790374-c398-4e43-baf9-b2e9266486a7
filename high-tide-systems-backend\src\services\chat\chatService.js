// src/services/chat/chatService.js
const prisma = require('../../utils/prisma');
const { NotFoundError, ForbiddenError } = require('../../utils/errors');

// Client select with person data
const clientSelectWithPerson = {
  id: true,
  login: true,
  email: true,
  fullName: true,
  clientPersons: {
    take: 1,
    select: {
      person: {
        select: {
          fullName: true
        }
      }
    }
  }
};

/**
 * Cria uma nova conversa
 * @param {Object} data - Dados da conversa
 * @param {string} userId - ID do usuário que está criando a conversa
 * @returns {Promise<Object>} - Conversa criada
 */
const createConversation = async (data, userId) => {
  try {
    // Validar se todos os participantes são da mesma empresa
    let creator = await prisma.user.findUnique({
      where: { id: userId },
      select: { companyId: true, branchId: true, role: true }
    });

    // Se não encontrou como usuário, verificar se é cliente
    if (!creator) {
      const client = await prisma.client.findUnique({
        where: { id: userId },
        select: { companyId: true }
      });
      
      if (client) {
        creator = {
          companyId: client.companyId,
          branchId: null,
          role: 'CLIENT'
        };
      } else {
        throw new ForbiddenError('Usuário não encontrado');
      }
    }
    
    // Verificar se includeClients está habilitado
    const includeClients = data.includeClients === true;
    
    // Separar participantes entre usuários e clientes
    const userParticipants = [];
    const clientParticipants = [];
    
    if (data.participantIds && data.participantIds.length > 0) {
      for (const participantId of data.participantIds) {
        // Verificar se é um usuário
        const user = await prisma.user.findUnique({
          where: { id: participantId },
          select: { id: true, companyId: true }
        });
        
        if (user) {
          userParticipants.push(participantId);
        } else if (includeClients) {
          // Verificar se é um cliente
          const client = await prisma.client.findUnique({
            where: { id: participantId },
            select: { id: true, companyId: true }
          });
          
          if (client) {
            clientParticipants.push(participantId);
          }
        }
      }
    }

    // System Admin pode criar conversas mesmo sem empresa associada
    const isSystemAdmin = creator.role === 'SYSTEM_ADMIN';

    // Para outros usuários, verificar se tem empresa associada
    if (!isSystemAdmin && !creator.companyId) {
      throw new ForbiddenError('Usuário não possui empresa associada');
    }

    // Se a conversa for restrita a uma unidade e o usuário não for system admin, verificar se todos os participantes são da mesma unidade
    if (!isSystemAdmin && data.branchId) {
      const participantsFromOtherBranches = await prisma.user.count({
        where: {
          id: { in: data.participantIds || [] },
          OR: [
            { branchId: { not: data.branchId } },
            { branchId: null }
          ]
        }
      });

      if (participantsFromOtherBranches > 0) {
        throw new ForbiddenError('Alguns participantes não pertencem à mesma unidade');
      }
    }

    // Verificar se já existe uma conversa individual entre os mesmos participantes
    if (data.type === 'INDIVIDUAL' && data.participantIds?.length === 1) {
      const existingConversation = await findIndividualConversation(userId, data.participantIds[0]);

      if (existingConversation) {
        return existingConversation;
      }
    }

    // Buscar a primeira empresa disponível para system admin
    let companyId = creator.companyId;

    // Se for system admin e não tiver empresa associada, buscar a primeira empresa disponível
    if (isSystemAdmin && !companyId) {
      const firstCompany = await prisma.company.findFirst({
        where: { active: true },
        select: { id: true }
      });

      if (!firstCompany) {
        throw new ForbiddenError('Não há empresas disponíveis para criar a conversa');
      }

      companyId = firstCompany.id;
    }

    // Criar a conversa
    const conversation = await prisma.conversation.create({
      data: {
        type: data.type,
        title: data.title,
        companyId: companyId, // Usar a empresa do usuário ou a primeira empresa disponível
        branchId: isSystemAdmin ? null : (data.branchId || creator.branchId),
        createdById: creator.role === 'CLIENT' ? null : userId,
        participants: {
          create: [
            // Adicionar o criador como participante e admin
            creator.role === 'CLIENT' ? {
              clientId: userId,
              isAdmin: true
            } : {
              userId: userId,
              isAdmin: true
            },
            // Adicionar participantes usuários
            ...userParticipants.map(participantId => ({
              userId: participantId,
              isAdmin: false
            })),
            // Adicionar participantes clientes
            ...clientParticipants.map(participantId => ({
              clientId: participantId,
              isAdmin: false
            }))
          ]
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      }
    });

    return conversation;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
};

/**
 * Busca uma conversa individual entre dois usuários
 * @param {string} userId1 - ID do primeiro usuário
 * @param {string} userId2 - ID do segundo usuário
 * @returns {Promise<Object|null>} - Conversa encontrada ou null
 */
const findIndividualConversation = async (userId1, userId2) => {
  try {
    // Buscar todas as conversas individuais do usuário 1
    const user1Conversations = await prisma.conversation.findMany({
      where: {
        type: 'INDIVIDUAL',
        participants: {
          some: {
            OR: [
              { userId: userId1, leftAt: null },
              { clientId: userId1, leftAt: null }
            ]
          }
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      }
    });

    // Filtrar as conversas que também têm o usuário 2 como participante
    const sharedConversation = user1Conversations.find(conversation => {
      return conversation.participants.some(participant =>
        (participant.userId === userId2 || participant.clientId === userId2) && participant.leftAt === null
      );
    });

    return sharedConversation || null;
  } catch (error) {
    console.error('Error finding individual conversation:', error);
    throw error;
  }
};

/**
 * Busca todas as conversas de um usuário
 * @param {string} userId - ID do usuário
 * @param {Object} filters - Filtros de busca
 * @returns {Promise<Array>} - Lista de conversas
 */
const getUserConversations = async (userId, filters = {}) => {
  try {
    const { search, limit = 20, offset = 0, includeMessages = true } = filters;

    // Buscar o usuário para verificar a empresa e a role
    let user = await prisma.user.findUnique({
      where: { id: userId },
      select: { companyId: true, role: true }
    });

    let isClient = false;
    
    // Se não encontrou como usuário, verificar se é cliente
    if (!user) {
      const client = await prisma.client.findUnique({
        where: { id: userId },
        select: { companyId: true }
      });
      
      if (client) {
        user = { companyId: client.companyId, role: 'CLIENT' };
        isClient = true;
      } else {
        throw new ForbiddenError('Usuário não encontrado');
      }
    }

    // System Admin pode acessar conversas mesmo sem empresa associada
    const isSystemAdmin = user.role === 'SYSTEM_ADMIN';

    // Para outros usuários, verificar se tem empresa associada
    if (!isSystemAdmin && !user.companyId) {
      throw new ForbiddenError('Usuário não possui empresa associada');
    }

    // Construir a query base - apenas conversas onde o usuário ainda é participante ativo
    const where = {
      AND: [
        {
          participants: {
            some: isClient ? {
              clientId: userId,
              leftAt: null
            } : {
              userId,
              leftAt: null
            }
          }
        },
        {
          isActive: true // Apenas conversas ativas
        },
        {
          // Garantir que a conversa tem pelo menos um participante ativo
          participants: {
            some: {
              leftAt: null
            }
          }
        }
      ]
    };

    // Removendo a filtragem por empresa para permitir que usuários vejam todas as conversas em que são participantes
    // Usuários devem poder ver todas as conversas em que são participantes, independentemente da empresa
    // A restrição por empresa só deve ser aplicada ao criar novas conversas

    // Adicionar filtro de busca se fornecido
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        {
          participants: {
            some: {
              user: {
                fullName: { contains: search, mode: 'insensitive' }
              }
            }
          }
        },
        {
          participants: {
            some: {
              client: {
                OR: [
                  { login: { contains: search, mode: 'insensitive' } },
                  { fullName: { contains: search, mode: 'insensitive' } }
                ]
              }
            }
          }
        }
      ];
    }

    // Buscar as conversas
    const conversations = await prisma.conversation.findMany({
      where,
      orderBy: {
        lastMessageAt: 'desc'
      },
      skip: offset,
      take: limit,
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        },
        ...(includeMessages ? {
          messages: {
            where: {
              isDeleted: false
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 1,
            include: {
              sender: {
                select: {
                  id: true,
                  fullName: true
                }
              },
              senderClient: {
                select: clientSelectWithPerson
              }
            }
          }
        } : {})
      }
    });

    // Contar o total de conversas
    const total = await prisma.conversation.count({ where });

    // Adicionar informação de hasUnread para cada conversa
    const conversationsWithUnreadStatus = await Promise.all(
      conversations.map(async (conversation) => {
        try {
          // Encontrar a participação do usuário atual nesta conversa
          const participation = await prisma.conversationParticipant.findFirst({
            where: {
              conversationId: conversation.id,
              ...(isClient ? { clientId: userId } : { userId }),
              leftAt: null
            }
          });

          if (!participation) {
            return { ...conversation, hasUnread: false };
          }

          // Verificar se há mensagens não lidas nesta conversa
          let hasUnreadMessages = false;

          if (participation.lastReadMessageId) {
            // Se há uma última mensagem lida, verificar se há mensagens mais recentes de outros usuários
            const unreadCount = await prisma.message.count({
              where: {
                conversationId: conversation.id,
                createdAt: {
                  gt: (await prisma.message.findUnique({
                    where: { id: participation.lastReadMessageId },
                    select: { createdAt: true }
                  }))?.createdAt || new Date(0)
                },
                isDeleted: false,
                AND: [
                  {
                    OR: [
                      { senderId: { not: userId } },
                      { senderId: null }
                    ]
                  },
                  {
                    OR: [
                      { senderClientId: { not: userId } },
                      { senderClientId: null }
                    ]
                  }
                ]
              }
            });

            hasUnreadMessages = unreadCount > 0;
          } else {
            // Se não há última mensagem lida, verificar se há mensagens de outros usuários
            const unreadCount = await prisma.message.count({
              where: {
                conversationId: conversation.id,
                isDeleted: false,
                AND: [
                  {
                    OR: [
                      { senderId: { not: userId } },
                      { senderId: null }
                    ]
                  },
                  {
                    OR: [
                      { senderClientId: { not: userId } },
                      { senderClientId: null }
                    ]
                  }
                ]
              }
            });

            hasUnreadMessages = unreadCount > 0;
          }

          return { ...conversation, hasUnread: hasUnreadMessages };
        } catch (error) {
          console.error(`Erro ao verificar status não lido para conversa ${conversation.id}:`, error);
          return { ...conversation, hasUnread: false };
        }
      })
    );

    return {
      conversations: conversationsWithUnreadStatus,
      total,
      limit,
      offset
    };
  } catch (error) {
    console.error('Error getting user conversations:', error);
    throw error;
  }
};

/**
 * Busca uma conversa pelo ID
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário que está buscando a conversa
 * @returns {Promise<Object>} - Conversa encontrada
 */
const getConversationById = async (conversationId, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é participante da conversa (usuário ou cliente)
    const isParticipant = conversation.participants.some(
      participant => (participant.userId === userId || participant.clientId === userId) && participant.leftAt === null
    );

    if (!isParticipant) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }

    return conversation;
  } catch (error) {
    console.error('Error getting conversation by ID:', error);
    throw error;
  }
};

/**
 * Adiciona um participante a uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} participantId - ID do usuário a ser adicionado
 * @param {string} userId - ID do usuário que está adicionando o participante
 * @returns {Promise<Object>} - Participante adicionado
 */
const addParticipant = async (conversationId, participantId, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: true
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é administrador da conversa
    const isAdmin = conversation.participants.some(
      participant => participant.userId === userId && participant.isAdmin && participant.leftAt === null
    );

    if (!isAdmin) {
      throw new ForbiddenError('Apenas administradores podem adicionar participantes');
    }

    // Verificar se a conversa é do tipo GROUP
    if (conversation.type !== 'GROUP') {
      throw new ForbiddenError('Apenas conversas em grupo podem ter participantes adicionados');
    }

    // Verificar se o participante já está na conversa
    const existingParticipant = conversation.participants.find(
      participant => participant.userId === participantId
    );

    if (existingParticipant) {
      // Se o participante saiu da conversa, reativá-lo
      if (existingParticipant.leftAt) {
        return prisma.conversationParticipant.update({
          where: { id: existingParticipant.id },
          data: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        });
      }

      throw new ForbiddenError('Usuário já é participante desta conversa');
    }

    // Verificar se o participante é da mesma empresa
    const [conversationUser, newParticipant] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true, branchId: true, role: true }
      }),
      prisma.user.findUnique({
        where: { id: participantId },
        select: { companyId: true, branchId: true }
      })
    ]);

    if (!newParticipant) {
      throw new NotFoundError('Usuário não encontrado');
    }

    // System Admin pode adicionar qualquer usuário
    const isSystemAdmin = conversationUser.role === 'SYSTEM_ADMIN';

    // Para outros usuários, verificar se o participante é da mesma empresa
    if (!isSystemAdmin && newParticipant.companyId !== conversationUser.companyId) {
      throw new ForbiddenError('Usuário não pertence à mesma empresa');
    }

    // Se a conversa for restrita a uma unidade e o usuário não for system admin, verificar se o participante é da mesma unidade
    if (!isSystemAdmin && conversation.branchId && newParticipant.branchId !== conversation.branchId) {
      throw new ForbiddenError('Usuário não pertence à mesma unidade');
    }

    // Adicionar o participante
    const participant = await prisma.conversationParticipant.create({
      data: {
        conversationId,
        userId: participantId,
        isAdmin: false
      },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
            profileImageUrl: true
          }
        }
      }
    });

    return participant;
  } catch (error) {
    console.error('Error adding participant:', error);
    throw error;
  }
};

/**
 * Remove um participante de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} participantId - ID do usuário a ser removido
 * @param {string} userId - ID do usuário que está removendo o participante
 * @returns {Promise<Object>} - Participante removido
 */
const removeParticipant = async (conversationId, participantId, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: true
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é administrador da conversa ou está removendo a si mesmo
    const userParticipant = conversation.participants.find(
      participant => (participant.userId === userId || participant.clientId === userId) && participant.leftAt === null
    );
    
    if (!userParticipant) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }
    
    const isAdmin = userParticipant.isAdmin;
    const isSelfRemoval = userId === participantId;

    if (!isAdmin && !isSelfRemoval) {
      throw new ForbiddenError('Apenas administradores podem remover outros participantes');
    }

    // Verificar se a conversa é do tipo GROUP
    if (conversation.type !== 'GROUP' && !isSelfRemoval) {
      throw new ForbiddenError('Apenas conversas em grupo podem ter participantes removidos');
    }

    // Buscar o participante (usuário ou cliente)
    const participant = conversation.participants.find(
      participant => (participant.userId === participantId || participant.clientId === participantId) && participant.leftAt === null
    );

    if (!participant) {
      throw new NotFoundError('Participante não encontrado ou já removido');
    }
    
    console.log(`[removeParticipant] Removendo participante ${participantId} da conversa ${conversationId}`);

    // Remover o participante (marcar como saído)
    const updatedParticipant = await prisma.conversationParticipant.update({
      where: { id: participant.id },
      data: { leftAt: new Date() },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true
          }
        },
        client: {
          select: clientSelectWithPerson
        }
      }
    });
    
    console.log(`[removeParticipant] Participante removido com sucesso:`, updatedParticipant);

    // Verificar quantos participantes ativos restam
    const activeParticipants = await prisma.conversationParticipant.count({
      where: {
        conversationId,
        leftAt: null
      }
    });
    
    console.log(`[removeParticipant] Participantes ativos restantes: ${activeParticipants}`);

    // Se a conversa ficar sem participantes ativos, desativá-la
    if (activeParticipants === 0) {
      console.log(`[removeParticipant] Desativando conversa ${conversationId} - sem participantes ativos`);
      await prisma.conversation.update({
        where: { id: conversationId },
        data: { isActive: false }
      });
    }

    return updatedParticipant;
  } catch (error) {
    console.error('Error removing participant:', error);
    throw error;
  }
};

/**
 * Atualiza uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {Object} data - Dados a serem atualizados
 * @param {string} userId - ID do usuário que está atualizando a conversa
 * @returns {Promise<Object>} - Conversa atualizada
 */
const updateConversation = async (conversationId, data, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: true
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é administrador da conversa
    const isAdmin = conversation.participants.some(
      participant => participant.userId === userId && participant.isAdmin && participant.leftAt === null
    );

    if (!isAdmin) {
      throw new ForbiddenError('Apenas administradores podem atualizar a conversa');
    }

    // Atualizar a conversa
    const updatedConversation = await prisma.conversation.update({
      where: { id: conversationId },
      data: {
        title: data.title,
        // Não permitir alterar companyId ou branchId
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        }
      }
    });

    return updatedConversation;
  } catch (error) {
    console.error('Error updating conversation:', error);
    throw error;
  }
};

/**
 * Verifica se um usuário é participante de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário
 * @returns {Promise<boolean>} - True se o usuário é participante, false caso contrário
 */
const isConversationParticipant = async (conversationId, userId) => {
  try {
    // Verificar se é cliente
    const isClient = await prisma.client.findUnique({ where: { id: userId } });
    
    const count = await prisma.conversationParticipant.count({
      where: {
        conversationId,
        OR: isClient ? [
          { clientId: userId, leftAt: null }
        ] : [
          { userId, leftAt: null }
        ]
      }
    });

    return count > 0;
  } catch (error) {
    console.error('Error checking if user is conversation participant:', error);
    throw error;
  }
};

/**
 * Busca um participante de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário
 * @returns {Promise<Object|null>} - Participante encontrado ou null
 */
const getConversationParticipant = async (conversationId, userId) => {
  try {
    // Verificar se é cliente
    const isClient = await prisma.client.findUnique({ where: { id: userId } });
    
    const participant = await prisma.conversationParticipant.findFirst({
      where: {
        conversationId,
        OR: isClient ? [
          { clientId: userId, leftAt: null }
        ] : [
          { userId, leftAt: null }
        ]
      }
    });

    return participant;
  } catch (error) {
    console.error('Error getting conversation participant:', error);
    throw error;
  }
};

/**
 * Atualiza a última mensagem lida por um participante
 * @param {string} participantId - ID do participante
 * @param {string} messageId - ID da mensagem
 * @returns {Promise<Object>} - Participante atualizado
 */
const updateLastReadMessage = async (participantId, messageId) => {
  try {
    const participant = await prisma.conversationParticipant.update({
      where: { id: participantId },
      data: { lastReadMessageId: messageId }
    });

    return participant;
  } catch (error) {
    console.error('Error updating last read message:', error);
    throw error;
  }
};

/**
 * Atualiza a última mensagem de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} messageId - ID da mensagem
 * @returns {Promise<Object>} - Conversa atualizada
 */
const updateLastMessage = async (conversationId, messageId) => {
  try {
    // Buscar a mensagem para obter o timestamp
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      select: { createdAt: true }
    });

    if (!message) {
      throw new NotFoundError('Mensagem não encontrada');
    }

    // Atualizar a conversa
    const conversation = await prisma.conversation.update({
      where: { id: conversationId },
      data: { lastMessageAt: message.createdAt }
    });

    return conversation;
  } catch (error) {
    console.error('Error updating last message:', error);
    throw error;
  }
};

module.exports = {
  createConversation,
  getUserConversations,
  getConversationById,
  addParticipant,
  removeParticipant,
  updateConversation,
  isConversationParticipant,
  getConversationParticipant,
  updateLastReadMessage,
  updateLastMessage
};