-- CreateEnum
CREATE TYPE "BugCategory" AS ENUM ('GENERAL', 'UI_UX', 'PERFORMANCE', 'FUNCTIONALITY', 'DATA', 'SECURITY', 'INTEGRATION');

-- Create<PERSON><PERSON>
CREATE TYPE "BugPriority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- <PERSON>reateEnum
CREATE TYPE "BugStatus" AS ENUM ('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED');

-- CreateTable
CREATE TABLE "BugReport" (
    "id" TEXT NOT NULL,
    "title" VARCHAR(100) NOT NULL,
    "description" TEXT NOT NULL,
    "location" VARCHAR(150),
    "category" "BugCategory" NOT NULL DEFAULT 'GENERAL',
    "priority" "BugPriority" NOT NULL DEFAULT 'MEDIUM',
    "status" "BugStatus" NOT NULL DEFAULT 'OPEN',
    "adminNotes" TEXT,
    "reportedById" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BugReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BugStatusHistory" (
    "id" TEXT NOT NULL,
    "bugReportId" TEXT NOT NULL,
    "oldStatus" "BugStatus",
    "newStatus" "BugStatus" NOT NULL,
    "oldPriority" "BugPriority",
    "newPriority" "BugPriority" NOT NULL,
    "notes" TEXT,
    "changedById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BugStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BugReport_companyId_idx" ON "BugReport"("companyId");

-- CreateIndex
CREATE INDEX "BugReport_reportedById_idx" ON "BugReport"("reportedById");

-- CreateIndex
CREATE INDEX "BugReport_status_idx" ON "BugReport"("status");

-- CreateIndex
CREATE INDEX "BugReport_priority_idx" ON "BugReport"("priority");

-- CreateIndex
CREATE INDEX "BugReport_category_idx" ON "BugReport"("category");

-- CreateIndex
CREATE INDEX "BugReport_createdAt_idx" ON "BugReport"("createdAt");

-- CreateIndex
CREATE INDEX "BugStatusHistory_bugReportId_idx" ON "BugStatusHistory"("bugReportId");

-- CreateIndex
CREATE INDEX "BugStatusHistory_changedById_idx" ON "BugStatusHistory"("changedById");

-- CreateIndex
CREATE INDEX "BugStatusHistory_createdAt_idx" ON "BugStatusHistory"("createdAt");

-- AddForeignKey
ALTER TABLE "BugReport" ADD CONSTRAINT "BugReport_reportedById_fkey" FOREIGN KEY ("reportedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BugReport" ADD CONSTRAINT "BugReport_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BugStatusHistory" ADD CONSTRAINT "BugStatusHistory_bugReportId_fkey" FOREIGN KEY ("bugReportId") REFERENCES "BugReport"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BugStatusHistory" ADD CONSTRAINT "BugStatusHistory_changedById_fkey" FOREIGN KEY ("changedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
