"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1f5ac842e7ca\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFByb2dyYW1hY2FvXFxISUdIIFRJREUgU1lTVEVNU1xcaGlnaC10aWRlLXN5c3RlbXMtZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFmNWFjODQyZTdjYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chat/ChatConversation.js":
/*!*************************************************!*\
  !*** ./src/components/chat/ChatConversation.js ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatInput */ \"(app-pages-browser)/./src/components/chat/ChatInput.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MultiUserSearch */ \"(app-pages-browser)/./src/components/chat/MultiUserSearch.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SharedItemMessage */ \"(app-pages-browser)/./src/components/chat/SharedItemMessage.js\");\n/* harmony import */ var _AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AttachmentViewer */ \"(app-pages-browser)/./src/components/chat/AttachmentViewer.js\");\n/* harmony import */ var _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useSharedItemNavigation */ \"(app-pages-browser)/./src/hooks/useSharedItemNavigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ChatConversation = (param)=>{\n    let { conversationId, onBack, compact = false } = param;\n    var _conversation_participants;\n    _s();\n    const { messages, loadMessages, conversations, setConversations, activeConversation, setActiveConversation, removeParticipantFromGroup, addParticipantToGroup, addMultipleParticipantsToGroup, markMessagesAsRead } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { handleSharedItemClick } = (0,_hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation)();\n    // Wrapper para logar o clique no item compartilhado\n    const handleSharedItemClickWithLog = (contentType, itemData)=>{\n        console.log('[CHAT] Clique em item compartilhado:', {\n            contentType,\n            itemData\n        });\n        handleSharedItemClick(contentType, itemData);\n    };\n    // Estados para gerenciar o menu de opções e adição de participantes\n    const [showOptionsMenu, setShowOptionsMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddParticipant, setShowAddParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticipantsList, setShowParticipantsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingParticipants, setAddingParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para diálogos de confirmação\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmAction, setConfirmAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        title: '',\n        message: '',\n        onConfirm: {\n            \"ChatConversation.useState\": ()=>{}\n        }[\"ChatConversation.useState\"]\n    });\n    // Memoizar a conversa atual para evitar recalculos\n    const conversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversation]\": ()=>{\n            return conversations.find({\n                \"ChatConversation.useMemo[conversation]\": (c)=>c.id === conversationId\n            }[\"ChatConversation.useMemo[conversation]\"]);\n        }\n    }[\"ChatConversation.useMemo[conversation]\"], [\n        conversations,\n        conversationId\n    ]);\n    // Memoizar as mensagens da conversa para evitar recalculos\n    const conversationMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversationMessages]\": ()=>{\n            return messages[conversationId] || [];\n        }\n    }[\"ChatConversation.useMemo[conversationMessages]\"], [\n        messages,\n        conversationId\n    ]);\n    // Referência para controlar se as mensagens já foram carregadas\n    const messagesLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Carregar mensagens ao montar o componente - apenas uma vez\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            if (conversationId && !messagesLoadedRef.current) {\n                // Sempre carregar mensagens completas quando abrir uma conversa\n                // O loadConversations só traz a última mensagem, precisamos de todas\n                console.log('Carregando mensagens completas para conversa:', conversationId);\n                loadMessages(conversationId);\n                messagesLoadedRef.current = true;\n            }\n            // Limpar a referência quando o componente for desmontado\n            return ({\n                \"ChatConversation.useEffect\": ()=>{\n                    messagesLoadedRef.current = false;\n                }\n            })[\"ChatConversation.useEffect\"];\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationId,\n        loadMessages\n    ]);\n    // Rolar para a última mensagem\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationMessages\n    ]);\n    // 🔄 NOVA LÓGICA: Não marcar automaticamente, apenas quando realmente visível\n    // TODO: Implementar Intersection Observer para marcar apenas mensagens visíveis\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            console.log(\"[ChatConversation] Conversa carregada - conversationId: \".concat(conversationId));\n            console.log(\"[ChatConversation] Total de mensagens: \".concat(conversationMessages.length));\n            // ❌ REMOVIDO: Marcação automática ao abrir conversa\n            // A marcação agora deve acontecer apenas quando o usuário VÊ a mensagem\n            // Implementação futura: Intersection Observer para detectar mensagens visíveis\n            if (conversationMessages.length > 0) {\n                const unreadMessages = conversationMessages.filter({\n                    \"ChatConversation.useEffect.unreadMessages\": (msg)=>!msg.isFromCurrentUser && (!msg.overallStatus || msg.overallStatus !== 'read')\n                }[\"ChatConversation.useEffect.unreadMessages\"]);\n                console.log(\"[ChatConversation] Mensagens n\\xe3o lidas detectadas: \".concat(unreadMessages.length));\n                // Para demonstração, vou marcar apenas a primeira mensagem não lida após 2 segundos\n                // (simula o usuário vendo a mensagem)\n                if (unreadMessages.length > 0) {\n                    const timer = setTimeout({\n                        \"ChatConversation.useEffect.timer\": ()=>{\n                            const firstUnread = unreadMessages[0];\n                            console.log(\"[ChatConversation] Simulando visualiza\\xe7\\xe3o da mensagem: \".concat(firstUnread.id));\n                            markMessageAsRead(conversationId, firstUnread.id);\n                        }\n                    }[\"ChatConversation.useEffect.timer\"], 2000);\n                    return ({\n                        \"ChatConversation.useEffect\": ()=>clearTimeout(timer)\n                    })[\"ChatConversation.useEffect\"];\n                }\n            }\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationId,\n        conversationMessages,\n        markMessageAsRead\n    ]);\n    // Verificar se o usuário atual é administrador do grupo\n    const isGroupAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupAdmin]\": ()=>{\n            var _conversation_participants;\n            if (!conversation || !user) return false;\n            const userParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useMemo[isGroupAdmin]\": (p)=>p.userId === user.id\n            }[\"ChatConversation.useMemo[isGroupAdmin]\"]);\n            return (userParticipant === null || userParticipant === void 0 ? void 0 : userParticipant.isAdmin) || false;\n        }\n    }[\"ChatConversation.useMemo[isGroupAdmin]\"], [\n        conversation,\n        user\n    ]);\n    // Verificar se é uma conversa de grupo\n    const isGroupChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupChat]\": ()=>{\n            return (conversation === null || conversation === void 0 ? void 0 : conversation.type) === 'GROUP';\n        }\n    }[\"ChatConversation.useMemo[isGroupChat]\"], [\n        conversation\n    ]);\n    // Função para confirmar saída do grupo\n    const handleLeaveGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'leave-group',\n                title: 'Sair do grupo',\n                message: 'Tem certeza que deseja sair do grupo \"'.concat(conversation.title || 'Grupo', '\"?'),\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                console.log('Saiu do grupo com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após sair do grupo');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleLeaveGroup]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]).catch({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": (error)=>{\n                                console.error('Erro ao sair do grupo:', error);\n                                alert('Não foi possível sair do grupo. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleLeaveGroup]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleLeaveGroup]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Função para adicionar múltiplos participantes\n    const handleAddParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleAddParticipants]\": (selectedUsers)=>{\n            if (!conversation || !selectedUsers || selectedUsers.length === 0) return;\n            setAddingParticipants(true);\n            addMultipleParticipantsToGroup(conversation.id, selectedUsers).then({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (result)=>{\n                    console.log('Resultado da adição de participantes:', result);\n                    if (result.success) {\n                        console.log(\"\".concat(result.successCount, \" participantes adicionados com sucesso\"));\n                        if (result.errorCount > 0) {\n                            console.warn(\"\".concat(result.errorCount, \" participantes n\\xe3o puderam ser adicionados\"));\n                        // Poderia mostrar um toast com essa informação\n                        }\n                    } else {\n                        console.error('Falha ao adicionar participantes');\n                        alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                    }\n                    setShowAddParticipant(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).catch({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (error)=>{\n                    console.error('Erro ao adicionar participantes:', error);\n                    alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).finally({\n                \"ChatConversation.useCallback[handleAddParticipants]\": ()=>{\n                    setAddingParticipants(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]);\n        }\n    }[\"ChatConversation.useCallback[handleAddParticipants]\"], [\n        conversation,\n        addMultipleParticipantsToGroup\n    ]);\n    // Função para apagar conversa (sair da conversa)\n    const handleDeleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'delete-conversation',\n                title: 'Apagar conversa',\n                message: 'Tem certeza que deseja apagar esta conversa? Esta ação não pode ser desfeita.',\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                        // Para apagar uma conversa, usamos a mesma função de sair do grupo\n                        // No backend, isso marca o participante como tendo saído da conversa\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                console.log('Conversa apagada com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após apagar conversa');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleDeleteConversation]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]).catch({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": (error)=>{\n                                console.error('Erro ao apagar conversa:', error);\n                                alert('Não foi possível apagar a conversa. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleDeleteConversation]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleDeleteConversation]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Obter o nome da conversa - memoizado para evitar recalculos\n    const getConversationName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationName]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return 'Conversa';\n            if (conversation.type === 'GROUP') {\n                return conversation.title || 'Grupo';\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationName]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationName]\"]);\n            // Priorizar fullName para usuários e clientes\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                return otherParticipant.user.fullName;\n            }\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                // Usar o nome do paciente titular se disponível\n                const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                if (clientPersonName && clientPersonName.trim() !== '') {\n                    return clientPersonName;\n                }\n                // Fallback para fullName do cliente ou login\n                return otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n            }\n            return 'Usuário';\n        }\n    }[\"ChatConversation.useCallback[getConversationName]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter a imagem da conversa - memoizado para evitar recalculos\n    const getConversationImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationImage]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return null;\n            if (conversation.type === 'GROUP') {\n                return null;\n            }\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationImage]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationImage]\"]);\n            return otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.profileImageUrl;\n        }\n    }[\"ChatConversation.useCallback[getConversationImage]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter iniciais para avatar - memoizado para evitar recalculos\n    const getInitials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getInitials]\": (name)=>{\n            if (!name) return 'U';\n            try {\n                const names = name.split(' ');\n                if (names.length === 1) return names[0].charAt(0);\n                return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n            } catch (error) {\n                console.error('Erro ao obter iniciais:', error);\n                return 'U';\n            }\n        }\n    }[\"ChatConversation.useCallback[getInitials]\"], []);\n    // Formatar data da mensagem - memoizado para evitar recalculos\n    const formatMessageDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[formatMessageDate]\": (timestamp)=>{\n            if (!timestamp) return '';\n            try {\n                return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(new Date(timestamp), 'HH:mm', {\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.ptBR\n                });\n            } catch (error) {\n                console.error('Erro ao formatar data:', error);\n                return '';\n            }\n        }\n    }[\"ChatConversation.useCallback[formatMessageDate]\"], []);\n    // Agrupar mensagens por data - memoizado para evitar recalculos\n    const groupMessagesByDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[groupMessagesByDate]\": (messages)=>{\n            if (!messages || messages.length === 0) {\n                console.log(\"Nenhuma mensagem para agrupar\");\n                return [];\n            }\n            console.log(\"Agrupando \".concat(messages.length, \" mensagens\"));\n            try {\n                const groups = {};\n                // Ordenar mensagens por data (mais antigas primeiro)\n                const sortedMessages = [\n                    ...messages\n                ].sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                }[\"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\"]);\n                sortedMessages.forEach({\n                    \"ChatConversation.useCallback[groupMessagesByDate]\": (message)=>{\n                        if (!message || !message.createdAt) {\n                            console.warn(\"Mensagem inválida encontrada:\", message);\n                            return;\n                        }\n                        try {\n                            const messageDate = new Date(message.createdAt);\n                            const date = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(messageDate, 'dd/MM/yyyy');\n                            if (!groups[date]) {\n                                groups[date] = [];\n                            }\n                            groups[date].push(message);\n                        } catch (err) {\n                            console.error(\"Erro ao processar mensagem:\", err, message);\n                        }\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate]\"]);\n                // Verificar se temos grupos\n                if (Object.keys(groups).length === 0) {\n                    console.warn(\"Nenhum grupo criado após processamento\");\n                    return [];\n                }\n                // Ordenar os grupos por data (mais antigos primeiro)\n                const result = Object.entries(groups).sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param, param1)=>{\n                        let [dateA] = param, [dateB] = param1;\n                        // Converter strings de data para objetos Date para comparação\n                        const [dayA, monthA, yearA] = dateA.split('/').map(Number);\n                        const [dayB, monthB, yearB] = dateB.split('/').map(Number);\n                        return new Date(yearA, monthA - 1, dayA) - new Date(yearB, monthB - 1, dayB);\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]).map({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param)=>{\n                        let [date, messages] = param;\n                        return {\n                            date,\n                            messages\n                        };\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]);\n                console.log(\"Criados \".concat(result.length, \" grupos de mensagens\"));\n                return result;\n            } catch (error) {\n                console.error('Erro ao agrupar mensagens:', error);\n                return [];\n            }\n        }\n    }[\"ChatConversation.useCallback[groupMessagesByDate]\"], []);\n    // Memoizar os grupos de mensagens para evitar recalculos\n    const messageGroups = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[messageGroups]\": ()=>{\n            return groupMessagesByDate(conversationMessages);\n        }\n    }[\"ChatConversation.useMemo[messageGroups]\"], [\n        groupMessagesByDate,\n        conversationMessages\n    ]);\n    // Se a conversa não for encontrada, mostrar mensagem de erro\n    if (!conversation) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 text-white rounded-t-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                            \"aria-label\": \"Voltar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: compact ? 16 : 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-white text-lg\",\n                            children: \"Conversa n\\xe3o encontrada\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4 text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"A conversa n\\xe3o foi encontrada ou foi removida.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n            lineNumber: 396,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                        \"aria-label\": \"Voltar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: compact ? 16 : 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-1\",\n                        children: [\n                            getConversationImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: getConversationImage(),\n                                alt: getConversationName(),\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full object-cover\"),\n                                onError: (e)=>{\n                                    e.target.onerror = null;\n                                    e.target.style.display = 'none';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full bg-white/20 flex items-center justify-center text-white font-medium shadow-sm\"),\n                                children: getInitials(getConversationName())\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white \".concat(compact ? 'text-sm' : 'text-base'),\n                                        children: getConversationName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.isOnline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-white/80\",\n                                        children: \"Online\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowOptionsMenu(!showOptionsMenu),\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Mais op\\xe7\\xf5es\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: compact ? 16 : 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, undefined),\n                            showOptionsMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 w-48 z-10\",\n                                children: isGroupChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowParticipantsList(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ver participantes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowAddParticipant(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Adicionar participante\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 481,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLeaveGroup,\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Sair do grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 493,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setConfirmAction({\n                                                    type: 'delete-group',\n                                                    title: 'Deletar grupo',\n                                                    message: 'Tem certeza que deseja deletar o grupo \"'.concat(conversation.title || 'Grupo', '\"? Todos os participantes ser\\xe3o removidos e esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.'),\n                                                    onConfirm: async ()=>{\n                                                        console.log('Deletando grupo:', conversation.id);\n                                                        try {\n                                                            const currentToken = localStorage.getItem('token');\n                                                            const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n                                                            console.log('Fazendo requisição DELETE para:', \"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id));\n                                                            // Remover todos os participantes para deletar o grupo\n                                                            const participants = conversation.participants || [];\n                                                            for (const participant of participants){\n                                                                const participantId = participant.userId || participant.clientId;\n                                                                if (participantId) {\n                                                                    await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id, \"/participants/\").concat(participantId), {\n                                                                        method: 'DELETE',\n                                                                        headers: {\n                                                                            'Authorization': \"Bearer \".concat(currentToken)\n                                                                        }\n                                                                    });\n                                                                }\n                                                            }\n                                                            const response = {\n                                                                ok: true\n                                                            }; // Simular sucesso\n                                                            console.log('Resposta:', response.status, response.statusText);\n                                                            if (response.ok) {\n                                                                console.log('Grupo deletado com sucesso');\n                                                                // Usar removeParticipantFromGroup para remover o usuário atual\n                                                                await removeParticipantFromGroup(conversation.id, user.id);\n                                                                onBack();\n                                                            } else {\n                                                                const errorText = await response.text();\n                                                                console.error('Erro na resposta:', errorText);\n                                                                alert('Erro ao deletar grupo: ' + response.status);\n                                                            }\n                                                        } catch (error) {\n                                                            console.error('Erro ao deletar grupo:', error);\n                                                            alert('Erro ao deletar grupo: ' + error.message);\n                                                        }\n                                                    }\n                                                });\n                                                setConfirmDialogOpen(true);\n                                                setShowOptionsMenu(false);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-t border-gray-200 dark:border-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Deletar grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 502,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : // Opções para conversas individuais\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDeleteConversation,\n                                    className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 563,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Apagar conversa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 564,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 559,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, undefined),\n            showAddParticipant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col h-full overflow-hidden\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddUsers: handleAddParticipants,\n                        onClose: ()=>setShowAddParticipant(false),\n                        title: \"Adicionar participantes ao grupo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, undefined),\n                    addingParticipants && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50 flex items-center justify-center z-30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-10 w-10 border-b-2 border-cyan-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 584,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 dark:text-gray-300\",\n                                    children: \"Adicionando participantes...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 585,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 583,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 582,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 574,\n                columnNumber: 9\n            }, undefined),\n            showParticipantsList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowParticipantsList(false),\n                                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                        \"aria-label\": \"Voltar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 597,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white text-base\",\n                                        children: \"Participantes do grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 596,\n                                columnNumber: 13\n                            }, undefined),\n                            isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setShowParticipantsList(false);\n                                    setShowAddParticipant(true);\n                                },\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Adicionar participante\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 616,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 608,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 595,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200 dark:divide-gray-700\",\n                            children: conversation === null || conversation === void 0 ? void 0 : (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.map((participant)=>{\n                                var _participant_user;\n                                // Determinar se é usuário ou cliente e obter nome correto\n                                const getParticipantName = ()=>{\n                                    var _participant_user;\n                                    if (participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        return 'Você';\n                                    }\n                                    // Se é usuário\n                                    if ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.fullName) {\n                                        return participant.user.fullName;\n                                    }\n                                    // Se é cliente\n                                    if (participant.client) {\n                                        var _participant_client_clientPersons__person, _participant_client_clientPersons_, _participant_client_clientPersons;\n                                        const clientPersonName = (_participant_client_clientPersons = participant.client.clientPersons) === null || _participant_client_clientPersons === void 0 ? void 0 : (_participant_client_clientPersons_ = _participant_client_clientPersons[0]) === null || _participant_client_clientPersons_ === void 0 ? void 0 : (_participant_client_clientPersons__person = _participant_client_clientPersons_.person) === null || _participant_client_clientPersons__person === void 0 ? void 0 : _participant_client_clientPersons__person.fullName;\n                                        if (clientPersonName && clientPersonName.trim() !== '') {\n                                            return clientPersonName;\n                                        }\n                                        return participant.client.fullName && participant.client.fullName.trim() !== '' ? participant.client.fullName : participant.client.login;\n                                    }\n                                    return 'Usuário';\n                                };\n                                const participantName = getParticipantName();\n                                const isCurrentUser = participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id);\n                                const participantId = participant.userId || participant.clientId;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex-shrink-0\",\n                                            children: ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.profileImageUrl) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: participant.user.profileImageUrl,\n                                                alt: participantName,\n                                                className: \"h-10 w-10 rounded-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                    e.target.parentNode.innerHTML = '<div class=\"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\">'.concat(getInitials(participantName), \"</div>\");\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 658,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\",\n                                                children: getInitials(participantName)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 669,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 656,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                    children: participantName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        participant.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-cyan-600 dark:text-cyan-400 bg-cyan-50 dark:bg-cyan-900/30 px-2 py-0.5 rounded-full\",\n                                                            children: \"Administrador\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        participant.client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"Cliente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 676,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        isGroupAdmin && !isCurrentUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                if (window.confirm(\"Tem certeza que deseja remover \".concat(participantName, \" do grupo?\"))) {\n                                                    removeParticipantFromGroup(conversation.id, participantId).catch((error)=>{\n                                                        console.error('Erro ao remover participante:', error);\n                                                        alert('Não foi possível remover o participante. Tente novamente mais tarde.');\n                                                    });\n                                                }\n                                            },\n                                            className: \"p-1.5 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n                                            \"aria-label\": \"Remover participante\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 709,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 696,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, participant.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 654,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 622,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 621,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 594,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-5 space-y-5\",\n                children: [\n                    messageGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-1 bg-cyan-100 dark:bg-cyan-900/30 rounded-full text-xs text-cyan-700 dark:text-cyan-300 shadow-sm\",\n                                        children: group.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 724,\n                                    columnNumber: 13\n                                }, undefined),\n                                group.messages.map((message)=>{\n                                    var _message_metadata;\n                                    const isOwnMessage = message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id);\n                                    const isGroupChat = conversation.type === 'GROUP';\n                                    // Encontrar o nome do remetente para mensagens de grupo\n                                    const getSenderName = ()=>{\n                                        var _message_sender;\n                                        if (message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                            return 'Você';\n                                        }\n                                        // Se a mensagem tem sender (usuário)\n                                        if ((_message_sender = message.sender) === null || _message_sender === void 0 ? void 0 : _message_sender.fullName) {\n                                            return message.sender.fullName;\n                                        }\n                                        // Se a mensagem tem senderClient (cliente)\n                                        if (message.senderClient) {\n                                            var _message_senderClient_clientPersons__person, _message_senderClient_clientPersons_, _message_senderClient_clientPersons;\n                                            // Usar o nome do paciente titular se disponível\n                                            const clientPersonName = (_message_senderClient_clientPersons = message.senderClient.clientPersons) === null || _message_senderClient_clientPersons === void 0 ? void 0 : (_message_senderClient_clientPersons_ = _message_senderClient_clientPersons[0]) === null || _message_senderClient_clientPersons_ === void 0 ? void 0 : (_message_senderClient_clientPersons__person = _message_senderClient_clientPersons_.person) === null || _message_senderClient_clientPersons__person === void 0 ? void 0 : _message_senderClient_clientPersons__person.fullName;\n                                            if (clientPersonName && clientPersonName.trim() !== '') {\n                                                return clientPersonName;\n                                            }\n                                            // Fallback para fullName do cliente ou login\n                                            return message.senderClient.fullName && message.senderClient.fullName.trim() !== '' ? message.senderClient.fullName : message.senderClient.login;\n                                        }\n                                        return 'Usuário';\n                                    };\n                                    // Obter as iniciais do remetente para mensagens de grupo\n                                    const getSenderInitials = ()=>{\n                                        if (isOwnMessage) return 'Você';\n                                        const senderName = getSenderName();\n                                        return getInitials(senderName);\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(isOwnMessage ? 'justify-end' : 'justify-start'),\n                                        children: [\n                                            isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-2 flex flex-col items-center justify-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-xs font-medium\",\n                                                    children: getSenderInitials()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 776,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[75%] \".concat(compact ? 'max-w-[85%]' : ''),\n                                                children: [\n                                                    isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 ml-1\",\n                                                        children: getSenderName()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-3 rounded-lg shadow-sm \".concat(isOwnMessage ? 'bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white rounded-br-none' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none border border-cyan-100 dark:border-cyan-800'),\n                                                        children: message.contentType && [\n                                                            'SHARED_APPOINTMENT',\n                                                            'SHARED_PERSON',\n                                                            'SHARED_CLIENT',\n                                                            'SHARED_USER',\n                                                            'SHARED_SERVICE_TYPE',\n                                                            'SHARED_LOCATION',\n                                                            'SHARED_WORKING_HOURS',\n                                                            'SHARED_INSURANCE',\n                                                            'SHARED_INSURANCE_LIMIT'\n                                                        ].includes(message.contentType) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            message: message,\n                                                            onItemClick: handleSharedItemClickWithLog\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.contentType === 'ATTACHMENT' && ((_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.attachments) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                message.metadata.attachments.map((attachment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        attachment: attachment,\n                                                                        compact: true\n                                                                    }, attachment.id || index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 29\n                                                                    }, undefined)),\n                                                                message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1.5 \".concat(isOwnMessage ? 'text-right' : '', \" text-gray-500 dark:text-gray-400\"),\n                                                        children: formatMessageDate(message.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 783,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 770,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            ]\n                        }, group.date, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 723,\n                            columnNumber: 11\n                        }, undefined)),\n                    conversationMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm\",\n                            children: \"Nenhuma mensagem ainda. Comece a conversar!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 833,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 832,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 839,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 721,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                conversationId: conversationId,\n                compact: compact\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 843,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ConfirmationDialog, {\n                isOpen: confirmDialogOpen,\n                onClose: ()=>setConfirmDialogOpen(false),\n                onConfirm: confirmAction.onConfirm,\n                title: confirmAction.title,\n                message: confirmAction.message,\n                confirmText: \"Confirmar\",\n                cancelText: \"Cancelar\",\n                variant: \"warning\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 846,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n        lineNumber: 417,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatConversation, \"a82LhqQwNzQoId0vbxwC6MawV1o=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation\n    ];\n});\n_c = ChatConversation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatConversation);\nvar _c;\n$RefreshReg$(_c, \"ChatConversation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatConversation.js\n"));

/***/ })

});