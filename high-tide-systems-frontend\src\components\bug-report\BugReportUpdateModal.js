'use client';

import React, { useState } from 'react';
import { 
  X, 
  Bug, 
  Save,
  Alert<PERSON>riangle, 
  Clock, 
  CheckCircle, 
  XCircle
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog.jsx';
import Button from '@/components/ui/Button';
import CustomSelect from '@/components/ui/CustomSelect';
import Label from '@/components/ui/Label';
import { toast } from 'react-toastify';
import api from '@/services/api';

const BugReportUpdateModal = ({ bug, isOpen, onClose, onUpdated }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    status: bug?.status || 'OPEN',
    priority: bug?.priority || 'MEDIUM',
    adminNotes: bug?.adminNotes || ''
  });

  if (!bug) return null;

  const statusOptions = [
    { value: 'OPEN', label: 'Abert<PERSON>' },
    { value: 'IN_PROGRESS', label: 'Em Progresso' },
    { value: 'RESOLVED', label: 'Resolvido' },
    { value: 'CLOSED', label: 'Fechado' }
  ];

  const priorityOptions = [
    { value: 'LOW', label: 'Baixa' },
    { value: 'MEDIUM', label: 'Média' },
    { value: 'HIGH', label: 'Alta' },
    { value: 'CRITICAL', label: 'Crítica' }
  ];

  const handleInputChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await api.patch(`/bug-reports/${bug.id}/status`, formData);

      if (response.data.success) {
        toast.success('Bug atualizado com sucesso!', {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
        
        onUpdated();
      }
    } catch (error) {
      console.error('Erro ao atualizar bug:', error);
      
      const errorMessage = error.response?.data?.message || 'Erro ao atualizar bug. Tente novamente.';
      
      toast.error(errorMessage, {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      OPEN: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      IN_PROGRESS: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      RESOLVED: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      CLOSED: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    };
    return colors[status] || colors.OPEN;
  };

  const getPriorityColor = (priority) => {
    const colors = {
      LOW: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      MEDIUM: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      HIGH: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      CRITICAL: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };
    return colors[priority] || colors.MEDIUM;
  };

  const getStatusIcon = (status) => {
    const icons = {
      OPEN: <AlertTriangle className="h-4 w-4" />,
      IN_PROGRESS: <Clock className="h-4 w-4" />,
      RESOLVED: <CheckCircle className="h-4 w-4" />,
      CLOSED: <XCircle className="h-4 w-4" />
    };
    return icons[status] || icons.OPEN;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <Bug className="h-6 w-6 text-blue-600" />
            Atualizar Bug Report
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações do bug */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              {bug.title}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
              {bug.description}
            </p>
            
            <div className="flex flex-wrap gap-2">
              <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(bug.status)}`}>
                {getStatusIcon(bug.status)}
                Status Atual
              </span>
              <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(bug.priority)}`}>
                Prioridade Atual
              </span>
            </div>
          </div>

          {/* Formulário de atualização */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Novo Status
                </Label>
                <CustomSelect
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={(value) => handleInputChange('status', value)}
                  options={statusOptions}
                  required
                  className="border-2 border-blue-300 dark:border-blue-600 focus:border-blue-500 dark:focus:border-blue-400"
                />
              </div>

              {/* Prioridade */}
              <div className="space-y-2">
                <Label htmlFor="priority" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Nova Prioridade
                </Label>
                <CustomSelect
                  id="priority"
                  name="priority"
                  value={formData.priority}
                  onChange={(value) => handleInputChange('priority', value)}
                  options={priorityOptions}
                  required
                  className="border-2 border-blue-300 dark:border-blue-600 focus:border-blue-500 dark:focus:border-blue-400"
                />
              </div>
            </div>

            {/* Notas do admin */}
            <div className="space-y-2">
              <Label htmlFor="adminNotes" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Notas do Administrador
              </Label>
              <textarea
                id="adminNotes"
                name="adminNotes"
                value={formData.adminNotes}
                onChange={(e) => handleInputChange('adminNotes', e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border-2 border-blue-300 dark:border-blue-600 rounded-lg focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none resize-none bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                placeholder="Adicione notas sobre as alterações realizadas..."
                maxLength={1000}
              />
              <div className="text-xs text-gray-500 dark:text-gray-400 text-right">
                {formData.adminNotes.length}/1000 caracteres
              </div>
            </div>

            {/* Botões de ação */}
            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BugReportUpdateModal;
