"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6e7940f3bbd5\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFByb2dyYW1hY2FvXFxISUdIIFRJREUgU1lTVEVNU1xcaGlnaC10aWRlLXN5c3RlbXMtZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjZlNzk0MGYzYmJkNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chat/ChatConversation.js":
/*!*************************************************!*\
  !*** ./src/components/chat/ChatConversation.js ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ChatContext */ \"(app-pages-browser)/./src/contexts/ChatContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,LogOut,MoreVertical,Trash2,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatInput */ \"(app-pages-browser)/./src/components/chat/ChatInput.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MultiUserSearch */ \"(app-pages-browser)/./src/components/chat/MultiUserSearch.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SharedItemMessage */ \"(app-pages-browser)/./src/components/chat/SharedItemMessage.js\");\n/* harmony import */ var _AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AttachmentViewer */ \"(app-pages-browser)/./src/components/chat/AttachmentViewer.js\");\n/* harmony import */ var _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useSharedItemNavigation */ \"(app-pages-browser)/./src/hooks/useSharedItemNavigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ChatConversation = (param)=>{\n    let { conversationId, onBack, compact = false } = param;\n    var _conversation_participants;\n    _s();\n    const { messages, loadMessages, conversations, setConversations, activeConversation, setActiveConversation, removeParticipantFromGroup, addParticipantToGroup, addMultipleParticipantsToGroup, markMessagesAsRead } = (0,_contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { handleSharedItemClick } = (0,_hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation)();\n    // Wrapper para logar o clique no item compartilhado\n    const handleSharedItemClickWithLog = (contentType, itemData)=>{\n        console.log('[CHAT] Clique em item compartilhado:', {\n            contentType,\n            itemData\n        });\n        handleSharedItemClick(contentType, itemData);\n    };\n    // Estados para gerenciar o menu de opções e adição de participantes\n    const [showOptionsMenu, setShowOptionsMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddParticipant, setShowAddParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticipantsList, setShowParticipantsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingParticipants, setAddingParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para diálogos de confirmação\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmAction, setConfirmAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        title: '',\n        message: '',\n        onConfirm: {\n            \"ChatConversation.useState\": ()=>{}\n        }[\"ChatConversation.useState\"]\n    });\n    // Memoizar a conversa atual para evitar recalculos\n    const conversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversation]\": ()=>{\n            return conversations.find({\n                \"ChatConversation.useMemo[conversation]\": (c)=>c.id === conversationId\n            }[\"ChatConversation.useMemo[conversation]\"]);\n        }\n    }[\"ChatConversation.useMemo[conversation]\"], [\n        conversations,\n        conversationId\n    ]);\n    // Memoizar as mensagens da conversa para evitar recalculos\n    const conversationMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[conversationMessages]\": ()=>{\n            return messages[conversationId] || [];\n        }\n    }[\"ChatConversation.useMemo[conversationMessages]\"], [\n        messages,\n        conversationId\n    ]);\n    // Referência para controlar se as mensagens já foram carregadas\n    const messagesLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Carregar mensagens ao montar o componente - apenas uma vez\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            if (conversationId && !messagesLoadedRef.current) {\n                // Sempre carregar mensagens completas quando abrir uma conversa\n                // O loadConversations só traz a última mensagem, precisamos de todas\n                console.log('Carregando mensagens completas para conversa:', conversationId);\n                loadMessages(conversationId);\n                messagesLoadedRef.current = true;\n            }\n            // Limpar a referência quando o componente for desmontado\n            return ({\n                \"ChatConversation.useEffect\": ()=>{\n                    messagesLoadedRef.current = false;\n                }\n            })[\"ChatConversation.useEffect\"];\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationId,\n        loadMessages\n    ]);\n    // Rolar para a última mensagem\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatConversation.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ChatConversation.useEffect\"], [\n        conversationMessages\n    ]);\n    // Verificar se o usuário atual é administrador do grupo\n    const isGroupAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupAdmin]\": ()=>{\n            var _conversation_participants;\n            if (!conversation || !user) return false;\n            const userParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useMemo[isGroupAdmin]\": (p)=>p.userId === user.id\n            }[\"ChatConversation.useMemo[isGroupAdmin]\"]);\n            return (userParticipant === null || userParticipant === void 0 ? void 0 : userParticipant.isAdmin) || false;\n        }\n    }[\"ChatConversation.useMemo[isGroupAdmin]\"], [\n        conversation,\n        user\n    ]);\n    // Verificar se é uma conversa de grupo\n    const isGroupChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[isGroupChat]\": ()=>{\n            return (conversation === null || conversation === void 0 ? void 0 : conversation.type) === 'GROUP';\n        }\n    }[\"ChatConversation.useMemo[isGroupChat]\"], [\n        conversation\n    ]);\n    // Função para confirmar saída do grupo\n    const handleLeaveGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'leave-group',\n                title: 'Sair do grupo',\n                message: 'Tem certeza que deseja sair do grupo \"'.concat(conversation.title || 'Grupo', '\"?'),\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                console.log('Saiu do grupo com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleLeaveGroup]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após sair do grupo');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleLeaveGroup]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]).catch({\n                            \"ChatConversation.useCallback[handleLeaveGroup]\": (error)=>{\n                                console.error('Erro ao sair do grupo:', error);\n                                alert('Não foi possível sair do grupo. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleLeaveGroup]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleLeaveGroup]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleLeaveGroup]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Função para adicionar múltiplos participantes\n    const handleAddParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleAddParticipants]\": (selectedUsers)=>{\n            if (!conversation || !selectedUsers || selectedUsers.length === 0) return;\n            setAddingParticipants(true);\n            addMultipleParticipantsToGroup(conversation.id, selectedUsers).then({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (result)=>{\n                    console.log('Resultado da adição de participantes:', result);\n                    if (result.success) {\n                        console.log(\"\".concat(result.successCount, \" participantes adicionados com sucesso\"));\n                        if (result.errorCount > 0) {\n                            console.warn(\"\".concat(result.errorCount, \" participantes n\\xe3o puderam ser adicionados\"));\n                        // Poderia mostrar um toast com essa informação\n                        }\n                    } else {\n                        console.error('Falha ao adicionar participantes');\n                        alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                    }\n                    setShowAddParticipant(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).catch({\n                \"ChatConversation.useCallback[handleAddParticipants]\": (error)=>{\n                    console.error('Erro ao adicionar participantes:', error);\n                    alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]).finally({\n                \"ChatConversation.useCallback[handleAddParticipants]\": ()=>{\n                    setAddingParticipants(false);\n                }\n            }[\"ChatConversation.useCallback[handleAddParticipants]\"]);\n        }\n    }[\"ChatConversation.useCallback[handleAddParticipants]\"], [\n        conversation,\n        addMultipleParticipantsToGroup\n    ]);\n    // Função para apagar conversa (sair da conversa)\n    const handleDeleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n            if (!conversation || !user) return;\n            // Configurar o diálogo de confirmação\n            setConfirmAction({\n                type: 'delete-conversation',\n                title: 'Apagar conversa',\n                message: 'Tem certeza que deseja apagar esta conversa? Esta ação não pode ser desfeita.',\n                onConfirm: {\n                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                        // Para apagar uma conversa, usamos a mesma função de sair do grupo\n                        // No backend, isso marca o participante como tendo saído da conversa\n                        removeParticipantFromGroup(conversation.id, user.id).then({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                console.log('Conversa apagada com sucesso');\n                                // Disparar evento para atualizar a lista de conversas\n                                setTimeout({\n                                    \"ChatConversation.useCallback[handleDeleteConversation]\": ()=>{\n                                        console.log('Forçando atualização da lista de conversas após apagar conversa');\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                action: 'delete',\n                                                conversationId: conversation.id,\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    }\n                                }[\"ChatConversation.useCallback[handleDeleteConversation]\"], 300);\n                            // A navegação de volta para a lista de conversas é tratada no contexto\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]).catch({\n                            \"ChatConversation.useCallback[handleDeleteConversation]\": (error)=>{\n                                console.error('Erro ao apagar conversa:', error);\n                                alert('Não foi possível apagar a conversa. Tente novamente mais tarde.');\n                            }\n                        }[\"ChatConversation.useCallback[handleDeleteConversation]\"]);\n                    }\n                }[\"ChatConversation.useCallback[handleDeleteConversation]\"]\n            });\n            // Abrir o diálogo de confirmação\n            setConfirmDialogOpen(true);\n            setShowOptionsMenu(false);\n        }\n    }[\"ChatConversation.useCallback[handleDeleteConversation]\"], [\n        conversation,\n        user,\n        removeParticipantFromGroup\n    ]);\n    // Obter o nome da conversa - memoizado para evitar recalculos\n    const getConversationName = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationName]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return 'Conversa';\n            if (conversation.type === 'GROUP') {\n                return conversation.title || 'Grupo';\n            }\n            // Encontrar o outro participante (não o usuário atual)\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationName]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id) && p.clientId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationName]\"]);\n            // Priorizar fullName para usuários e clientes\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.fullName) {\n                return otherParticipant.user.fullName;\n            }\n            if (otherParticipant === null || otherParticipant === void 0 ? void 0 : otherParticipant.client) {\n                var _otherParticipant_client_clientPersons__person, _otherParticipant_client_clientPersons_, _otherParticipant_client_clientPersons;\n                // Usar o nome do paciente titular se disponível\n                const clientPersonName = (_otherParticipant_client_clientPersons = otherParticipant.client.clientPersons) === null || _otherParticipant_client_clientPersons === void 0 ? void 0 : (_otherParticipant_client_clientPersons_ = _otherParticipant_client_clientPersons[0]) === null || _otherParticipant_client_clientPersons_ === void 0 ? void 0 : (_otherParticipant_client_clientPersons__person = _otherParticipant_client_clientPersons_.person) === null || _otherParticipant_client_clientPersons__person === void 0 ? void 0 : _otherParticipant_client_clientPersons__person.fullName;\n                if (clientPersonName && clientPersonName.trim() !== '') {\n                    return clientPersonName;\n                }\n                // Fallback para fullName do cliente ou login\n                return otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '' ? otherParticipant.client.fullName : otherParticipant.client.login;\n            }\n            return 'Usuário';\n        }\n    }[\"ChatConversation.useCallback[getConversationName]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter a imagem da conversa - memoizado para evitar recalculos\n    const getConversationImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getConversationImage]\": ()=>{\n            var _conversation_participants, _otherParticipant_user;\n            if (!conversation) return null;\n            if (conversation.type === 'GROUP') {\n                return null;\n            }\n            const otherParticipant = (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.find({\n                \"ChatConversation.useCallback[getConversationImage]\": (p)=>p.userId !== (user === null || user === void 0 ? void 0 : user.id)\n            }[\"ChatConversation.useCallback[getConversationImage]\"]);\n            return otherParticipant === null || otherParticipant === void 0 ? void 0 : (_otherParticipant_user = otherParticipant.user) === null || _otherParticipant_user === void 0 ? void 0 : _otherParticipant_user.profileImageUrl;\n        }\n    }[\"ChatConversation.useCallback[getConversationImage]\"], [\n        conversation,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Obter iniciais para avatar - memoizado para evitar recalculos\n    const getInitials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[getInitials]\": (name)=>{\n            if (!name) return 'U';\n            try {\n                const names = name.split(' ');\n                if (names.length === 1) return names[0].charAt(0);\n                return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n            } catch (error) {\n                console.error('Erro ao obter iniciais:', error);\n                return 'U';\n            }\n        }\n    }[\"ChatConversation.useCallback[getInitials]\"], []);\n    // Formatar data da mensagem - memoizado para evitar recalculos\n    const formatMessageDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[formatMessageDate]\": (timestamp)=>{\n            if (!timestamp) return '';\n            try {\n                return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(new Date(timestamp), 'HH:mm', {\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.ptBR\n                });\n            } catch (error) {\n                console.error('Erro ao formatar data:', error);\n                return '';\n            }\n        }\n    }[\"ChatConversation.useCallback[formatMessageDate]\"], []);\n    // Agrupar mensagens por data - memoizado para evitar recalculos\n    const groupMessagesByDate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatConversation.useCallback[groupMessagesByDate]\": (messages)=>{\n            if (!messages || messages.length === 0) {\n                console.log(\"Nenhuma mensagem para agrupar\");\n                return [];\n            }\n            console.log(\"Agrupando \".concat(messages.length, \" mensagens\"));\n            try {\n                const groups = {};\n                // Ordenar mensagens por data (mais antigas primeiro)\n                const sortedMessages = [\n                    ...messages\n                ].sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                }[\"ChatConversation.useCallback[groupMessagesByDate].sortedMessages\"]);\n                sortedMessages.forEach({\n                    \"ChatConversation.useCallback[groupMessagesByDate]\": (message)=>{\n                        if (!message || !message.createdAt) {\n                            console.warn(\"Mensagem inválida encontrada:\", message);\n                            return;\n                        }\n                        try {\n                            const messageDate = new Date(message.createdAt);\n                            const date = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(messageDate, 'dd/MM/yyyy');\n                            if (!groups[date]) {\n                                groups[date] = [];\n                            }\n                            groups[date].push(message);\n                        } catch (err) {\n                            console.error(\"Erro ao processar mensagem:\", err, message);\n                        }\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate]\"]);\n                // Verificar se temos grupos\n                if (Object.keys(groups).length === 0) {\n                    console.warn(\"Nenhum grupo criado após processamento\");\n                    return [];\n                }\n                // Ordenar os grupos por data (mais antigos primeiro)\n                const result = Object.entries(groups).sort({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param, param1)=>{\n                        let [dateA] = param, [dateB] = param1;\n                        // Converter strings de data para objetos Date para comparação\n                        const [dayA, monthA, yearA] = dateA.split('/').map(Number);\n                        const [dayB, monthB, yearB] = dateB.split('/').map(Number);\n                        return new Date(yearA, monthA - 1, dayA) - new Date(yearB, monthB - 1, dayB);\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]).map({\n                    \"ChatConversation.useCallback[groupMessagesByDate].result\": (param)=>{\n                        let [date, messages] = param;\n                        return {\n                            date,\n                            messages\n                        };\n                    }\n                }[\"ChatConversation.useCallback[groupMessagesByDate].result\"]);\n                console.log(\"Criados \".concat(result.length, \" grupos de mensagens\"));\n                return result;\n            } catch (error) {\n                console.error('Erro ao agrupar mensagens:', error);\n                return [];\n            }\n        }\n    }[\"ChatConversation.useCallback[groupMessagesByDate]\"], []);\n    // Memoizar os grupos de mensagens para evitar recalculos\n    const messageGroups = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatConversation.useMemo[messageGroups]\": ()=>{\n            return groupMessagesByDate(conversationMessages);\n        }\n    }[\"ChatConversation.useMemo[messageGroups]\"], [\n        groupMessagesByDate,\n        conversationMessages\n    ]);\n    // Se a conversa não for encontrada, mostrar mensagem de erro\n    if (!conversation) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 text-white rounded-t-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onBack,\n                            className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                            \"aria-label\": \"Voltar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: compact ? 16 : 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-white text-lg\",\n                            children: \"Conversa n\\xe3o encontrada\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4 text-gray-500 dark:text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"A conversa n\\xe3o foi encontrada ou foi removida.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-cyan-200 dark:border-cyan-700 flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                        \"aria-label\": \"Voltar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: compact ? 16 : 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-1\",\n                        children: [\n                            getConversationImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: getConversationImage(),\n                                alt: getConversationName(),\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full object-cover\"),\n                                onError: (e)=>{\n                                    e.target.onerror = null;\n                                    e.target.style.display = 'none';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(compact ? 'h-8 w-8' : 'h-10 w-10', \" rounded-full bg-white/20 flex items-center justify-center text-white font-medium shadow-sm\"),\n                                children: getInitials(getConversationName())\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white \".concat(compact ? 'text-sm' : 'text-base'),\n                                        children: getConversationName()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (conversation === null || conversation === void 0 ? void 0 : conversation.isOnline) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-white/80\",\n                                        children: \"Online\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowOptionsMenu(!showOptionsMenu),\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Mais op\\xe7\\xf5es\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: compact ? 16 : 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, undefined),\n                            showOptionsMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 w-48 z-10\",\n                                children: isGroupChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowParticipantsList(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ver participantes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 437,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowOptionsMenu(false);\n                                                setShowAddParticipant(true);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Adicionar participante\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 449,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLeaveGroup,\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Sair do grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 461,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setConfirmAction({\n                                                    type: 'delete-group',\n                                                    title: 'Deletar grupo',\n                                                    message: 'Tem certeza que deseja deletar o grupo \"'.concat(conversation.title || 'Grupo', '\"? Todos os participantes ser\\xe3o removidos e esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.'),\n                                                    onConfirm: async ()=>{\n                                                        console.log('Deletando grupo:', conversation.id);\n                                                        try {\n                                                            const currentToken = localStorage.getItem('token');\n                                                            const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n                                                            console.log('Fazendo requisição DELETE para:', \"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id));\n                                                            // Remover todos os participantes para deletar o grupo\n                                                            const participants = conversation.participants || [];\n                                                            for (const participant of participants){\n                                                                const participantId = participant.userId || participant.clientId;\n                                                                if (participantId) {\n                                                                    await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversation.id, \"/participants/\").concat(participantId), {\n                                                                        method: 'DELETE',\n                                                                        headers: {\n                                                                            'Authorization': \"Bearer \".concat(currentToken)\n                                                                        }\n                                                                    });\n                                                                }\n                                                            }\n                                                            const response = {\n                                                                ok: true\n                                                            }; // Simular sucesso\n                                                            console.log('Resposta:', response.status, response.statusText);\n                                                            if (response.ok) {\n                                                                console.log('Grupo deletado com sucesso');\n                                                                // Usar removeParticipantFromGroup para remover o usuário atual\n                                                                await removeParticipantFromGroup(conversation.id, user.id);\n                                                                onBack();\n                                                            } else {\n                                                                const errorText = await response.text();\n                                                                console.error('Erro na resposta:', errorText);\n                                                                alert('Erro ao deletar grupo: ' + response.status);\n                                                            }\n                                                        } catch (error) {\n                                                            console.error('Erro ao deletar grupo:', error);\n                                                            alert('Erro ao deletar grupo: ' + error.message);\n                                                        }\n                                                    }\n                                                });\n                                                setConfirmDialogOpen(true);\n                                                setShowOptionsMenu(false);\n                                            },\n                                            className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-t border-gray-200 dark:border-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Deletar grupo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 470,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : // Opções para conversas individuais\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleDeleteConversation,\n                                    className: \"w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Apagar conversa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 532,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined),\n            showAddParticipant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col h-full overflow-hidden\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultiUserSearch__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddUsers: handleAddParticipants,\n                        onClose: ()=>setShowAddParticipant(false),\n                        title: \"Adicionar participantes ao grupo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 543,\n                        columnNumber: 11\n                    }, undefined),\n                    addingParticipants && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50 flex items-center justify-center z-30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-10 w-10 border-b-2 border-cyan-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 552,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 dark:text-gray-300\",\n                                    children: \"Adicionando participantes...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 553,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 551,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 550,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 542,\n                columnNumber: 9\n            }, undefined),\n            showParticipantsList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-background z-20 flex flex-col\",\n                style: {\n                    borderRadius: '0.75rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowParticipantsList(false),\n                                        className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                        \"aria-label\": \"Voltar\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-white text-base\",\n                                        children: \"Participantes do grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 564,\n                                columnNumber: 13\n                            }, undefined),\n                            isGroupAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setShowParticipantsList(false);\n                                    setShowAddParticipant(true);\n                                },\n                                className: \"p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors\",\n                                \"aria-label\": \"Adicionar participante\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 584,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                lineNumber: 576,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 563,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200 dark:divide-gray-700\",\n                            children: conversation === null || conversation === void 0 ? void 0 : (_conversation_participants = conversation.participants) === null || _conversation_participants === void 0 ? void 0 : _conversation_participants.map((participant)=>{\n                                var _participant_user;\n                                // Determinar se é usuário ou cliente e obter nome correto\n                                const getParticipantName = ()=>{\n                                    var _participant_user;\n                                    if (participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        return 'Você';\n                                    }\n                                    // Se é usuário\n                                    if ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.fullName) {\n                                        return participant.user.fullName;\n                                    }\n                                    // Se é cliente\n                                    if (participant.client) {\n                                        var _participant_client_clientPersons__person, _participant_client_clientPersons_, _participant_client_clientPersons;\n                                        const clientPersonName = (_participant_client_clientPersons = participant.client.clientPersons) === null || _participant_client_clientPersons === void 0 ? void 0 : (_participant_client_clientPersons_ = _participant_client_clientPersons[0]) === null || _participant_client_clientPersons_ === void 0 ? void 0 : (_participant_client_clientPersons__person = _participant_client_clientPersons_.person) === null || _participant_client_clientPersons__person === void 0 ? void 0 : _participant_client_clientPersons__person.fullName;\n                                        if (clientPersonName && clientPersonName.trim() !== '') {\n                                            return clientPersonName;\n                                        }\n                                        return participant.client.fullName && participant.client.fullName.trim() !== '' ? participant.client.fullName : participant.client.login;\n                                    }\n                                    return 'Usuário';\n                                };\n                                const participantName = getParticipantName();\n                                const isCurrentUser = participant.userId === (user === null || user === void 0 ? void 0 : user.id) || participant.clientId === (user === null || user === void 0 ? void 0 : user.id);\n                                const participantId = participant.userId || participant.clientId;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex-shrink-0\",\n                                            children: ((_participant_user = participant.user) === null || _participant_user === void 0 ? void 0 : _participant_user.profileImageUrl) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: participant.user.profileImageUrl,\n                                                alt: participantName,\n                                                className: \"h-10 w-10 rounded-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.target.onerror = null;\n                                                    e.target.style.display = 'none';\n                                                    e.target.parentNode.innerHTML = '<div class=\"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\">'.concat(getInitials(participantName), \"</div>\");\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 626,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\",\n                                                children: getInitials(participantName)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 637,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 624,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                    children: participantName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        participant.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-cyan-600 dark:text-cyan-400 bg-cyan-50 dark:bg-cyan-900/30 px-2 py-0.5 rounded-full\",\n                                                            children: \"Administrador\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        participant.client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"Cliente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 644,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        isGroupAdmin && !isCurrentUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                if (window.confirm(\"Tem certeza que deseja remover \".concat(participantName, \" do grupo?\"))) {\n                                                    removeParticipantFromGroup(conversation.id, participantId).catch((error)=>{\n                                                        console.error('Erro ao remover participante:', error);\n                                                        alert('Não foi possível remover o participante. Tente novamente mais tarde.');\n                                                    });\n                                                }\n                                            },\n                                            className: \"p-1.5 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n                                            \"aria-label\": \"Remover participante\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_LogOut_MoreVertical_Trash2_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 677,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                            lineNumber: 664,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, participant.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 622,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 590,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-5 space-y-5\",\n                children: [\n                    messageGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-1 bg-cyan-100 dark:bg-cyan-900/30 rounded-full text-xs text-cyan-700 dark:text-cyan-300 shadow-sm\",\n                                        children: group.date\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, undefined),\n                                group.messages.map((message)=>{\n                                    var _message_metadata;\n                                    const isOwnMessage = message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id);\n                                    const isGroupChat = conversation.type === 'GROUP';\n                                    // Encontrar o nome do remetente para mensagens de grupo\n                                    const getSenderName = ()=>{\n                                        var _message_sender;\n                                        if (message.senderId === (user === null || user === void 0 ? void 0 : user.id) || message.senderClientId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                            return 'Você';\n                                        }\n                                        // Se a mensagem tem sender (usuário)\n                                        if ((_message_sender = message.sender) === null || _message_sender === void 0 ? void 0 : _message_sender.fullName) {\n                                            return message.sender.fullName;\n                                        }\n                                        // Se a mensagem tem senderClient (cliente)\n                                        if (message.senderClient) {\n                                            var _message_senderClient_clientPersons__person, _message_senderClient_clientPersons_, _message_senderClient_clientPersons;\n                                            // Usar o nome do paciente titular se disponível\n                                            const clientPersonName = (_message_senderClient_clientPersons = message.senderClient.clientPersons) === null || _message_senderClient_clientPersons === void 0 ? void 0 : (_message_senderClient_clientPersons_ = _message_senderClient_clientPersons[0]) === null || _message_senderClient_clientPersons_ === void 0 ? void 0 : (_message_senderClient_clientPersons__person = _message_senderClient_clientPersons_.person) === null || _message_senderClient_clientPersons__person === void 0 ? void 0 : _message_senderClient_clientPersons__person.fullName;\n                                            if (clientPersonName && clientPersonName.trim() !== '') {\n                                                return clientPersonName;\n                                            }\n                                            // Fallback para fullName do cliente ou login\n                                            return message.senderClient.fullName && message.senderClient.fullName.trim() !== '' ? message.senderClient.fullName : message.senderClient.login;\n                                        }\n                                        return 'Usuário';\n                                    };\n                                    // Obter as iniciais do remetente para mensagens de grupo\n                                    const getSenderInitials = ()=>{\n                                        if (isOwnMessage) return 'Você';\n                                        const senderName = getSenderName();\n                                        return getInitials(senderName);\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(isOwnMessage ? 'justify-end' : 'justify-start'),\n                                        children: [\n                                            isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-2 flex flex-col items-center justify-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-xs font-medium\",\n                                                    children: getSenderInitials()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 744,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[75%] \".concat(compact ? 'max-w-[85%]' : ''),\n                                                children: [\n                                                    isGroupChat && !isOwnMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 ml-1\",\n                                                        children: getSenderName()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-3 rounded-lg shadow-sm \".concat(isOwnMessage ? 'bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800 text-white rounded-br-none' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none border border-cyan-100 dark:border-cyan-800'),\n                                                        children: message.contentType && [\n                                                            'SHARED_APPOINTMENT',\n                                                            'SHARED_PERSON',\n                                                            'SHARED_CLIENT',\n                                                            'SHARED_USER',\n                                                            'SHARED_SERVICE_TYPE',\n                                                            'SHARED_LOCATION',\n                                                            'SHARED_WORKING_HOURS',\n                                                            'SHARED_INSURANCE',\n                                                            'SHARED_INSURANCE_LIMIT'\n                                                        ].includes(message.contentType) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SharedItemMessage__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            message: message,\n                                                            onItemClick: handleSharedItemClickWithLog\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.contentType === 'ATTACHMENT' && ((_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.attachments) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                message.metadata.attachments.map((attachment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentViewer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        attachment: attachment,\n                                                                        compact: true\n                                                                    }, attachment.id || index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                        lineNumber: 772,\n                                                                        columnNumber: 29\n                                                                    }, undefined)),\n                                                                message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: message.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 25\n                                                        }, undefined) : message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1.5 \".concat(isOwnMessage ? 'text-right' : '', \" text-gray-500 dark:text-gray-400\"),\n                                                        children: formatMessageDate(message.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                                        lineNumber: 738,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            ]\n                        }, group.date, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, undefined)),\n                    conversationMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm\",\n                            children: \"Nenhuma mensagem ainda. Comece a conversar!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                            lineNumber: 801,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 800,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                        lineNumber: 807,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 689,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                conversationId: conversationId,\n                compact: compact\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 811,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ConfirmationDialog, {\n                isOpen: confirmDialogOpen,\n                onClose: ()=>setConfirmDialogOpen(false),\n                onConfirm: confirmAction.onConfirm,\n                title: confirmAction.title,\n                message: confirmAction.message,\n                confirmText: \"Confirmar\",\n                cancelText: \"Cancelar\",\n                variant: \"warning\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n                lineNumber: 814,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\components\\\\chat\\\\ChatConversation.js\",\n        lineNumber: 385,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatConversation, \"ToOznQniIv2EXH07YIk5AfRpQDQ=\", false, function() {\n    return [\n        _contexts_ChatContext__WEBPACK_IMPORTED_MODULE_2__.useChat,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useSharedItemNavigation__WEBPACK_IMPORTED_MODULE_9__.useSharedItemNavigation\n    ];\n});\n_c = ChatConversation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatConversation);\nvar _c;\n$RefreshReg$(_c, \"ChatConversation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/ChatConversation.js\n"));

/***/ })

});