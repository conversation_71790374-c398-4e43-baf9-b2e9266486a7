"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/ChatContext.js":
/*!*************************************!*\
  !*** ./src/contexts/ChatContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\n// Cache para evitar requisições duplicadas\nconst requestCache = {\n    conversations: {\n        data: null,\n        timestamp: 0\n    },\n    unreadCount: {\n        data: null,\n        timestamp: 0\n    },\n    messages: {},\n    tokenCheck: {\n        timestamp: 0,\n        valid: false\n    } // Cache para verificação de token\n};\n// Tempo de expiração do cache em milissegundos\nconst CACHE_EXPIRATION = 30000; // 30 segundos para dados normais\nconst TOKEN_CHECK_EXPIRATION = 60000; // 1 minuto para verificação de token\n// Função de debounce para limitar a frequência de chamadas\nconst debounce = (func, wait)=>{\n    let timeout;\n    return function executedFunction() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n};\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const { user, logout } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversation, setActiveConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPanelOpen, setIsPanelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Obter o token atual do localStorage - memoizado para evitar chamadas desnecessárias\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"ChatProvider.useCallback[getCurrentToken]\"], []);\n    // Função para lidar com erros de autenticação\n    const handleAuthError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[handleAuthError]\": ()=>{\n            console.error('Erro de autenticação detectado. Redirecionando para login...');\n            // Limpar dados locais\n            setConversations([]);\n            setMessages({});\n            setActiveConversation(null);\n            setUnreadCount(0);\n            setIsPanelOpen(false);\n            setIsModalOpen(false);\n            // Desconectar socket se existir\n            if (socket) {\n                try {\n                    socket.disconnect();\n                } catch (error) {\n                    console.error('Erro ao desconectar socket:', error);\n                }\n                setIsConnected(false);\n            }\n            // Limpar cache\n            requestCache.conversations.data = null;\n            requestCache.unreadCount.data = null;\n            requestCache.messages = {}; // Limpar cache de mensagens\n            // Redirecionar para login\n            if (logout) {\n                logout();\n            }\n        }\n    }[\"ChatProvider.useCallback[handleAuthError]\"], [\n        socket,\n        logout\n    ]);\n    // Usar referências para controlar estados que não devem causar re-renderizações\n    const socketInitializedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isLoadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const lastInitAttemptRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const reconnectAttemptsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // Redefinir a referência quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário for null (logout), desconectar o socket\n            if (!user && socket) {\n                try {\n                    socket.disconnect();\n                    setIsConnected(false);\n                    setSocket(null);\n                } catch (error) {\n                    console.error('Erro ao desconectar socket após logout:', error);\n                }\n            }\n            // Resetar estado quando o usuário muda\n            socketInitializedRef.current = false;\n            lastInitAttemptRef.current = 0;\n            reconnectAttemptsRef.current = 0;\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        socket\n    ]);\n    // Limpar estado do chat quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se o usuário mudou (novo login), limpar todo o estado do chat\n            if (user) {\n                console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);\n                // Limpar conversas e mensagens para o novo usuário\n                setConversations([]);\n                setMessages({});\n                setActiveConversation(null);\n                setUnreadCount(0);\n                setIsPanelOpen(false);\n                setIsModalOpen(false);\n                // Limpar cache\n                requestCache.conversations.data = null;\n                requestCache.unreadCount.data = null;\n                requestCache.messages = {};\n                requestCache.tokenCheck.timestamp = 0;\n                requestCache.tokenCheck.valid = false;\n                console.log('Cache limpo para novo usuário');\n                // Limpar dados residuais do localStorage relacionados ao chat\n                if (true) {\n                    // Limpar qualquer cache específico do chat que possa estar no localStorage\n                    const keysToRemove = [];\n                    for(let i = 0; i < localStorage.length; i++){\n                        const key = localStorage.key(i);\n                        if (key && (key.startsWith('chat_') || key.startsWith('conversation_') || key.startsWith('message_'))) {\n                            keysToRemove.push(key);\n                        }\n                    }\n                    keysToRemove.forEach({\n                        \"ChatProvider.useEffect\": (key)=>localStorage.removeItem(key)\n                    }[\"ChatProvider.useEffect\"]);\n                    console.log('Dados residuais do chat removidos do localStorage');\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Executar quando o ID do usuário mudar\n    // Inicializar conexão WebSocket\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Se não há usuário, não inicializar\n            if (!user) {\n                console.log('Usuário não logado, não inicializando WebSocket');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Inicializando chat para usuário:', user.role || 'USER');\n            // Se já existe um socket conectado, não fazer nada\n            if (socket && isConnected) {\n                console.log('WebSocket já está conectado, não é necessário reinicializar');\n                return;\n            }\n            // Se já está inicializado e a última tentativa foi recente, não tentar novamente\n            if (socketInitializedRef.current && Date.now() - lastInitAttemptRef.current < 60000) {\n                console.log('Socket já inicializado recentemente, aguardando...');\n                return;\n            }\n            // Marcar como inicializado imediatamente para evitar múltiplas tentativas\n            socketInitializedRef.current = true;\n            lastInitAttemptRef.current = Date.now();\n            // Função assíncrona para inicializar o WebSocket\n            const initializeWebSocket = {\n                \"ChatProvider.useEffect.initializeWebSocket\": async ()=>{\n                    // Verificar se o usuário está logado antes de inicializar o WebSocket\n                    if (!user) return;\n                    // Clientes agora podem usar o chat\n                    console.log('Inicializando WebSocket para:', user.role || 'USER');\n                    const currentToken = getCurrentToken();\n                    if (!currentToken) return;\n                    // Evitar reconexões desnecessárias - verificação adicional\n                    if (socket) {\n                        if (isConnected) {\n                            console.log('WebSocket já conectado, ignorando inicialização');\n                            return socket; // Retornar o socket existente\n                        } else {\n                            // Se o socket existe mas não está conectado, tentar reconectar em vez de criar um novo\n                            console.log('Socket existe mas não está conectado, tentando reconectar');\n                            try {\n                                // Tentar reconectar o socket existente em vez de criar um novo\n                                if (!socket.connected && socket.connect) {\n                                    socket.connect();\n                                    console.log('Tentativa de reconexão iniciada');\n                                    return socket;\n                                }\n                            } catch (error) {\n                                console.error('Erro ao reconectar socket existente:', error);\n                                // Só desconectar se a reconexão falhar\n                                try {\n                                    socket.disconnect();\n                                } catch (disconnectError) {\n                                    console.error('Erro ao desconectar socket após falha na reconexão:', disconnectError);\n                                }\n                            }\n                        }\n                    }\n                    // Marcar como inicializado para evitar múltiplas inicializações\n                    socketInitializedRef.current = true;\n                    // Limitar a frequência de reconexões\n                    const maxReconnectAttempts = 5;\n                    const reconnectDelay = 3000; // 3 segundos\n                    console.log('Inicializando WebSocket...');\n                    try {\n                        // Verificar se o token é válido antes de inicializar o WebSocket\n                        // Usar uma verificação mais simples para evitar múltiplas requisições\n                        // Assumir que o token é válido se o usuário está logado\n                        console.log('Token válido, inicializando WebSocket para o usuário:', user.fullName);\n                        console.log('Inicializando WebSocket com autenticação');\n                        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(API_URL, {\n                            path: '/socket.io',\n                            auth: {\n                                token: currentToken\n                            },\n                            transports: [\n                                'websocket',\n                                'polling'\n                            ],\n                            reconnectionAttempts: maxReconnectAttempts,\n                            reconnectionDelay: reconnectDelay,\n                            timeout: 10000,\n                            autoConnect: true,\n                            reconnection: true\n                        });\n                        socketInstance.on('connect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket connected');\n                                setIsConnected(true);\n                                reconnectAttemptsRef.current = 0;\n                                // Disparar evento personalizado para notificar componentes sobre a conexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'connected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('disconnect', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": ()=>{\n                                console.log('WebSocket disconnected');\n                                setIsConnected(false);\n                                // Disparar evento personalizado para notificar componentes sobre a desconexão\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'connection',\n                                        status: 'disconnected',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        socketInstance.on('connect_error', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (error)=>{\n                                console.error('WebSocket connection error:', error);\n                                reconnectAttemptsRef.current++;\n                                // Se o erro for de autenticação, não tentar reconectar\n                                if (error.message && error.message.includes('Authentication error')) {\n                                    console.error('Erro de autenticação no WebSocket, não tentando reconectar');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                    return;\n                                }\n                                if (reconnectAttemptsRef.current >= maxReconnectAttempts) {\n                                    console.error('Máximo de tentativas de reconexão atingido');\n                                    socketInstance.disconnect();\n                                    setIsConnected(false);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para receber novas mensagens\n                        socketInstance.on('message:new', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (message)=>{\n                                if (!message || !message.conversationId) {\n                                    console.error('Mensagem inválida recebida:', message);\n                                    return;\n                                }\n                                // Usar uma função anônima para evitar dependências circulares\n                                const userId = user === null || user === void 0 ? void 0 : user.id;\n                                // Verificar se a mensagem tem um tempId associado (para substituir mensagem temporária)\n                                // O backend pode não retornar o tempId, então precisamos verificar se é uma mensagem do usuário atual\n                                const tempId = message.tempId;\n                                const isCurrentUserMessage = message.senderId === userId;\n                                // Atualizar mensagens da conversa\n                                setMessages({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        const conversationMessages = prev[message.conversationId] || [];\n                                        // Verificar se a mensagem já existe para evitar duplicação\n                                        if (conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === message.id\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return prev;\n                                        }\n                                        // Se tiver um tempId, substituir a mensagem temporária pela mensagem real\n                                        if (tempId && conversationMessages.some({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                            return {\n                                                ...prev,\n                                                [message.conversationId]: conversationMessages.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempId ? message : m\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                            };\n                                        }\n                                        // Se for uma mensagem do usuário atual, verificar se há mensagens temporárias recentes\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId) {\n                                            // Procurar por mensagens temporárias recentes (nos últimos 10 segundos)\n                                            const now = new Date();\n                                            const tempMessages = conversationMessages.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket.tempMessages\": (m)=>{\n                                                    if (!m.isTemp) return false;\n                                                    if (!(m.senderId === userId || m.senderClientId === userId)) return false;\n                                                    if (now - new Date(m.createdAt) >= 10000) return false; // 10 segundos\n                                                    // Para mensagens com anexos, verificar se ambas são ATTACHMENT\n                                                    if (message.contentType === 'ATTACHMENT' && m.contentType === 'ATTACHMENT') {\n                                                        return true;\n                                                    }\n                                                    // Para mensagens de texto, verificar se o conteúdo é igual\n                                                    if (message.contentType === 'TEXT' && m.contentType === 'TEXT' && m.content === message.content) {\n                                                        return true;\n                                                    }\n                                                    return false;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket.tempMessages\"]);\n                                            if (tempMessages.length > 0) {\n                                                // Substituir a primeira mensagem temporária encontrada\n                                                const tempMessage = tempMessages[0];\n                                                return {\n                                                    ...prev,\n                                                    [message.conversationId]: conversationMessages.map({\n                                                        \"ChatProvider.useEffect.initializeWebSocket\": (m)=>m.id === tempMessage.id ? message : m\n                                                    }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                };\n                                            }\n                                        }\n                                        // Caso contrário, adicionar a nova mensagem\n                                        // Manter a ordem cronológica (mais antigas primeiro)\n                                        const updatedMessages = [\n                                            ...conversationMessages,\n                                            message\n                                        ].sort({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedMessages\"]);\n                                        return {\n                                            ...prev,\n                                            [message.conversationId]: updatedMessages\n                                        };\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Atualizar lista de conversas (mover para o topo)\n                                setConversations({\n                                    \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                        // Verificar se a conversa existe\n                                        const conversation = prev.find({\n                                            \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                        if (!conversation) return prev;\n                                        // Se a conversa já tiver uma mensagem temporária com o mesmo tempId, não mover para o topo\n                                        if (tempId && conversation.lastMessage && conversation.lastMessage.id === tempId) {\n                                            // Apenas atualizar a última mensagem sem reordenar\n                                            return prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                    if (c.id === message.conversationId) {\n                                                        return {\n                                                            ...c,\n                                                            lastMessage: message\n                                                        };\n                                                    }\n                                                    return c;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        }\n                                        // Se for uma mensagem do usuário atual e não tiver tempId, verificar se há uma mensagem temporária recente\n                                        const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;\n                                        if (isCurrentUserMessage && !tempId && conversation.lastMessage && conversation.lastMessage.isTemp) {\n                                            // Verificar se a última mensagem é temporária e tem o mesmo conteúdo\n                                            if (conversation.lastMessage.content === message.content && (conversation.lastMessage.senderId === userId || conversation.lastMessage.senderClientId === userId)) {\n                                                // Apenas atualizar a última mensagem sem reordenar\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>{\n                                                        if (c.id === message.conversationId) {\n                                                            return {\n                                                                ...c,\n                                                                lastMessage: message\n                                                            };\n                                                        }\n                                                        return c;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }\n                                        // Caso contrário, mover para o topo\n                                        const updatedConversations = prev.filter({\n                                            \"ChatProvider.useEffect.initializeWebSocket.updatedConversations\": (c)=>c.id !== message.conversationId\n                                        }[\"ChatProvider.useEffect.initializeWebSocket.updatedConversations\"]);\n                                        return [\n                                            {\n                                                ...conversation,\n                                                lastMessage: message\n                                            },\n                                            ...updatedConversations\n                                        ];\n                                    }\n                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                // Marcar conversa como tendo mensagens não lidas se a mensagem não for do usuário atual\n                                const isFromCurrentUser = message.senderId === userId || message.senderClientId === userId;\n                                if (!isFromCurrentUser) {\n                                    console.log('[WebSocket] Nova mensagem de outro usuário, marcando conversa como não lida');\n                                    // Verificar se a conversa já estava marcada como não lida\n                                    const conversation = conversations.find({\n                                        \"ChatProvider.useEffect.initializeWebSocket.conversation\": (c)=>c.id === message.conversationId\n                                    }[\"ChatProvider.useEffect.initializeWebSocket.conversation\"]);\n                                    const wasAlreadyUnread = (conversation === null || conversation === void 0 ? void 0 : conversation.hasUnread) || false;\n                                    // Marcar conversa como tendo mensagens não lidas\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                    if (conv.id === message.conversationId) {\n                                                        console.log(\"[WebSocket] Conversa \".concat(conv.id, \" marcada como n\\xe3o lida\"));\n                                                        return {\n                                                            ...conv,\n                                                            hasUnread: true\n                                                        };\n                                                    }\n                                                    return conv;\n                                                }\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Incrementar contador total apenas se a conversa não estava marcada como não lida\n                                    if (!wasAlreadyUnread) {\n                                        setUnreadCount({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const newCount = prev + 1;\n                                                console.log('[WebSocket] Contador de conversas não lidas atualizado de', prev, 'para', newCount);\n                                                return newCount;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                } else {\n                                    console.log('[WebSocket] Mensagem do próprio usuário, não alterando status de não lida');\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a nova mensagem\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'message',\n                                        conversationId: message.conversationId,\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de contagem de mensagens não lidas\n                        socketInstance.on('unread:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('[WebSocket] Atualização de mensagens não lidas recebida:', data);\n                                // Usar o novo campo totalUnreadConversations ou fallback para totalUnread\n                                const totalUnreadConversations = data.totalUnreadConversations || data.totalUnread || 0;\n                                if (typeof totalUnreadConversations === 'number') {\n                                    console.log(\"[WebSocket] Atualizando contador total de conversas n\\xe3o lidas para: \".concat(totalUnreadConversations));\n                                    setUnreadCount(totalUnreadConversations);\n                                    // Atualizar as conversas com status de não lidas\n                                    if (data.conversations && Array.isArray(data.conversations)) {\n                                        console.log(\"[WebSocket] Atualizando \".concat(data.conversations.length, \" conversas com status de n\\xe3o lidas\"));\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                return prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        // Procurar esta conversa nos dados de não lidas\n                                                        const unreadInfo = data.conversations.find({\n                                                            \"ChatProvider.useEffect.initializeWebSocket.unreadInfo\": (item)=>item.conversationId === conv.id\n                                                        }[\"ChatProvider.useEffect.initializeWebSocket.unreadInfo\"]);\n                                                        if (unreadInfo) {\n                                                            const hasUnread = unreadInfo.hasUnread || unreadInfo.unreadCount && unreadInfo.unreadCount > 0;\n                                                            console.log(\"[WebSocket] Conversa \".concat(conv.id, \" tem mensagens n\\xe3o lidas: \").concat(hasUnread));\n                                                            return {\n                                                                ...conv,\n                                                                hasUnread\n                                                            };\n                                                        }\n                                                        // Resetar status se não estiver na lista de não lidas\n                                                        return {\n                                                            ...conv,\n                                                            hasUnread: false\n                                                        };\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                                // Disparar evento personalizado para notificar componentes sobre a atualização de não lidas\n                                window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                    detail: {\n                                        type: 'unread',\n                                        timestamp: Date.now()\n                                    }\n                                }));\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para atualização de lista de conversas\n                        socketInstance.on('conversations:update', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Atualização de lista de conversas recebida via WebSocket:', data);\n                                // Verificar se os dados estão no formato esperado\n                                if (data) {\n                                    // Pode vir como array direto ou como objeto com propriedade conversations\n                                    const conversationsArray = Array.isArray(data) ? data : data.conversations || [];\n                                    if (Array.isArray(conversationsArray) && conversationsArray.length > 0) {\n                                        console.log(\"Atualizando \".concat(conversationsArray.length, \" conversas via WebSocket\"));\n                                        setConversations(conversationsArray);\n                                        // Atualizar a flag para indicar que os dados foram carregados\n                                        initialDataLoadedRef.current = true;\n                                        // Atualizar o cache\n                                        requestCache.conversations.data = conversationsArray;\n                                        requestCache.conversations.timestamp = Date.now();\n                                        // Disparar evento personalizado para notificar componentes sobre a atualização\n                                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                            detail: {\n                                                type: 'conversations',\n                                                timestamp: Date.now()\n                                            }\n                                        }));\n                                    } else {\n                                        console.error('Formato inválido ou array vazio de conversas recebido via WebSocket:', data);\n                                        // Se recebemos um array vazio, forçar carregamento das conversas\n                                        if (Array.isArray(conversationsArray) && conversationsArray.length === 0) {\n                                            console.log('Array vazio recebido, forçando carregamento de conversas...');\n                                            loadConversations(true);\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para notificar que uma nova conversa foi criada\n                        socketInstance.on('conversation:created', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Nova conversa criada recebida via WebSocket:', data);\n                                if (data && data.id) {\n                                    // Adicionar a nova conversa ao início da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            // Verificar se a conversa já existe\n                                            if (prev.some({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id === data.id\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])) {\n                                                return prev;\n                                            }\n                                            return [\n                                                data,\n                                                ...prev\n                                            ];\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando um participante é removido\n                        socketInstance.on('participant:removed', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Participante removido recebido via WebSocket:', data);\n                                if (data && data.conversationId && data.participantId) {\n                                    // Se o usuário atual foi removido, remover a conversa da lista\n                                    if (data.participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                        // Se era a conversa ativa, limpar\n                                        if (activeConversation === data.conversationId) {\n                                            setActiveConversation(null);\n                                        }\n                                        // Limpar mensagens da conversa\n                                        setMessages({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                                const updated = {\n                                                    ...prev\n                                                };\n                                                delete updated[data.conversationId];\n                                                return updated;\n                                            }\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    } else {\n                                        // Atualizar a lista de participantes da conversa\n                                        setConversations({\n                                            \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.map({\n                                                    \"ChatProvider.useEffect.initializeWebSocket\": (conv)=>{\n                                                        if (conv.id === data.conversationId) {\n                                                            return {\n                                                                ...conv,\n                                                                participants: conv.participants.filter({\n                                                                    \"ChatProvider.useEffect.initializeWebSocket\": (p)=>p.userId !== data.participantId && p.clientId !== data.participantId\n                                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                                            };\n                                                        }\n                                                        return conv;\n                                                    }\n                                                }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    }\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        // Evento para quando o usuário sai de uma conversa\n                        socketInstance.on('conversation:left', {\n                            \"ChatProvider.useEffect.initializeWebSocket\": (data)=>{\n                                console.log('Usuário saiu da conversa via WebSocket:', data);\n                                if (data && data.conversationId && data.userId === (user === null || user === void 0 ? void 0 : user.id)) {\n                                    // Remover a conversa da lista\n                                    setConversations({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>prev.filter({\n                                                \"ChatProvider.useEffect.initializeWebSocket\": (c)=>c.id !== data.conversationId\n                                            }[\"ChatProvider.useEffect.initializeWebSocket\"])\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Se era a conversa ativa, limpar\n                                    if (activeConversation === data.conversationId) {\n                                        setActiveConversation(null);\n                                    }\n                                    // Limpar mensagens da conversa\n                                    setMessages({\n                                        \"ChatProvider.useEffect.initializeWebSocket\": (prev)=>{\n                                            const updated = {\n                                                ...prev\n                                            };\n                                            delete updated[data.conversationId];\n                                            return updated;\n                                        }\n                                    }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                                    // Disparar evento para atualizar a interface\n                                    window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                        detail: {\n                                            type: 'conversations',\n                                            action: 'left',\n                                            conversationId: data.conversationId,\n                                            timestamp: Date.now()\n                                        }\n                                    }));\n                                }\n                            }\n                        }[\"ChatProvider.useEffect.initializeWebSocket\"]);\n                        setSocket(socketInstance);\n                        return socketInstance;\n                    } catch (error) {\n                        console.error('Erro ao inicializar WebSocket:', error);\n                        setIsConnected(false);\n                        socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                        return null;\n                    }\n                }\n            }[\"ChatProvider.useEffect.initializeWebSocket\"];\n            // Usar uma variável para controlar se já estamos tentando inicializar\n            let initializationInProgress = false;\n            // Função para inicializar com segurança\n            const safeInitialize = {\n                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                    if (initializationInProgress) {\n                        console.log('Já existe uma inicialização em andamento, ignorando');\n                        return;\n                    }\n                    initializationInProgress = true;\n                    // Usar um timeout para garantir que não tentamos inicializar muito frequentemente\n                    setTimeout({\n                        \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                            initializeWebSocket().then({\n                                \"ChatProvider.useEffect.safeInitialize\": (result)=>{\n                                    console.log('Inicialização do WebSocket concluída:', result ? 'sucesso' : 'falha');\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).catch({\n                                \"ChatProvider.useEffect.safeInitialize\": (error)=>{\n                                    console.error('Erro ao inicializar WebSocket:', error);\n                                    socketInitializedRef.current = false; // Permitir nova tentativa no futuro\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]).finally({\n                                \"ChatProvider.useEffect.safeInitialize\": ()=>{\n                                    initializationInProgress = false;\n                                }\n                            }[\"ChatProvider.useEffect.safeInitialize\"]);\n                        }\n                    }[\"ChatProvider.useEffect.safeInitialize\"], 2000); // Esperar 2 segundos antes de inicializar\n                }\n            }[\"ChatProvider.useEffect.safeInitialize\"];\n            // Chamar a função de inicialização\n            safeInitialize();\n            // Cleanup function - só desconectar quando o componente for desmontado completamente\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                    // Verificar se estamos realmente desmontando o componente (usuário fez logout)\n                    if (!user) {\n                        console.log('Usuário fez logout, desconectando socket');\n                        if (socket) {\n                            try {\n                                socket.disconnect();\n                                socketInitializedRef.current = false; // Permitir nova tentativa após cleanup\n                            } catch (error) {\n                                console.error('Erro ao desconectar socket:', error);\n                            }\n                        }\n                    } else {\n                        console.log('Componente sendo remontado, mantendo socket conectado');\n                    }\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo socket e isConnected das dependências para evitar loops\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Carregar conversas do usuário com cache\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n            console.log('loadConversations chamado com forceRefresh =', forceRefresh);\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar conversas');\n                    return [];\n                }\n                // Clientes agora podem carregar conversas\n                console.log('Carregando conversas para:', user.role || 'USER');\n                // Verificar se já está carregando\n                if (isLoading || isLoadingRef.current) {\n                    console.log('Já está carregando conversas, retornando estado atual');\n                    return conversations;\n                }\n                // Verificar se já temos conversas carregadas e não é uma atualização forçada\n                if (!forceRefresh && conversations.length > 0) {\n                    console.log('Já temos conversas carregadas e não é uma atualização forçada, retornando estado atual');\n                    return conversations;\n                }\n                // Marcar como carregando para evitar chamadas simultâneas\n                isLoadingRef.current = true;\n                // Verificar cache apenas se não for refresh forçado\n                const now = Date.now();\n                if (!forceRefresh && requestCache.conversations.data && requestCache.conversations.data.length > 0 && now - requestCache.conversations.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para conversas');\n                    return requestCache.conversations.data;\n                }\n                console.log('Buscando conversas atualizadas da API');\n                const currentToken = getCurrentToken();\n                console.log('Token ao carregar conversas:', currentToken ? 'Disponível' : 'Não disponível');\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar conversas');\n                    return [];\n                }\n                setIsLoading(true);\n                try {\n                    console.log('Buscando conversas da API...');\n                    console.log('Fazendo requisição para:', \"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"));\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations?includeMessages=true\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    console.log('Status da resposta:', response.status, response.statusText);\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar conversas');\n                            handleAuthError();\n                            return [];\n                        }\n                        throw new Error(\"Erro ao carregar conversas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de conversas:', data);\n                    if (data.success) {\n                        var _data_data;\n                        // Verificar se há dados válidos\n                        // A API retorna { success: true, data: { conversations: [...], total, limit, offset } }\n                        console.log('Estrutura completa da resposta da API:', JSON.stringify(data));\n                        const conversationsArray = ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.conversations) || [];\n                        console.log('Array de conversas extraído:', conversationsArray);\n                        if (!Array.isArray(conversationsArray)) {\n                            console.error('Resposta da API não contém um array de conversas:', data);\n                            return [];\n                        }\n                        console.log(\"Recebidas \".concat(conversationsArray.length, \" conversas da API\"));\n                        // Processar as últimas mensagens das conversas\n                        console.log('Processando últimas mensagens das conversas...');\n                        const processedConversations = conversationsArray.map({\n                            \"ChatProvider.useCallback[loadConversations].processedConversations\": (conversation)=>{\n                                // Verificar se a conversa tem mensagens\n                                if (conversation.messages && conversation.messages.length > 0) {\n                                    console.log(\"Conversa \".concat(conversation.id, \" tem \").concat(conversation.messages.length, \" mensagens\"));\n                                    // Extrair a última mensagem\n                                    const lastMessage = conversation.messages[0]; // A primeira mensagem é a mais recente (ordenada por createdId desc)\n                                    console.log('Ultima mensagem:', lastMessage);\n                                    // IMPORTANTE: Também salvar todas as mensagens desta conversa no estado\n                                    const sortedMessages = [\n                                        ...conversation.messages\n                                    ].sort({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations.sortedMessages\"]);\n                                    console.log(\"DEBUG: Salvando \".concat(sortedMessages.length, \" mensagens da conversa \").concat(conversation.id), sortedMessages);\n                                    // Atualizar estado das mensagens\n                                    setMessages({\n                                        \"ChatProvider.useCallback[loadConversations].processedConversations\": (prev)=>({\n                                                ...prev,\n                                                [conversation.id]: sortedMessages\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                                    // Remover o array de mensagens para evitar duplicação\n                                    const { messages, ...conversationWithoutMessages } = conversation;\n                                    // Adicionar a última mensagem como propriedade lastMessage\n                                    return {\n                                        ...conversationWithoutMessages,\n                                        lastMessage\n                                    };\n                                } else {\n                                    console.log(\"Conversa \".concat(conversation.id, \" n\\xe3o tem mensagens\"));\n                                }\n                                return conversation;\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations].processedConversations\"]);\n                        // Log para debug dos dados das conversas\n                        console.log('Conversas processadas:', processedConversations.map({\n                            \"ChatProvider.useCallback[loadConversations]\": (c)=>{\n                                var _c_participants;\n                                return {\n                                    id: c.id,\n                                    participants: (_c_participants = c.participants) === null || _c_participants === void 0 ? void 0 : _c_participants.map({\n                                        \"ChatProvider.useCallback[loadConversations]\": (p)=>({\n                                                userId: p.userId,\n                                                clientId: p.clientId,\n                                                user: p.user,\n                                                client: p.client\n                                            })\n                                    }[\"ChatProvider.useCallback[loadConversations]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[loadConversations]\"]));\n                        // Atualizar cache com as conversas processadas\n                        const now = Date.now();\n                        requestCache.conversations.data = processedConversations;\n                        requestCache.conversations.timestamp = now;\n                        // Atualizar estado - garantir que estamos atualizando o estado mesmo se o array estiver vazio\n                        setConversations(processedConversations);\n                        // Disparar evento para notificar componentes sobre a atualização\n                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                            detail: {\n                                type: 'conversations',\n                                timestamp: Date.now()\n                            }\n                        }));\n                        return conversationsArray;\n                    } else {\n                        console.error('Resposta da API não foi bem-sucedida:', data);\n                        return [];\n                    }\n                } catch (error) {\n                    console.error('Error loading conversations:', error);\n                    return [];\n                } finally{\n                    setIsLoading(false);\n                    isLoadingRef.current = false;\n                }\n            } catch (error) {\n                console.error('Error in loadConversations:', error);\n                return [];\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadConversations]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError,\n        isLoading\n    ]);\n    // Carregar mensagens de uma conversa com cache aprimorado\n    const loadMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadMessages]\": async (conversationId)=>{\n            try {\n                var _requestCache_messages_conversationId;\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar mensagens');\n                    return;\n                }\n                // Verificar cache de mensagens\n                const now = Date.now();\n                if (requestCache.messages[conversationId] && now - requestCache.messages[conversationId].timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para mensagens da conversa:', conversationId);\n                    return requestCache.messages[conversationId].data;\n                }\n                // Verificar se já tem mensagens completas carregadas no cache\n                // Não usar o estado messages aqui pois pode conter apenas a última mensagem do loadConversations\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar mensagens');\n                    return;\n                }\n                // Evitar múltiplas requisições simultâneas para a mesma conversa\n                if ((_requestCache_messages_conversationId = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId === void 0 ? void 0 : _requestCache_messages_conversationId.loading) {\n                    console.log('Já existe uma requisição em andamento para esta conversa');\n                    return;\n                }\n                // Marcar como carregando\n                requestCache.messages[conversationId] = {\n                    loading: true\n                };\n                try {\n                    console.log('Buscando mensagens da API para conversa:', conversationId);\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/messages\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar mensagens');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar mensagens: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    if (data.success) {\n                        // Debug para verificar mensagens carregadas\n                        console.log('DEBUG: Mensagens carregadas para conversa', conversationId, data.data);\n                        // Atualizar cache\n                        requestCache.messages[conversationId] = {\n                            data: data.data,\n                            timestamp: now,\n                            loading: false\n                        };\n                        // Atualizar estado das mensagens\n                        // Garantir que as mensagens estejam na ordem correta (mais antigas primeiro)\n                        // para que a ordenação no componente funcione corretamente\n                        const sortedMessages = [\n                            ...data.data\n                        ].sort({\n                            \"ChatProvider.useCallback[loadMessages].sortedMessages\": (a, b)=>new Date(a.createdAt) - new Date(b.createdAt)\n                        }[\"ChatProvider.useCallback[loadMessages].sortedMessages\"]);\n                        console.log('DEBUG: Mensagens ordenadas', sortedMessages);\n                        setMessages({\n                            \"ChatProvider.useCallback[loadMessages]\": (prev)=>({\n                                    ...prev,\n                                    [conversationId]: sortedMessages\n                                })\n                        }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        // Atualizar a última mensagem da conversa\n                        if (data.data && data.data.length > 0) {\n                            const lastMessage = data.data[data.data.length - 1];\n                            setConversations({\n                                \"ChatProvider.useCallback[loadMessages]\": (prev)=>{\n                                    return prev.map({\n                                        \"ChatProvider.useCallback[loadMessages]\": (conv)=>{\n                                            if (conv.id === conversationId && (!conv.lastMessage || new Date(lastMessage.createdAt) > new Date(conv.lastMessage.createdAt))) {\n                                                return {\n                                                    ...conv,\n                                                    lastMessage\n                                                };\n                                            }\n                                            return conv;\n                                        }\n                                    }[\"ChatProvider.useCallback[loadMessages]\"]);\n                                }\n                            }[\"ChatProvider.useCallback[loadMessages]\"]);\n                        }\n                        return data.data;\n                    }\n                } catch (error) {\n                    console.error('Error loading messages:', error);\n                } finally{\n                    var _requestCache_messages_conversationId1;\n                    // Remover flag de carregamento em caso de erro\n                    if ((_requestCache_messages_conversationId1 = requestCache.messages[conversationId]) === null || _requestCache_messages_conversationId1 === void 0 ? void 0 : _requestCache_messages_conversationId1.loading) {\n                        requestCache.messages[conversationId].loading = false;\n                    }\n                }\n            } catch (error) {\n                console.error('Error in loadMessages:', error);\n            }\n        // Removendo messages das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadMessages]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Enviar mensagem\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[sendMessage]\": function(conversationId, content) {\n            let contentType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'TEXT', metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            if (!socket || !isConnected) return;\n            // Gerar um ID temporário para a mensagem\n            const tempId = \"temp-\".concat(Date.now());\n            // Criar uma mensagem temporária para exibir imediatamente\n            const tempMessage = {\n                id: tempId,\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                senderId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : user === null || user === void 0 ? void 0 : user.id,\n                senderClientId: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? user === null || user === void 0 ? void 0 : user.id : null,\n                createdAt: new Date().toISOString(),\n                sender: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? null : {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: user === null || user === void 0 ? void 0 : user.fullName\n                },\n                senderClient: (user === null || user === void 0 ? void 0 : user.isClient) || (user === null || user === void 0 ? void 0 : user.role) === 'CLIENT' ? {\n                    id: user === null || user === void 0 ? void 0 : user.id,\n                    fullName: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.login),\n                    login: user === null || user === void 0 ? void 0 : user.login\n                } : null,\n                // Marcar como temporária para evitar duplicação\n                isTemp: true\n            };\n            // Atualizar mensagens localmente antes de enviar\n            setMessages({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>({\n                        ...prev,\n                        [conversationId]: [\n                            ...prev[conversationId] || [],\n                            tempMessage\n                        ]\n                    })\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Atualizar a conversa com a última mensagem\n            setConversations({\n                \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                    return prev.map({\n                        \"ChatProvider.useCallback[sendMessage]\": (conv)=>{\n                            if (conv.id === conversationId) {\n                                return {\n                                    ...conv,\n                                    lastMessage: tempMessage\n                                };\n                            }\n                            return conv;\n                        }\n                    }[\"ChatProvider.useCallback[sendMessage]\"]);\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n            // Enviar a mensagem via WebSocket\n            const messageData = {\n                conversationId,\n                content,\n                contentType,\n                metadata,\n                tempId\n            };\n            socket.emit('message:send', messageData, {\n                \"ChatProvider.useCallback[sendMessage]\": (response)=>{\n                    if (response.success) {\n                    // A mensagem real será adicionada pelo evento message:new do WebSocket\n                    // Não precisamos fazer nada aqui, pois o WebSocket vai atualizar a mensagem\n                    } else {\n                        console.error('Error sending message:', response.error);\n                        // Em caso de erro, podemos marcar a mensagem como falha\n                        setMessages({\n                            \"ChatProvider.useCallback[sendMessage]\": (prev)=>{\n                                const conversationMessages = prev[conversationId] || [];\n                                return {\n                                    ...prev,\n                                    [conversationId]: conversationMessages.map({\n                                        \"ChatProvider.useCallback[sendMessage]\": (msg)=>msg.id === tempId ? {\n                                                ...msg,\n                                                failed: true\n                                            } : msg\n                                    }[\"ChatProvider.useCallback[sendMessage]\"])\n                                };\n                            }\n                        }[\"ChatProvider.useCallback[sendMessage]\"]);\n                    }\n                }\n            }[\"ChatProvider.useCallback[sendMessage]\"]);\n        }\n    }[\"ChatProvider.useCallback[sendMessage]\"], [\n        socket,\n        isConnected,\n        user\n    ]);\n    // Criar nova conversa\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createConversation]\": async function(participantIds) {\n            let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n            try {\n                console.log('Criando conversa com participantes:', participantIds);\n                console.log('Tipo de conversa:', participantIds.length > 1 ? 'GRUPO' : 'INDIVIDUAL');\n                console.log('Título da conversa:', title);\n                // ✅ VALIDAÇÃO: System admin não pode criar grupos com usuários de empresas diferentes\n                if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && participantIds.length > 1) {\n                    console.log('Validando empresas para system admin...');\n                    // Buscar informações dos participantes para validar empresas\n                    const currentToken = getCurrentToken();\n                    if (currentToken) {\n                        try {\n                            const participantPromises = participantIds.map({\n                                \"ChatProvider.useCallback[createConversation].participantPromises\": async (id)=>{\n                                    const response = await fetch(\"\".concat(API_URL, \"/users/\").concat(id), {\n                                        headers: {\n                                            Authorization: \"Bearer \".concat(currentToken)\n                                        }\n                                    });\n                                    if (response.ok) {\n                                        const userData = await response.json();\n                                        return userData.success ? userData.data : userData;\n                                    }\n                                    return null;\n                                }\n                            }[\"ChatProvider.useCallback[createConversation].participantPromises\"]);\n                            const participants = await Promise.all(participantPromises);\n                            console.log('Participantes carregados:', participants);\n                            const validParticipants = participants.filter({\n                                \"ChatProvider.useCallback[createConversation].validParticipants\": (p)=>p && p.companyId\n                            }[\"ChatProvider.useCallback[createConversation].validParticipants\"]);\n                            console.log('Participantes válidos:', validParticipants);\n                            if (validParticipants.length > 1) {\n                                const companies = [\n                                    ...new Set(validParticipants.map({\n                                        \"ChatProvider.useCallback[createConversation]\": (p)=>p.companyId\n                                    }[\"ChatProvider.useCallback[createConversation]\"]))\n                                ];\n                                console.log('Empresas encontradas:', companies);\n                                if (companies.length > 1) {\n                                    alert('Não é possível criar grupos com usuários de empresas diferentes.');\n                                    return null;\n                                }\n                            }\n                        } catch (validationError) {\n                            console.error('Erro na validação de empresas:', validationError);\n                            alert('Erro na validação: ' + validationError.message);\n                            return null;\n                        }\n                    }\n                }\n                // Verificar se algum participante é cliente\n                const hasClientParticipants = participantIds.some({\n                    \"ChatProvider.useCallback[createConversation].hasClientParticipants\": (id)=>{\n                        // Verificar se o ID corresponde a um cliente (assumindo que clientes têm isClient: true)\n                        return typeof id === 'object' && id.isClient;\n                    }\n                }[\"ChatProvider.useCallback[createConversation].hasClientParticipants\"]);\n                console.log('Tem participantes clientes:', hasClientParticipants);\n                // Obter o token mais recente do localStorage\n                const currentToken = getCurrentToken();\n                console.log('Token disponível:', currentToken ? 'Sim' : 'Não');\n                if (!currentToken) {\n                    console.error('Token não disponível. Usuário precisa fazer login novamente.');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        type: participantIds.length > 1 ? 'GROUP' : 'INDIVIDUAL',\n                        title,\n                        participantIds: participantIds.map({\n                            \"ChatProvider.useCallback[createConversation]\": (id)=>typeof id === 'object' ? id.id : id\n                        }[\"ChatProvider.useCallback[createConversation]\"]),\n                        includeClients: true // Permitir incluir clientes nas conversas\n                    })\n                });\n                console.log('Resposta da API:', response.status, response.statusText);\n                const data = await response.json();\n                console.log('Dados da resposta:', data);\n                if (data.success) {\n                    // Extrair os dados da conversa criada\n                    const conversationData = data.data;\n                    console.log('Conversa criada com sucesso:', conversationData);\n                    // Adicionar a nova conversa à lista\n                    setConversations({\n                        \"ChatProvider.useCallback[createConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createConversation]\": (c)=>c.id === conversationData.id\n                            }[\"ChatProvider.useCallback[createConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                conversationData,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"]);\n                    // Disparar evento para notificar componentes sobre a nova conversa\n                    setTimeout({\n                        \"ChatProvider.useCallback[createConversation]\": ()=>{\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: conversationData.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createConversation]\"], 300);\n                    return conversationData;\n                } else {\n                    console.error('Erro ao criar conversa:', data.error || 'Erro desconhecido');\n                    // Se o erro for de autenticação, redirecionar para login\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido. Redirecionando para login...');\n                        handleAuthError();\n                    }\n                }\n                return null;\n            } catch (error) {\n                console.error('Error creating conversation:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[createConversation]\"], [\n        handleAuthError\n    ]);\n    // Criar ou obter conversa com um usuário específico\n    const createOrGetConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createOrGetConversation]\": async (otherUser)=>{\n            try {\n                console.log('createOrGetConversation chamado com usuário:', otherUser);\n                if (!otherUser || !otherUser.id) {\n                    console.error('Usuário inválido:', otherUser);\n                    return null;\n                }\n                // Primeiro, verificar se já existe uma conversa individual com este usuário\n                const existingConversation = conversations.find({\n                    \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (conv)=>{\n                        var _conv_participants;\n                        if (conv.type !== 'INDIVIDUAL') return false;\n                        return (_conv_participants = conv.participants) === null || _conv_participants === void 0 ? void 0 : _conv_participants.some({\n                            \"ChatProvider.useCallback[createOrGetConversation].existingConversation\": (p)=>p.userId === otherUser.id\n                        }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                    }\n                }[\"ChatProvider.useCallback[createOrGetConversation].existingConversation\"]);\n                if (existingConversation) {\n                    console.log('Conversa existente encontrada:', existingConversation);\n                    setActiveConversation(existingConversation.id);\n                    return existingConversation;\n                }\n                console.log('Criando nova conversa com usuário:', otherUser.fullName);\n                // Se não existir, criar uma nova conversa\n                const newConversation = await createConversation([\n                    otherUser.id\n                ]);\n                if (newConversation) {\n                    console.log('Nova conversa criada com sucesso:', newConversation);\n                    // Garantir que a conversa seja adicionada à lista antes de definir como ativa\n                    setConversations({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": (prev)=>{\n                            // Verificar se a conversa já existe na lista para evitar duplicação\n                            if (prev.some({\n                                \"ChatProvider.useCallback[createOrGetConversation]\": (c)=>c.id === newConversation.id\n                            }[\"ChatProvider.useCallback[createOrGetConversation]\"])) {\n                                return prev;\n                            }\n                            return [\n                                newConversation,\n                                ...prev\n                            ];\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"]);\n                    // Definir a conversa como ativa após um pequeno delay para garantir que a lista foi atualizada\n                    setTimeout({\n                        \"ChatProvider.useCallback[createOrGetConversation]\": ()=>{\n                            setActiveConversation(newConversation.id);\n                            // Disparar evento para notificar componentes sobre a nova conversa\n                            window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                                detail: {\n                                    type: 'conversations',\n                                    conversationId: newConversation.id,\n                                    timestamp: Date.now()\n                                }\n                            }));\n                        }\n                    }[\"ChatProvider.useCallback[createOrGetConversation]\"], 300);\n                } else {\n                    console.error('Falha ao criar nova conversa, retorno nulo ou indefinido');\n                }\n                return newConversation;\n            } catch (error) {\n                console.error('Error creating or getting conversation:', error);\n                return null;\n            }\n        // Removendo conversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[createOrGetConversation]\"], [\n        createConversation,\n        setActiveConversation,\n        handleAuthError\n    ]);\n    // Carregar contagem de mensagens não lidas com cache\n    const loadUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[loadUnreadCount]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            try {\n                // Verificar se o usuário está logado\n                if (!user) {\n                    console.error('Usuário não logado ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                // Verificar cache\n                const now = Date.now();\n                if (!forceRefresh && requestCache.unreadCount.data !== null && now - requestCache.unreadCount.timestamp < CACHE_EXPIRATION) {\n                    console.log('Usando cache para contagem de mensagens não lidas');\n                    return requestCache.unreadCount.data;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao carregar contagem de mensagens não lidas');\n                    return;\n                }\n                try {\n                    console.log('Buscando contagem de mensagens não lidas da API...');\n                    const response = await fetch(\"\".concat(API_URL, \"/chat/messages/unread\"), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(currentToken)\n                        }\n                    });\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            console.error('Token expirado ou inválido ao carregar contagem de mensagens não lidas');\n                            handleAuthError();\n                            return;\n                        }\n                        throw new Error(\"Erro ao carregar contagem de mensagens n\\xe3o lidas: \".concat(response.status));\n                    }\n                    const data = await response.json();\n                    console.log('Resposta da API de mensagens não lidas:', data);\n                    if (data.success) {\n                        console.log('Dados de mensagens não lidas recebidos:', data.data);\n                        // Usar o novo campo totalUnreadConversations ou fallback para totalUnread\n                        const totalUnreadConversations = data.data.totalUnreadConversations || data.data.totalUnread || 0;\n                        // Atualizar cache com o totalUnreadConversations\n                        requestCache.unreadCount.data = totalUnreadConversations;\n                        requestCache.unreadCount.timestamp = now;\n                        // Atualizar estado\n                        setUnreadCount(totalUnreadConversations);\n                        // Atualizar as conversas com status de não lidas\n                        if (data.data.conversations && Array.isArray(data.data.conversations)) {\n                            // Primeiro, verificar se já temos as conversas carregadas\n                            const conversationIds = data.data.conversations.map({\n                                \"ChatProvider.useCallback[loadUnreadCount].conversationIds\": (c)=>c.conversationId\n                            }[\"ChatProvider.useCallback[loadUnreadCount].conversationIds\"]);\n                            // Se não temos conversas carregadas ou se temos menos conversas do que as não lidas,\n                            // forçar uma atualização das conversas\n                            if (conversations.length === 0 || !conversationIds.every({\n                                \"ChatProvider.useCallback[loadUnreadCount]\": (id)=>conversations.some({\n                                        \"ChatProvider.useCallback[loadUnreadCount]\": (c)=>c.id === id\n                                    }[\"ChatProvider.useCallback[loadUnreadCount]\"])\n                            }[\"ChatProvider.useCallback[loadUnreadCount]\"])) {\n                                console.log('Forçando atualização das conversas porque há mensagens não lidas em conversas não carregadas');\n                                await loadConversations(true);\n                            } else {\n                                // Atualizar as conversas existentes com status de não lidas\n                                setConversations({\n                                    \"ChatProvider.useCallback[loadUnreadCount]\": (prev)=>{\n                                        return prev.map({\n                                            \"ChatProvider.useCallback[loadUnreadCount]\": (conv)=>{\n                                                // Procurar esta conversa nos dados de não lidas\n                                                const unreadInfo = data.data.conversations.find({\n                                                    \"ChatProvider.useCallback[loadUnreadCount].unreadInfo\": (item)=>item.conversationId === conv.id\n                                                }[\"ChatProvider.useCallback[loadUnreadCount].unreadInfo\"]);\n                                                if (unreadInfo) {\n                                                    const hasUnread = unreadInfo.hasUnread || unreadInfo.unreadCount && unreadInfo.unreadCount > 0;\n                                                    return {\n                                                        ...conv,\n                                                        hasUnread\n                                                    };\n                                                }\n                                                return {\n                                                    ...conv,\n                                                    hasUnread: false\n                                                };\n                                            }\n                                        }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                                    }\n                                }[\"ChatProvider.useCallback[loadUnreadCount]\"]);\n                            }\n                        }\n                        return totalUnreadConversations;\n                    }\n                } catch (error) {\n                    console.error('Error loading unread count:', error);\n                }\n            } catch (error) {\n                console.error('Error in loadUnreadCount:', error);\n            }\n        // Removendo conversations e loadConversations das dependências para evitar loops infinitos\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useCallback[loadUnreadCount]\"], [\n        user,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Referência para controlar se os dados iniciais já foram carregados\n    const initialDataLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Resetar a flag quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user) {\n                console.log('Usuário alterado, resetando flag de dados carregados');\n                initialDataLoadedRef.current = false;\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Dependendo apenas do ID do usuário para evitar re-renders desnecessários\n    // Efeito para carregar dados iniciais e configurar atualização periódica\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de carregar dados\n            if (!user) {\n                console.log('Usuário não logado, não carregando dados iniciais');\n                return;\n            }\n            // Clientes agora podem usar o chat\n            console.log('Carregando dados iniciais para:', user.role || 'USER');\n            // Verificar token\n            const currentToken = getCurrentToken();\n            if (!currentToken) {\n                console.log('Token não disponível, não carregando dados iniciais');\n                return;\n            }\n            // Verificar se já está carregando\n            if (isLoading || isLoadingRef.current) {\n                console.log('Já está carregando dados, aguardando...');\n                return;\n            }\n            // Verificar se os dados já foram carregados\n            if (initialDataLoadedRef.current) {\n                console.log('Dados já foram carregados anteriormente, ignorando');\n                return;\n            }\n            // Função para carregar dados iniciais\n            const loadInitialData = {\n                \"ChatProvider.useEffect.loadInitialData\": async ()=>{\n                    if (isLoading || isLoadingRef.current) {\n                        console.log('Já está carregando dados, cancelando carregamento inicial');\n                        return;\n                    }\n                    // Marcar como carregando\n                    isLoadingRef.current = true;\n                    console.log('Iniciando carregamento de dados do chat...');\n                    try {\n                        // Primeiro carregar as conversas\n                        console.log('Carregando conversas...');\n                        const conversationsData = await loadConversations(true);\n                        console.log('Conversas carregadas:', (conversationsData === null || conversationsData === void 0 ? void 0 : conversationsData.length) || 0, 'conversas');\n                        // Carregar contagem de mensagens não lidas\n                        console.log('Carregando contagem de mensagens não lidas...');\n                        await loadUnreadCount(true);\n                        console.log('Contagem de não lidas carregada');\n                        // Marcar como carregado\n                        initialDataLoadedRef.current = true;\n                        console.log('Carregamento de dados concluído com sucesso');\n                    } catch (error) {\n                        console.error('Erro ao carregar dados:', error);\n                    } finally{\n                        isLoadingRef.current = false;\n                    }\n                }\n            }[\"ChatProvider.useEffect.loadInitialData\"];\n            // Carregar dados iniciais com debounce para evitar múltiplas chamadas\n            console.log('Agendando carregamento de dados iniciais...');\n            const debouncedLoadData = debounce({\n                \"ChatProvider.useEffect.debouncedLoadData\": ()=>{\n                    console.log('Carregando dados iniciais (debounced)...');\n                    loadInitialData();\n                }\n            }[\"ChatProvider.useEffect.debouncedLoadData\"], 1000); // Esperar 1 segundo antes de carregar\n            // Chamar a função com debounce\n            debouncedLoadData();\n            // Removemos a atualização periódica via HTTP para evitar flood no backend\n            // Agora dependemos apenas do WebSocket para atualizações em tempo real\n            return ({\n                \"ChatProvider.useEffect\": ()=>{\n                // Cleanup function\n                }\n            })[\"ChatProvider.useEffect\"];\n        // Removendo loadUnreadCount e isPanelOpen, isModalOpen das dependências para evitar chamadas desnecessárias\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken,\n        loadConversations,\n        isLoading\n    ]);\n    // Efeito para verificar se activeConversation é válido\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (activeConversation && conversations.length > 0) {\n                const conversationExists = conversations.some({\n                    \"ChatProvider.useEffect.conversationExists\": (c)=>c.id === activeConversation\n                }[\"ChatProvider.useEffect.conversationExists\"]);\n                if (!conversationExists) {\n                    console.warn('Conversa ativa não encontrada na lista de conversas, resetando...', activeConversation);\n                    setActiveConversation(null);\n                }\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        activeConversation,\n        conversations\n    ]);\n    // Efeito para monitorar mudanças no token e reconectar quando necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            // Verificar se o usuário está logado antes de monitorar o token\n            if (!user) return;\n            // Verificar o token a cada 2 minutos (aumentado para reduzir a frequência)\n            const tokenCheckInterval = setInterval({\n                \"ChatProvider.useEffect.tokenCheckInterval\": ()=>{\n                    // Evitar verificações desnecessárias se estiver carregando\n                    if (isLoading || isLoadingRef.current) return;\n                    const currentToken = getCurrentToken();\n                    // Se não há token mas há conexão, desconectar\n                    if (!currentToken && isConnected && socket) {\n                        console.log('Token não encontrado, desconectando WebSocket...');\n                        try {\n                            socket.disconnect();\n                        } catch (error) {\n                            console.error('Erro ao desconectar socket:', error);\n                        }\n                        setIsConnected(false);\n                    }\n                    // Se há token mas não há conexão, tentar reconectar (com verificação de tempo)\n                    if (currentToken && !isConnected && !socket) {\n                        const now = Date.now();\n                        // Limitar tentativas de reconexão (no máximo uma a cada 2 minutos)\n                        if (now - lastInitAttemptRef.current > 120000) {\n                            console.log('Token encontrado, tentando reconectar WebSocket...');\n                            // Permitir nova tentativa de inicialização do WebSocket\n                            socketInitializedRef.current = false;\n                            lastInitAttemptRef.current = now;\n                        }\n                    }\n                }\n            }[\"ChatProvider.useEffect.tokenCheckInterval\"], 120000); // Verificar a cada 2 minutos\n            return ({\n                \"ChatProvider.useEffect\": ()=>clearInterval(tokenCheckInterval)\n            })[\"ChatProvider.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Abrir/fechar painel de chat\n    const toggleChatPanel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatPanel]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsPanelOpen({\n                \"ChatProvider.useCallback[toggleChatPanel]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o painel, fechar o modal\n                    if (newState) {\n                        setIsModalOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatPanel]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatPanel]\"], [\n        user\n    ]);\n    // Abrir/fechar modal de chat\n    const toggleChatModal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[toggleChatModal]\": ()=>{\n            // Verificar se o usuário está logado\n            if (!user) return;\n            setIsModalOpen({\n                \"ChatProvider.useCallback[toggleChatModal]\": (prev)=>{\n                    const newState = !prev;\n                    // Se estiver abrindo o modal, fechar o painel\n                    if (newState) {\n                        setIsPanelOpen(false);\n                    }\n                    return newState;\n                }\n            }[\"ChatProvider.useCallback[toggleChatModal]\"]);\n        }\n    }[\"ChatProvider.useCallback[toggleChatModal]\"], [\n        user\n    ]);\n    // Verificar se createConversation é uma função válida\n    console.log('ChatContext: createConversation é uma função?', typeof createConversation === 'function');\n    // Adicionar participante a um grupo\n    const addParticipantToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addParticipantToGroup]\": async (conversationId, participantId)=>{\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao adicionar participante');\n                    return null;\n                }\n                console.log(\"Adicionando participante \".concat(participantId, \" \\xe0 conversa \").concat(conversationId));\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        participantId\n                    })\n                });\n                console.log(\"Resposta da API: \".concat(response.status, \" \").concat(response.statusText));\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao adicionar participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    // Log detalhado do erro\n                    const errorText = await response.text();\n                    console.error(\"Erro \".concat(response.status, \" ao adicionar participante:\"), errorText);\n                    throw new Error(\"Erro ao adicionar participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Atualizar a conversa com o novo participante\n                    setConversations({\n                        \"ChatProvider.useCallback[addParticipantToGroup]\": (prev)=>{\n                            return prev.map({\n                                \"ChatProvider.useCallback[addParticipantToGroup]\": (conv)=>{\n                                    if (conv.id === conversationId) {\n                                        // Verificar se o participante já existe\n                                        const participantExists = conv.participants.some({\n                                            \"ChatProvider.useCallback[addParticipantToGroup].participantExists\": (p)=>p.userId === participantId\n                                        }[\"ChatProvider.useCallback[addParticipantToGroup].participantExists\"]);\n                                        if (participantExists) {\n                                            return conv;\n                                        }\n                                        // Adicionar o novo participante\n                                        return {\n                                            ...conv,\n                                            participants: [\n                                                ...conv.participants,\n                                                data.data\n                                            ]\n                                        };\n                                    }\n                                    return conv;\n                                }\n                            }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                        }\n                    }[\"ChatProvider.useCallback[addParticipantToGroup]\"]);\n                    // Criar uma mensagem do sistema para notificar que um participante foi adicionado\n                    if (socket && isConnected) {\n                        const systemMessage = {\n                            conversationId,\n                            content: \"\".concat(data.data.user.fullName, \" foi adicionado ao grupo\"),\n                            contentType: 'SYSTEM'\n                        };\n                        socket.emit('message:send', systemMessage);\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao adicionar participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[addParticipantToGroup]\"], [\n        socket,\n        isConnected,\n        getCurrentToken,\n        handleAuthError\n    ]);\n    // Adicionar múltiplos participantes a um grupo\n    const addMultipleParticipantsToGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[addMultipleParticipantsToGroup]\": async (conversationId, participants)=>{\n            try {\n                if (!conversationId || !participants || !participants.length) {\n                    console.error('ID da conversa e lista de participantes são obrigatórios');\n                    return null;\n                }\n                console.log(\"Adicionando \".concat(participants.length, \" participantes ao grupo \").concat(conversationId));\n                // Array para armazenar os resultados\n                const results = [];\n                const errors = [];\n                // Adicionar participantes um por um\n                for (const participant of participants){\n                    try {\n                        const result = await addParticipantToGroup(conversationId, participant.id);\n                        if (result) {\n                            results.push(result);\n                        } else {\n                            errors.push({\n                                user: participant,\n                                error: 'Falha ao adicionar participante'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao adicionar participante \".concat(participant.id, \":\"), error);\n                        errors.push({\n                            user: participant,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    added: results,\n                    errors: errors,\n                    total: participants.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao adicionar múltiplos participantes:', error);\n                return {\n                    success: false,\n                    added: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: participants.length,\n                    successCount: 0,\n                    errorCount: participants.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[addMultipleParticipantsToGroup]\"], [\n        addParticipantToGroup\n    ]);\n    // Remover participante de um grupo (ou sair do grupo)\n    const removeParticipantFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[removeParticipantFromGroup]\": async (conversationId, participantId)=>{\n            console.log('removeParticipantFromGroup chamado:', {\n                conversationId,\n                participantId,\n                userId: user === null || user === void 0 ? void 0 : user.id\n            });\n            try {\n                if (!conversationId || !participantId) {\n                    console.error('ID da conversa e ID do participante são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao remover participante');\n                    return null;\n                }\n                const response = await fetch(\"\".concat(API_URL, \"/chat/conversations/\").concat(conversationId, \"/participants/\").concat(participantId), {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (!response.ok) {\n                    if (response.status === 401) {\n                        console.error('Token expirado ou inválido ao remover participante');\n                        handleAuthError();\n                        return null;\n                    }\n                    throw new Error(\"Erro ao remover participante: \".concat(response.status));\n                }\n                const data = await response.json();\n                if (data.success) {\n                    // Sempre remover da lista local quando o usuário sai\n                    if (participantId === (user === null || user === void 0 ? void 0 : user.id)) {\n                        console.log(\"Removendo conversa \".concat(conversationId, \" da lista local\"));\n                        // Remover da lista local\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>prev.filter({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>conv.id !== conversationId\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar mensagens\n                        setMessages({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                const updated = {\n                                    ...prev\n                                };\n                                delete updated[conversationId];\n                                return updated;\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Limpar cache\n                        requestCache.conversations.data = null;\n                        requestCache.conversations.timestamp = 0;\n                        if (requestCache.messages[conversationId]) {\n                            delete requestCache.messages[conversationId];\n                        }\n                        // Se era a conversa ativa, limpar\n                        if (activeConversation === conversationId) {\n                            setActiveConversation(null);\n                        }\n                        // Forçar recarregamento das conversas após sair\n                        setTimeout({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": ()=>{\n                                loadConversations(true);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], 500);\n                        console.log(\"Conversa \".concat(conversationId, \" removida da lista local\"));\n                    } else {\n                        var _data_data_user, _data_data;\n                        // Atualizar a conversa removendo o participante\n                        setConversations({\n                            \"ChatProvider.useCallback[removeParticipantFromGroup]\": (prev)=>{\n                                return prev.map({\n                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (conv)=>{\n                                        if (conv.id === conversationId) {\n                                            return {\n                                                ...conv,\n                                                participants: conv.participants.filter({\n                                                    \"ChatProvider.useCallback[removeParticipantFromGroup]\": (p)=>p.userId !== participantId\n                                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"])\n                                            };\n                                        }\n                                        return conv;\n                                    }\n                                }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                            }\n                        }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"]);\n                        // Criar uma mensagem do sistema para notificar que um participante saiu\n                        if (socket && isConnected && ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : (_data_data_user = _data_data.user) === null || _data_data_user === void 0 ? void 0 : _data_data_user.fullName)) {\n                            const systemMessage = {\n                                conversationId,\n                                content: \"\".concat(data.data.user.fullName, \" saiu do grupo\"),\n                                contentType: 'SYSTEM'\n                            };\n                            socket.emit('message:send', systemMessage);\n                        }\n                    }\n                    return data.data;\n                }\n                return null;\n            } catch (error) {\n                console.error('Erro ao remover participante:', error);\n                return null;\n            }\n        }\n    }[\"ChatProvider.useCallback[removeParticipantFromGroup]\"], [\n        socket,\n        isConnected,\n        user,\n        activeConversation,\n        getCurrentToken,\n        handleAuthError,\n        loadUnreadCount\n    ]);\n    // Marcar mensagens como lidas\n    const markMessagesAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markMessagesAsRead]\": async (conversationId, messageId)=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/mark-read\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    },\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                if (response.ok) {\n                    // Marcar conversa como lida localmente\n                    setConversations({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[markMessagesAsRead]\": (conv)=>conv.id === conversationId ? {\n                                        ...conv,\n                                        hasUnread: false\n                                    } : conv\n                            }[\"ChatProvider.useCallback[markMessagesAsRead]\"])\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                    // Decrementar contador de conversas não lidas se a conversa estava marcada como não lida\n                    setUnreadCount({\n                        \"ChatProvider.useCallback[markMessagesAsRead]\": (prev)=>{\n                            const conv = conversations.find({\n                                \"ChatProvider.useCallback[markMessagesAsRead].conv\": (c)=>c.id === conversationId\n                            }[\"ChatProvider.useCallback[markMessagesAsRead].conv\"]);\n                            const wasUnread = (conv === null || conv === void 0 ? void 0 : conv.hasUnread) || (conv === null || conv === void 0 ? void 0 : conv.unreadCount) > 0;\n                            return wasUnread ? Math.max(0, prev - 1) : prev;\n                        }\n                    }[\"ChatProvider.useCallback[markMessagesAsRead]\"]);\n                }\n            } catch (error) {\n                console.error('Error marking messages as read:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[markMessagesAsRead]\"], [\n        user,\n        getCurrentToken,\n        conversations\n    ]);\n    // Resetar contador de mensagens não lidas\n    const resetUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[resetUnreadCount]\": async ()=>{\n            try {\n                if (!user) return;\n                const currentToken = getCurrentToken();\n                if (!currentToken) return;\n                const response = await fetch(\"\".concat(API_URL, \"/chat/messages/reset-unread\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        Authorization: \"Bearer \".concat(currentToken)\n                    }\n                });\n                if (response.ok) {\n                    setUnreadCount(0);\n                    setConversations({\n                        \"ChatProvider.useCallback[resetUnreadCount]\": (prev)=>prev.map({\n                                \"ChatProvider.useCallback[resetUnreadCount]\": (conv)=>({\n                                        ...conv,\n                                        unreadCount: 0\n                                    })\n                            }[\"ChatProvider.useCallback[resetUnreadCount]\"])\n                    }[\"ChatProvider.useCallback[resetUnreadCount]\"]);\n                }\n            } catch (error) {\n                console.error('Error resetting unread count:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[resetUnreadCount]\"], [\n        user,\n        getCurrentToken\n    ]);\n    // Apagar mensagens\n    const deleteMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[deleteMessages]\": async (messageIds, conversationId)=>{\n            try {\n                if (!messageIds || messageIds.length === 0) {\n                    console.error('IDs das mensagens são obrigatórios');\n                    return null;\n                }\n                const currentToken = getCurrentToken();\n                if (!currentToken) {\n                    console.error('Token não disponível ao apagar mensagens');\n                    return null;\n                }\n                const results = [];\n                const errors = [];\n                // Apagar mensagens uma por uma\n                for (const messageId of messageIds){\n                    try {\n                        const response = await fetch(\"\".concat(API_URL, \"/chat/messages/\").concat(messageId), {\n                            method: 'DELETE',\n                            headers: {\n                                Authorization: \"Bearer \".concat(currentToken)\n                            }\n                        });\n                        if (!response.ok) {\n                            if (response.status === 401) {\n                                console.error('Token expirado ou inválido ao apagar mensagem');\n                                handleAuthError();\n                                return null;\n                            }\n                            throw new Error(\"Erro ao apagar mensagem: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        if (data.success) {\n                            results.push(data.data);\n                            // Atualizar o estado das mensagens\n                            setMessages({\n                                \"ChatProvider.useCallback[deleteMessages]\": (prev)=>{\n                                    const updatedMessages = {\n                                        ...prev\n                                    };\n                                    // Percorrer todas as conversas\n                                    Object.keys(updatedMessages).forEach({\n                                        \"ChatProvider.useCallback[deleteMessages]\": (convId)=>{\n                                            // Atualizar a mensagem na conversa correspondente\n                                            updatedMessages[convId] = updatedMessages[convId].map({\n                                                \"ChatProvider.useCallback[deleteMessages]\": (msg)=>{\n                                                    if (msg.id === messageId) {\n                                                        return {\n                                                            ...msg,\n                                                            ...data.data,\n                                                            isDeleted: true\n                                                        };\n                                                    }\n                                                    return msg;\n                                                }\n                                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                        }\n                                    }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                                    return updatedMessages;\n                                }\n                            }[\"ChatProvider.useCallback[deleteMessages]\"]);\n                        } else {\n                            errors.push({\n                                messageId,\n                                error: 'Falha ao apagar mensagem'\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao apagar mensagem \".concat(messageId, \":\"), error);\n                        errors.push({\n                            messageId,\n                            error: error.message || 'Erro desconhecido'\n                        });\n                    }\n                }\n                // Limpar o cache de mensagens para forçar uma nova busca\n                if (conversationId && results.length > 0) {\n                    console.log('Limpando cache de mensagens para a conversa:', conversationId);\n                    if (requestCache.messages[conversationId]) {\n                        delete requestCache.messages[conversationId];\n                    }\n                    // Recarregar as mensagens da conversa para garantir que estamos sincronizados com o backend\n                    try {\n                        console.log('Recarregando mensagens após exclusão');\n                        await loadMessages(conversationId);\n                        // Também recarregar a lista de conversas para garantir que tudo está atualizado\n                        await loadConversations(true);\n                    } catch (reloadError) {\n                        console.error('Erro ao recarregar mensagens após exclusão:', reloadError);\n                    }\n                }\n                return {\n                    success: results.length > 0,\n                    deleted: results,\n                    errors: errors,\n                    total: messageIds.length,\n                    successCount: results.length,\n                    errorCount: errors.length\n                };\n            } catch (error) {\n                console.error('Erro ao apagar mensagens:', error);\n                return {\n                    success: false,\n                    deleted: [],\n                    errors: [\n                        {\n                            error: error.message || 'Erro desconhecido'\n                        }\n                    ],\n                    total: messageIds.length,\n                    successCount: 0,\n                    errorCount: messageIds.length\n                };\n            }\n        }\n    }[\"ChatProvider.useCallback[deleteMessages]\"], [\n        getCurrentToken,\n        handleAuthError,\n        loadMessages,\n        loadConversations\n    ]);\n    // Memoizar o valor do contexto para evitar re-renderizações desnecessárias\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatProvider.useMemo[contextValue]\": ()=>({\n                conversations,\n                messages,\n                unreadCount,\n                activeConversation,\n                isPanelOpen,\n                isModalOpen,\n                isConnected,\n                isLoading,\n                setActiveConversation,\n                loadMessages,\n                sendMessage,\n                createConversation,\n                createOrGetConversation,\n                toggleChatPanel,\n                toggleChatModal,\n                loadConversations,\n                loadUnreadCount,\n                markMessagesAsRead,\n                resetUnreadCount,\n                addParticipantToGroup,\n                addMultipleParticipantsToGroup,\n                removeParticipantFromGroup,\n                deleteMessages\n            })\n    }[\"ChatProvider.useMemo[contextValue]\"], [\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation,\n        isPanelOpen,\n        isModalOpen,\n        isConnected,\n        isLoading,\n        setActiveConversation,\n        loadMessages,\n        sendMessage,\n        createConversation,\n        createOrGetConversation,\n        toggleChatPanel,\n        toggleChatModal,\n        loadConversations,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        deleteMessages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programacao\\\\HIGH TIDE SYSTEMS\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ChatContext.js\",\n        lineNumber: 1934,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatProvider, \"knvLij1shdeGVeBVK+PUiQX2mtg=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ChatProvider;\nconst useChat = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChat deve ser usado dentro de um ChatProvider');\n    }\n    return context;\n};\n_s1(useChat, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.js\n"));

/***/ })

});