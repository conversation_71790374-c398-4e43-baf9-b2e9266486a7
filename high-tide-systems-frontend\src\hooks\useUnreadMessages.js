import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export const useUnreadMessages = () => {
  const [unreadConversationsCount, setUnreadConversationsCount] = useState(0);
  const [conversationsWithUnread, setConversationsWithUnread] = useState(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const getCurrentToken = useCallback(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('token');
    }
    return null;
  }, []);

  const fetchUnreadCount = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const token = getCurrentToken();
      if (!token) return;

      const response = await fetch(`${API_URL}/chat/messages/unread`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Agora contamos conversas não lidas, não mensagens
          setUnreadConversationsCount(data.data.totalUnreadConversations || data.data.totalUnread);

          // Criar Set de conversas com mensagens não lidas
          const conversationsSet = new Set();
          data.data.conversations.forEach(conv => {
            if (conv.hasUnread || conv.unreadCount > 0) {
              conversationsSet.add(conv.conversationId);
            }
          });
          setConversationsWithUnread(conversationsSet);
        }
      }
    } catch (error) {
      console.error('Error fetching unread count:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user, getCurrentToken]);

  const markAsRead = useCallback(async (conversationId, messageId) => {
    if (!user) return;

    try {
      const token = getCurrentToken();
      if (!token) return;

      const response = await fetch(`${API_URL}/chat/messages/mark-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ conversationId, messageId })
      });

      if (response.ok) {
        // Remover conversa da lista de não lidas
        setConversationsWithUnread(prev => {
          const newSet = new Set(prev);
          newSet.delete(conversationId);
          return newSet;
        });

        // Decrementar contador de conversas não lidas
        setUnreadConversationsCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  }, [user, getCurrentToken]); // ✅ CORRIGIDO: Removida dependência que causava stale closure

  const addUnreadConversation = useCallback((conversationId) => {
    setConversationsWithUnread(prev => {
      if (!prev.has(conversationId)) {
        const newSet = new Set(prev);
        newSet.add(conversationId);
        setUnreadConversationsCount(prevCount => prevCount + 1);
        return newSet;
      }
      return prev;
    });
  }, []);

  const removeUnreadConversation = useCallback((conversationId) => {
    setConversationsWithUnread(prev => {
      if (prev.has(conversationId)) {
        const newSet = new Set(prev);
        newSet.delete(conversationId);
        setUnreadConversationsCount(prevCount => Math.max(0, prevCount - 1));
        return newSet;
      }
      return prev;
    });
  }, []);

  // Carregar contagem inicial
  useEffect(() => {
    if (user) {
      fetchUnreadCount();
    } else {
      setUnreadConversationsCount(0);
      setConversationsWithUnread(new Set());
    }
  }, [user, fetchUnreadCount]);

  return {
    // Manter compatibilidade com código existente
    unreadCount: unreadConversationsCount,
    unreadConversationsCount,
    conversationsWithUnread,
    // Função para verificar se uma conversa tem mensagens não lidas
    hasUnreadMessages: (conversationId) => conversationsWithUnread.has(conversationId),
    isLoading,
    fetchUnreadCount,
    markAsRead,
    addUnreadConversation,
    removeUnreadConversation
  };
};