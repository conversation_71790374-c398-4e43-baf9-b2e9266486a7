// NOVA LÓGICA FRONTEND - Estilo WhatsApp

/**
 * 📱 COMPONENTE ChatConversation - NOVA VERSÃO
 * 
 * ❌ REMOVE: Marcação automática quando abre conversa
 * ✅ ADICIONA: Marcação baseada em visibilidade real
 */

import { useEffect, useRef, useCallback } from 'react';
import { useIntersectionObserver } from '../hooks/useIntersectionObserver';

const ChatConversation = ({ conversationId }) => {
  const messagesContainerRef = useRef();
  const messageRefs = useRef(new Map()); // Para rastrear referências das mensagens
  
  // ❌ REMOVER ESTE USEEFFECT:
  // useEffect(() => {
  //   // Marcação automática quando abre conversa
  //   if (conversationId && conversationMessages.length > 0) {
  //     const timer = setTimeout(() => {
  //       markMessagesAsRead(conversationId, lastMessage.id);
  //     }, 500);
  //     return () => clearTimeout(timer);
  //   }
  // }, [conversationId, conversationMessages.length, markMessagesAsRead]);

  /**
   * 👀 NOVA LÓGICA: Marcar como lida quando mensagem aparece na viewport
   */
  const handleMessageVisible = useCallback((messageId) => {
    const message = conversationMessages.find(msg => msg.id === messageId);
    
    if (message && !message.isFromCurrentUser && !message.isRead) {
      console.log(`[ChatConversation] Mensagem ${messageId} ficou visível, marcando como lida`);
      markMessageAsRead(conversationId, messageId);
    }
  }, [conversationId, conversationMessages, markMessageAsRead]);

  /**
   * 📱 COMPONENTE MessageItem com Intersection Observer
   */
  const MessageItem = ({ message, isFromCurrentUser }) => {
    const messageRef = useRef();
    
    // Hook para detectar quando a mensagem fica visível
    const isVisible = useIntersectionObserver(messageRef, {
      threshold: 0.5, // 50% da mensagem deve estar visível
      rootMargin: '0px 0px -100px 0px' // Margem para garantir que está realmente visível
    });

    useEffect(() => {
      if (isVisible && !isFromCurrentUser && !message.isRead) {
        // Delay pequeno para garantir que o usuário realmente viu
        const timer = setTimeout(() => {
          handleMessageVisible(message.id);
        }, 1000); // 1 segundo de visualização real
        
        return () => clearTimeout(timer);
      }
    }, [isVisible, message.id, isFromCurrentUser, message.isRead]);

    return (
      <div 
        ref={messageRef}
        className={`message-item ${isFromCurrentUser ? 'sent' : 'received'}`}
      >
        <div className="message-content">
          {message.content}
        </div>
        
        {/* Status visual estilo WhatsApp */}
        {isFromCurrentUser && (
          <div className="message-status">
            {message.overallStatus === 'read' && (
              <span className="status-read">✓✓</span> // Azul
            )}
            {message.overallStatus === 'DELIVERED' && (
              <span className="status-delivered">✓✓</span> // Cinza
            )}
            {message.overallStatus === 'SENT' && (
              <span className="status-sent">✓</span> // Cinza
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="chat-conversation">
      <div ref={messagesContainerRef} className="messages-container">
        {conversationMessages.map(message => (
          <MessageItem
            key={message.id}
            message={message}
            isFromCurrentUser={message.senderId === user.id || message.senderClientId === user.id}
          />
        ))}
      </div>
    </div>
  );
};

/**
 * 🔧 HOOK personalizado para Intersection Observer
 */
const useIntersectionObserver = (elementRef, options) => {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
    }, options);

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, options]);

  return isIntersecting;
};

/**
 * 📡 CHATCONTEXT - Novas funções
 */
const ChatContext = () => {
  
  /**
   * 👀 Marcar UMA mensagem específica como lida (não todas de uma vez)
   */
  const markMessageAsRead = useCallback(async (conversationId, messageId) => {
    if (!user || !socket || !isConnected) return;
    
    console.log(`[markMessageAsRead] Marcando mensagem específica: ${messageId}`);
    
    socket.emit('message:markAsRead', {
      conversationId,
      messageId
    }, (response) => {
      if (response.success) {
        console.log(`[markMessageAsRead] Mensagem ${messageId} marcada como lida com sucesso`);
      } else {
        console.error(`[markMessageAsRead] Erro:`, response.error);
      }
    });
  }, [user, socket, isConnected]);

  /**
   * 🟢 Marcar como DELIVERED quando usuário conecta
   */
  useEffect(() => {
    if (socket && isConnected && user) {
      console.log('[ChatContext] Usuário conectado, marcando mensagens como delivered');
      
      socket.emit('user:online', {
        userId: user.id
      });
    }
  }, [socket, isConnected, user]);

  /**
   * 📱 Listener para status de mensagem atualizado
   */
  useEffect(() => {
    if (!socket) return;

    const handleMessageStatusUpdate = (data) => {
      console.log('[ChatContext] Status de mensagem atualizado:', data);
      
      // Atualizar o status da mensagem no estado local
      setMessages(prev => {
        const conversationMessages = prev[data.conversationId] || [];
        const updatedMessages = conversationMessages.map(message => {
          if (message.id === data.messageId) {
            return {
              ...message,
              overallStatus: data.newStatus,
              readByCount: data.readByCount || message.readByCount
            };
          }
          return message;
        });
        
        return {
          ...prev,
          [data.conversationId]: updatedMessages
        };
      });
    };

    socket.on('message:statusUpdate', handleMessageStatusUpdate);
    socket.on('message:read', handleMessageStatusUpdate);

    return () => {
      socket.off('message:statusUpdate', handleMessageStatusUpdate);
      socket.off('message:read', handleMessageStatusUpdate);
    };
  }, [socket]);

  // ... resto da implementação
};

export default ChatConversation;