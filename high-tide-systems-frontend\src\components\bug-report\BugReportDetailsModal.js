'use client';

import React from 'react';
import { 
  X, 
  Bug, 
  User, 
  Building, 
  Calendar, 
  MapPin, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  XCircle,
  FileText,
  Tag
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog.jsx';
import Button from '@/components/ui/Button';

const BugReportDetailsModal = ({ bug, isOpen, onClose }) => {
  if (!bug) return null;

  const getStatusColor = (status) => {
    const colors = {
      OPEN: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      IN_PROGRESS: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      RESOLVED: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      CLOSED: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    };
    return colors[status] || colors.OPEN;
  };

  const getPriorityColor = (priority) => {
    const colors = {
      LOW: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      MEDIUM: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      HIGH: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      CRITICAL: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };
    return colors[priority] || colors.MEDIUM;
  };

  const getStatusIcon = (status) => {
    const icons = {
      OPEN: <AlertTriangle className="h-4 w-4" />,
      IN_PROGRESS: <Clock className="h-4 w-4" />,
      RESOLVED: <CheckCircle className="h-4 w-4" />,
      CLOSED: <XCircle className="h-4 w-4" />
    };
    return icons[status] || icons.OPEN;
  };

  const statusLabels = {
    OPEN: 'Aberto',
    IN_PROGRESS: 'Em Progresso',
    RESOLVED: 'Resolvido',
    CLOSED: 'Fechado'
  };

  const priorityLabels = {
    LOW: 'Baixa',
    MEDIUM: 'Média',
    HIGH: 'Alta',
    CRITICAL: 'Crítica'
  };

  const categoryLabels = {
    GENERAL: 'Geral',
    UI_UX: 'Interface/UX',
    PERFORMANCE: 'Performance',
    FUNCTIONALITY: 'Funcionalidade',
    DATA: 'Dados',
    SECURITY: 'Segurança',
    INTEGRATION: 'Integração'
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <Bug className="h-6 w-6 text-red-600" />
            Detalhes do Bug Report
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header com título e badges */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
              {bug.title}
            </h2>
            
            <div className="flex flex-wrap gap-2">
              <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(bug.status)}`}>
                {getStatusIcon(bug.status)}
                {statusLabels[bug.status]}
              </span>
              <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(bug.priority)}`}>
                {priorityLabels[bug.priority]}
              </span>
              <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                <Tag className="h-3 w-3" />
                {categoryLabels[bug.category]}
              </span>
            </div>
          </div>

          {/* Informações principais */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Coluna esquerda */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Descrição
                </h3>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {bug.description}
                </p>
              </div>

              {bug.location && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-green-600" />
                    Local
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    {bug.location}
                  </p>
                </div>
              )}

              {bug.adminNotes && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Notas do Administrador
                  </h3>
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                    <p className="text-blue-800 dark:text-blue-200">
                      {bug.adminNotes}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Coluna direita */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <User className="h-5 w-5 text-purple-600" />
                  Informações do Usuário
                </h3>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Nome:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {bug.reportedBy.fullName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Email:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {bug.reportedBy.email}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Função:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {bug.reportedBy.role}
                    </span>
                  </div>
                </div>
              </div>

              {bug.company && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <Building className="h-5 w-5 text-indigo-600" />
                    Empresa
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <span className="font-medium text-gray-900 dark:text-white">
                      {bug.company.name}
                    </span>
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-amber-600" />
                  Datas
                </h3>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Criado em:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {new Date(bug.createdAt).toLocaleString('pt-BR')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Atualizado em:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {new Date(bug.updatedAt).toLocaleString('pt-BR')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Histórico de status */}
          {bug.statusHistory && bug.statusHistory.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Histórico de Alterações
              </h3>
              <div className="space-y-3">
                {bug.statusHistory.map((history, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {history.changedBy && (
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {history.changedBy.fullName}
                          </span>
                        )}
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(history.createdAt).toLocaleString('pt-BR')}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 mb-2">
                      {history.oldStatus && history.newStatus && (
                        <div className="flex items-center gap-2 text-sm">
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(history.oldStatus)}`}>
                            {statusLabels[history.oldStatus]}
                          </span>
                          <span className="text-gray-400">→</span>
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(history.newStatus)}`}>
                            {statusLabels[history.newStatus]}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    {history.notes && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {history.notes}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Botões de ação */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Fechar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BugReportDetailsModal;
