-- 🗃️ MIGRAÇÃO PARA NOVA ARQUITETURA DO CHAT
-- Simplificar e corrigir o sistema de mensagens não lidas

-- 1️⃣ Remover lastReadMessageId (fonte de inconsistência)
ALTER TABLE "ConversationParticipant" 
DROP COLUMN IF EXISTS "lastReadMessageId";

-- 2️⃣ Adicionar lastSeenAt para controle melhor
ALTER TABLE "ConversationParticipant" 
ADD COLUMN "lastSeenAt" TIMESTAMP;

-- 3️⃣ Limpar status inconsistentes das mensagens
-- Resetar todas as mensagens para SENT (reprocessar depois)
UPDATE "MessageStatus" 
SET "status" = 'SENT', "timestamp" = NOW()
WHERE "status" IN ('READ', 'READ'); -- Corrigir possíveis inconsistências

-- 4️⃣ Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS "idx_messagestatus_participant_status" 
ON "MessageStatus" ("participantId", "status");

CREATE INDEX IF NOT EXISTS "idx_messagestatus_message_participant" 
ON "MessageStatus" ("messageId", "participantId");

CREATE INDEX IF NOT EXISTS "idx_conversation_lastmessage" 
ON "Conversation" ("lastMessageAt" DESC);

-- 5️⃣ Criar função para contar não lidas (performance)
CREATE OR REPLACE FUNCTION get_unread_count_for_user(user_id UUID, is_client BOOLEAN DEFAULT FALSE)
RETURNS TABLE(conversation_id UUID, unread_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp."conversationId"::UUID as conversation_id,
        COUNT(ms.id) as unread_count
    FROM "ConversationParticipant" cp
    LEFT JOIN "MessageStatus" ms ON ms."participantId" = cp.id
    WHERE 
        (
            (NOT is_client AND cp."userId" = user_id) OR
            (is_client AND cp."clientId" = user_id)
        )
        AND cp."leftAt" IS NULL
        AND ms."status" IN ('SENT', 'DELIVERED')
    GROUP BY cp."conversationId"
    HAVING COUNT(ms.id) > 0;
END;
$$ LANGUAGE plpgsql;

-- 6️⃣ View para facilitar queries de mensagens com status
CREATE OR REPLACE VIEW message_with_status AS
SELECT 
    m.*,
    json_agg(
        json_build_object(
            'participantId', ms."participantId",
            'status', ms.status,
            'timestamp', ms.timestamp,
            'userId', cp."userId",
            'clientId', cp."clientId"
        )
    ) as statuses
FROM "Message" m
LEFT JOIN "MessageStatus" ms ON ms."messageId" = m.id
LEFT JOIN "ConversationParticipant" cp ON cp.id = ms."participantId"
GROUP BY m.id;

-- 7️⃣ Trigger para atualizar lastMessageAt automaticamente
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE "Conversation"
    SET "lastMessageAt" = NEW."createdAt"
    WHERE id = NEW."conversationId";
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_conversation_last_message ON "Message";
CREATE TRIGGER trigger_update_conversation_last_message
    AFTER INSERT ON "Message"
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_last_message();

-- 8️⃣ Limpar dados órfãos
DELETE FROM "MessageStatus" ms
WHERE NOT EXISTS (
    SELECT 1 FROM "Message" m WHERE m.id = ms."messageId"
);

DELETE FROM "MessageStatus" ms
WHERE NOT EXISTS (
    SELECT 1 FROM "ConversationParticipant" cp WHERE cp.id = ms."participantId"
);

COMMIT;