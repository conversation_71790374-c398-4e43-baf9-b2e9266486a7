'use client';

import React, { useState, useEffect } from 'react';
import {
  Filter,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Edit,
  Trash,
  User,
  Building,
  Calendar
} from 'lucide-react';
import ModuleHeader from '@/components/ui/ModuleHeader';
import ModuleTable from '@/components/ui/ModuleTable';
import BugReportsFilters from '@/components/bug-report/BugReportsFilters';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import api from '@/services/api';

const BugReportsPage = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [bugs, setBugs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  const [filters, setFilters] = useState({
    search: '',
    status: [],
    priority: [],
    category: [],
    companies: []
  });

  // Carregar dados
  useEffect(() => {
    if (user && user.role !== 'SYSTEM_ADMIN') {
      router.push('/dashboard');
      return;
    }

    if (user) {
      loadBugReports();
      loadStats();
    }
  }, [user, router, filters, pagination.page]);

  const loadBugReports = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      };

      // Remove filtros vazios
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key]) && params[key].length === 0) {
          delete params[key];
        } else if (params[key] === '') {
          delete params[key];
        }
      });

      const response = await api.get('/bug-reports/admin/all', { params });

      if (response.data.success) {
        setBugs(response.data.data.bugReports);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.pagination.total,
          totalPages: response.data.data.pagination.totalPages
        }));
      }
    } catch (error) {
      console.error('Erro ao carregar bug reports:', error);
      toast.error('Erro ao carregar relatórios de bugs');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await api.get('/bug-reports/admin/stats');
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  if (user?.role !== 'SYSTEM_ADMIN') {
    return null;
  }

  const handleViewBug = (bug) => {
    alert(`Visualizar bug: ${bug.title}\nFuncionalidade será implementada com o backend`);
  };

  const handleEditBug = (bug) => {
    alert(`Editar bug: ${bug.title}\nFuncionalidade será implementada com o backend`);
  };

  const handleDeleteBug = (bug) => {
    if (window.confirm(`Tem certeza que deseja excluir o bug "${bug.title}"?`)) {
      alert(`Bug "${bug.title}" excluído!\nFuncionalidade será implementada com o backend`);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'OPEN':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'IN_PROGRESS':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'CLOSED':
        return <XCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-500 rounded-full"></div>;
    }
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'CRITICAL': 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
      'HIGH': 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',
      'MEDIUM': 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
      'LOW': 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
    };
    return colors[priority] || 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';
  };

  const getPriorityLabel = (priority) => {
    const labels = {
      'CRITICAL': 'Crítica',
      'HIGH': 'Alta',
      'MEDIUM': 'Média',
      'LOW': 'Baixa'
    };
    return labels[priority] || 'Desconhecida';
  };

  const columns = [
    { header: 'Status', field: 'status', width: '120px' },
    { header: 'Bug', field: 'title', width: 'auto' },
    { header: 'Prioridade', field: 'priority', width: '120px' },
    { header: 'Reportado por', field: 'reportedBy', width: '200px' },
    { header: 'Data', field: 'createdAt', width: '120px' },
    { header: 'Ações', field: 'actions', width: '120px', sortable: false }
  ];

  return (
    <div className="space-y-6">
      <ModuleHeader
        title="Relatórios de Bugs"
        description="Gerencie todos os bugs reportados pelos usuários do sistema. Utilize os filtros abaixo para encontrar bugs específicos."
        icon={<Filter size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        moduleColor="admin"
        filters={
          <BugReportsFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={() => {}}
          />
        }
      />

      <ModuleTable
        moduleColor="admin"
        title="Lista de Bugs Reportados"
        data={bugs}
        columns={columns}
        isLoading={false}
        emptyMessage="Nenhum bug reportado ainda."
        emptyIcon="🐛"
        renderRow={(bug, _index, moduleColors, visibleColumns) => (
          <tr key={bug.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('status') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center gap-2">
                  {getStatusIcon(bug.status)}
                  <span className="text-sm font-medium">
                    {bug.status === 'OPEN' ? 'Aberto' :
                     bug.status === 'IN_PROGRESS' ? 'Em Progresso' :
                     bug.status === 'RESOLVED' ? 'Resolvido' : 'Fechado'}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('title') && (
              <td className="px-6 py-4">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">{bug.title}</div>
                  <div
                    className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs cursor-help"
                    title={bug.description}
                  >
                    {bug.description}
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('priority') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(bug.priority)}`}>
                  {getPriorityLabel(bug.priority)}
                </span>
              </td>
            )}

            {visibleColumns.includes('reportedBy') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">{bug.reportedBy.name}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">{bug.reportedBy.company}</div>
                </div>
              </td>
            )}

            {visibleColumns.includes('createdAt') && (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {new Date(bug.createdAt).toLocaleDateString('pt-BR')}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right">
                <div className="flex justify-end gap-1">
                  <button
                    onClick={() => handleViewBug(bug)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    title="Visualizar"
                  >
                    <Eye size={16} />
                  </button>
                  <button
                    onClick={() => handleEditBug(bug)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteBug(bug)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />
    </div>
  );
};

export default BugReportsPage;
