{"timestamp":"2025-08-07 14:18:50","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:18:50 GMT+0000 (Coordinated Universal Time)","process":{"pid":1719,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":194883584,"heapTotal":109195264,"heapUsed":41835528,"external":3381484,"arrayBuffers":109459}},"os":{"loadavg":[1.24,1.57,1.7],"uptime":1269912.25},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:27:18","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:27:18 GMT+0000 (Coordinated Universal Time)","process":{"pid":1795,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195739648,"heapTotal":109981696,"heapUsed":42360840,"external":3382454,"arrayBuffers":110429}},"os":{"loadavg":[0.7,1.41,1.63],"uptime":1270420.23},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:27:18","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:27:18 GMT+0000 (Coordinated Universal Time)","process":{"pid":1795,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195751936,"heapTotal":109981696,"heapUsed":42504656,"external":3382518,"arrayBuffers":110453}},"os":{"loadavg":[0.7,1.41,1.63],"uptime":1270420.23},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:30:50","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:30:50 GMT+0000 (Coordinated Universal Time)","process":{"pid":1869,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":194916352,"heapTotal":108933120,"heapUsed":41878504,"external":3382103,"arrayBuffers":110078}},"os":{"loadavg":[2.26,2.15,1.9],"uptime":1270632.22},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:31:20","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:31:20 GMT+0000 (Coordinated Universal Time)","process":{"pid":1943,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195653632,"heapTotal":110243840,"heapUsed":42402744,"external":3382442,"arrayBuffers":110417}},"os":{"loadavg":[1.37,1.95,1.84],"uptime":1270662.24},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:31:20","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:31:20 GMT+0000 (Coordinated Universal Time)","process":{"pid":1943,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195682304,"heapTotal":110505984,"heapUsed":42808384,"external":3382506,"arrayBuffers":110441}},"os":{"loadavg":[1.37,1.95,1.84],"uptime":1270662.24},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:33:55","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:33:55 GMT+0000 (Coordinated Universal Time)","process":{"pid":146,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195399680,"heapTotal":110243840,"heapUsed":44620472,"external":3385842,"arrayBuffers":111259}},"os":{"loadavg":[1.12,1.75,1.8],"uptime":1270817.24},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 14:40:09","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 14:40:09 GMT+0000 (Coordinated Universal Time)","process":{"pid":533,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196235264,"heapTotal":109457408,"heapUsed":42539288,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[2.23,2.14,1.94],"uptime":1271191.25},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:08:49","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:08:49 GMT+0000 (Coordinated Universal Time)","process":{"pid":804,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196014080,"heapTotal":110243840,"heapUsed":45120184,"external":3386830,"arrayBuffers":112247}},"os":{"loadavg":[2.08,1.97,1.91],"uptime":1272911.31},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:32:39","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:32:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":882,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196558848,"heapTotal":109719552,"heapUsed":42444832,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[1.54,1.77,1.82],"uptime":1274341.41},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:33:20","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:33:20 GMT+0000 (Coordinated Universal Time)","process":{"pid":956,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":194842624,"heapTotal":109981696,"heapUsed":41816304,"external":3381472,"arrayBuffers":109447}},"os":{"loadavg":[0.79,1.55,1.74],"uptime":1274382.4},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:34:08","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:34:08 GMT+0000 (Coordinated Universal Time)","process":{"pid":1030,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196206592,"heapTotal":110780416,"heapUsed":42429576,"external":3382442,"arrayBuffers":110417}},"os":{"loadavg":[1.54,1.57,1.73],"uptime":1274430.42},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:34:08","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:34:08 GMT+0000 (Coordinated Universal Time)","process":{"pid":1030,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196231168,"heapTotal":111042560,"heapUsed":42819480,"external":3382506,"arrayBuffers":110441}},"os":{"loadavg":[1.54,1.57,1.73],"uptime":1274430.43},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:34:39","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:34:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":1104,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195461120,"heapTotal":110256128,"heapUsed":42412200,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[2.56,1.82,1.81],"uptime":1274461.4},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:34:48","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:34:48 GMT+0000 (Coordinated Universal Time)","process":{"pid":1178,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":195637248,"heapTotal":108945408,"heapUsed":41926432,"external":3381472,"arrayBuffers":109447}},"os":{"loadavg":[2.24,1.77,1.8],"uptime":1274470.42},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 15:55:44","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 15:55:44 GMT+0000 (Coordinated Universal Time)","process":{"pid":436,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":137547776,"heapTotal":43933696,"heapUsed":41863032,"external":3375516,"arrayBuffers":101443}},"os":{"loadavg":[3.23,2.5,2.1],"uptime":1275726.48},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:00:50","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:00:50 GMT+0000 (Coordinated Universal Time)","process":{"pid":511,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197185536,"heapTotal":110518272,"heapUsed":42366352,"external":3381472,"arrayBuffers":109447}},"os":{"loadavg":[0.98,1.88,1.96],"uptime":1276032.49},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:01:07","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:01:07 GMT+0000 (Coordinated Universal Time)","process":{"pid":585,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197697536,"heapTotal":109469696,"heapUsed":42843896,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[1.62,1.97,1.99],"uptime":1276049.47},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:01:17","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:01:17 GMT+0000 (Coordinated Universal Time)","process":{"pid":659,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197906432,"heapTotal":109469696,"heapUsed":42804784,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[2.22,2.09,2.02],"uptime":1276059.49},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:01:35","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:01:35 GMT+0000 (Coordinated Universal Time)","process":{"pid":733,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197599232,"heapTotal":109993984,"heapUsed":42828656,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[2.64,2.19,2.06],"uptime":1276077.48},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:01:51","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:01:51 GMT+0000 (Coordinated Universal Time)","process":{"pid":807,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197627904,"heapTotal":108945408,"heapUsed":42776792,"external":3382484,"arrayBuffers":110459}},"os":{"loadavg":[2.21,2.12,2.04],"uptime":1276093.48},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:02:03","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:02:03 GMT+0000 (Coordinated Universal Time)","process":{"pid":881,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":200151040,"heapTotal":111042560,"heapUsed":42814312,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[1.95,2.07,2.02],"uptime":1276105.48},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:02:11","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:02:11 GMT+0000 (Coordinated Universal Time)","process":{"pid":955,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197263360,"heapTotal":109731840,"heapUsed":42306600,"external":3381472,"arrayBuffers":109447}},"os":{"loadavg":[1.73,2.01,2],"uptime":1276113.49},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:02:23","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:02:23 GMT+0000 (Coordinated Universal Time)","process":{"pid":1029,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197337088,"heapTotal":110780416,"heapUsed":42816968,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[1.54,1.96,1.99],"uptime":1276125.5},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
{"timestamp":"2025-08-07 16:02:33","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:261:44)","rejection":true,"date":"Thu Aug 07 2025 16:02:33 GMT+0000 (Coordinated Universal Time)","process":{"pid":1103,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":196710400,"heapTotal":110518272,"heapUsed":42847144,"external":3382472,"arrayBuffers":110447}},"os":{"loadavg":[1.46,1.93,1.98],"uptime":1276135.46},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":261,"method":null,"native":false}]}
