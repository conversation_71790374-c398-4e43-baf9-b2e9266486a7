const prisma = require('../utils/prisma');
const emailService = require('../services/emailService');
const { responseUtil } = require('../utils/responseUtil');

class BugReportController {
  /**
   * Criar um novo relatório de bug
   */
  async create(req, res) {
    try {
      const { title, description, location, category, priority } = req.body;
      const userId = req.user.id;
      const companyId = req.user.companyId;

      // Validações básicas
      if (!title || !description) {
        return responseUtil.error(res, 'Título e descrição são obrigatórios', 400);
      }

      if (title.length > 100) {
        return responseUtil.error(res, 'Título deve ter no máximo 100 caracteres', 400);
      }

      if (location && location.length > 150) {
        return responseUtil.error(res, 'Local deve ter no máximo 150 caracteres', 400);
      }

      // Criar o relatório de bug
      const bugReport = await prisma.bugReport.create({
        data: {
          title,
          description,
          location,
          category: category || 'GENERAL',
          priority: priority || 'MEDIUM',
          reportedById: userId,
          companyId,
        },
        include: {
          reportedBy: {
            select: {
              id: true,
              fullName: true,
              email: true,
              role: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
              contactEmail: true,
            },
          },
        },
      });

      // Criar histórico inicial
      await prisma.bugStatusHistory.create({
        data: {
          bugReportId: bugReport.id,
          newStatus: 'OPEN',
          newPriority: bugReport.priority,
          notes: 'Bug report criado',
          changedById: userId,
        },
      });

      // Enviar email de notificação para admins
      try {
        await emailService.sendBugReportNotification(bugReport);
      } catch (emailError) {
        console.error('Erro ao enviar email de notificação de bug:', emailError);
        // Não falhar a criação do bug por causa do email
      }

      return responseUtil.success(res, bugReport, 'Bug reportado com sucesso', 201);
    } catch (error) {
      console.error('Erro ao criar relatório de bug:', error);
      return responseUtil.error(res, 'Erro interno do servidor', 500);
    }
  }

  /**
   * Listar relatórios de bug (para usuários normais - apenas seus próprios)
   */
  async list(req, res) {
    try {
      const userId = req.user.id;
      const companyId = req.user.companyId;
      const { page = 1, limit = 10, status, priority, category } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      // Filtros
      const where = {
        reportedById: userId,
        companyId,
      };

      if (status) where.status = status;
      if (priority) where.priority = priority;
      if (category) where.category = category;

      const [bugReports, total] = await Promise.all([
        prisma.bugReport.findMany({
          where,
          skip,
          take,
          orderBy: { createdAt: 'desc' },
          include: {
            reportedBy: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
            statusHistory: {
              orderBy: { createdAt: 'desc' },
              take: 1,
              include: {
                changedBy: {
                  select: {
                    id: true,
                    fullName: true,
                  },
                },
              },
            },
          },
        }),
        prisma.bugReport.count({ where }),
      ]);

      const totalPages = Math.ceil(total / take);

      return responseUtil.success(res, {
        bugReports,
        pagination: {
          page: parseInt(page),
          limit: take,
          total,
          totalPages,
        },
      });
    } catch (error) {
      console.error('Erro ao listar relatórios de bug:', error);
      return responseUtil.error(res, 'Erro interno do servidor', 500);
    }
  }

  /**
   * Listar todos os relatórios de bug (apenas para SYSTEM_ADMIN)
   */
  async listAll(req, res) {
    try {
      const { page = 1, limit = 20, status, priority, category, companyId, search } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      // Filtros
      const where = {};

      if (status) where.status = status;
      if (priority) where.priority = priority;
      if (category) where.category = category;
      if (companyId) where.companyId = companyId;

      // Busca por texto
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { location: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [bugReports, total] = await Promise.all([
        prisma.bugReport.findMany({
          where,
          skip,
          take,
          orderBy: { createdAt: 'desc' },
          include: {
            reportedBy: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true,
              },
            },
            company: {
              select: {
                id: true,
                name: true,
              },
            },
            statusHistory: {
              orderBy: { createdAt: 'desc' },
              take: 3,
              include: {
                changedBy: {
                  select: {
                    id: true,
                    fullName: true,
                    role: true,
                  },
                },
              },
            },
          },
        }),
        prisma.bugReport.count({ where }),
      ]);

      const totalPages = Math.ceil(total / take);

      return responseUtil.success(res, {
        bugReports,
        pagination: {
          page: parseInt(page),
          limit: take,
          total,
          totalPages,
        },
      });
    } catch (error) {
      console.error('Erro ao listar todos os relatórios de bug:', error);
      return responseUtil.error(res, 'Erro interno do servidor', 500);
    }
  }

  /**
   * Obter detalhes de um relatório de bug específico
   */
  async getById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const isSystemAdmin = req.user.role === 'SYSTEM_ADMIN';

      const where = { id };

      // Se não for system admin, só pode ver seus próprios bugs
      if (!isSystemAdmin) {
        where.reportedById = userId;
      }

      const bugReport = await prisma.bugReport.findFirst({
        where,
        include: {
          reportedBy: {
            select: {
              id: true,
              fullName: true,
              email: true,
              role: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          statusHistory: {
            orderBy: { createdAt: 'desc' },
            include: {
              changedBy: {
                select: {
                  id: true,
                  fullName: true,
                  role: true,
                },
              },
            },
          },
        },
      });

      if (!bugReport) {
        return responseUtil.error(res, 'Relatório de bug não encontrado', 404);
      }

      return responseUtil.success(res, bugReport);
    } catch (error) {
      console.error('Erro ao obter relatório de bug:', error);
      return responseUtil.error(res, 'Erro interno do servidor', 500);
    }
  }

  /**
   * Atualizar status/prioridade de um bug (apenas SYSTEM_ADMIN)
   */
  async updateStatus(req, res) {
    try {
      const { id } = req.params;
      const { status, priority, adminNotes } = req.body;
      const userId = req.user.id;

      // Buscar o bug atual
      const currentBug = await prisma.bugReport.findUnique({
        where: { id },
      });

      if (!currentBug) {
        return responseUtil.error(res, 'Relatório de bug não encontrado', 404);
      }

      // Preparar dados para atualização
      const updateData = {};
      if (status) updateData.status = status;
      if (priority) updateData.priority = priority;
      if (adminNotes !== undefined) updateData.adminNotes = adminNotes;

      // Atualizar o bug
      const updatedBug = await prisma.bugReport.update({
        where: { id },
        data: updateData,
        include: {
          reportedBy: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Criar histórico se houve mudança de status ou prioridade
      if (status || priority) {
        await prisma.bugStatusHistory.create({
          data: {
            bugReportId: id,
            oldStatus: status ? currentBug.status : undefined,
            newStatus: status || currentBug.status,
            oldPriority: priority ? currentBug.priority : undefined,
            newPriority: priority || currentBug.priority,
            notes: adminNotes || `Status/prioridade atualizado por admin`,
            changedById: userId,
          },
        });
      }

      return responseUtil.success(res, updatedBug, 'Bug atualizado com sucesso');
    } catch (error) {
      console.error('Erro ao atualizar status do bug:', error);
      return responseUtil.error(res, 'Erro interno do servidor', 500);
    }
  }

  /**
   * Obter estatísticas dos bugs (apenas SYSTEM_ADMIN)
   */
  async getStats(req, res) {
    try {
      const { companyId } = req.query;

      const where = companyId ? { companyId } : {};

      const [
        totalBugs,
        openBugs,
        inProgressBugs,
        resolvedBugs,
        closedBugs,
        criticalBugs,
        highPriorityBugs,
        bugsByCategory,
        recentBugs,
      ] = await Promise.all([
        prisma.bugReport.count({ where }),
        prisma.bugReport.count({ where: { ...where, status: 'OPEN' } }),
        prisma.bugReport.count({ where: { ...where, status: 'IN_PROGRESS' } }),
        prisma.bugReport.count({ where: { ...where, status: 'RESOLVED' } }),
        prisma.bugReport.count({ where: { ...where, status: 'CLOSED' } }),
        prisma.bugReport.count({ where: { ...where, priority: 'CRITICAL' } }),
        prisma.bugReport.count({ where: { ...where, priority: 'HIGH' } }),
        prisma.bugReport.groupBy({
          by: ['category'],
          where,
          _count: { category: true },
        }),
        prisma.bugReport.findMany({
          where,
          take: 5,
          orderBy: { createdAt: 'desc' },
          include: {
            reportedBy: {
              select: {
                fullName: true,
              },
            },
          },
        }),
      ]);

      const stats = {
        total: totalBugs,
        byStatus: {
          open: openBugs,
          inProgress: inProgressBugs,
          resolved: resolvedBugs,
          closed: closedBugs,
        },
        byPriority: {
          critical: criticalBugs,
          high: highPriorityBugs,
          medium: totalBugs - criticalBugs - highPriorityBugs,
        },
        byCategory: bugsByCategory.reduce((acc, item) => {
          acc[item.category.toLowerCase()] = item._count.category;
          return acc;
        }, {}),
        recent: recentBugs,
      };

      return responseUtil.success(res, stats);
    } catch (error) {
      console.error('Erro ao obter estatísticas de bugs:', error);
      return responseUtil.error(res, 'Erro interno do servidor', 500);
    }
  }
}

module.exports = new BugReportController();
