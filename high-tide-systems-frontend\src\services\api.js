// Função auxiliar para obter o token atual
const getCurrentToken = () => {
  return localStorage.getItem('token');
};

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

// Função auxiliar para fazer requisições HTTP
const fetchWithAuth = async (endpoint, options = {}) => {
  const token = getCurrentToken();
  if (!token) {
    throw new Error('Usuário não autenticado');
  }

  const response = await fetch(`${API_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    }
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Erro HTTP: ${response.status}`);
  }

  return response.json();
};

// Serviço de API genérico
const api = {
  // Métodos HTTP genéricos
  get: (endpoint, options = {}) => fetchWithAuth(endpoint, { method: 'GET', ...options }),
  post: (endpoint, data, options = {}) => fetchWithAuth(endpoint, { 
    method: 'POST', 
    body: JSON.stringify(data),
    ...options 
  }),
  put: (endpoint, data, options = {}) => fetchWithAuth(endpoint, { 
    method: 'PUT', 
    body: JSON.stringify(data),
    ...options 
  }),
  patch: (endpoint, data, options = {}) => fetchWithAuth(endpoint, { 
    method: 'PATCH', 
    body: JSON.stringify(data),
    ...options 
  }),
  delete: (endpoint, options = {}) => fetchWithAuth(endpoint, { method: 'DELETE', ...options }),

  // Métodos específicos para bug reports
  bugReports: {
    // Criar um novo bug report
    create: (data) => api.post('/bug-reports', data),
    
    // Listar bug reports do usuário
    list: (params = {}) => {
      const queryString = new URLSearchParams(params).toString();
      return api.get(`/bug-reports${queryString ? `?${queryString}` : ''}`);
    },
    
    // Obter detalhes de um bug report
    getById: (id) => api.get(`/bug-reports/${id}`),
    
    // Métodos administrativos (apenas SYSTEM_ADMIN)
    admin: {
      // Listar todos os bug reports
      listAll: (params = {}) => {
        const queryString = new URLSearchParams(params).toString();
        return api.get(`/bug-reports/admin/all${queryString ? `?${queryString}` : ''}`);
      },
      
      // Obter estatísticas
      getStats: (params = {}) => {
        const queryString = new URLSearchParams(params).toString();
        return api.get(`/bug-reports/admin/stats${queryString ? `?${queryString}` : ''}`);
      },
      
      // Atualizar status de um bug report
      updateStatus: (id, data) => api.patch(`/bug-reports/${id}/status`, data)
    }
  }
};

export default api;
