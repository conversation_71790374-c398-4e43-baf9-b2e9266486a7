'use client';

import React from 'react';
import { MessageCircle } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { useUnreadMessages } from '@/hooks/useUnreadMessages';
import { motion } from 'framer-motion';

const ChatButton = () => {
  const { toggleChatPanel, resetUnreadCount, unreadCount } = useChat();
  const { user } = useAuth();

  // Se o usuário não estiver logado, não mostrar o botão
  if (!user) return null;

  return (
    <div className="relative">
    <button
      onClick={toggleChatPanel}
      className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full relative transition-colors"
      aria-label="Mensagens"
    >
      <MessageCircle size={20} aria-hidden="true" />
      {unreadCount > 0 && (
        <motion.span
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute -top-1 -right-1 min-w-[20px] h-5 flex items-center justify-center rounded-full bg-cyan-500 text-white text-xs font-bold px-1"
        >
          {unreadCount > 99 ? '99+' : unreadCount}
        </motion.span>
      )}
    </button>
    </div>
  );
};

export default ChatButton;
