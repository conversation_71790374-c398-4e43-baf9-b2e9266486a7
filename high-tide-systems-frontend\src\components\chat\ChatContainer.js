'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, MessageCircle, Users, Settings, Search, Plus } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { useSharedItemNavigation } from '@/hooks/useSharedItemNavigation';
import ChatList from './ChatList';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import ChatHeader from './ChatHeader';

const ChatContainer = ({ isOpen, onClose, compact = false }) => {
  const { conversations, activeConversation, setActiveConversation } = useChat();
  const { handleSharedItemClick } = useSharedItemNavigation();
  const [isMinimized, setIsMinimized] = useState(false);
  const [showConversationList, setShowConversationList] = useState(false);
  const containerRef = useRef(null);

  // Fechar ao clicar fora do container
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const toggleConversationList = () => {
    setShowConversationList(!showConversationList);
  };

  const handleNewConversation = () => {
    // Implementar criação de nova conversa
    console.log('Nova conversa');
  };

  const containerVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 20
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      y: 20,
      transition: {
        duration: 0.2,
        ease: "easeIn"
      }
    }
  };

  const minimizedVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 20
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  if (!isOpen) return null;

  if (isMinimized) {
    return (
      <AnimatePresence>
        <motion.div
          ref={containerRef}
          className="fixed bottom-4 right-4 z-50"
          variants={minimizedVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-cyan-200/50 dark:border-cyan-800/50 flex items-center space-x-4">
            <div className="w-8 h-8 border-3 border-cyan-200 dark:border-cyan-700 rounded-full"></div>
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Chat ativo
            </div>
            <button
              onClick={toggleMinimize}
              className="p-2 text-cyan-500 hover:text-cyan-600 dark:text-cyan-400 dark:hover:text-cyan-300 rounded-lg hover:bg-cyan-100/50 dark:hover:bg-cyan-900/20 transition-all duration-200"
            >
              <MessageCircle size={16} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-all duration-200"
            >
              <X size={16} />
            </button>
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      <motion.div
        ref={containerRef}
        className={`chat-container fixed z-50 flex flex-col overflow-hidden bg-gradient-to-br from-white via-cyan-50 to-cyan-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 shadow-2xl rounded-2xl border border-cyan-200/50 dark:border-cyan-800/50 backdrop-blur-sm ${
          compact 
            ? 'bottom-4 right-4 w-80 h-96' 
            : 'bottom-4 right-4 w-96 h-[600px]'
        }`}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        {/* Header */}
        <ChatHeader
          onClose={onClose}
          onMinimize={toggleMinimize}
          onToggleList={toggleConversationList}
          onNewConversation={handleNewConversation}
          showConversationList={showConversationList}
          compact={compact}
        />

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Lista de conversas */}
          <AnimatePresence>
            {showConversationList && (
              <motion.div
                className="w-1/2 border-r border-cyan-200/50 dark:border-cyan-800/50"
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: '50%', opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                <ChatList
                  conversations={conversations}
                  activeConversation={activeConversation}
                  onSelectConversation={setActiveConversation}
                  compact={compact}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Área de mensagens */}
          <div className={`flex-1 flex flex-col ${!showConversationList ? 'w-full' : ''}`}>
            {activeConversation ? (
              <>
                <ChatMessages
                  conversationId={activeConversation.id}
                  compact={compact}
                  onSharedItemClick={handleSharedItemClick}
                />
                <ChatInput
                  conversationId={activeConversation.id}
                  compact={compact}
                />
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center p-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <MessageCircle size={24} className="text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    Selecione uma conversa
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Escolha uma conversa para começar a enviar mensagens
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ChatContainer;
